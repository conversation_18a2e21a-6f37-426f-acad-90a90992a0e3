{"extends": ["eslint:recommended", "next", "next/core-web-vitals", "plugin:storybook/recommended", "plugin:jsx-a11y/strict", "prettier"], "rules": {"jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-autofocus": "off"}, "plugins": ["jsx-a11y"], "parserOptions": {"ecmaVersion": 2022, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"es6": true, "browser": true, "node": true, "jest": true}}