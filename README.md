## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Translations in the project

The project uses https://react.i18next.com/ for translations. The dictionaries are located in `app/i18n/locales` folder.

If you use translations in the client component, use the `useTranslation` hook from the `app/i18n/client.js` (check out the Header for the example).

If you use translations in the server component, use the **async** `useTranslation` function from the `app/i18n/index.js` (check out the Footer for the example).

### Translation dictionaries

Let's keep the keys inside the `common.js` file in alphabetical order so that it’s easy to find if we’ve already had that key translated.

Let’s name the key exactly as the English original text is, but written in camelCase.
If the original text is long, let’s name the key creatively, extracting the essence of its meaning.

## How/where to use some of the components

Use the `Container` component to wrap the content of the page where needed. It will add the max-width to the page considering different breakpoints.

For all the texts use the `Typography` or the `Label` component.
Typography types are the following: `h1, h2, h3, h4, caption1, caption2, body1, body2`.
Label types are the following: `bigger, smaller, uppercase`.

## Styling

We can use either **styled-components** or **Tailwind** for styling.

### Styled-components

The global styles are located in the `app/GlobalStyles.ts` file.
The theme is located in the `app/theme.ts` file.

If you want to use the theme in the component, you can import it directly from `app/theme.ts` file.

**If you create a new styled component, please name it with the `Styled` prefix, e.g. `StyledLoginButton`.**

## Environmental variables

The environment variables are located in the `.env.local` file.

If you need them in the browser, it is necessary to prefix them with `NEXT_PUBLIC_`.

Variables without the `NEXT_PUBLIC_` prefix are only available in the server environment.

#
