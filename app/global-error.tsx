'use client';
import * as Sentry from '@sentry/nextjs';
import { useEffect } from 'react';
import { isClientReady } from '@/utils';
import { Button, Icon, Typography, Image } from '@/atomic-design-components';
import { DEFAULT_LANGUAGE, LANGUAGES } from '@/app/i18n/settings.ts';
import { LanguagesType } from '@/types/global';
import { theme } from '@/theme.ts';
import { g<PERSON><PERSON> } from '@/app/fonts';
import '@/app/globals.css';
import Link from 'next/link';
import IntercomScript from '@/app/scripts/IntercomScript.ts';
import { StyledLiveSupport } from '@/app/components/NavMenu/styled.ts';

export default function GlobalError({ error }: { error: Error }) {
  // const [errorInfo, setErrorInfo] = useState<Error | null>(null);
  const lngFromUrl = isClientReady() && window.location.pathname.split('/')[1];
  const lng = LANGUAGES.includes(lngFromUrl as LanguagesType)
    ? (lngFromUrl as LanguagesType)
    : DEFAULT_LANGUAGE;

  useEffect(() => {
    console.log('ERROR HERE:', error);
    Sentry.captureException(error);
    // setErrorInfo(error);
  }, [error]);

  const commonTranslations = require(`@/app/i18n/locales/${lng}/common.json`);
  const defaultCommonTranslations = require(
    `@/app/i18n/locales/${DEFAULT_LANGUAGE}/common.json`,
  );

  return (
    <html lang={lng} className={gilroy.className}>
      <head>
        <meta httpEquiv='Permissions-Policy' content='fullscreen=(self)' />
        {/* iOS and Android status bar style */}
        <meta name='apple-mobile-web-app-capable' content='yes' />
        <meta
          name='apple-mobile-web-app-status-bar-style'
          content='black-translucent'
        />
        <meta name='theme-color' content={theme?.color?.general?.darkest} />
      </head>
      <body className='bg-general-darkest'>
        <div className='mx-auto mt-[-25px] flex h-screen max-w-[900px] flex-col items-center justify-center px-8'>
          <div className='relative h-[140px] w-[100%] md:h-[370px]'>
            <Image
              src='/errorPageImage.png'
              alt='404'
              fill={true}
              sizes='350px'
              style={{
                objectFit: 'contain',
              }}
              unoptimized
            />
          </div>
          <Typography
            fontSize='44px'
            lineHeight='58px'
            fontWeight='600'
            textAlign='center'
            className='max-md:!hidden'
            theme={theme}
          >
            {commonTranslations.somethingWentWrong ||
              defaultCommonTranslations.somethingWentWrong}
            <br />
            {commonTranslations.noWorriesWeFixing ||
              defaultCommonTranslations.noWorriesWeFixing}
          </Typography>
          <Typography
            fontSize='20px'
            lineHeight='28px'
            fontWeight='600'
            textAlign='center'
            className='md:!hidden'
            theme={theme}
          >
            {commonTranslations.somethingWentWrong ||
              defaultCommonTranslations.somethingWentWrong}
            <br />
            {commonTranslations.noWorriesWeFixing ||
              defaultCommonTranslations.noWorriesWeFixing}
          </Typography>
          <Typography
            type='body2'
            textAlign='center'
            className='max-md:!hidden'
            theme={theme}
          >
            {commonTranslations.refreshPageOrGoBack ||
              defaultCommonTranslations.refreshPageOrGoBack}
            <br />
            {commonTranslations.thanksForSticking ||
              defaultCommonTranslations.thanksForSticking}
          </Typography>
          <Typography textAlign='center' className='md:!hidden' theme={theme}>
            {commonTranslations.refreshPageOrGoBack ||
              defaultCommonTranslations.refreshPageOrGoBack}
            <br />
            {commonTranslations.thanksForSticking ||
              defaultCommonTranslations.thanksForSticking}
          </Typography>
          <Link href={`/${lng}`}>
            <Button
              className='mt-4'
              text={
                commonTranslations.reload || defaultCommonTranslations.reload
              }
              theme={theme}
            />
          </Link>
        </div>
        <IntercomScript lng={lng} />
        <StyledLiveSupport className='live_support_button' isOpen theme={theme} right='25px' bottom='25px'>
          <Icon
            name='headphones'
            theme={theme}
            fill={theme?.color?.general?.lightest}
            width={24}
            height={24}
          />
        </StyledLiveSupport>
      </body>
    </html>
  );
}
