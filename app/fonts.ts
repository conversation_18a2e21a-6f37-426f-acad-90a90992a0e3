import localFont from 'next/font/local';

export const gilroy = localFont({
  src: [
    { path: './fonts/<PERSON>roy-Light.woff2', weight: '300', style: 'normal' },
    { path: './fonts/<PERSON><PERSON>-Regular.woff2', weight: '400', style: 'normal' },
    { path: './fonts/Gilroy-Semibold.woff2', weight: '600', style: 'normal' },
    { path: './fonts/<PERSON>roy-Bold.woff2', weight: '700', style: 'normal' },
    { path: './fonts/<PERSON>roy-Heavy.ttf', weight: '800', style: 'normal' },
  ],
});

export const fontello = localFont({
  src: [
    { path: './fonts/fontello.woff2', weight: '400', style: 'normal' },
    { path: './fonts/fontello.woff', weight: '400', style: 'normal' },
    { path: './fonts/fontello.ttf', weight: '400', style: 'normal' },
  ],
});
