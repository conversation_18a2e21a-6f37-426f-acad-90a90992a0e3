interface FetchDataProps {
  url: string;
  token?: string;
  method?: 'GET' | 'POST' | 'PATCH';
  withCache?: boolean;
  filters?: { [key: string]: (string | number)[] };
  revalidateTags?: string[];
  type?: string;
  body?: any;
  contentType?: 'application/json' | 'application/x-www-form-urlencoded';
  revalidateTime?: number;
  fetchTimeout?: number;
}

class TimeoutError extends Error {
  constructor(message = 'Network request timed out', cause?: unknown) {
    super(message);
    this.name = 'TimeoutError';
    if (cause !== undefined) this.cause = cause;
  }
}

const fetchData = async ({
  url,
  withCache = false,
  revalidateTime,
  token,
  method = 'GET',
  filters,
  revalidateTags,
  type,
  body,
  contentType = 'application/json',
  fetchTimeout = 0,
}: FetchDataProps) => {
  const timeoutMs = fetchTimeout || (method === 'POST' && !url.includes('/payment/') ? 15000 : 0);
  try {
    if (filters && Object.keys(filters)?.length) {
      let filtersArray: string[] = [];
      Object.keys(filters).forEach((key) => {
        filtersArray.push(`${key}=${filters[key].join('|')}`);
      });
      url = `${url}${url.includes('?') ? '&' : '?'}filters=${filtersArray.join(',')}`;
    }

    // Timeout controller with real Error reason
    const controller = new AbortController();
    let timeoutId: ReturnType<typeof setTimeout> | undefined;

    if (timeoutMs > 0) {
      timeoutId = setTimeout(() => {
        try {
          controller.abort(new TimeoutError());
        } catch {
          controller.abort();
        }
      }, timeoutMs);
    }

    const requestOptions: {
      method: 'GET' | 'POST' | 'PATCH';
      cache?: 'no-store' | 'force-cache';
      headers?: { [key: string]: string };
      body?: string;
      next?: { revalidate?: number; tags?: string[] };
      signal?: AbortSignal;
    } = {
      method,
      headers: {
        'Content-Type':
          contentType === 'application/json' ? contentType + '; charset=utf-8' : contentType,
      },
      signal: controller.signal,
    };

    if (withCache) {
      requestOptions.next = { revalidate: revalidateTime || 60 };
    } else {
      requestOptions.cache = 'no-store';
    }

    if (revalidateTags?.length) {
      if (!requestOptions.next) {
        requestOptions.next = { tags: revalidateTags };
      } else {
        requestOptions.next.tags = revalidateTags;
      }
    }

    if (token) {
      requestOptions.headers = {
        ...requestOptions.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    if (body) {
      requestOptions.body =
        contentType === 'application/x-www-form-urlencoded'
          ? new URLSearchParams(body).toString()
          : JSON.stringify(body);
    }

    try {
      const response = await fetch(url, requestOptions);

      const result = await response.json();

      if (!response.ok) {
        console.error(
          `Error fetching ${type || 'data'}: ${result.detail?.parameters?.error_text || result.detail?.message || result.detail || ''}`,
        );
      }
      return result || {};
    } catch (error) {
      const isTimeout =
        (timeoutMs > 0 &&
          controller.signal.aborted &&
          (controller.signal as any).reason?.name === 'TimeoutError') ||
        error instanceof TimeoutError ||
        error === 'Timeout' ||
        (error instanceof Error && (error.name === 'TimeoutError' || error.name === 'AbortError'));

      if (isTimeout) {
        const result = {
          error: 'timeoutError',
          detail: {
            message: 'Request timed out, please try again.',
            entity: 'Request',
            parameters: {
              error_text: 'timeout error',
              error_code: 408,
            },
          },
        };
        return result;
      }
      throw error; // Re-throw other errors to be caught by outer catch block
    } finally {
      if (timeoutId) clearTimeout(timeoutId);
    }
  } catch (error) {
    console.error(
      `Fetch error: ${(error instanceof Error && error?.message) || 'An unknown error occurred.'}`,
    );
    return { error: (error instanceof Error && error?.message) || 'An unknown error occurred.' };
  }
};
export { fetchData };
