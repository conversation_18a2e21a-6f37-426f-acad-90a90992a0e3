'use server';
import { cookies } from 'next/headers';
import { COOKIES_HTTP_ONLY_OPTIONS } from '@/constants.ts';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';

export const setUserDataAfterLogin = async (result: any) => {
  cookies().set('token', result.access_token, COOKIES_HTTP_ONLY_OPTIONS);
  cookies().set('refresh', result.refresh_token, COOKIES_HTTP_ONLY_OPTIONS);
  cookies().set(
    'expires',
    (Date.now() + result.expires_in * 1000).toString(),
    COOKIES_HTTP_ONLY_OPTIONS,
  );

  const userResult = await getCurrentUserServer();

  cookies().set('userCurrency', userResult?.currency || '', {
    httpOnly: false,
    sameSite: 'none',
    secure: true,
    path: '/',
    maxAge: 34560000,
  });
  cookies().set('userExternalId', userResult?.id || '', {
    httpOnly: false,
    sameSite: 'none',
    secure: true,
    path: '/',
    maxAge: 34560000,
  });

  return userResult;
};
