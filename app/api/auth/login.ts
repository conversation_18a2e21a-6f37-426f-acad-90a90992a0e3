import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';
import { cookies } from 'next/headers';
import { COOKIES_HTTP_ONLY_OPTIONS } from '@/constants.ts';

export interface LoginData {
  email?: string;
  phone?: string;
  password: string;
}

export const setUserDataAfterLogin = async (result: any) => {
  cookies().set('token', result.access_token, COOKIES_HTTP_ONLY_OPTIONS);
  cookies().set('refresh', result.refresh_token, COOKIES_HTTP_ONLY_OPTIONS);
  cookies().set(
    'expires',
    (Date.now() + result.expires_in * 1000).toString(),
    COOKIES_HTTP_ONLY_OPTIONS,
  );

  const userResult = await getCurrentUserServer();

  cookies().set('userCurrency', userResult?.currency || '', {
    httpOnly: false,
    sameSite: 'none',
    secure: true,
    path: '/',
    maxAge: 34560000,
  });
  cookies().set('userExternalId', userResult?.id || '', {
    httpOnly: false,
    sameSite: 'none',
    secure: true,
    path: '/',
    maxAge: 34560000,
  });

  return userResult;
};

const login = async (requestBody: LoginData) => {
  try {
    let url = `${API_BASE_URL}/platform/auth/signin`;

    const result = await fetchData({
      url,
      method: 'POST',
      body: requestBody,
      fetchTimeout: 12000,
    });

    if (result?.detail || result?.error) {
      return {
        error:
          result.detail.parameters?.error_text ||
          result.detail.message ||
          result.detail ||
          result.error,
      };
    }

    if (result.access_token && result.refresh_token) {
      const userResult = await setUserDataAfterLogin(result);
      return userResult;
    }
  } catch (error) {
    return { error };
  }
};

export { login };
