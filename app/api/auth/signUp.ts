export interface SignUpData {
  clickid?: string;
  click_time?: string;
  country: string;
  currency: string;
  email?: string;
  ip_address?: string;
  landing?: string;
  language: string;
  password: string;
  phone?: string;
  promo_code?: string;
  subid?: string;
  sub1?: string;
  sub2?: string;
  sub3?: string;
  sub4?: string;
  sub5?: string;
  tracker?: string;
}

const signUp = async (requestBody: SignUpData) => {
  const r = await fetch('/api/auth/signUp', {
    method: 'POST',
    cache: 'no-store',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(requestBody),
  });

  return r.ok ? (await r.json()) ?? null : null;
};

export { signUp };
