import { NextResponse } from 'next/server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { setUserDataAfterAuth } from '@/app/api/auth/setUserDataAfterAuth.ts';
import { SignUpData } from '@/app/api/auth/signUp.ts';

export async function POST(req: Request): Promise<Response> {
  try {
    const requestBody: SignUpData = await req.json();

    const url = `${API_BASE_URL}/platform/auth/signup`;

    const result = await fetchData({
      url,
      method: 'POST',
      body: requestBody,
      fetchTimeout: 12000,
    });

    console.log(result, 'signup');

    if (result?.detail || result?.error) {
      return NextResponse.json({
        error:
          result.detail?.parameters?.error_text ||
          result.detail?.message ||
          result.detail ||
          result.error,
      });
    }

    if (result.access_token && result.refresh_token) {
      const userResult = await setUserDataAfterAuth(result);
      return NextResponse.json(userResult);
    } else {
      return NextResponse.json({
        error: 'Error logging in after signup',
      });
    }
  } catch (error) {
    console.error('Error during signup:', error);
    return NextResponse.json({ error: 'Signup failed' }, { status: 500 });
  }
}
