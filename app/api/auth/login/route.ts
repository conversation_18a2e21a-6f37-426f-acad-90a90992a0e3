import { NextResponse } from 'next/server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { setUserDataAfterAuth } from '@/app/api/auth/setUserDataAfterAuth.ts';

export interface LoginData {
  email?: string;
  phone?: string;
  password: string;
}

export async function POST(req: Request): Promise<Response> {
  try {
    const requestBody: LoginData = await req.json();

    const url = `${API_BASE_URL}/platform/auth/signin`;

    const result = await fetchData({
      url,
      method: 'POST',
      body: requestBody,
      fetchTimeout: 12000,
    });

    if (result?.detail || result?.error) {
      return NextResponse.json({
        error:
          result.detail?.parameters?.error_text ||
          result.detail?.message ||
          result.detail ||
          result.error,
      });
    }

    if (result.access_token && result.refresh_token) {
      const userResult = await setUserDataAfterAuth(result);
      return NextResponse.json(userResult);
    }

    return NextResponse.json({ error: 'Invalid response from server' });
  } catch (error) {
    console.error('Error during login:', error);
    return NextResponse.json({ error: 'Login failed' }, { status: 500 });
  }
}
