import { cookies } from 'next/headers';
import { API_BASE_URL } from './constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

const getGameToken = async (currency: string) => {
  const tokenVerified = cookies().get('token')?.value;

  let url = `${API_BASE_URL}/platform/games/token/${currency}`;

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: 'game token',
  });

  return result;
};

export { getGameToken };
