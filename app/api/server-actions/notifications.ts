'use server';
import { fetchData } from '@/app/api/fetchData.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { ITranslations } from '@/types/global';

export type NotificationTypes =
  | 'deposit_completed'
  | 'deposit_completed_tournament'
  | 'withdrawal_rollback'
  | 'withdrawal_rejected'
  | 'withdrawal_completed'
  | 'wager_open'
  | 'wager_cancel'
  | 'wager_lose'
  | 'wager_win'
  | 'wager_win_cut'
  | 'freespins_open'
  | 'freespins_cancel'
  | 'level_up'
  | 'freespins_new'
  | 'wager_new'
  | 'new_bonus'
  | 'new_bonuses'
  // | 'freespins_close' // don't show to user!!
  // | 'deposit_failed'
  | '';

export interface INotification {
  id: number | string;
  created_at: string;
  updated_at?: string;
  details: {
    amount?: number;
    cutwin?: number;
    level?: number;
    paycode?: number;
    spins?: number;
    tempid?: number;
    extra?: {
      game_name?: string;
      game_slug?: string;
      game_provider_slug?: string;
      bonus_name?: string;
      bonus_title?: ITranslations;
    };
  };
  newBonusesMessages?: Array<INotification>;
  notification_type: NotificationTypes;
  status?: 'new' | 'read';
  buttonText?: string;
  buttonLink?: string;
  translationsData: {
    amountInCurrency?: string;
    spinsAmount?: number;
    gameName?: string;
    level?: number;
    bonusesCount?: string | number;
    count?: number;
    templateName?: string;
  };
}

const readNotificationsAll = async () => {
  let url = `${API_BASE_URL}/platform/user/notifications/read-all`;

  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    type: 'notifications all read',
    method: 'POST',
    token: tokenVerified,
  });

  return result;
};

const readNotification = async (id: number | string) => {
  let url = `${API_BASE_URL}/platform/user/notifications/${id}`;

  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    type: 'notification read',
    method: 'PATCH',
    token: tokenVerified,
  });

  return result;
};

export { readNotification, readNotificationsAll };
