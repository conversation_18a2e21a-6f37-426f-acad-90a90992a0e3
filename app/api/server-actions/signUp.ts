'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { setUserDataAfterLogin } from '@/app/api/auth/login.ts';

export interface SignUpData {
  clickid?: string;
  click_time?: string;
  country: string;
  currency: string;
  email?: string;
  ip_address?: string;
  landing?: string;
  language: string;
  password: string;
  phone?: string;
  promo_code?: string;
  subid?: string;
  sub1?: string;
  sub2?: string;
  sub3?: string;
  sub4?: string;
  sub5?: string;
  tracker?: string;
}

const signUp = async (requestBody: SignUpData) => {
  let url = `${API_BASE_URL}/platform/auth/signup`;

  const result = await fetchData({
    url,
    method: 'POST',
    body: requestBody,
    fetchTimeout: 12000,
  });

  console.log(result, 'signup');

  if (result?.detail || result?.error) {
    return {
      error:
        result.detail.parameters?.error_text ||
        result.detail.message ||
        result.detail ||
        result.error,
    };
  }

  if (result.access_token && result.refresh_token) {
    const userResult = await setUserDataAfterLogin(result);
    return userResult;
  } else {
    return {
      error: 'Error logging in after signup',
    };
  }
};

export { signUp };
