'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';

export interface RequestResetData {
  email?: string;
  phone?: string;
  phone_type?: 'call' | 'sms' | ''; // + on backend: | 'telegram' | 'viber' | 'vk' | 'whatsapp'
}

export interface VerifyData {
  code: string;
  code_type: 'reset_password_by_phone' | 'confirm_phone' | 'confirm_email' | 'reset_password'; // by email
  isPhone?: boolean;
}

const setRequestResetPassNumber = async (number: string) => {
  cookies().set('requestResetPassNumber', number, {
    httpOnly: false,
    sameSite: 'none',
    secure: true,
    path: '/',
    maxAge: 34560000,
  });
};

const requestResetPassword = async (requestBody: RequestResetData) => {
  try {
    let url = `${API_BASE_URL}/platform/auth/request-reset-password`;
    const result = await fetchData({ url, method: 'POST', body: requestBody });

    if (result?.detail) {
      return {
        error: result.detail.parameters?.error_text || result.detail.message || result.detail,
      };
    }

    return result;
  } catch (error) {
    return { error };
  }
};

const verifyCode = async (requestBody: VerifyData) => {
  try {
    let url = `${API_BASE_URL}/platform/auth/verify-code`;

    const result = await fetchData({
      url,
      method: 'POST',
      body: { ...requestBody },
    });

    if (result?.detail) {
      return {
        error: result.detail.parameters?.error_text || result.detail.message || result.detail,
      };
    }

    if (
      result.result === 'ok' &&
      requestBody.code_type === 'reset_password' &&
      requestBody.isPhone
    ) {
      cookies().delete('requestResetPassNumber');
    }

    return result;
  } catch (error) {
    return { error };
  }
};

export { requestResetPassword, verifyCode, setRequestResetPassNumber };
