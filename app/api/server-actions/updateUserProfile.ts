'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';

export const updateUserProfile = async (data: any) => {
  let url = `${API_BASE_URL}/platform/user/profile`;
  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    type: 'update user profile',
    body: data,
    token: tokenVerified,
    method: 'PATCH',
  });

  return result;
};
