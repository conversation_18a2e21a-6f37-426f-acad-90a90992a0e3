'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { ITranslations } from '@/types/global';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';

export type IBonusType =
  | 'wager_deposit'
  | 'wager_no_deposit'
  | 'freespins_deposit'
  | 'freespins_no_deposit'
  | 'wager_freespins_deposit'
  | '';

export type BonusCategoryType =
  | 'birthday'
  | 'cashback'
  | 'cron'
  | 'deposit_1'
  | 'deposit_2'
  | 'deposit_3'
  | 'deposit_4'
  | 'deposit_5'
  | 'individual'
  | 'level_up'
  | 'once'
  | 'promo_code'
  | 'sign_up';

export type IBonusGlobalType = 'freespins' | 'wager' | 'template' | 'wager_no_deposit';

export interface IBonus {
  status: string;
  bonus_amount: number;
  bonus_category: BonusCategoryType;
  bonus_type: IBonusType;
  bonus_global_type: IBonusGlobalType;
  currency: string;
  end_date: string;
  errors?: number[];
  external_id: number;
  game_name: string;
  game_slug: string;
  game_provider_slug: string;
  id: number;
  name: string;
  photo_main_url: string;
  photo_round_url: string;
  start_date: string;
  title: ITranslations;
  affiliate_id: number;
  bet_amount: number;
  birthday_lifetime_days: number;
  deposit_amount_percentage: number;
  description: ITranslations;
  freespins_amount: number;
  freespins_denomination: number;
  freespins_game_id: number;
  freespins_provider: string;
  hours_to_activate: number;
  hours_to_play: number;
  time_to_activate: number; // timer from server in seconds: how many seconds now remains until the end of activation period
  time_to_play_seconds: number;
  is_timer_activated: boolean;
  is_verified_email: boolean;
  is_verified_kyc: boolean;
  is_verified_passport: boolean;
  is_verified_phone: boolean;
  level_max: number;
  level_min: number;
  min_deposit_amount: number;
  max_deposit_amount: number;
  max_activation_count: number;
  max_bet_wager: number;
  max_win_amount?: number;
  max_win_sum?: number;
  max_win_multiplier: number;
  multiplier?: number;
  min_balance_amount: number;
  wager_multiplier: number;
  wager_amount?: number;
  payment_codes: number[];
  payment_setting_ids: number[];
  photos: object;
}

// const applyFiltersOnFrontend = (
//   filters: { [key: string]: (string | number)[] },
//   items: any,
// ) => {
//   return items.filter((item: any) => {
//     return Object.entries(filters).every(([filterKey, filterValues]) => {
//       return filterValues.includes(item[filterKey]);
//     });
//   });
// };

export const activateBonus = async (bonusId: number, type: IBonusGlobalType) => {
  const url = `${API_BASE_URL}/platform/user/bonus/activate`;

  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return;
  }
  console.log(`activate bonus func with type: ${type}, id: ${bonusId}`);

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: `activate bonus with type: ${type}, id: ${bonusId}`,
    method: 'POST',
    body: {
      bonus_id: bonusId,
      bonus_type: type,
    },
  });

  if (!result?.success || result?.detail) {
    const errorText = JSON.stringify(result.detail?.parameters); //result.detail?.parameters?.error_text;
    //     ?.split(
    //   'template activate error: ',
    // )?.[1];
    return {
      error: errorText,
      // error: errorText
      //   ? errorText.charAt(0).toUpperCase() + errorText.slice(1)
      //   : 'Error activating bonus',
    };
  }

  const newUserInfo = await getCurrentUserServer();

  if (!newUserInfo || newUserInfo.detail) {
    return {
      error: newUserInfo?.detail?.message || newUserInfo?.detail || 'Error activating bonus',
    };
  }

  return {
    user: newUserInfo,
  };
};

export const cancelBonus = async (bonusId: number, type: 'wager' | 'freespins') => {
  const url = `${API_BASE_URL}/platform/user/bonus/cancel`;

  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return;
  }

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: `cancel bonus with type: ${type}, id: ${bonusId}`,
    method: 'POST',
    body: {
      bonus_id: bonusId,
      bonus_type: type,
    },
  });

  if (!result?.success || result?.detail || result?.error) {
    return {
      error: result.detail?.message || result.detail || result.error || 'Error canceling bonus',
    };
  }

  const newUserInfo = await getCurrentUserServer();

  if (!newUserInfo || newUserInfo.detail) {
    return {
      error: newUserInfo?.detail?.message || newUserInfo?.detail || 'Error canceling bonus',
    };
  }

  return {
    user: newUserInfo,
  };
};

export const activatePromoCode = async (promoCode: string) => {
  const url = `${API_BASE_URL}/platform/user/promocode`;

  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return;
  }

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: `activate promo code: ${promoCode}`,
    method: 'POST',
    body: {
      promocode: promoCode,
    },
  });

  if (result.result !== 'ok') {
    return {
      error: 'Error activating promo code',
    };
  }

  return { ok: true };
};
