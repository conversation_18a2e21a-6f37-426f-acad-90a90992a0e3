import { cookies as cookiesForServerComponent } from 'next/headers';
import { refreshToken } from '@/app/api/server-actions/refreshToken.ts';

export function setRtFailCookie(cookies: any, value: number) {
  cookies.set('rt_fail', String(value), {
    httpOnly: false,
    sameSite: 'none',
    path: '/',
    secure: true,
    maxAge: 34560000,
  });
}

export const verifyToken = async (
  reqCookiesFromMiddleware?: any,
  responseCookiesFromMiddleware?: any,
  pathname?: string,
) => {
  const cookies = reqCookiesFromMiddleware || cookiesForServerComponent();

  const refreshTokenFromCookies = cookies.get('refresh')?.value;

  if (!refreshTokenFromCookies) {
    return null;
  }

  // verify validity of the token
  const expiresFromCookies = cookies.get('expires')?.value;
  const expirationDate = expiresFromCookies;

  const accessTokenFromCookies = cookies.get('token')?.value;
  const tokenToVerify = accessTokenFromCookies;

  const userId = cookies.get('userExternalId')?.value;

  // 5 minutes before expiration
  const isTokenExpired = !tokenToVerify || Date.now() >= +expirationDate - 60000;

  if (isTokenExpired) {
    console.log(
      'refreshToken',
      userId,
      pathname,
      refreshTokenFromCookies,
      expiresFromCookies,
      !!accessTokenFromCookies,
    );
    const prevRtFail = cookies.get('rt_fail')?.value;

    const refreshTokenResult = await refreshToken(
      responseCookiesFromMiddleware,
      refreshTokenFromCookies,
      prevRtFail,
      pathname,
      userId,
    );

    return {
      accessToken: refreshTokenResult?.access_token,
      refreshToken: refreshTokenResult?.refresh_token,
      expires: refreshTokenResult?.expires_in,
      isNew: true,
    };
  }

  return {
    accessToken: tokenToVerify,
    isNew: false,
  };
};
