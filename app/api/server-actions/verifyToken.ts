import { cookies as cookiesForServerComponent } from 'next/headers';
import { refreshToken } from '@/app/api/server-actions/refreshToken.ts';

export const verifyToken = async (
  reqCookiesFromMiddleware: any,
  responseCookiesFromMiddleware: any,
  pathname: string,
) => {
  const cookies = reqCookiesFromMiddleware || cookiesForServerComponent();
  // verify validity of the token
  const expiresFromCookies = cookies.get('expires')?.value;
  const expirationDate = expiresFromCookies;

  const accessTokenFromCookies = cookies.get('token')?.value;
  const tokenToVerify = accessTokenFromCookies;

  const isTokenExpired = !tokenToVerify || Date.now() >= +expirationDate;

  if (isTokenExpired) {
    console.log('refreshToken', expiresFromCookies, !!accessTokenFromCookies, pathname);
    const newAccessToken = await refreshToken(
      reqCookiesFromMiddleware,
      responseCookiesFromMiddleware,
    );
    return newAccessToken;
  }

  return tokenToVerify;
};
