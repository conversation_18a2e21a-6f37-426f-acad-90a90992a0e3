'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';

export const toggleFavouriteGame = async (id: number) => {
  const headers: { 'Content-Type': string; Authorization?: string } = {
    'Content-Type': 'application/json',
  };

  const tokenVerified = cookies().get('token')?.value;

  if (tokenVerified) {
    headers.Authorization = `Bearer ${tokenVerified}`;
  }

  const res = await fetch(`${API_BASE_URL}/platform/games/favourites/${id}`, {
    method: 'PATCH',
    headers,
  });

  if (!res.ok) {
    console.error(
      `Error toggling favourite game: ${id}. Status: ${res.statusText}`,
    );
    return false;
  }

  return await res.json();
};
