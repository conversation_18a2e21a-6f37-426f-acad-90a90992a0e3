'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';

export interface RequestConfirmEmail {
  email: string;
}

export interface RequestConfirmPhone {
  phone: string;
  phone_type?: 'call' | 'sms' | ''; // + on backend: | 'telegram' | 'viber' | 'vk' | 'whatsapp'
}

const requestConfirmEmail = async (requestBody: RequestConfirmEmail) => {
  try {
    let url = `${API_BASE_URL}/platform/user/request-confirm-email`;
    const tokenVerified = cookies().get('token')?.value;

    const result = await fetchData({
      url,
      method: 'POST',
      body: requestBody,
      token: tokenVerified,
    });

    if (result?.detail) {
      return {
        error: result.detail.parameters?.error_text || result.detail.message || result.detail,
      };
    }
    return result;
  } catch (error) {
    return { error };
  }
};

const requestConfirmPhone = async (requestBody: RequestConfirmPhone) => {
  try {
    let url = `${API_BASE_URL}/platform/user/request-confirm-phone`;
    const tokenVerified = cookies().get('token')?.value;

    const result = await fetchData({
      url,
      method: 'POST',
      body: requestBody,
      token: tokenVerified,
    });
    if (result?.detail) {
      return {
        error: result.detail.parameters?.error_text || result.detail.message || result.detail,
      };
    }
    return result;
  } catch (error) {
    return { error };
  }
};

export { requestConfirmEmail, requestConfirmPhone };
