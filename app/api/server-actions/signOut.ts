'use server';
import { cookies } from 'next/headers';
import { LanguagesType } from '@/types/global';
import { redirect } from 'next/navigation';

export async function signOut(lng?: LanguagesType) {
  cookies().delete('token');
  cookies().delete('refresh');
  cookies().delete('expires');
  cookies().delete('userExternalId');
  // cookies().delete('userCurrency');
  if (lng) {
    redirect(`/${lng}`);
  }
}
