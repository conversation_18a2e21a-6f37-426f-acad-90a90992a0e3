'use server';

import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '../fetchData';

const updatePassword = async (requestBody: {
  old_password?: string;
  new_password: string;
  token?: string;
}) => {
  let url = `${API_BASE_URL}/platform/user/update_password`;
  const tokenVerified = requestBody.token || cookies().get('token')?.value;

  const result = await fetchData({
    url,
    type: 'update password',
    body: requestBody,
    token: tokenVerified,
    method: 'POST',
  });

  return result;
};
export { updatePassword };
