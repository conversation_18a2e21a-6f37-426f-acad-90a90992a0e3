import { API_BASE_URL } from '@/app/api/constants.ts';
import { COOKIES_HTTP_ONLY_OPTIONS } from '@/constants.ts';
import { setRtFailCookie } from '@/app/api/server-actions/verifyToken.ts';

const refreshToken = async (
  responseCookiesFromMiddleware?: any,
  refreshTokenFromCookies?: string,
  prevRtFail?: string,
  pathname?: string,
  userId?: string,
) => {
  const response = await fetch(`${API_BASE_URL}/platform/auth/refresh-token`, {
    headers: { 'Content-Type': 'application/json' },
    method: 'POST',
    body: JSON.stringify({ refresh_token: refreshTokenFromCookies }),
  });

  const result = await response.json();

  console.log(result, 'refreshResponse', pathname, userId);

  // not for /api route handlers
  if (result?.detail) {
    if (responseCookiesFromMiddleware) {
      setRtFailCookie(responseCookiesFromMiddleware, +(prevRtFail || 0) + 1);

      if (prevRtFail && +prevRtFail >= 1) {
        responseCookiesFromMiddleware.delete('token');
        responseCookiesFromMiddleware.delete('refresh');
        responseCookiesFromMiddleware.delete('expires');
        responseCookiesFromMiddleware.delete('rt_fail');
      }
    }

    return {
      error: result.detail.parameters?.error_text || result.detail.message || result.detail,
    };
  }

  if (responseCookiesFromMiddleware && result.access_token && result.refresh_token) {
    setRtFailCookie(responseCookiesFromMiddleware, 0);
    responseCookiesFromMiddleware.set('token', result.access_token, COOKIES_HTTP_ONLY_OPTIONS);
    responseCookiesFromMiddleware.set('refresh', result.refresh_token, COOKIES_HTTP_ONLY_OPTIONS);
    responseCookiesFromMiddleware.set(
      'expires',
      (Date.now() + result.expires_in * 1000).toString(),
      COOKIES_HTTP_ONLY_OPTIONS,
    );
  }

  return result;
};

export { refreshToken };
