import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies as cookiesForServerComponent } from 'next/headers';

const refreshToken = async (
  reqCookiesFromMiddleware?: any,
  responseCookiesFromMiddleware?: any,
  pathname?: string,
) => {
  const refreshTokenFromCookies = (
    reqCookiesFromMiddleware || cookiesForServerComponent()
  ).get('refresh')?.value;

  if (!refreshTokenFromCookies) {
    return null;
  }

  const response = await fetch(`${API_BASE_URL}/platform/auth/refresh-token`, {
    headers: { 'Content-Type': 'application/json' },
    method: 'POST',
    body: JSON.stringify({ refresh_token: refreshTokenFromCookies }),
  });

  const result = await response.json();

  console.log(result, 'refreshResponse', pathname);

  const cookies = responseCookiesFromMiddleware || cookiesForServerComponent();

  if (result.detail) {
    cookies.delete('token');
    cookies.delete('refresh');
    cookies.delete('expires');
    return {
      error:
        result.detail.parameters?.error_text ||
        result.detail.message ||
        result.detail,
    };
  }

  if (result.access_token && result.refresh_token) {
    cookies.set('token', result.access_token, {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: 34560000,
    });
    cookies.set('refresh', result.refresh_token, {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: 34560000,
    });
    cookies.set('expires', (Date.now() + result.expires_in * 1000).toString(), {
      httpOnly: true,
      sameSite: 'none',
      secure: true,
      maxAge: 34560000,
    });

    return result;
  }
};

export { refreshToken };
