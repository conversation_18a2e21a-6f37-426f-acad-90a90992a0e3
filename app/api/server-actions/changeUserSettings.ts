'use server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { LanguagesType } from '@/types/global';

export const changeUserSettings = async ({
  bonusId,
  lng,
}: {
  bonusId?: number;
  lng?: LanguagesType;
}) => {
  const url = `${API_BASE_URL}/platform/user/settings`;

  const tokenVerified = cookies().get('token')?.value;

  const body = {} as {
    deposit_bonus_external_id?: number | null;
    language?: string;
  };

  if (bonusId || bonusId === 0) {
    body.deposit_bonus_external_id = bonusId || null;
  }

  if (lng) {
    body.language = lng;
  }

  const result = await fetchData({
    url,
    type: 'change user settings',
    token: tokenVerified,
    method: 'PATCH',
    body,
  });

  return result;
};
