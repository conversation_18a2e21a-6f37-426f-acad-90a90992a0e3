import { cookies, headers } from 'next/headers';
import { NextResponse } from 'next/server';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';

export async function GET() {
  try {
    const tokenVerified = headers().get('x-access-token') ?? cookies().get('token')?.value ?? '';

    const user = await getCurrentUserServer(tokenVerified);
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching current user:', error);
    return NextResponse.json({ error: 'Failed to fetch current user' }, { status: 500 });
  }
}
