import { NextResponse } from 'next/server';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';

export async function GET() {
  try {
    const user = await getCurrentUserServer();
    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching current user:', error);
    return NextResponse.json({ error: 'Failed to fetch current user' }, { status: 500 });
  }
}
