import { NextResponse } from 'next/server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { INotificationsResponse } from '@/app/api/user/getNotificationsClient.ts';

export async function GET() {
  try {
    let url = `${API_BASE_URL}/platform/user/notifications?sort=created_at=asc`;

    const tokenVerified = cookies().get('token')?.value;

    const result = await fetchData({
      url,
      type: 'notifications',
      token: tokenVerified,
    });

    const notificationsResponse: INotificationsResponse = {
      total: result?.total_items || 0,
      items: result?.items || [],
    };
    return NextResponse.json(notificationsResponse);
  } catch (error) {
    console.error('Error fetching user bonuses:', error);
    return NextResponse.json({ error: 'Failed to fetch user bonuses' }, { status: 500 });
  }
}
