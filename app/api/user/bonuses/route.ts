import { NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import { getUserBonusesServer } from '@/app/api/user/getUserBonusesServer.ts';

export async function GET() {
  try {
    const tokenVerified = headers().get('x-access-token') ?? cookies().get('token')?.value ?? '';

    const bonuses = await getUserBonusesServer(tokenVerified);

    return NextResponse.json(bonuses);
  } catch (error) {
    console.error('Error fetching user bonuses:', error);
    return NextResponse.json({ error: 'Failed to fetch user bonuses' }, { status: 500 });
  }
}
