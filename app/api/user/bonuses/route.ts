import { NextResponse } from 'next/server';
import { getUserBonusesServer } from '@/app/api/user/getUserBonusesServer.ts';

export async function GET() {
  try {
    const bonuses = await getUserBonusesServer();
    return NextResponse.json(bonuses);
  } catch (error) {
    console.error('Error fetching user bonuses:', error);
    return NextResponse.json({ error: 'Failed to fetch user bonuses' }, { status: 500 });
  }
}
