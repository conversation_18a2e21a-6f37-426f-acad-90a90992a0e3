import { NextRequest, NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import { setRtFailCookie, verifyToken } from '@/app/api/server-actions/verifyToken.ts';
import { COOKIES_HTTP_ONLY_OPTIONS } from '@/constants.ts';

export async function GET(req: NextRequest) {
  const pathname = '/api/user/ws-token';

  let verifyTokenRes: any;
  try {
    verifyTokenRes = await verifyToken(req.cookies, undefined, pathname);
  } catch {
    verifyTokenRes = null;
  }

  const fromHeader = headers().get('x-access-token') || undefined;
  const fromResult = verifyTokenRes?.accessToken as string | undefined;
  const fromCookie = cookies().get('token')?.value || undefined;
  const token = fromHeader ?? fromResult ?? fromCookie ?? '';

  const res = NextResponse.json({ token }, { headers: { 'cache-control': 'no-store' } });


  if (!token) {
    const prevRtFail = +(req.cookies.get('rt_fail')?.value ?? 0);
    const nextRtFail = prevRtFail + 1;

    if (+nextRtFail >= 2) {
      res.cookies.delete('token');
      res.cookies.delete('refresh');
      res.cookies.delete('expires');
      res.cookies.delete('rt_fail');
    } else {
      setRtFailCookie(res.cookies, nextRtFail);
    }

    return res;
  }

  setRtFailCookie(res.cookies, 0);

  if (verifyTokenRes?.isNew) {
    if (verifyTokenRes.accessToken)
      res.cookies.set('token', verifyTokenRes.accessToken, COOKIES_HTTP_ONLY_OPTIONS);
    if (verifyTokenRes.refreshToken)
      res.cookies.set('refreshToken', verifyTokenRes.refreshToken, COOKIES_HTTP_ONLY_OPTIONS);
    if (typeof verifyTokenRes.expires === 'number') {
      res.cookies.set(
        'expires',
        String(Date.now() + verifyTokenRes.expires * 1000),
        COOKIES_HTTP_ONLY_OPTIONS,
      );
    }
  }

  return res;
}
