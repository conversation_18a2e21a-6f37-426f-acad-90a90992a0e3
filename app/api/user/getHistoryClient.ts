export interface IHistoryItem {
  balance: number;
  bet_amount: number;
  date: string;
  game_external_id: number;
  win_amount: number;
}

export interface IHistoryResponse {
  total: number;
  items: Array<IHistoryItem>;
}

const getHistoryClient = async ({
  dateFrom,
  dateTo,
}: {
  dateFrom: string;
  dateTo: string;
}): Promise<IHistoryResponse> => {
  const routeHandlerUrl = `/api/user/history?dateFrom=${encodeURIComponent(dateFrom)}&dateTo=${encodeURIComponent(dateTo)}`;

  const r = await fetch(routeHandlerUrl, {
    cache: 'no-store',
  });

  return r.ok ? (await r.json()) ?? { total: 0, items: [] } : { total: 0, items: [] };
};

export { getHistoryClient };
