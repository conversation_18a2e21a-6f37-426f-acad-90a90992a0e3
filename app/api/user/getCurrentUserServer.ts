import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { getBonuses } from '@/app/api/getBonuses.ts';

export const transformUserResult = async (result: any) => {
  if (!result?.currency) {
    return result;
  }

  const newUserValues: any = {
    active_wager: result.active_wager || null,
    active_freespins: result.active_freespins || null,
  };

  newUserValues.currency = result.currency;

  if (result.active_bonus_id) {
    const userBonus = await getBonuses(0, 1, {
      external_id: [result.active_wager?.template_id || result.active_freespins?.template_id],
    });

    if (userBonus?.items?.[0]) {
      newUserValues.active_bonus_title = userBonus.items[0].title;
      newUserValues.active_bonus_photo = userBonus.items[0].photo_main_url;
      newUserValues.active_bonus_template_type = userBonus.items[0].bonus_type;
      newUserValues.active_bonus_end_date = userBonus.items[0].end_date;
      if (result?.active_bonus_type.includes('wager')) {
        newUserValues.active_wager.goal_sum =
          ((result.active_wager.deposit_amount || 0) +
            (result.active_wager.bonus_amount || result.active_wager.wager_amount)) *
          result.active_wager.multiplier;
      }
    }
    if (result.active_freespins?.game_id && userBonus?.items?.[0]) {
      newUserValues.active_freespins.game_name = userBonus?.items?.[0].game_name;
      newUserValues.active_freespins.game_slug = userBonus?.items?.[0].game_slug;
      newUserValues.active_freespins.game_provider_slug = userBonus?.items?.[0].game_provider_slug;
    }
  }

  return {
    ...result,
    ...newUserValues,
  };
};

const getCurrentUserServer = async () => {
  let url = `${API_BASE_URL}/platform/user/info`;

  const tokenVerified = cookies().get('token')?.value;
  if (!tokenVerified) {
    return;
  }

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: 'current user info',
  });

  return transformUserResult(result);
};

export { getCurrentUserServer };
