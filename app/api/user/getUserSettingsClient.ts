'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';

export const getUserSettingsClient = async () => {
  const url = `${API_BASE_URL}/platform/user/settings`;

  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    type: 'get user settings: bonus, cashback timer',
    token: tokenVerified,
  });

  return result;
};
