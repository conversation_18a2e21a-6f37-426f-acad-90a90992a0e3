import { NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

export async function GET(): Promise<Response> {
  try {
    const url = `${API_BASE_URL}/platform/user/settings`;

    const tokenVerified = headers().get('x-access-token') ?? cookies().get('token')?.value ?? '';

    const result = await fetchData({
      url,
      type: 'get user settings: bonus, cashback timer',
      token: tokenVerified,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching user settings:', error);
    return NextResponse.json({ error: 'Failed to fetch user settings' }, { status: 500 });
  }
}
