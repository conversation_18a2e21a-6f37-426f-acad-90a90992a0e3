import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { getTournaments } from '@/app/api/getTournaments.ts';

const getUserScoreboard = async (externalTournamentId: number, token: string) => {
  const url = `${API_BASE_URL}/platform/user/scoreboard/${externalTournamentId}`;
  const result = await fetchData({
    url,
    type: 'get user scoreboard',
    token,
  });

  if (!result?.board || result.detail) {
    return null;
  }

  return result.board.find((item: any) => item.place === result.user_place) || null;
};

export const getUserTournaments = async () => {
  const tokenVerified = cookies().get('token')?.value || '';

  const activeTournaments = await getTournaments(0, 50, {
    status: ['active'],
  });

  const userTournamentsScoreboardsResult = await Promise.allSettled(
    activeTournaments.items.map(async (tournament) => {
      const scoreboard = await getUserScoreboard(tournament.external_id, tokenVerified);

      if (scoreboard) {
        return {
          ...tournament,
          currency: tournament.currency,
          name: tournament.name,
          title: tournament.title,
          external_id: tournament.external_id,
          slug: tournament.slug,
          rates: tournament.rates,
          scoreData: scoreboard,
        };
      } else {
        return null;
      }
    }),
  );

  const userTournaments: any = userTournamentsScoreboardsResult
    .filter((resItem) => resItem.status === 'fulfilled' && resItem.value)
    .map((resItem: any) => resItem.value);

  return userTournaments;
};
