import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

export async function GET(req: Request): Promise<Response> {
  try {
    const handlerUrl = new URL(req.url);
    const dateFrom = handlerUrl.searchParams.get('dateFrom') || '';
    const dateTo = handlerUrl.searchParams.get('dateTo') || '';

    const tokenVerified = cookies().get('token')?.value;

    const url = `${API_BASE_URL}/platform/user/history?date_from=${dateFrom}&date_to=${dateTo}`;

    const result = await fetchData({
      url,
      type: 'history',
      token: tokenVerified,
    });

    return NextResponse.json({
      total: result?.total_items || 0,
      items: result?.items || [],
    });
  } catch (error) {
    console.error('Error fetching user history:', error);
    return NextResponse.json({ error: 'Failed to fetch user history' }, { status: 500 });
  }
}