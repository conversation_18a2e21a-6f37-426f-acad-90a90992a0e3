import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { IGame } from '@/app/api/getGames.ts';
import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IFavouriteGamesResponse {
  total: number;
  items: IGame[];
}

const getFavouriteGamesServer = async (
  skip = 0,
  limit: number = 50,
  isIdsOnly: boolean = false,
  filters?: { [key: string]: string[] },
): Promise<IFavouriteGamesResponse> => {
  const tokenVerified = cookies().get('token')?.value;
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

  if (!tokenVerified) {
    return {
      total: 0,
      items: [],
    };
  }

  let url = `${API_BASE_URL}/platform/games/favourites?limit=${limit}&skip=${skip}`;

  let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

  if (filters && Object.keys(filters)?.length) {
    Object.keys(filters).forEach((key) => {
      if (key !== 'provider') {
        filtersArray.push(`${key}=${filters[key].join('|')}`);
      }
    });
  }

  filtersArray.length
    ? (url = `${url}&filters=${filtersArray.join(',')}`)
    : url;

  if (isIdsOnly) {
    url = `${url}&is_ids_only=true`;
  }

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: 'favouriteGames',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getFavouriteGamesServer };
