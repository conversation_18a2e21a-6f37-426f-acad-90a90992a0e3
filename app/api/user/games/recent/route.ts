import { NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export async function GET(req: Request): Promise<Response> {
  try {
    const handlerUrl = new URL(req.url);
    const url = handlerUrl.searchParams.get('url') || '';
    const filtersParam = handlerUrl.searchParams.get('filters');

    let filters: { [key: string]: (string | number)[] } = {};
    if (filtersParam) {
      try {
        filters = JSON.parse(filtersParam);
      } catch (e) {
        console.error('Error parsing filters:', e);
      }
    }

    const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

    const tokenVerified = headers().get('x-access-token') ?? cookies().get('token')?.value ?? '';

    // Build filters array for recent games
    let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

    if (filters && Object.keys(filters).length) {
      Object.keys(filters).forEach((key) => {
        if (key !== 'provider') {
          filtersArray.push(`${key}=${filters[key].join('|')}`);
        }
      });
    }

    let finalUrl = url;
    if (filtersArray.length) {
      finalUrl = `${url}&filters=${filtersArray.join(',')}`;
    }

    const result = await fetchData({
      url: finalUrl,
      token: tokenVerified,
      type: 'recent games',
    });

    return NextResponse.json({
      total: result?.total_items || 0,
      items: result?.items || [],
    });
  } catch (error) {
    console.error('Error fetching recent games:', error);
    return NextResponse.json({ error: 'Failed to fetch recent games' }, { status: 500 });
  }
}
