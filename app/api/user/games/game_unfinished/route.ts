import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

export async function GET(): Promise<Response> {
  try {
    const tokenVerified = cookies().get('token')?.value;

    if (!tokenVerified) {
      return NextResponse.json(null);
    }

    const url = `${API_BASE_URL}/platform/user/game_unfinished`;

    const result = await fetchData({
      url,
      token: tokenVerified,
      type: 'check for unfinished game',
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error checking for unfinished game:', error);
    return NextResponse.json({ error: 'Failed to check for unfinished game' }, { status: 500 });
  }
}
