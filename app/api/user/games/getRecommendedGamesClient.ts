import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export const getRecommendedGamesClient = async (
  skip = 0,
  limit = 24,
  filters?: { [key: string]: (string | number)[] },
) => {
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

  let url = `${API_BASE_URL}/platform/games/recommended?limit=${limit}&skip=${skip}`;

  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return {
      total: 0,
      items: [],
    };
  }

  const result = await fetchData({
    url,
    type: 'user recommended games',
    token: tokenVerified,
    filters: {
      ...filters,
      provider_currencies: [userCurrency],
    },
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};
