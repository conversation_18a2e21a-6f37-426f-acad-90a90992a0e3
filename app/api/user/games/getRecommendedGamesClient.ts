import { API_BASE_URL } from '@/app/api/constants.ts';

export const getRecommendedGamesClient = async (
  skip = 0,
  limit = 24,
  filters?: { [key: string]: (string | number)[] },
) => {
  const url = `${API_BASE_URL}/platform/games/recommended?limit=${limit}&skip=${skip}`;

  let routeHandlerUrl = `/api/user/games/recommended?url=${encodeURIComponent(url)}`;

  if (filters && Object.keys(filters).length > 0) {
    routeHandlerUrl += `&filters=${encodeURIComponent(JSON.stringify(filters))}`;
  }

  const r = await fetch(routeHandlerUrl, {
    cache: 'no-store',
  });

  return r.ok ? (await r.json()) ?? { total: 0, items: [] } : { total: 0, items: [] };
};
