import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export async function GET(req: Request): Promise<Response> {
  try {
    const tokenVerified = cookies().get('token')?.value;

    if (!tokenVerified) {
      return NextResponse.json({
        total: 0,
        items: [],
      });
    }

    const handlerUrl = new URL(req.url);
    const url = handlerUrl.searchParams.get('url') || '';
    const filtersParam = handlerUrl.searchParams.get('filters');

    let filters = {};
    if (filtersParam) {
      try {
        filters = JSON.parse(filtersParam);
      } catch (e) {
        console.error('Error parsing filters:', e);
      }
    }

    const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;
    console.log(filters);
    const result = await fetchData({
      url,
      type: 'user recommended games',
      token: tokenVerified,
      filters: {
        ...filters,
        provider_currencies: [userCurrency],
      },
    });

    return NextResponse.json({
      total: result?.total_items || 0,
      items: result?.items || [],
    });
  } catch (error) {
    console.error('Error fetching recommended games:', error);
    return NextResponse.json({ error: 'Failed to fetch recommended games' }, { status: 500 });
  }
}