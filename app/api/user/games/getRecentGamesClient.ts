import { IGame } from '@/app/api/getGames.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';

export interface IRecentGamesResponse {
  total: number;
  items: Array<IGame>;
}

const getRecentGamesClient = async (
  skip = 0,
  limit: number = 20,
  filters?: { [key: string]: string[] },
): Promise<IRecentGamesResponse> => {
  const url = `${API_BASE_URL}/platform/games/recents?limit=${limit}&skip=${skip}`; // TODO: add sort &sort=last_played=desc

  let routeHandlerUrl = `/api/user/games/recent?url=${encodeURIComponent(url)}`;

  if (filters && Object.keys(filters).length > 0) {
    routeHandlerUrl += `&filters=${encodeURIComponent(JSON.stringify(filters))}`;
  }

  const r = await fetch(routeHandlerUrl, {
    cache: 'no-store',
  });

  return r.ok ? (await r.json()) ?? { total: 0, items: [] } : { total: 0, items: [] };
};

export { getRecentGamesClient };
