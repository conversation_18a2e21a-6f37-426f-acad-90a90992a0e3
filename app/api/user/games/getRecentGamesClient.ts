import { IGame } from '@/app/api/getGames.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IRecentGamesResponse {
  total: number;
  items: Array<IGame>;
}

const getRecentGamesClient = async (
  skip = 0,
  limit: number = 20,
  filters?: { [key: string]: string[] },
): Promise<IRecentGamesResponse> => {
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

  let url = `${API_BASE_URL}/platform/games/recents?limit=${limit}&skip=${skip}`; // TODO: add sort &sort=last_played=desc

  let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

  if (filters && Object.keys(filters)?.length) {
    Object.keys(filters).forEach((key) => {
      if (key !== 'provider') {
        filtersArray.push(`${key}=${filters[key].join('|')}`);
      }
    });
  }

  filtersArray.length
    ? (url = `${url}&filters=${filtersArray.join(',')}`)
    : url;

  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: 'recent games',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getRecentGamesClient };
