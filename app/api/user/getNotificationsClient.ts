import { INotification } from '@/app/api/server-actions/notifications.ts';

export interface INotificationsResponse {
  total: number;
  items: Array<INotification>;
}

const getNotificationsClient = async () => {
  const r = await fetch('/api/user/notifications', { cache: 'no-store' });
  return r.ok ? (await r.json()) ?? { total: 0, items: [] } : { total: 0, items: [] };
};

export { getNotificationsClient };
