import { API_BASE_URL } from '@/app/api/constants.ts';
import { cookies } from 'next/headers';
import { fetchData } from '../fetchData';
import { transformUserBonuses } from '@/utils/transformUserBonuses.ts';

export const getUserBonusesServer = async (tokenFromRouteHandler?: string | null) => {
  let url = `${API_BASE_URL}/platform/user/bonuses`;

  const tokenFromCookies = cookies().get('token')?.value;
  const tokenVerified = tokenFromRouteHandler ?? tokenFromCookies;

  if (!tokenVerified) {
    console.log('no token for bonuses', tokenFromRouteHandler, tokenFromCookies);
    return [];
  }

  const result = await fetchData({
    url,
    type: 'user bonuses',
    token: tokenVerified,
  });

  if (!result?.templates && !result?.freespins && !result?.wagers) {
    return [];
  }

  return transformUserBonuses(result);
};
