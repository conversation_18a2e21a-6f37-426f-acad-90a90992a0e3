import { fetchData } from '@/app/api/fetchData.ts';
import { API_BASE_URL } from './constants.ts';

type ConstantKeyType =
  | 'banners_order'
  | 'providers_order'
  | 'refresh_tournaments_date'
  | 'posts_order'
  | 'promotions_order'
  | 'tournaments_order'
  | 'payment_settings_order';

export interface IConstant {
  key: string;
  value: string;
}

const getConstantsServer = async (constantKey?: ConstantKeyType) => {
  let url = `${API_BASE_URL}/platform/constants`;

  if (constantKey) {
    url += `?filters=key=${constantKey}`;
  }

  const result = await fetchData({
    url,
    type: 'constants',
  });

  return result?.items || [];
};

export { getConstantsServer };
