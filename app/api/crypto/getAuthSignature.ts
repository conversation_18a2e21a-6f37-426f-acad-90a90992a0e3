import crypto from 'crypto';

function calcSignature(secret: string, message: string) {
  const hmac = crypto.createHmac('sha512', secret);
  hmac.update(message);
  return hmac.digest('hex');
}

export function getAuthSignature(message: string) {
  const timeStamp = Math.floor(Date.now() / 1000); // Current timestamp in seconds
  const timeStampString = timeStamp.toString();

  const signature = calcSignature(
    process.env.EXNODE_PRIVATE_KEY!,
    timeStampString + message,
  );
  return { signature, timeStamp: timeStampString };
}
