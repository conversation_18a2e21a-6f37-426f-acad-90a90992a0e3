import { cookies, headers } from 'next/headers';
import { NextResponse } from 'next/server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { IWallet } from '@/app/api/crypto/getWalletsClient.ts';

export async function GET() {
  try {
    const url = `${API_BASE_URL}/platform/user/wallets`;

    const tokenVerified = headers().get('x-access-token') ?? cookies().get('token')?.value ?? '';

    if (!tokenVerified) {
      return NextResponse.json({ error: 'Authentication token not found' }, { status: 401 });
    }

    const result = await fetchData({
      url,
      type: 'wallets',
      token: tokenVerified,
    });

    const wallets: IWallet[] = result?.items || [];

    return NextResponse.json(wallets);
  } catch (error) {
    console.error('Error fetching wallets:', error);
    return NextResponse.json({ error: 'Failed to fetch wallets' }, { status: 500 });
  }
}
