'use server';
import { getAuthSignature } from '@/app/api/crypto/getAuthSignature.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import uuid4 from 'uuid4';
import { cookies } from 'next/headers';

interface ExnodeResponse {
  status: string;
  refer: string;
  tracker_id: string;
  token_name: string;
  alter_refer?: string;
  dest_tag?: string | null;
  description?: string | null;
  extra_info?: object | null;
}

export const createWallet = async (
  token: string,
  playerId?: number,
  paymentProviderId?: number,
  paymentSettingId?: number,
) => {
  const client_transaction_id = uuid4();
  const body = JSON.stringify({
    token,
    client_transaction_id,
    transaction_description: `Create wallet for ${token}`,
    address_type: 'STATIC',
    call_back_url: `${API_BASE_URL}/webhooks/payments/exnode`,
  });
  const { signature, timeStamp } = getAuthSignature(body);

  const resultFromExnode: ExnodeResponse | { error: any } = await fetch(
    `${process.env.EXNODE_SERVER_URL}/api/transaction/create/in`,
    {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        ApiPublic: process.env.EXNODE_PUBLIC_KEY!,
        Signature: signature,
        Timestamp: timeStamp,
        'Content-Type': 'application/json',
      },
      body,
    },
  )
    .then((response) => response.json())
    .then((data) => {
      // console.log(data, body);
      if (data.status !== 'ACCEPTED') {
        // console.log('!ACCEPTED', data.status);
        throw new Error(data.description);
      }
      return data as ExnodeResponse;
    })
    .catch((error) => {
      console.error(error);
      return { error: error.toString() };
    });

  if ('error' in resultFromExnode) {
    return resultFromExnode;
  }

  // const resuFromEx = {
  //   description: null,
  //   extra_info: {},
  //   refer: 'TLTM3po7FY11K3zysBmjMTEcEkrQBLXfWV',
  //   status: 'ACCEPTED',
  //   token_name: 'USDTTRC',
  //   tracker_id:
  //     '2ad9de33bb64c49fb27261b6a07a23a046e68f68d75d2f0f7366477d436d959395eacff98ae03ca90a69617cd225e6c6df511286f98e9e79f25719f10970af22',
  //   dest_tag: null,
  // };

  const url = `${API_BASE_URL}/platform/user/wallet`;

  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    method: 'POST',
    type: 'Create wallet',
    token: tokenVerified,
    body: {
      address: resultFromExnode.refer,
      client_transaction_id,
      dest_tag: resultFromExnode.dest_tag || '',
      player_id: playerId,
      currency: resultFromExnode.token_name,
      tracker_id: resultFromExnode.tracker_id,
      payment_service: 'exnode',
      payment_provider_id: paymentProviderId,
      payment_setting_id: paymentSettingId,
    },
  });
  // console.log(result);
  if (result.detail) {
    return {
      error: result.detail.parameters?.error_text || result.detail.message || result.detail,
    };
  }
  // console.log(result);
  return {
    address: resultFromExnode.refer,
    currency: result.currency,
    dest_tag: resultFromExnode.dest_tag,
    ...result,
    error: '',
  };
};
