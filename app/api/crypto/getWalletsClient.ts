export interface IWallet {
  id: number;
  address: string;
  dest_tag?: string;
  currency: string;
  bonus_external_id?: number;
  payment_setting_id: number;
}

export const getWalletsClient = async (): Promise<IWallet[]> => {
  try {
    const response = await fetch('/api/crypto/wallets');
    if (!response.ok) {
      return [];
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching wallets:', error);
    return [];
  }
};
