import { fetchData } from '@/app/api/fetchData.ts';
import { ITranslations } from '@/types/global';
import { API_BASE_URL } from './constants.ts';
import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IBanner {
  id: number;
  banner_type: string;
  title: ITranslations;
  photo_main_url: string;
  photo_background_url: string;
  name: string;
  button_text?: ITranslations;
  visibility: string;
  target_url?: string;
  target_type?: string;
  display_mode: string;
}

export interface IBannersResponse {
  total: number;
  items: Array<IBanner>;
  isUserAuthorized?: boolean;
}

const getBannersServer = async (
  skip = 0,
  limit = 100,
  filters?: { [key: string]: string[] },
): Promise<IBannersResponse> => {
  const cookiesCurrency = cookies().get('userCurrency')?.value;

  let url = `${API_BASE_URL}/platform/banners?skip=${skip}&limit=${limit}`;
  const result = await fetchData({
    url,
    type: 'banners',
    filters: {
      ...filters,
      currency: [cookiesCurrency || DEFAULT_CURRENCY, 'null'],
    },
  });

  if (!result?.items)
    return {
      total: 0,
      items: [],
    };

  return {
    total: result?.total_items,
    items: result?.items,
  };
};

export { getBannersServer };
