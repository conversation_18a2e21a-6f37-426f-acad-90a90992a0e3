import { fetchData } from '@/app/api/fetchData.ts';
import { ITranslations } from '@/types/global';
import { API_BASE_URL } from './constants.ts';

export interface ILevel {
  bonus_birthday_external_id: number | null;
  bonus_cashback_external_id: number | null;
  bonus_level_up_external_id: number | null;
  cashback_interval_days: number;
  prev_cashback_interval_days?: number;
  prev_birthday_bonus_id?: number;
  cashback_max: number;
  cashback_percentage: number;
  prev_cashback_percentage?: number;
  currency: string;
  max_daily_withdrawal: number;
  prev_max_daily_withdrawal?: number;
  name: string;
  next_level_points: number;
  curr_level_points?: number;
  number: number;
  photo_url: string;
  photos: object;
  point_value: number;
  title: ITranslations;
}

export interface ILevelsResponse {
  total: number;
  items: Array<ILevel>;
}

const getLevels = async (
  skip = 0,
  limit?: number,
  filters?: { [key: string]: (string | number)[] },
): Promise<ILevelsResponse> => {
  let url = `${API_BASE_URL}/platform/levels?limit=${limit}&skip=${skip}&sort=number=asc`;

  const result = await fetchData({
    url,
    filters,
    type: 'levels',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getLevels };
