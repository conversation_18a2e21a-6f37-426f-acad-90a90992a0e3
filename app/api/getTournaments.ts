'use server';
import { fetchData } from '@/app/api/fetchData.ts';
import { ITranslations } from '@/types/global.js';
import { cookies } from 'next/headers';
import { API_BASE_URL } from './constants.ts';

export interface ITournament {
  name: string;
  status: string;
  title: ITranslations;
  description: ITranslations;
  amounts_freespins: number[];
  amounts_prizes: number[];
  bet_max: number;
  bet_min: number;
  calculation_type: string;
  currency: string;
  start_date: string;
  end_date: string;
  fund_type: string;
  games_external_ids: number[];
  photo_large_url: string;
  photo_small_url: string;
  progressive_percentage: number;
  total_prize: number;
  external_id: number;
  slug: string;
  id: number;
  prizes: {
    [key: string]: {
      prize: number;
      freespins: number;
    };
  };
  rates: { [key: string]: number };
}

export interface ITournamentsResponse {
  total: number;
  items: Array<ITournament>;
}

const getTournaments = async (
  skip = 0,
  limit?: number,
  filters?: { [key: string]: string[] },
): Promise<ITournamentsResponse> => {
  let url = `${API_BASE_URL}/platform/tournaments?limit=${limit}&skip=${skip}`;

  const result = await fetchData({
    url,
    filters,
    type: 'tournaments',
  });

  if (!result?.items) {
    console.error('Error fetching tournaments');
  }

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

const getTournamentByIdOrSlug = async (slug: string | number) => {
  const res = await fetch(`${API_BASE_URL}/platform/tournaments/${slug}`, {
    cache: 'no-store',
  });

  if (!res.ok) {
    console.error(`Error fetching tournaments: ${slug}`);
    return { slug };
  }

  return await res.json();
};

const getScoreBoard = async (
  skip = 0,
  limit: number,
  externalTournamentId: number,
  isAuthorized: boolean,
  currency?: string,
) => {
  const tokenVerified = cookies().get('token')?.value;

  const urlPart = isAuthorized ? 'user/scoreboard' : 'tournaments/scoreboard';
  let url = `${API_BASE_URL}/platform/${urlPart}/${externalTournamentId}?limit=${limit}&skip=${skip}`;
  if (currency) {
    url += `&currency=${currency}`;
  }
  const res = await fetchData({
    url,
    token: tokenVerified,
    type: 'scoreboard',
  });

  return res;
};

export { getScoreBoard, getTournamentByIdOrSlug, getTournaments };
