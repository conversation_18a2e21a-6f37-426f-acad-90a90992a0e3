import { DEFAULT_CURRENCY } from '@/constants.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

const getGamesTournaments = async (
  skip = 0,
  limit = 24,
  currency?: string,
  filters?: { [key: string]: (number | string)[] },
) => {
  const userCurrency = currency || DEFAULT_CURRENCY;

  let url = `${API_BASE_URL}/platform/games/tournaments?limit=${limit}&skip=${skip}`;

  let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

  if (filters && Object.keys(filters)?.length) {
    Object.keys(filters).forEach((key) => {
      filtersArray.push(`${key}=${filters[key].join('|')}`);
    });
  }

  filtersArray.length ? (url = `${url}&filters=${filtersArray.join(',')}`) : url;

  const result = await fetchData({
    url,
    type: 'tournaments games',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getGamesTournaments };
