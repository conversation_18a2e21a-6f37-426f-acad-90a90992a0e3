import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

const getGamesTopWinsServer = async (skip = 0, limit = 24, filters?: { [key: string]: string[] }) => {
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

  let url = `${API_BASE_URL}/platform/games/topwins?limit=${limit}&skip=${skip}`;

  let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

  if (filters && Object.keys(filters)?.length) {
    Object.keys(filters).forEach((key) => {
      filtersArray.push(`${key}=${filters[key].join('|')}`);
    });
  }

  if (filtersArray.length) {
    url = `${url}&filters=${filtersArray.join(',')}`;
  }

  const result = await fetchData({
    url,
    type: 'top wins games',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getGamesTopWinsServer };
