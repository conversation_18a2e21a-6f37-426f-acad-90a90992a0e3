import { API_BASE_URL } from './constants.ts';
import { ITranslations } from '@/types/global';
import { fetchData } from '@/app/api/fetchData.ts';
import { BonusCategoryType } from '@/app/api/server-actions/bonuses.ts';

export type IBonusType =
  | 'wager_deposit'
  | 'wager_no_deposit'
  | 'freespins_deposit'
  | 'freespins_no_deposit'
  | 'wager_freespins_deposit';

export interface IBonus {
  status: string;
  bonus_amount: number;
  bonus_category: BonusCategoryType;
  bonus_type: IBonusType;
  currency: string;
  end_date: string;
  external_id: number;
  id: number;
  name: string;
  photo_main_url: string;
  photo_round_url: string;
  start_date: string;
  title: ITranslations;
  affiliate_id: number;
  bet_amount: number;
  birthday_lifetime_days: number;
  deposit_amount_percentage: number;
  description: ITranslations;
  freespins_amount: number;
  freespins_denomination: number;
  freespins_game_id: number;
  freespins_provider: string;
  game_name: string | null;
  game_slug: string | null;
  game_provider_slug: string | null;
  hours_to_activate: number;
  hours_to_play: number;
  is_verified_email: boolean;
  is_verified_kyc: boolean;
  is_verified_passport: boolean;
  is_verified_phone: boolean;
  level_max: number;
  level_min: number;
  min_deposit_amount: number;
  max_deposit_amount: number;
  max_activation_count: number;
  max_bet_wager: number;
  max_win_amount: number;
  max_win_multiplier: number;
  min_balance_amount: number;
  wager_amount?: number;
  wager_multiplier: number;
  payment_codes: number[];
  photos: object;
}

export interface IBonusesResponse {
  total: number;
  items: Array<IBonus>;
  detail?: string;
}

const getBonuses = async (
  skip = 0,
  limit?: number,
  filters?: { [key: string]: (string | number)[] },
  revalidateTags?: string[],
): Promise<IBonusesResponse> => {
  let url = `${API_BASE_URL}/platform/bonuses?limit=${limit}&skip=${skip}`;

  const result = await fetchData({
    url,
    type: 'bonuses',
    filters,
    revalidateTags,
  });
  // console.log('get bonuses');
  if (result.detail) {
    return result;
  }

  // console.log(result);
  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getBonuses };
