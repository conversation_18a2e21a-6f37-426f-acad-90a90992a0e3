export const runtime = 'nodejs';

import { NextResponse } from 'next/server';

export interface RecaptchaVerificationResponse {
  success: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
  error?: string;
}

export async function POST(request: Request) {
  try {
    const { type, token } = await request.json();

    const secretKey =
      type === 'v3'
        ? process.env.RECAPTCHA_V3_PRIVATE_KEY
        : process.env.RECAPTCHA_V2_PRIVATE_KEY;
    if (!secretKey) {
      console.error('Recaptcha secret is missing');
      return NextResponse.json({ success: false }, { status: 500 });
    }

    const params = new URLSearchParams();
    params.append('secret', secretKey);
    params.append('response', token);

    const googleRes = await fetch(
      'https://www.google.com/recaptcha/api/siteverify',
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: params.toString(),
      },
    );
    const result: RecaptchaVerificationResponse = await googleRes.json();
    // console.log('Google verify response:', result);

    return NextResponse.json(result);
  } catch (err) {
    console.error('Error in recaptcha verification route:', err);
    return NextResponse.json({ success: false }, { status: 500 });
  }
}
