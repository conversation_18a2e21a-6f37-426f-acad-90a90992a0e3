export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
export const revalidate = 0;

import { NextResponse } from 'next/server';

export interface RecaptchaVerificationResponse {
  success?: boolean;
  score?: number;
  action?: string;
  challenge_ts?: string;
  hostname?: string;
  'error-codes'?: string[];
  error?: string;
}

export async function POST(request: Request) {
  try {
    const { type, token } = await request.json();

    if (!token) {
      console.error('No token for recaptcha verification');
      return NextResponse.json({ success: false }, { status: 400 });
    }

    const secretKey =
      type === 'v3' ? process.env.RECAPTCHA_V3_PRIVATE_KEY : process.env.RECAPTCHA_V2_PRIVATE_KEY;
    if (!secretKey) {
      console.error('Recaptcha secret is missing');
      return NextResponse.json({ success: false }, { status: 500 });
    }

    const params = new URLSearchParams();
    params.append('secret', secretKey);
    params.append('response', token);

    const googleRes = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: params.toString(),
      cache: 'no-store',
    });

    console.log('Google verify response:', googleRes, 'params', params.toString());
    const result: RecaptchaVerificationResponse = await googleRes.json();
    console.log('Google verify result:', result);

    if (result?.['error-codes'] || result?.error) {
      return NextResponse.json(
        {
          success: false,
          'error-codes': result?.['error-codes'] || [result?.error || 'unknown-recaptcha-error'],
        },
        { status: 400 },
      );
    }

    return NextResponse.json(result, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (err) {
    console.error('Error in recaptcha verification route:', err);
    return NextResponse.json({ success: false }, { status: 500 });
  }
}
