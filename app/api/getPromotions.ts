import { fetchData } from '@/app/api/fetchData.ts';
import { ITranslations } from '@/types/global.js';
import { API_BASE_URL } from './constants.ts';
import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IPromotion {
  badge_text?: ITranslations;
  button_text?: ITranslations;
  currency?: string;
  description: ITranslations;
  id: number;
  photo_large_url: string;
  photo_small_url: string;
  slug: string;
  status: string;
  target_url: string;
  text: ITranslations;
  title: ITranslations;
  visibility: string;
}

export interface IPromotionsResponse {
  total: number;
  items: Array<IPromotion>;
}

const getPromotions = async (
  skip = 0,
  limit?: number,
): Promise<IPromotionsResponse> => {
  const currency = cookies().get('userCurrency')?.value;

  let url = `${API_BASE_URL}/platform/promotions?limit=${limit}&skip=${skip}`;

  const result = await fetchData({
    url,
    filters: { currency: [currency || DEFAULT_CURRENCY, 'null'] },
    type: 'promotions',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

const getPromotionByIdOrSlug = async (slug: string | number) => {
  const res = await fetch(`${API_BASE_URL}/platform/promotions/${slug}`, {
    cache: 'no-store',
  });

  if (!res.ok) {
    console.error(`Error fetching promo: ${slug}`);
    return { slug };
  }

  return await res.json();
};

export { getPromotionByIdOrSlug, getPromotions };
