'use server';
import { headers } from 'next/headers';

// check if the IP is valid
const isValidIp = (ip: string): boolean => {
  const ipRegex = /^\d{1,3}(\.\d{1,3}){3}$/;
  return ipRegex.test(ip) && ip.length >= 7 && ip.length <= 15;
};

// attempt to get the user's IP address from different possible headers
export const getIp = async (): Promise<string> => {
  const headersList = headers();

  const ipHeaders = [
    'x-client-ip',
    'x-forwarded-for',
    'cf-connecting-ip',
    'fastly-client-ip',
    'true-client-ip',
    'x-real-ip',
    'x-cluster-client-ip',
    'forwarded-for',
    'forwarded',
    'appengine-user-ip',
    'cf-pseudo-ipv4',
    'remote-addr',
    'connection.remoteAddress',
    'socket.remoteAddress',
    'connection.socket.remoteAddress',
    'info.remoteAddress',
    'request.raw',
    'x-original-forwarded-for',
    'x-coming-from',
    'via',
    'x-originating-ip',
  ];

  const extractIpFromChain = (ipChain: string): string | null => {
    // Split the chain and clean each IP
    const ips = ipChain.split(',').map((ip) => ip.trim());

    // Try to get the first non-private IP from the chain
    for (const ip of ips) {
      if (isValidIp(ip)) {
        // Skip private and special-use IP addresses
        if (
          !ip.startsWith('10.') &&
          !ip.startsWith('172.16.') &&
          !ip.startsWith('192.168.') &&
          !ip.startsWith('127.') &&
          !ip.startsWith('0.') &&
          !ip.startsWith('fc00:') &&
          !ip.startsWith('fe80:')
        ) {
          return ip;
        }
      }
    }

    // If no public IP found, return the first valid IP
    return ips.find((ip) => isValidIp(ip)) || null;
  };

  for (const header of ipHeaders) {
    const ip = headersList.get(header);
    if (ip) {
      const validIp =
        header.includes('forwarded') || header === 'x-forwarded-for'
          ? extractIpFromChain(ip)
          : ip.trim();

      if (validIp && isValidIp(validIp)) {
        return validIp;
      }
    }
  }

  // otherwise return empty string
  return '';
};
