import { API_BASE_URL } from '@/app/api/constants.ts';

const resetPassword = async (requestBody: { email: string }) => {
  let url = `${API_BASE_URL}/platform/auth/reset_password`;

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  });

  const result = await response.json();

  if (result.detail) {
    return { error: result.detail.parameters?.error_text || result.detail.message || result.detail };
  }

  return result;
};
export { resetPassword };
