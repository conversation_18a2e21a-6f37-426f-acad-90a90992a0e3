'use server';
import { fetchData } from '@/app/api/fetchData.ts';
import { API_BASE_URL } from './constants.ts';
import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IFacet {
  [key: string]: [
    {
      id: number;
      count: number;
    },
  ];
}

const getGamesFacets = async (filters?: {
  [key: string]: (string | number)[];
}) => {
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

  let url = `${API_BASE_URL}/platform/games/facets`;

  let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

  if (filters && Object.keys(filters)?.length) {
    Object.keys(filters).forEach((key) => {
      filtersArray.push(`${key}=${filters[key].join('|')}`);
    });
  }

  url = `${url}?filters=${filtersArray.join(',')}`;

  const result = await fetchData({ url, type: 'gamesFacets' });

  return result?.facets || {};
};

export { getGamesFacets };
