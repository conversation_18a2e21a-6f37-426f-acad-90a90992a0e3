'use server';
import { RecaptchaVerificationResponse } from '@/app/api/recaptcha/route.ts';
import { fetchData } from '@/app/api/fetchData.ts';

export const verifyRecaptcha = async (
  type: string,
  token: string,
  appBaseUrl: string,
): Promise<RecaptchaVerificationResponse> => {
  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      const response = await fetchData({
        url: appBaseUrl + '/api/recaptcha',
        method: 'POST',
        body: { type, token },
      });

      console.log(response, 'api recaptcha response');

      if (response?.error || response?.detail) {
        return {
          success: false,
          'error-codes': [response.error || response.detail],
        };
      }

      return response;
    } catch (error) {
      console.log(error, 'verifyRecaptcha error');
      console.log(error);
      if (attempt === 2) {
        return { success: false };
      }
    }
    // wait before retrying if recaptcha response has an error
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }
  return { success: false };
};
