import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';
import { verifyToken } from '@/app/api/server-actions/verifyToken.ts';

export async function GET(req: Request) {
  try {
    const tokenResult = await verifyToken();
    const tokenVerified = tokenResult?.access_token || tokenResult || cookies().get('token')?.value;

    if (!tokenVerified) {
      return NextResponse.json({
        total: 0,
        items: [],
      });
    }

    const handlerUrl = new URL(req.url);
    const url = handlerUrl.searchParams.get('url') || '';
    const revalidateTags = handlerUrl.searchParams.get('revalidateTags');

    const result = await fetchData({
      url,
      type: 'payment transactions',
      token: tokenVerified,
      revalidateTags: revalidateTags ? JSON.parse(revalidateTags) : undefined,
    });

    return NextResponse.json({
      total: result?.total_items || 0,
      items: result?.items || [],
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json({ error: 'Failed to fetch transactions' }, { status: 500 });
  }
}
