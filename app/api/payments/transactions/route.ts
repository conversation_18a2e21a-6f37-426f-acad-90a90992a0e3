import { NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import { fetchData } from '@/app/api/fetchData.ts';

export async function GET(req: Request) {
  try {
    const tokenVerified = headers().get('x-access-token') ?? cookies().get('token')?.value ?? '';

    if (!tokenVerified) {
      console.log('no token for transactions');
      return NextResponse.json({
        total: 0,
        items: [],
      });
    }

    const handlerUrl = new URL(req.url);
    const url = handlerUrl.searchParams.get('url') || '';
    const revalidateTags = handlerUrl.searchParams.get('revalidateTags');

    const result = await fetchData({
      url,
      type: 'payment transactions',
      token: tokenVerified,
      revalidateTags: revalidateTags ? JSON.parse(revalidateTags) : undefined,
    });

    return NextResponse.json({
      total: result?.total_items || 0,
      items: result?.items || [],
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json({ error: 'Failed to fetch transactions' }, { status: 500 });
  }
}
