export interface IPaymentPreference {
  payment_setting_id?: number;
  payment_cascade_id?: number;
  player_id: number;
  data: {
    card_number?: string;
    card_holder?: string;
    wallet_address?: string;
  };
}

export const getPaymentPreferencesClient = async (paymentMethodId: number, isCascade: boolean) => {
  const r = await fetch('/api/payments/preferences', {
    cache: 'no-store',
  });

  const result = await r.json();

  if (result?.detail) {
    return null;
  }

  const methodPreferences = result?.items?.find((item: IPaymentPreference) =>
    isCascade
      ? item.payment_cascade_id === paymentMethodId
      : item.payment_setting_id === paymentMethodId,
  );

  return methodPreferences || null;
};
