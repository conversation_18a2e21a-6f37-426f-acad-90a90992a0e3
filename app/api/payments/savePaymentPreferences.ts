'use server';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

interface PaymentPreferences {
  savedPreferencesId: number;
  player_id: number;
  payment_setting_id?: number;
  payment_cascade_id?: number;
  bank_name?: string;
  phone_number?: string | null;
  card_number?: number | null;
  card_holder?: string;
  wallet_address?: string;
  isRememberInfoChecked: boolean;
}

export const savePaymentPreferences = async ({
  savedPreferencesId,
  player_id,
  payment_setting_id,
  payment_cascade_id,
  bank_name,
  card_number,
  card_holder,
  phone_number,
  wallet_address,
  isRememberInfoChecked,
}: PaymentPreferences) => {
  let url = `${API_BASE_URL}/platform/user/payment/preferences`;

  if (savedPreferencesId) {
    url += `/${savedPreferencesId}`;
  }

  const tokenVerified = cookies().get('token')?.value;

  const body: {
    payment_cascade_id?: number;
    payment_setting_id?: number;
    player_id: number;
    data: {
      bank_name?: string;
      card_number?: string;
      card_holder?: string;
      wallet_address?: string;
      phone_number?: string;
    };
  } = {
    payment_setting_id,
    payment_cascade_id,
    player_id,
    data: {},
  };

  if (isRememberInfoChecked) {
    if (card_number) {
      body.data.card_number = card_number.toString();
    }

    if (card_holder) {
      body.data.card_holder = card_holder;
    }

    if (wallet_address) {
      body.data.wallet_address = wallet_address;
    }

    if (phone_number) {
      body.data.phone_number = phone_number;
    }

    if (bank_name) {
      body.data.bank_name = bank_name;
    }
  }

  const result = await fetchData({
    url,
    type: 'save payment preferences',
    token: tokenVerified,
    method: savedPreferencesId ? 'PATCH' : 'POST',
    body,
  });

  if (result.detail) {
    return {
      error: result.detail,
    };
  }

  return result;
};
