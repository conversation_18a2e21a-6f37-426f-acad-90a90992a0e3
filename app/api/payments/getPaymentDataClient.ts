import { ITranslations } from '@/types/global';
import { IConstant } from '@/app/api/getConstants.ts';

export interface IPaymentCascade {
  amount_max: number;
  amount_min: number;
  comment: ITranslations;
  currency: string;
  id: number;
  payment_operation: 'deposit' | 'withdrawal';
  payment_settings_ids: number[];
  photo_url: string;
  photos: object;
  status: 'active' | 'inactive';
  title: string;
  cascadeId?: number;
  payment_type?: 'p2p';
}

export interface IMethodGroup {
  title: string;
  methods: Array<IMethod>;
  group: string;
  payment_operation: 'deposit' | 'withdrawal';
  photo_url?: string;
  payment_type: 'card' | 'crypto' | 'p2p';
  id: number;
  amount_min: number;
  amount_max: number;
}

export type PaymentType = 'card' | 'crypto' | 'p2p';

export interface IMethod {
  id: number;
  h2h_bank_name: string[];
  code: number;
  currency_platform: string;
  currency_user: string;
  payment_operation: 'deposit' | 'withdrawal';
  payment_provider_id: number;
  payment_provider_name: string;
  payment_type: PaymentType;
  photo_url: string;
  title: string;
  title_platform: string;
  amount_min: number;
  amount_max: number;
  fee_fixed_user: number;
  fee_percent_user: number;
  exchange_fee_percent_deposit: number;
  exchange_fee_percent_withdrawal: number;
  is_featured: boolean;
  is_phone_number: boolean;
  is_deposit_h2h: boolean;
  // currency_incoming: string;
  group: string;
  comment: ITranslations;
  isLatestUsed?: boolean;
  lastPaidAmount?: number;
  withdrawal_interval_minutes_max: number;
  withdrawal_interval_minutes_min: number;
  cascadeId?: number; // only in cascade
  payment_settings_ids?: number[]; // only in cascade
}

export type IPaymentCard = IMethod | IMethodGroup;

export interface IMethodsResponse {
  deposit: IPaymentCard[];
  withdrawal: IPaymentCard[];
}

export interface IFeaturedMethodsResponse {
  deposit: IMethod[];
  withdrawal: IMethod[];
}

export const getPaymentDataClient = async (constants?: IConstant[] | null) => {
  const r = await fetch('/api/payments/options', {
    method: 'POST',
    cache: 'no-store',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ constants }),
  });
  return r.ok ? (await r.json()) ?? null : null;
};
