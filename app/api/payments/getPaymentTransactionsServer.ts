import { cookies } from 'next/headers';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';

export const getPaymentTransactionsServer = async (
  skip = 0,
  limit = 24,
  filters?: string,
  sort?: string,
  revalidateTags?: string[],
) => {
  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return {
      total: 0,
      items: [],
    };
  }

  let url = `${API_BASE_URL}/platform/user/payment/transactions?limit=${limit}&skip=${skip}`;
  url += filters ? `&filters=${filters}` : '';

  if (sort) {
    url = `${url}&sort=${sort}`;
  }

  const result = await fetchData({
    url,
    type: 'payment transactions',
    token: tokenVerified,
    revalidateTags,
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};
