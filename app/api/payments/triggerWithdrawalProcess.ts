'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';
import { revalidateTag } from 'next/cache';

export interface IWithdrawalData {
  amount: number;
  bank_name?: string;
  card_number?: number | null;
  currency: string;
  card_holder?: string;
  payment_setting_id: number;
  wallet_address?: string;
  phone_number?: string;
  client_ip: string;
  payment_cascade_id?: number;
  cascade_payment_settings_ids?: number[];
}

export const triggerWithdrawalProcess = async ({
  amount,
  bank_name,
  card_number,
  currency,
  card_holder = '',
  payment_setting_id,
  wallet_address = '',
  phone_number = '',
  client_ip = '',
  payment_cascade_id,
  cascade_payment_settings_ids,
}: IWithdrawalData) => {
  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return {
      error: 'Token not found',
    };
  }

  const url = `${API_BASE_URL}/platform/user/payment/withdrawal`;

  const result = await fetchData({
    url,
    method: 'POST',
    body: {
      amount,
      bank_name,
      card_number,
      currency,
      card_holder,
      payment_setting_id,
      wallet_address,
      phone_number,
      client_ip,
      payment_cascade_id,
      cascade_payment_settings_ids,
    },
    token: tokenVerified,
  });

  if (result.status === 'failed' || result.error_message || result.error) {
    return result.error_message || result.error
      ? {
          error: result.error_message || result.error,
        }
      : '';
  } else {
    revalidateTag('pending_withdrawals');
    return result;
  }
};
