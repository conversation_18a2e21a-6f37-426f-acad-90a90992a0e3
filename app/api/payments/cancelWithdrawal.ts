'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';
import { revalidateTag } from 'next/cache';

export const cancelWithdrawal = async (id: string) => {
  const tokenVerified = cookies().get('token')?.value;

  if (!tokenVerified) {
    return {
      error: 'Token not found',
    };
  }

  const url = `${API_BASE_URL}/platform/user/payment/withdrawal-cancel`;

  const result = await fetchData({
    url,
    method: 'POST',
    token: tokenVerified,
    body: { transaction_id: id },
  });

  if (result?.detail || result?.error) {
    return {
      error: result.detail?.message || result.detail || result.error,
    };
  } else {
    revalidateTag('pending_withdrawals');
  }

  return result;
};
