import { API_BASE_URL } from '@/app/api/constants.ts';

export type PaymentOperation = 'deposit' | 'withdrawal';
export type PaymentStatus =
  | 'denied'
  | 'success'
  | 'failed'
  | 'pending'
  | 'in_progress'
  | 'cancelled'
  | 'completed';

export interface IPaymentTransaction {
  transaction_id: string;
  created_at: string;
  currency_user: string;
  payment_cascade_id?: number;
  cascade_uuid?: string;
  payment_operation: PaymentOperation;
  payment_provider_id?: number;
  payment_setting_id?: number;
  payment_setting_title?: string;
  payment_setting_title_platform?: string;
  photo_url?: string;
  status: PaymentStatus;
  amount_user: null;
  is_cascade_in_progress?: boolean;
}

export const getPaymentTransactionsClient = async (
  skip = 0,
  limit = 24,
  filters?: string,
  sort?: string,
  revalidateTags?: string[],
  origin?: string,
  cookieHeader?: string,
) => {
  let url = `${API_BASE_URL}/platform/user/payment/transactions?limit=${limit}&skip=${skip}`;
  url += filters ? `&filters=${filters}` : '';

  if (sort) {
    url = `${url}&sort=${sort}`;
  }

  const reqOptions = {
    cache: 'no-store',
  } as any;

  if (cookieHeader) {
    reqOptions.headers = {
      cookie: cookieHeader,
    };
  }

  let routeHandlerUrl = `${origin || ''}/api/payments/transactions?url=${encodeURIComponent(url)}`;

  if (revalidateTags?.length) {
    routeHandlerUrl += `&revalidateTags=${encodeURIComponent(JSON.stringify(revalidateTags))}`;
  }

  const r = await fetch(routeHandlerUrl, reqOptions);

  return r.ok
    ? (await r.json()) ?? {
        total: 0,
        items: [],
      }
    : {
        total: 0,
        items: [],
      };
};
