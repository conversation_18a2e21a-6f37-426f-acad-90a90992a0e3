import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import {
  getPaymentTransactionsClient,
  IPaymentTransaction,
} from '@/app/api/payments/getPaymentTransactionsClient.ts';
import { sortItems } from '@/utils/sortItems.ts';
import { deleteSubsequentDuplicateObjectsInPayments } from '@/utils/deleteSubsequentDuplicateObjectsInPayments.ts';
import { IMethod, IMethodGroup, IPaymentCascade } from '@/app/api/payments/getPaymentDataClient.ts';
import { IConstant } from '@/app/api/getConstantsServer.ts';
import { NextResponse } from 'next/server';
import { verifyToken } from '@/app/api/server-actions/verifyToken.ts';

const GROUPS_PHOTOS = {
  // local_bank_transfer:
  //   'https://like-storage-dev.s3.eu-central-1.amazonaws.com/payment_provider/3/general/fc856284-8e37-4bc6-931b-be63624548b9.png',
  sbb: '',
  card_transfer: '',
  instant_payment: '',
};

export async function POST(req: Request): Promise<Response> {
  try {
    const tokenResult = await verifyToken();
    const tokenVerified = tokenResult?.access_token || tokenResult || cookies().get('token')?.value;

    if (!tokenVerified) {
      return NextResponse.json({ error: 'No valid token' });
    }

    const getPaymentMethods = async () => {
      let url = `${API_BASE_URL}/platform/user/payment/options`;

      return fetchData({
        url,
        type: 'payment-options',
        token: tokenVerified,
      });
    };

    const getLatestTransactions = async () => {
      const origin = new URL(req.url).origin;

      const cookieHeader = cookies()
        .getAll()
        .map((c) => `${c.name}=${c.value}`);

      cookieHeader.push(`token=${tokenVerified}`);

      return getPaymentTransactionsClient(
        0,
        20,
        'payment_operation=deposit&status=success',
        'created_at=desc',
        undefined,
        origin,
        cookieHeader.join('; '),
      );
    };

    try {
      const [
        { value: result }, //status: methodsStatus,
        { value: transactionsResult }, //status: transactionsStatus,
      ]: any = await Promise.allSettled([getPaymentMethods(), getLatestTransactions()]);

      if (!result?.settings?.length && !result?.cascades?.length) {
        return NextResponse.json({
          methods: {
            deposit: [],
            withdrawal: [],
          },
          featuredMethods: {
            deposit: [],
            withdrawal: [],
          },
        });
      }

      const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;
      const { constants }: { constants: IConstant[] | null } = await req.json();

      const optionsOrderResult = constants?.find(
        (constant) => constant.key === 'payment_settings_order',
      );

      const parsedOptionsOrderResult = optionsOrderResult
        ? JSON.parse(optionsOrderResult.value)
        : {};
      const depositOptionsOrderArr = optionsOrderResult
        ? parsedOptionsOrderResult['deposit']?.[userCurrency] || []
        : [];

      const withdrawalOptionsOrderArr = optionsOrderResult
        ? parsedOptionsOrderResult['withdrawal']?.[userCurrency] || []
        : [];

      const optionsWithCascades = [
        ...result.settings,
        ...(result?.cascades?.map((c: IPaymentCascade) => {
          const paymentSettingsIds = c.payment_settings_ids || [];
          const orderData =
            c.payment_operation === 'deposit' ? depositOptionsOrderArr : withdrawalOptionsOrderArr;

          const firstInOrderSettingIndex = orderData.findIndex((orderedIndex: number) =>
            paymentSettingsIds.includes(orderedIndex),
          );
          const firstInOrderSettingId = orderData[firstInOrderSettingIndex];

          return {
            ...c,
            payment_type: 'p2p',
            id: firstInOrderSettingId,
            cascadeId: c.id,
          };
        }) || []),
      ];

      const sortedDepositOptions = sortItems({
        items: optionsWithCascades.filter((item: IMethod) => item.payment_operation === 'deposit'),
        order: depositOptionsOrderArr,
      });

      const sortedWithdrawalOptions = sortItems({
        items: optionsWithCascades.filter(
          (item: IMethod) => item.payment_operation === 'withdrawal',
        ),
        order: withdrawalOptionsOrderArr,
      });

      const getOptionsWithGroups = (items: IMethod[]) => {
        const groupsAdded: string[] = [];

        return items.reduce((acc: any, option: IMethod) => {
          if (option.group) {
            if (groupsAdded.includes(option.group)) {
              return acc;
            }

            groupsAdded.push(option.group);
            const groupMethods = items.filter((item) => item.group === option.group);

            const amountMinInGroup = groupMethods
              .map((item) => item.amount_min)
              .reduce((a, b) => Math.min(a, b));
            const amountMaxInGroup = groupMethods
              .map((item) => item.amount_max)
              .reduce((a, b) => Math.max(a, b));

            if (groupMethods.length > 1) {
              return [
                ...acc,
                {
                  amount_min: amountMinInGroup,
                  amount_max: amountMaxInGroup,
                  methods: groupMethods,
                  group: option.group,
                  id: option.id,
                  payment_type: option.payment_type,
                  payment_operation: option.payment_operation,
                  photo_url:
                    GROUPS_PHOTOS[option.group as keyof typeof GROUPS_PHOTOS] ||
                    option.photo_url ||
                    '',
                } as IMethodGroup,
              ];
            } else {
              return [...acc, option];
            }
          } else {
            return [...acc, option];
          }
        }, []);
      };

      const getFeaturedMethods = (paymentMethods: IMethod[]) => {
        const latestMethodsUsed =
          deleteSubsequentDuplicateObjectsInPayments(transactionsResult?.items)
            // find payment method info that matches the transaction, set lastPaidAmount from transaction, return method info with lastPaidAmount
            ?.map((transaction: IPaymentTransaction) => {
              const methodInfo = paymentMethods.find((method: IMethod) =>
                transaction?.payment_cascade_id
                  ? transaction.payment_cascade_id === method.cascadeId
                  : transaction?.payment_setting_id === method?.id,
              );

              if (methodInfo) {
                (methodInfo as IMethod).lastPaidAmount = transaction?.amount_user || 0;
              }
              return methodInfo as IMethod;
            })
            ?.filter((method) => method)
            ?.slice(0, 3) || [];

        if (latestMethodsUsed.length < 3) {
          const featuredMethods = paymentMethods.filter(
            (method) =>
              method.is_featured &&
              !latestMethodsUsed.some((latestMethod: IMethod) =>
                latestMethod?.cascadeId
                  ? latestMethod.cascadeId === method?.cascadeId
                  : latestMethod?.id === method?.id,
              ),
          );

          return [...latestMethodsUsed, ...featuredMethods];
        }
        return latestMethodsUsed;
      };

      return NextResponse.json({
        methods: {
          deposit: getOptionsWithGroups(sortedDepositOptions as IMethod[]),
          withdrawal: getOptionsWithGroups(sortedWithdrawalOptions as IMethod[]),
        },
        featuredMethods: {
          deposit: getFeaturedMethods(sortedDepositOptions as IMethod[]),
          withdrawal: getFeaturedMethods(sortedWithdrawalOptions as IMethod[]),
        },
      });
    } catch (error) {
      console.error('Error:', error);
      return NextResponse.json({
        methods: {
          deposit: [],
          withdrawal: [],
        },
        featuredMethods: {
          deposit: [],
          withdrawal: [],
        },
      });
    }
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({
      methods: {
        deposit: [],
        withdrawal: [],
      },
      featuredMethods: {
        deposit: [],
        withdrawal: [],
      },
    });
  }
}
