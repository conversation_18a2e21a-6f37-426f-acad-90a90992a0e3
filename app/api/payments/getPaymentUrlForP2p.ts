'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';

interface PaymentGetP2pUrlType {
  payment_setting_id: number;
  currency: string;
  amount: number;
  callback_url: string;
  client_ip: string;
  bank_name?: string;
}

export const getPaymentUrlForP2p = async ({
  payment_setting_id,
  currency,
  amount,
  callback_url,
  client_ip,
  bank_name,
}: PaymentGetP2pUrlType) => {
  let url = `${API_BASE_URL}/platform/user/payment/deposit/get-url`;

  const tokenVerified = cookies().get('token')?.value;

  const body: PaymentGetP2pUrlType = {
    payment_setting_id,
    currency,
    amount,
    callback_url,
    client_ip,
    bank_name,
  };

  const result = await fetchData({
    url,
    type: 'get p2p url',
    token: tokenVerified,
    method: 'POST',
    body,
  });

  if (result.detail) {
    return {
      error: result.detail,
    };
  }

  return result;
};
