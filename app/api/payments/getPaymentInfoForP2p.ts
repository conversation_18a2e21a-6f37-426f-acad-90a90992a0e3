'use server';
import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';

interface PaymentGetP2pInfoType {
  payment_setting_ids: number[];
  currency: string;
  amount: number;
  callback_url: string;
  client_ip: string;
  payment_cascade_id?: number;
  bank_name?: string;
}

export const getPaymentInfoForP2p = async ({
  payment_setting_ids,
  currency,
  amount,
  callback_url,
  client_ip,
  payment_cascade_id,
  bank_name,
}: PaymentGetP2pInfoType) => {
  let url = `${API_BASE_URL}/platform/user/payment/deposit/get-info`;

  const tokenVerified = cookies().get('token')?.value;

  const body: PaymentGetP2pInfoType = {
    payment_setting_ids,
    currency,
    amount,
    callback_url,
    client_ip,
    bank_name,
  };

  if (payment_cascade_id) {
    body.payment_cascade_id = payment_cascade_id;
  }

  const result = await fetchData({
    url,
    type: 'get p2p info',
    token: tokenVerified,
    method: 'POST',
    body,
  });

  if (result.detail) {
    return {
      error: result.detail,
    };
  }

  return result;
};
