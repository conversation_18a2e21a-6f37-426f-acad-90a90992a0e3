import { NextResponse } from 'next/server';

export async function GET(req: Request): Promise<Response> {
  try {
    const handlerUrl = new URL(req.url);
    let url = handlerUrl.searchParams.get('url') || '';
    url += `&apikey=${process.env.CURRENCY_FREAKS_API_KEY}`;

    const r = await fetch(url.toString(), { cache: 'no-store' });

    if (!r.ok) {
      return NextResponse.json(
        { error: r.statusText || 'Error fetching currency rate' },
        { status: r.status },
      );
    }

    const result = await r.json();

    return NextResponse.json({ value: result.rate });
  } catch (err: any) {
    return NextResponse.json({ error: err?.message || 'Unexpected error' }, { status: 500 });
  }
}
