export const getCurrencyRateClient = async ({
  from,
  to,
  amount = 1,
}: {
  from: string;
  to: string;
  amount?: number;
}) => {
  const url = `https://api.currencyfreaks.com/v2.0/convert/latest?from=${from}&to=${to}&amount=${amount}`;

  const r = await fetch(`/api/payments/currency-rate?url=${encodeURIComponent(url)}`, {
    cache: 'no-store',
  });

  return r.ok ? (await r.json()) ?? null : null;
};
