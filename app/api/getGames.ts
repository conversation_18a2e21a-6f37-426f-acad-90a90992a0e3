import { fetchData } from '@/app/api/fetchData.ts';
import { API_BASE_URL } from './constants.ts';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IGame {
  external_id: number;
  slug: string;
  status: string;
  currency: string;
  genres: string[];
  holidays: string;
  game_type: string;
  name: string;
  manual_rating: number;
  photo_url_vertical: string | null;
  photo_url_horizontal: string | null;
  provider_slug: string;
  release_date?: string;
  is_favorite?: boolean;
  is_demo: boolean;
  is_live: boolean;
  categories?: string[];
  hot: number | null;
  top: number | null;
  rtp_hot: number | null;
  rtp_cold: number | null;
  win_amount?: number;
  nickname?: string;
  email?: string;
  phone?: string;
  rate?: number | null;
  wager_percentage?: number;
}

export interface IGamesResponse {
  total: number;
  items: Array<IGame>;
}

const getGames = async (
  skip = 0,
  limit = 32,
  currency?: string,
  filters?: { [key: string]: (string | number)[] },
  sort?: string,
) => {
  const userCurrency = currency || DEFAULT_CURRENCY;

  let url = `${API_BASE_URL}/platform/games?limit=${limit}&skip=${skip}`;

  let filtersArray: string[] = [`provider_currencies=${userCurrency}`];

  if (filters && Object.keys(filters)?.length) {
    Object.keys(filters).forEach((key) => {
      if (key !== 'provider') {
        filtersArray.push(`${key}=${filters[key].join('|')}`);
      }
    });
  }

  filtersArray.length ? (url = `${url}&filters=${filtersArray.join(',')}`) : url;

  if (sort) {
    url = `${url}&sort=${sort}`;
  }

  let result;
  try {
    result = await fetchData({
      url,
      type: 'games',
    });
  } catch (error) {
    console.log('ERROR', error);
    return {
      total: 0,
      items: [],
    };
  }

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

const getGameByIdOrSlug = async (slug: string) => {
  const res = await fetch(`${API_BASE_URL}/platform/games/${slug}`);

  if (!res.ok) {
    console.error(`Error fetching game: ${slug}`);
    return { id: slug };
  }

  return await res.json();
};

export { getGameByIdOrSlug, getGames };
