import { fetchData } from '@/app/api/fetchData.ts';
import { ITranslations } from '@/types/global';
import { API_BASE_URL } from './constants.ts';

export interface IPost {
  slug: string;
  name: string;
  translations: ITranslations;
  body: ITranslations;
  state: string;
  topic_id: number;
  created_at: string;
  id: number;
  post_type: 'post' | 'faq';
  updated_at: string;
  icon: string | null;
  photos: any;
}

export interface ITopic {
  name: string;
  translations: ITranslations;
  slug: string;
  id?: number;
  body: ITranslations;
}

const getPostsServer = async (
  skip = 0,
  limit = 24,
  filters?: { [key: string]: (string | number)[] },
) => {
  let url = `${API_BASE_URL}/platform/posts?limit=${limit}&skip=${skip}`;

  const result = await fetchData({
    url,
    type: 'post',
    filters,
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

const getPostServer = async (id: number | string) => {
  const res = await fetch(`${API_BASE_URL}/platform/posts/${id}`, {
    cache: 'no-store',
  });

  if (!res.ok) {
    console.error(`Error fetching post: ${id}`);
    return { id };
  }

  return await res.json();
};

const getTopicsServer = async (): Promise<{ items: ITopic[]; total: number }> => {
  let url = `${API_BASE_URL}/platform/posts/topics`;

  const result = await fetchData({ url, type: 'topics' });

  return {
    items: result?.items?.reverse() || [],
    total: result?.total_items || 0,
  };
};

export { getPostServer, getPostsServer, getTopicsServer };
