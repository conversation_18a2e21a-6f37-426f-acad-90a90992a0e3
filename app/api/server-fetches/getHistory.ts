'use server';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';
import { API_BASE_URL } from '../constants.ts';

export interface IHistoryItem {
  balance: number;
  bet_amount: number;
  date: string;
  game_external_id: number;
  win_amount: number;
}

export interface IHistoryResponse {
  total: number;
  items: Array<IHistoryItem>;
}

const getHistory = async ({
  dateFrom,
  dateTo,
}: {
  dateFrom: string;
  dateTo: string;
}): Promise<IHistoryResponse> => {
  let url = `${API_BASE_URL}/platform/user/history?date_from=${dateFrom}&date_to=${dateTo}`;
  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    type: 'history',
    token: tokenVerified,
  });
  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getHistory };
