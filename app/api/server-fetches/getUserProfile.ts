import { API_BASE_URL } from '@/app/api/constants.ts';
import { fetchData } from '@/app/api/fetchData.ts';
import { cookies } from 'next/headers';

export interface UserProfileData {
  birth_date: string;
  currency: string;
  email: string;
  full_name?: string;
  id: number;
  is_email_verified: boolean;
  is_phone_verified: boolean;
  is_passport_verified: boolean;
  nickname: string;
  phone: string;
}

const getUserProfile = async () => {
  let url = `${API_BASE_URL}/platform/user/profile`;

  const tokenVerified = cookies().get('token')?.value;

  const result = await fetchData({
    url,
    token: tokenVerified,
    type: 'current user profile',
  });

  return result as UserProfileData;
};

// const patchUserProfile = async (requestBody: UserData) => {
//   let url = `${API_BASE_URL}/platform/user/profile`;

//   const tokenVerified = cookies().get('token')?.value;

//   const result = await fetchData({
//     url,
//     token: tokenVerified,
//     type: 'update user profile',
//     method: 'PATCH',
//     body: requestBody,
//   });

//   return result;
// };

export { getUserProfile };
