import { fetchData } from '@/app/api/fetchData.ts';
import { API_BASE_URL } from './constants.ts';
import { cookies } from 'next/headers';
import { DEFAULT_CURRENCY } from '@/constants.ts';

export interface IProvider {
  photo_url_primary: string | null;
  photo_url_secondary: string | null;
  name: string;
  external_id: number;
  is_featured: boolean;
  id: number;
  slug: string;
  is_live: boolean;
}

export interface IProvidersResponse {
  total: number;
  items: Array<IProvider>;
}

const getProvidersServer = async (
  skip = 0,
  limit?: number,
  filters?: { [key: string]: string[] },
): Promise<IProvidersResponse> => {
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;
  let url = `${API_BASE_URL}/platform/providers?limit=${limit}&skip=${skip}`;

  const result = await fetchData({
    url,
    filters: { ...filters, currencies: [userCurrency] },
    type: 'providers',
  });

  return {
    total: result?.total_items || 0,
    items: result?.items || [],
  };
};

export { getProvidersServer };
