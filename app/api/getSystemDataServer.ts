import { getFavouriteGamesServer } from '@/app/api/user/games/getFavouriteGamesServer.ts';
import { getGames } from '@/app/api/getGames.ts';
import { getProviders } from '@/app/api/getProviders.ts';
import { getConstants } from '@/app/api/getConstants.ts';
import { sortItems } from '@/utils/sortItems.ts';
import { getBanners } from '@/app/api/getBanners.ts';

const getSystemDataServer = async () => {
  // console.log('getSystemData server');
  const getFavourites = async () => {
    return getFavouriteGamesServer();
  };
  const getNewGamesLive = async () =>
    getGames(
      0,
      100,
      {
        is_live: ['true'],
      },
      'release_date=desc,external_id=desc',
    );
  const getNewGamesSlots = async () =>
    getGames(
      0,
      100,
      {
        is_live: ['false'],
      },
      'release_date=desc,external_id=desc',
    );

  const getProvidersPromise = async () => getProviders(0, 500);
  const getConstantsPromise = async () => getConstants();
  const getBannersPromise = async () => getBanners(0, 300);

  try {
    const [
      banners,
      favourites,
      newGamesLive,
      newGamesSlots,
      providers,
      constants,
    ]: any = await Promise.allSettled([
      getBannersPromise(),
      getFavourites(),
      getNewGamesLive(),
      getNewGamesSlots(),
      getProvidersPromise(),
      getConstantsPromise(),
    ]);

    const providersConstant = constants.value?.find(
      (constant: any) => constant.key === 'providers_order',
    );
    const providersOrderArr = providersConstant
      ? JSON.parse(providersConstant.value)
      : [];

    const bannersConstant = constants.value?.find(
      (constant: any) => constant.key === 'banners_order',
    );
    const bannersOrderArr = bannersConstant
      ? JSON.parse(bannersConstant.value)
      : [];

    return {
      favourites: favourites.value?.items || [],
      newGames: [
        ...(newGamesSlots.value?.items || []),
        ...(newGamesLive.value?.items || []),
      ],
      providers:
        sortItems({
          items: providers.value?.items || [],
          order: providersOrderArr,
        }) || [],
      banners: sortItems({
        items: banners?.value?.items || [],
        order: bannersOrderArr || [],
      }),
      constants: constants.value || [],
    };
  } catch (error) {
    console.log(error);
    return { favourites: [], newGames: [], providers: [], constants: [] };
  }
};

export { getSystemDataServer };
