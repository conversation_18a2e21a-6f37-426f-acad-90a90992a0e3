import { StateCreator } from 'zustand/index';
import { AppStore } from '@/app/store';

export interface NavigationState {
  activeModalData: any;
  // eslint-disable-next-line no-unused-vars
  setActiveModalData: (data: any) => void;
  previousRoute: string;
  activeAuthTab: 'login' | 'signUp';
  // eslint-disable-next-line no-unused-vars
  setActiveAuthTab: (activeAuthTab: 'login' | 'signUp') => void;
  // eslint-disable-next-line no-unused-vars
  setPreviousRoute: (route: string) => void;
}

export type NavigationSlice = NavigationState;

export const initialNavigationState: NavigationState = {
  previousRoute: '',
  activeAuthTab: 'login',
  setActiveAuthTab: () => {},
  setPreviousRoute: () => {},
  activeModalData: {},
  setActiveModalData: () => {},
};

export const createNavigationSlice: StateCreator<
  AppStore,
  [['zustand/devtools', never]],
  [],
  NavigationSlice
> = (set) => ({
  ...initialNavigationState,
  setActiveModalData: (data) =>
    set(
      (state) => {
        return {
          ...state,
          activeModalData: data,
        };
      },
      undefined,
      'games/setActiveModalData',
    ),
  setActiveAuthTab: (activeAuthTab: 'login' | 'signUp') =>
    set(
      (state) => {
        return {
          ...state,
          activeAuthTab,
        };
      },
      undefined,
      'games/setActiveAuthTab',
    ),
  setPreviousRoute: (route) =>
    set(
      (state) => {
        return {
          ...state,
          previousRoute: route,
        };
      },
      undefined,
      'games/setPreviousRoute',
    ),
});
