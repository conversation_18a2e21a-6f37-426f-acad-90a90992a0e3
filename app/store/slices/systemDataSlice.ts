import { StateCreator } from 'zustand';
import { AppStore } from '@/app/store';
import { IGame } from '@/app/api/getGames.ts';
import { IProvider } from '@/app/api/getProviders.ts';
import { IConstant } from '@/app/api/getConstants.ts';

interface GameCategoryState {
  items: IGame[];
  total: number;
  isInitiallyLoaded: boolean;
}

interface ProviderState {
  items: IProvider[];
  total: number;
  isInitiallyLoaded: boolean;
}

interface ConstantsState {
  items: IConstant[];
  isInitiallyLoaded: boolean;
}

export interface SystemDataState {
  favourites: GameCategoryState;
  newGames: GameCategoryState;
  providers: ProviderState;
  constants: ConstantsState;
  // eslint-disable-next-line no-unused-vars
  saveSystemData: ({ dataKey, gamesResult }: SystemDataArgs) => void;
}

export type SystemDataKeyType = keyof SystemDataState;

export interface SystemDataArgs {
  dataKey: SystemDataKeyType;
  gamesResult: { items: IGame[]; total: number };
}

export interface SystemDataActions {
  // eslint-disable-next-line no-unused-vars
  saveSystemData: ({ dataKey, gamesResult }: SystemDataArgs) => void;
  // resetAppState(): void;
  // setIsLoading(isLoading: AppState['isLoading']): void;
}

export type SystemDataSlice = SystemDataState;

const initialSystemPartState: GameCategoryState & ProviderState = {
  items: [],
  total: 0,
  isInitiallyLoaded: false,
};

export const initialSystemDataState: SystemDataState = {
  // eslint-disable-next-line no-unused-vars
  saveSystemData: () => {},
  favourites: initialSystemPartState,
  newGames: initialSystemPartState,
  providers: initialSystemPartState,
  constants: {
    items: [],
    isInitiallyLoaded: false,
  },
};

export const createSystemDataSlice: StateCreator<
  AppStore,
  [['zustand/devtools', never]],
  [],
  SystemDataSlice
> = (set) => ({
  ...initialSystemDataState,
  saveSystemData: ({ dataKey, gamesResult }: SystemDataArgs) =>
    set(
      (state: SystemDataState) => {
        return {
          ...state,
          [dataKey]: {
            items: gamesResult.items,
            total: gamesResult.total,
            isInitiallyLoaded: true,
          },
        };
      },
      undefined,
      'games/saveSystemData',
    ),
});
