import { StateCreator } from 'zustand';
import { AppStore } from '@/app/store';
import { IGame } from '@/app/api/getGames.ts';
import { GamePageType } from '@/types/global';

interface GameCategoryState {
  items: IGame[];
  total: number;
  isInitiallyLoaded: boolean;
  initialPageFilterId?: number;
  rtpType?: 'hot' | 'cold';
}

interface RtpGameCategoryState {
  hot: GameCategoryState;
  cold: GameCategoryState;
}

export interface GamesCasinoState {
  all: GameCategoryState;
  top: GameCategoryState;
  hot: GameCategoryState;
  liked: GameCategoryState;
  crash: GameCategoryState;
  bonus: GameCategoryState;
  tables: GameCategoryState;
  tournament: GameCategoryState;
  rtp: RtpGameCategoryState;
  new: GameCategoryState;
}

export interface GamesLiveCasinoState {
  all: GameCategoryState;
  top: GameCategoryState;
  roulette: GameCategoryState;
  blackjack: GameCategoryState;
  baccarat: GameCategoryState;
  'video-poker': GameCategoryState;
  shows: GameCategoryState;
  other: GameCategoryState;
}

export interface GamesState {
  recommended: GameCategoryState;
  recent: GameCategoryState;
  casino: GamesCasinoState;
  'live-casino': GamesLiveCasinoState;
  // eslint-disable-next-line no-unused-vars
  resetGameCategoryState: (category: 'recommended' | 'recent') => void;
  saveGames: ({
    // eslint-disable-next-line no-unused-vars
    gamePageType,
    // eslint-disable-next-line no-unused-vars
    category,
    // eslint-disable-next-line no-unused-vars
    gamesResult,
    // eslint-disable-next-line no-unused-vars
    initialPageFilterId,
    // eslint-disable-next-line no-unused-vars
    rtpType,
  }: GamesArgs) => void;
}

export interface GamesArgs {
  gamePageType?: GamePageType;
  category: GameCategoryType;
  gamesResult: { items: IGame[]; total: number };
  initialPageFilterId?: number;
  rtpType?: 'hot' | 'cold';
}

export type GameCategoryType =
  | keyof GamesCasinoState
  | keyof GamesLiveCasinoState
  | 'recommended'
  | 'recent';

export type GamesSlice = GamesState;

const initialGameState: GameCategoryState = {
  items: [],
  total: 0,
  isInitiallyLoaded: false,
  initialPageFilterId: 0,
};

export const initialGamesState: GamesState = {
  saveGames: () => {},
  resetGameCategoryState: () => {},
  recommended: initialGameState,
  recent: initialGameState,
  casino: {
    all: initialGameState,
    top: initialGameState,
    hot: initialGameState,
    liked: initialGameState,
    crash: initialGameState,
    bonus: initialGameState,
    tables: initialGameState,
    tournament: { ...initialGameState, initialPageFilterId: 0 },
    rtp: { hot: initialGameState, cold: initialGameState },
    new: initialGameState,
  },
  'live-casino': {
    all: initialGameState,
    top: initialGameState,
    roulette: initialGameState,
    blackjack: initialGameState,
    baccarat: initialGameState,
    'video-poker': initialGameState,
    shows: initialGameState,
    other: initialGameState,
  },
};

export const createGamesSlice: StateCreator<
  AppStore,
  [['zustand/devtools', never]],
  [],
  GamesSlice
> = (set) => ({
  ...initialGamesState,
  saveGames: ({
    gamePageType,
    category,
    gamesResult,
    initialPageFilterId,
    rtpType,
  }: GamesArgs) => {
    if (category === 'rtp' && rtpType) {
      return set((state) => {
        return {
          ...state,
          casino: {
            ...state.casino,
            rtp: {
              ...state.casino.rtp,
              [rtpType as 'hot' | 'cold']: {
                items: gamesResult.items,
                total: gamesResult.total,
                isInitiallyLoaded: true,
              },
            },
          },
        };
      });
    }
    if (!gamePageType) {
      return set((state) => {
        return {
          ...state,
          [category]: {
            items: gamesResult.items,
            total: gamesResult.total,
            isInitiallyLoaded: true,
          },
        };
      });
    }
    return set(
      (state: GamesState) => {
        return {
          ...state,
          [gamePageType]: {
            ...state[gamePageType],
            [category]: {
              items: gamesResult.items,
              total: gamesResult.total,
              isInitiallyLoaded: true,
              ...(initialPageFilterId && {
                initialPageFilterId,
              }),
            },
          },
        };
      },
      undefined,
      'games/saveGames',
    );
  },
  resetGameCategoryState: (category: 'recommended' | 'recent') => {
    set((state) => {
      return {
        ...state,
        [category]: initialGameState,
      };
    });
  },
  // resetGamesWhenLogout: () => {},
  // resetAppState: () => set(initialAppState),
});
