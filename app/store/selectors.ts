import { useStore } from '@/app/providers/app-store-provider.tsx';
import { useShallow } from 'zustand/react/shallow';
import { GamePageType } from '@/types/global';
import {
  GameCategoryType,
  GamesCasinoState,
  GamesLiveCasinoState,
} from '@/app/store/slices/gamesSlice.ts';

export const useGamesInitialSelector = (
  category: GameCategoryType,
  gamePageType?: GamePageType,
) => {
  const { isInitiallyLoaded, saveGames, total, items, initialPageFilterId } =
    useStore(
      useShallow((state) => {
        const pageTypeState = state[gamePageType as GamePageType];
        if (gamePageType === 'casino') {
          const categoryState = (
            pageTypeState as Omit<GamesCasinoState, 'rtp'>
          )[category as keyof Omit<GamesCasinoState, 'rtp'>];
          return {
            total: categoryState?.total ?? 0,
            items: categoryState?.items ?? [],
            isInitiallyLoaded: categoryState?.isInitiallyLoaded ?? false,
            saveGames: state.saveGames ?? (() => {}),
            ...(category === 'tournament' && {
              initialPageFilterId:
                'initialPageFilterId' in categoryState
                  ? categoryState?.initialPageFilterId
                  : 0,
            }),
          };
        }

        if (gamePageType === 'live-casino') {
          const categoryState = (pageTypeState as GamesLiveCasinoState)[
            category as keyof GamesLiveCasinoState
          ];
          return {
            total: categoryState?.total ?? 0,
            items: categoryState?.items ?? [],
            isInitiallyLoaded: categoryState?.isInitiallyLoaded ?? false,
            saveGames: state.saveGames ?? (() => {}),
            ...(category === 'tournament' && {
              initialPageFilterId: categoryState?.initialPageFilterId,
            }),
          };
        }

        if (category === 'recommended' || category === 'recent') {
          const categoryState = state[category];
          return {
            total: categoryState?.total ?? 0,
            items: categoryState?.items ?? [],
            isInitiallyLoaded: categoryState?.isInitiallyLoaded ?? false,
            saveGames: state.saveGames ?? (() => {}),
          };
        }

        return {
          total: 0,
          items: [],
          isInitiallyLoaded: false,
          saveGames: () => {},
        }; // Fallback case
      }),
    );
  return {
    isInitiallyLoaded,
    saveGames,
    total,
    items,
    initialPageFilterId,
  };
};

export const useRtpGamesSelector = (rtpType: 'hot' | 'cold') => {
  const { isInitiallyLoaded, saveGames, total, items } = useStore(
    useShallow((state) => {
      const rtpTypeState = state.casino.rtp[rtpType];
      return {
        total: rtpTypeState?.total ?? 0,
        items: rtpTypeState?.items ?? [],
        isInitiallyLoaded: rtpTypeState?.isInitiallyLoaded ?? false,
        saveGames: state.saveGames ?? (() => {}),
      };
    }),
  );

  return {
    isInitiallyLoaded,
    saveGames,
    total,
    items,
  };
};

export const useResetGameCategoryState = () => {
  const { resetGameCategoryState } = useStore(
    useShallow((state) => ({
      resetGameCategoryState: state.resetGameCategoryState,
    })),
  );
  return { resetGameCategoryState };
};

// export const useNavigation = () => {
//   const { previousRoute, setPreviousRoute, activeAuthTab, setActiveAuthTab } =
//     useStore(
//       useShallow((state) => ({
//         previousRoute: state.previousRoute,
//         activeAuthTab: state.activeAuthTab,
//         setActiveAuthTab: state.setActiveAuthTab,
//         setPreviousRoute: state.setPreviousRoute,
//       })),
//     );
//   return { previousRoute, setPreviousRoute, activeAuthTab, setActiveAuthTab };
// };
