import { createStore } from 'zustand/vanilla';

import { defaultState } from './defaultState';
import { GamesSlice, GamesState, createGamesSlice } from './slices/gamesSlice';
import { devtools } from 'zustand/middleware';
import {
  createSystemDataSlice,
  SystemDataSlice,
  SystemDataState,
} from '@/app/store/slices/systemDataSlice.ts';

export type AppStore = GamesSlice & SystemDataSlice;
export type AppState = GamesState & SystemDataState; //& NavigationSlice

export const initStore = (): AppState => {
  return defaultState;
};

export const createAppStore = (initState: AppState = defaultState) => {
  return createStore<AppStore>()(
    devtools((...args) => ({
      ...initState,
      ...createGamesSlice(...args),
      ...createSystemDataSlice(...args),
      // ...createNavigationSlice(...args),
    })),
  );
};
