'use client';
import { Icon } from '@/atomic-design-components';
import { useEffect, useRef } from 'react';
import { isClientReady } from '@/utils';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import clsx from 'clsx';

const ButtonToTop = () => {
  const ref = useRef<HTMLButtonElement>(null);
  const { isTouchDevice } = useIsTouchMobileView();

  useEffect(() => {
    window.onscroll = function () {
      scrollFunction();
    };
  }, []);

  function scrollFunction() {
    if (ref && ref.current) {
      if (
        document.body.getClientRects()[0].height -
          document.body.getClientRects()[0].bottom >
        window.innerHeight
      ) {
        ref.current.style.display = 'block';
      } else {
        ref.current.style.display = 'none';
      }
    }
  }

  function scrollToTop(e: any) {
    e.preventDefault();
    if (!isClientReady()) return;
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  }
  return (
    <button
      ref={ref}
      id='toTopButton'
      className={clsx('toTopButton z-[85]', isTouchDevice && 'mobileView')}
      onClick={scrollToTop}
    >
      <Icon name='chevronDown' isRotated height={16} width={16} />
    </button>
  );
};

export default ButtonToTop;
