import ProgressBar from '@/app/components/ProgressBar';
import { Typography, Image } from '@/atomic-design-components';
import { theme } from '@/theme';
import { ITranslations, LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { useParams } from 'next/navigation';

const WagerProgressCard = ({
  currentProgress,
  imageUrl,
  title,
  total,
}: {
  currency: string;
  currentProgress: number;
  imageUrl?: string;
  title?: ITranslations;
  total: number;
}) => {
  const { lng }: { lng: LanguagesType } = useParams();

  return (
    <>
      <div className='flex items-center rounded-[4px] bg-[#2D4672] pl-[5px]'>
        {imageUrl && (
          <div className='relative h-[53px] w-[53px] rounded-[50%] border-[3px] border-solid border-[#253A5E] p-[2px]'>
            <Image
              src={imageUrl}
              alt='activeWager'
              width={43}
              height={43}
              unoptimized
              className='absolute left-[2px] top-[2px] h-[43px] w-[43px] rounded-[50%]'
            />
          </div>
        )}

        <div className='block pb-[22px] pl-[11px] pt-[19px]'>
          {title && (
            <Typography
              fontSize='12px'
              type='label2'
              text={getAvailableTranslation(title, lng)}
              margin='0 0 9px 0'
            />
          )}
        </div>
      </div>
      <ProgressBar
        currentProgress={currentProgress}
        total={total}
        color={theme.color?.secondary.dark}
      />
    </>
  );
};

export default WagerProgressCard;
