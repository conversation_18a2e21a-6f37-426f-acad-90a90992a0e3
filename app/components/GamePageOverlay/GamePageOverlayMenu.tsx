'use client';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { IGame } from '@/app/api/getGames';
import { IProvider } from '@/app/api/getProviders.ts';
import { useTranslation } from '@/app/i18n/client';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Icon, Typography } from '@/atomic-design-components';
import useIsGameFavourite from '@/hooks/useIsGameFavourite.ts';
import { theme } from '@/theme';
import { closeModal } from '@/utils/closeModal';
import { openModal } from '@/utils/openModal';

const GamePageOverlayMenu = ({
  item,
  currRef,
}: {
  item: IGame;
  currRef: any;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const [{ favourites = [], providers = [] }, setSystemData] = useSystemData();
  const gameProvider = providers?.find(
    (provider: IProvider) => provider.slug === item?.provider_slug,
  );
  const { isGameFavourite, onToggleFavourite } = useIsGameFavourite(
    item,
    favourites,
    setSystemData,
  );

  return (
    <div
      id='gamePageOverlayMenu'
      className='relative mt-auto rounded-t-2xl bg-[#1E293B] px-4 pb-3 pt-4'
      ref={currRef}
    >
      <Icon
        name='cross'
        className='crossIcon gameCrossIcon absolute right-4 top-3 z-10'
        onClick={(e: any) => {
          e.preventDefault();
          document
            .getElementById('gamePageOverlayModal')
            ?.setAttribute('data-active-page-game-slug', '');
          closeModal('gamePageOverlayModal');
        }}
        width={38}
        height={16}
        wrapperWidth={32}
        wrapperHeight={32}
        strokeWidth={2}
        fill={theme.color?.general.lightest}
        wrapperColor={theme.color?.general.darker}
      />
      <div className='mb-1 flex gap-2'>
        <Typography text={item.name} type='h1' withEllipsis />
        {isUserAuthorized && (
          <Icon
            name='heartShape'
            stroke={
              isGameFavourite
                ? theme.color?.primary.main
                : theme.color?.general.lightest
            }
            fill={isGameFavourite ? theme.color?.primary.main : 'none'}
            className='favoriteIcon'
            onClick={async (e: Event) => {
              await onToggleFavourite(e);
            }}
          />
        )}
      </div>

      {gameProvider?.id && (
        <Typography
          href={`/${lng}/${gameProvider.is_live ? 'live-casino' : 'casino'}/games/all?filters=provider_id=${gameProvider.id}`}
          text={gameProvider.name}
          type='label1'
          component={Link}
          className='!mb-3'
        />
      )}
      <Button
        size='medium'
        text={`+ ${t('deposit')}`}
        fullWidth
        onClick={(e: any) => {
          e.preventDefault();
          openModal('cashierModal');
        }}
      />
    </div>
  );
};

export default GamePageOverlayMenu;
