'use client';
import { useEffect, useState } from 'react';
import { useCookies } from 'react-cookie';
import {
  getGames,
  getGamesTournaments,
  IGamesResponse,
} from '@/app/api/getGames.ts';
import { getRecentGamesClient } from '@/app/api/user/games/getRecentGamesClient.ts';
import GameCard from '@/app/components/Games/GameCard';
import Slider from '@/app/components/Slider/Slider.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useSystemData } from '@/app/wrappers/systemDataProvider';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import 'keen-slider/keen-slider.min.css';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { GameGroup } from '@/app/[lng]/[gamePageType]/(landing-pages)/page.tsx';

export const GAME_PAGE_GAMES_GROUPS = {
  'live-casino': {
    recent: {},
    favourite: {},
    top: {
      icon: 'starFlying',
      filter: { top__gte: [0] },
      sort: 'top=asc',
    },
    roulette: {
      icon: 'roulette',
      filter: { game_type: ['roulette'] },
      sort: '',
    },
    blackjack: {
      icon: 'cards',
      filter: { game_type: ['blackjack'] },
      sort: '',
      perView: 7,
    },
    baccarat: {
      icon: 'man',
      filter: { game_type: ['baccarat'] },
    },
    videoPoker: {
      icon: 'chipPlay',
      filter: { game_type: ['video_poker'] },
    },
    showsGames: {
      icon: 'videoPlay',
      filter: { game_type: ['tv_shows'] },
    },
  },
  casino: {
    recent: {},
    favourite: {},
    top: {
      icon: 'star',
      filter: { top__gte: [0] },
      sort: 'top=asc',
      iconProps: { fill: '#0083FF' },
    },
    hot: {
      icon: 'fire',
      filter: { hot__gte: [0] },
      sort: 'hot=desc',
    },
    liked: {
      icon: 'like',
      filter: { manual_rating__gte: [1] },
      sort: 'manual_rating=asc',
    },
    crash: {
      icon: 'rocket',
      filter: { game_type: ['crash'] },
    },
    new: {
      icon: 'newIcon',
      sort: 'release_date=desc,external_id=desc',
      maxLimit: 100,
      iconProps: { fill: '#489F37', width: 24, height: 24 },
    },
    Live: {
      icon: 'chip',
      sort: 'popularity=asc',
    },
    tournament: {
      icon: 'cup',
    },
    bonus: {
      icon: 'giftBonus',
      filter: { has_bonus: ['true'] },
    },
    tables: {
      icon: 'dices',
      iconProps: { width: 22, height: 22 },
      filter: { game_type: ['roulette|blackjack|baccarat'] },
    },
  },
} as any;

const LIMIT = 36;

const GameOverlayCarousel = ({
  lng,
  gamePageType,
  group,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
  group: GameGroup;
}) => {
  const { t } = useTranslation(lng);
  const [games, setGames] = useState<IGamesResponse | null>(null);
  const [{ favourites }] = useSystemData();
  const [perView, setPerView] = useState(8);
  const { height, width } = useWindowSize();

  const [cookies] = useCookies(['isBonusActive']);
  const isBonusActive = cookies.isBonusActive === true;
  const bonusFilter =
    isBonusActive && !['favourite', 'recent'].includes(group as string)
      ? { wager_percentage__gte: [1] }
      : {};

  const mainFilter = GAME_PAGE_GAMES_GROUPS[gamePageType][group]?.filter;
  const sort = GAME_PAGE_GAMES_GROUPS[gamePageType][group]?.sort;
  const maxLimit = GAME_PAGE_GAMES_GROUPS[gamePageType][group]?.maxLimit;

  const filters =
    group === 'Live' || gamePageType === 'live-casino'
      ? {
          ...mainFilter,
          is_live: ['true'],
        }
      : {
          ...mainFilter,
          ...bonusFilter,
          is_live: ['false'],
        };

  const fetchGames = async () => {
    const gamesInitial =
      group === 'tournament'
        ? await getGamesTournaments(0, LIMIT, filters)
        : group === 'recent'
          ? await getRecentGamesClient(0, 50)
          : await getGames(0, LIMIT, filters, sort);

    setGames(gamesInitial);
  };

  useEffect(() => {
    if (group !== 'favourite') {
      fetchGames();
    } else {
      setGames({ items: favourites || [], total: favourites?.length || 0 });
    }
  }, [group, favourites]);

  useEffect(() => {
    const newPerView = () => {
      if (width && height) {
        let minCards = 6;
        if (width <= 959) minCards = 7;
        if (width <= 770) minCards = 6;
        if (width <= 520) minCards = 4.4;
        if (width <= 414) minCards = 3.8;

        // For horizontal layout
        if (height <= 550) {
          const cardWidth = (3 / 4) * (height - 200);
          const maxCards = Math.floor(((width + 8) / (cardWidth + 8)) * 2) / 2;
          return Math.max(minCards, maxCards);
        }
        // For vertical layout
        return minCards;
      }
      // backup default
      return 6;
    };

    if (newPerView() !== perView) {
      setPerView(newPerView());
    }
  }, [height, width]);

  const GET_GAMES_SLIDER_BREAKPOINTS = () => ({
    '(min-width: 0px)': {
      slides: {
        perView,
        spacing: 8,
        dragSpeed: 0,
        defaultAnimation: { duration: 4000 },
      },
    },
  });

  return (
    <div id='gameOverlayCarousel' className='mt-2'>
      {(games === null || !!games?.items?.length) && (
        <Slider
          items={games?.items || Array(8).fill(<LoadingGameCard />)}
          total={games?.total || 8}
          Slide={GameCard}
          isBadgeShown={!!games}
          filters={filters}
          sort={sort}
          isCarousel
          limit={LIMIT}
          maxLimit={maxLimit}
          perView={perView}
          withLoadMore
          breakpoints={GET_GAMES_SLIDER_BREAKPOINTS()}
        />
      )}
      {games?.items && !games.items.length && (
        <Typography type='body2' padding='24px 0' justifyContent='center'>
          {t('nothingFound')}
        </Typography>
      )}
    </div>
  );
};

export default GameOverlayCarousel;
