'use client';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

import { getGameByIdOrSlug, IGame } from '@/app/api/getGames';
import { useUnfinishedGame } from '@/app/wrappers/UnfinishedGameProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import { usePrevious } from '@/hooks/useReact';
import { closeModal } from '@/utils/closeModal.ts';
import ModalDialog from '../ModalDialog';
import GamePageOverlay from './GamePageOverlay';
import { getCurrentUserClient } from '@/app/api/user/getCurrentUserClient.ts';

const GamePageOverlayModal = () => {
  const ref = useRef<HTMLDialogElement>(null);
  const { isDialogOpen } = useIsDialogOpen(ref);
  const [item, setItem] = useState<IGame | null>(null);

  const router = useRouter();
  const pathname = usePathname();
  const prevPathname = usePrevious(pathname);
  const { lng } = useParams();
  const { setUser } = useUser();
  const { getUnfinishedGame } = useUnfinishedGame();

  useEffect(() => {
    if (!isDialogOpen) {
      router.prefetch(`/${lng}`);
      return;
    }
    getCurrentUserClient().then((user) => setUser(user));
    getUnfinishedGame();

    if (pathname !== prevPathname) {
      closeModal('gamePageOverlayModal');
    }
  }, [isDialogOpen, pathname, prevPathname]);

  useEffect(() => {
    const dndButton = document.getElementById('draggableButton');

    if (isDialogOpen) {
      const slug = ref?.current?.getAttribute('data-active-page-game-slug');
      if (slug) {
        dndButton?.style.setProperty('display', 'none');
        fetch(slug);
      }
    } else {
      ref?.current?.setAttribute('data-active-page-game-slug', '');
      setItem(null);
      dndButton?.style.setProperty('display', 'flex');
    }
  }, [isDialogOpen]);

  const fetch = async (slug: string) => {
    const item = await getGameByIdOrSlug(slug);
    setItem(item);
  };

  return (
    <ModalDialog id='gamePageOverlayModal' ref={ref} closeButtonPosition='hidden'>
      {item ? <GamePageOverlay item={item} /> : <></>}
    </ModalDialog>
  );
};

GamePageOverlayModal.displayName = 'GamePageOverlayModal';
export default GamePageOverlayModal;
