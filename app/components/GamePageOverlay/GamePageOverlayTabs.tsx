'use client';
import { useTranslation } from '@/app/i18n/client';
import { Tabs, Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { getGamePageType } from '@/utils/getGamePageType';
import { GAME_PAGE_GAMES_GROUPS } from './GameOverlayCarousel';
import { GameGroup } from '@/app/[lng]/[gamePageType]/(landing-pages)/page.tsx';
import { StyledGamePageOverlayTabs } from './styled';

const GamePageOverlayTabs = ({
  lng,
  gamePageType,
  contents,
  isUserAuthorized,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
  contents: any;
  isUserAuthorized: boolean;
}) => {
  const { t } = useTranslation(lng);

  const gamePageTypeChecked = getGamePageType(gamePageType);

  const tabsTitles = Object.keys(
    GAME_PAGE_GAMES_GROUPS[gamePageTypeChecked],
  ).filter((group) => {
    const typedGroup = group as GameGroup;
    return isUserAuthorized
      ? typedGroup
      : typedGroup !== 'favourite' && typedGroup !== 'recent'
        ? typedGroup
        : false;
  });

  const getTabTitle = (tab: any) => (
    <Typography
      text={tab === 'tournament' ? t('tournamentss') : t(tab)}
      type='body2'
      className='px-3 py-2'
    />
  );

  return (
    <StyledGamePageOverlayTabs>
      <Tabs
        className='withBGColor'
        tabsTitles={tabsTitles}
        tabsContents={contents}
        tabPadding='0 4px'
        getTabTitle={getTabTitle}
      />
    </StyledGamePageOverlayTabs>
  );
};

export default GamePageOverlayTabs;
