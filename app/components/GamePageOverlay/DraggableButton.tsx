'use client';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import { Rnd } from 'react-rnd';
import { useTranslation } from '@/app/i18n/client';
import { Icon, Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal.ts';
import { getFromLocalStorage, saveToLocalStorage } from '@/utils/localStorage';
import { openModal } from '@/utils/openModal';
import ThreeDots from '../Header/components/ThreeDots';
import { StyledDraggableButton } from './styled';
import useWindowSize from '@/hooks/useWindowSize.ts';

const style = {
  position: 'absolute',
  zIndex: 3000,
} as const;

const DraggableButton = ({
  slug,
  isOpen,
  lng,
  isMobileView,
}: {
  slug: string;
  isOpen: boolean;
  lng: LanguagesType;
  isMobileView: boolean;
}) => {
  const { t } = useTranslation(lng);
  const ref = useRef<HTMLDivElement>(null);

  const dragMenuPositionFromLS = getFromLocalStorage('dragMenuPosition');
  const [position, setPosition] = useState({ x: 0, y: 36 });
  const [isOpened, setIsOpened] = useState(isOpen);
  const [isOnRight, setIsOnRight] = useState(false);
  const [buttonWidth, setButtonWidth] = useState(isOpen ? 136 : 41);
  const { width, height } = useWindowSize();
  useEffect(() => {
    setIsOpened(isOpen);
    setButtonWidth(isOpen ? 136 : 41);
  }, [isOpen]);

  useEffect(() => {
    if (dragMenuPositionFromLS) {
      const wasOnRight = dragMenuPositionFromLS.x > window.innerWidth / 2;
      setPosition({
        x: wasOnRight ? window.innerWidth - buttonWidth : 0,
        y: dragMenuPositionFromLS.y,
      });
      setIsOnRight(wasOnRight);
    }
  }, [isOpened]);

  useEffect(() => {
    if (ref.current && dragMenuPositionFromLS) {
      const rect = ref.current.getBoundingClientRect();
      const isOffScreenHeight = rect.bottom > window.innerHeight;
      const newX = isOnRight ? window.innerWidth - buttonWidth : 0;
      const newY = isOffScreenHeight ? 36 : dragMenuPositionFromLS.y;
      if (newX !== position.x || newY !== position.y) {
        setPosition({ x: newX, y: newY });
        saveToLocalStorage('dragMenuPosition', { x: newX, y: newY });
      }
    }
  }, [width, height]);

  if (!isMobileView) return null;

  return (
    <Rnd
      style={style}
      size={{ width: 'auto', height: 'auto' }}
      enableResizing={false}
      dragAxis='both'
      bounds='parent'
      position={{ x: position.x, y: position.y }}
      onDragStop={(e, d) => {
        const right =
          d.x > window.innerWidth / 2 ? window.innerWidth - buttonWidth : 0;
        setIsOnRight(!!right);
        setPosition({ x: right, y: d.y });
        saveToLocalStorage('dragMenuPosition', { x: right, y: d.y });
        if (position.x === d.x && position.y === d.y) {
          e.preventDefault();
          document
            .getElementById('gamePageOverlayModal')
            ?.setAttribute('data-active-page-game-slug', slug);
          openModal('gamePageOverlayModal');
          closeModal('continueGameWithZeroWager');
          closeModal('activeFreespinsInGame');
        }
      }}
    >
      <StyledDraggableButton
        id='draggableButton'
        className={clsx(
          'flex gap-2 bg-[#1E293B] p-2',
          isOpen && 'opened animate-pulse',
          isOnRight && 'right',
        )}
        ref={ref}
      >
        <ThreeDots id='dragMenu' />
        {isOpened && <Typography type='sub2' text={t('quickMenu')} />}
        {isOpened && (
          <div className='moveIcon absolute z-[300] flex w-max flex-col gap-2'>
            <Icon name='arrowsRevers' />
            <Typography text={t('moveIt')} type='body2' />
          </div>
        )}
      </StyledDraggableButton>
    </Rnd>
  );
};

export default DraggableButton;
