'use client';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { IGame } from '@/app/api/getGames';
import { IProvider } from '@/app/api/getProvidersServer.ts';
import { useTranslation } from '@/app/i18n/client';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Icon, Typography } from '@/atomic-design-components';
import useIsGameFavourite from '@/hooks/useIsGameFavourite.ts';
import { theme } from '@/theme';
import { openModal } from '@/utils/openModal';

const GamePageOverlayMenuHorizontal = ({
  item,
  currRef,
}: {
  item: IGame;
  currRef: any;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const [{ favourites = [], providers = [] }, setSystemData] = useSystemData();
  const gameProvider = providers?.find(
    (provider: IProvider) => provider.slug === item?.provider_slug,
  );
  const { isGameFavourite, onToggleFavourite } = useIsGameFavourite(
    item,
    favourites,
    setSystemData,
  );

  return (
    <div
      id='gamePageOverlayMenu'
      className='relative mt-auto flex items-center rounded-t-2xl bg-[#1E293B] px-4'
      ref={currRef}
    >
      <div className='flex max-w-[50%] grow flex-col pb-2 pt-3'>
        <div className='mb-1 flex gap-2'>
          <Typography text={item.name} type='h3' withEllipsis />
          {isUserAuthorized && (
            <Icon
              name='heartShape'
              stroke={
                isGameFavourite
                  ? theme.color?.primary.main
                  : theme.color?.general.lightest
              }
              fill={isGameFavourite ? theme.color?.primary.main : 'none'}
              className='favoriteIcon'
              onClick={async (e: Event) => {
                await onToggleFavourite(e);
              }}
            />
          )}
        </div>{' '}
        {gameProvider?.id && (
          <Typography
            href={`/${lng}/${gameProvider.is_live ? 'live-casino' : 'casino'}/games/all?filters=provider_id=${gameProvider.id}`}
            text={gameProvider.name}
            type='label1'
            component={Link}
            className='!mb-2'
          />
        )}
      </div>
      <div className='max-w-[50%] grow'>
        <Button
          size='medium'
          text={`+ ${t('deposit')}`}
          fullWidth
          onClick={(e: any) => {
            e.preventDefault();
            openModal('cashierModal');
          }}
        />
      </div>
    </div>
  );
};

export default GamePageOverlayMenuHorizontal;
