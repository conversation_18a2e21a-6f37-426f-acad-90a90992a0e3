'use client';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Button } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal';
import { getGamePageType } from '@/utils/getGamePageType';
import { openModal } from '@/utils/openModal';
import { useParams, usePathname } from 'next/navigation';
import NotAuthorizedRightPart from '../Header/components/NotAuthorizedRightPart';
import NotificationsBonusesProfileLinks from '@/app/components/Header/components/NotificationsBonusesProfileLinks.tsx';
import Link from 'next/link';
import useWindowSize from '@/hooks/useWindowSize.ts';

const GamePageOverlayButtons = () => {
  const pathname = usePathname();
  const {
    lng,
    gamePageType,
  }: { lng: LanguagesType; gamePageType: GamePageType } = useParams();
  const { t } = useTranslation(lng);
  const { height } = useWindowSize();
  const isHorizontal = height && height <= 550;
  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const gamePageTypeChecked = getGamePageType(gamePageType);

  return (
    <div className='headerButtons flex justify-between p-2'>
      <div className='flex gap-4'>
        <Button
          as={Link}
          href={`/${lng}`}
          iconName='home'
          size='medium'
          variant='secondary'
          borderRadius='8px'
          padding='8px'
          iconRightProps={{ margin: 0 }}
          onClick={() => {
            closeModal('gamePageOverlayModal');
          }}
        />
        <Button
          iconName='search'
          size='medium'
          variant='secondary'
          borderRadius='8px'
          padding='12px'
          iconRightProps={{ margin: 0, width: 16, height: 16 }}
          onClick={(e: any) => {
            e.preventDefault();
            openModal('searchModal');
          }}
        />
      </div>
      <div className='flex gap-4'>
        {!isUserAuthorized && (
          <NotAuthorizedRightPart t={t} lng={lng} isGamePage={true} />
        )}
        {isUserAuthorized && (
          <NotificationsBonusesProfileLinks
            lng={lng}
            gamePageTypeChecked={gamePageTypeChecked}
            user={user}
            pathname={pathname}
          />
        )}
        {isHorizontal && (
          <Button
            iconName='cross'
            size='medium'
            variant='secondary'
            borderRadius='8px'
            padding='12px'
            iconRightProps={{ margin: 0 }}
            onClick={() => {
              closeModal('gamePageOverlayModal');
            }}
          />
        )}
      </div>
    </div>
  );
};

export default GamePageOverlayButtons;
