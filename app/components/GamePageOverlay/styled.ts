'use client';

import styled from 'styled-components';

export const StyledDraggableButton = styled.div<any>`
  border-radius: 0px 8px 8px 0px;
  border: 1px solid ${({ theme }) => theme.color?.general.dark};
  border-left: 0;
  top: 0;
  left: 0;
  pointer-events: all;
  touch-action: none;
  will-change: transform;
  #dragMenu {
    width: 24px;
    height: 24px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    justify-content: center;
    align-items: center;
    border: none;
    .dots_dot {
      width: 4px;
      height: 4px;
      margin: 0 !important;
    }
  }
  &.right {
    border-radius: 8px 0px 0px 8px;
    border-left: 1px solid ${({ theme }) => theme.color?.general.dark};
    border-right: 0;
  }
  &.opened {
    border-color: ${({ theme }) => theme.color?.primary.main};
  }
  .moveIcon {
    top: 58px;
    left: 42px;
  }
`;

export const StyledGamePageOverlayTabs = styled.div<any>`
  button {
    width: fit-content !important;
  }
  .gameCard__provider {
    display: none !important;
  }
  @media (max-height: 550px) {
    .gameCard__title, 
    .loadingTitle {
      display: none !important;
    }
  }
`;
