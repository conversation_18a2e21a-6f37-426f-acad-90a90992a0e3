'use client';
import { IGame } from '@/app/api/getGames';
import { useUser } from '@/app/wrappers/userProvider';
import useClickOutsideElement from '@/hooks/useClickOutsideElement';
import { GamePageType, LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal';
import { getGamePageType } from '@/utils/getGamePageType';
import { useParams } from 'next/navigation';
import { Suspense, useRef } from 'react';
import GameOverlayCarousel, {
  GAME_PAGE_GAMES_GROUPS,
} from './GameOverlayCarousel';
import GamePageOverlayButtons from './GamePageOverlayButtons';
import GamePageOverlayMenu from './GamePageOverlayMenu';
import GamePageOverlayTabs from './GamePageOverlayTabs';
import { GameGroup } from '@/app/[lng]/[gamePageType]/(landing-pages)/page.tsx';
import useWindowSize from '@/hooks/useWindowSize';
import GamePageOverlayMenuHorizontal from './GamePageOverlayMenuHorizontal';

const GamePageOverlay = ({ item }: { item: IGame }) => {
  const {
    lng,
    gamePageType,
  }: { lng: LanguagesType; gamePageType: GamePageType } = useParams();

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const { height } = useWindowSize();
  const isHorizontal = height && height <= 550;

  const gamePageTypeChecked = getGamePageType(gamePageType);

  const ref = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);
  useClickOutsideElement(ref, (target: any) => {
    const dialogs = document.querySelectorAll('dialog');
    const isInDialogs = Array.from(dialogs).some(
      (dialog) =>
        dialog.id !== 'gamePageOverlayModal' && dialog.contains(target),
    );
    if (ref?.current && !menuRef.current?.contains(target) && !isInDialogs)
      closeModal('gamePageOverlayModal');
  });

  const tabsTitles = Object.keys(
    GAME_PAGE_GAMES_GROUPS[gamePageTypeChecked],
  ).filter((group) => {
    const typedGroup = group as GameGroup;
    if (isUserAuthorized) return typedGroup;
    if (typedGroup === 'favourite' || typedGroup === 'recent') return false;
    return typedGroup;
  });

  const tabsContents = tabsTitles.map((group) => {
    return (
      <Suspense key={group}>
        <GameOverlayCarousel
          gamePageType={gamePageTypeChecked}
          group={group}
          lng={lng}
        />
      </Suspense>
    );
  });

  return (
    <div className='gamePageOverlay fixed top-0 z-[10] flex h-dvh w-full flex-col'>
      <div
        id='gamePageOverlayContent'
        className='gamePageOverlayContent bg-[#0F172A] px-2 pb-2'
        ref={ref}
      >
        <GamePageOverlayButtons />
        <GamePageOverlayTabs
          contents={tabsContents}
          lng={lng}
          gamePageType={gamePageType}
          isUserAuthorized={isUserAuthorized}
        />
      </div>
      {isHorizontal ? (
        <GamePageOverlayMenuHorizontal item={item} currRef={menuRef} />
      ) : (
        <GamePageOverlayMenu item={item} currRef={menuRef} />
      )}
    </div>
  );
};

export default GamePageOverlay;
