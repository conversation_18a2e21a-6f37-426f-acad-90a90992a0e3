'use client';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { parsePhoneNumber } from 'react-phone-number-input';
import ct from 'countries-and-timezones';

import {
  requestResetPassword,
  verifyCode,
  VerifyData,
} from '@/app/api/server-actions/forgotPassword';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { Button, Input, Typography } from '@/atomic-design-components';
import PhoneInput from '@/atomic-design-components/molecules/PhoneInput/PhoneInput';
import useClickOutside from '@/hooks/useClickOutside';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import { closeModal } from '@/utils/closeModal';
import { openModal } from '@/utils/openModal';
import useCountries from '@/hooks/useCountries.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import ButtonWithCountdownTimer from 'app/components/ButtonWithCountdownTimer';
import { requestConfirmPhone } from '@/app/api/server-actions/confirmUserData.ts';
import { FIRST_PHONE_CODE_TIMER_SEC, LAST_PHONE_CODE_TIMER_SEC } from '@/constants.ts';
import {
  getFromLocalStorage,
  removeFromLocalStorage,
  saveToLocalStorage,
} from '@/utils/localStorage.ts';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

const PhoneVerificationModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const ref = useRef<HTMLDialogElement>(null);
  useClickOutside(ref);
  const { isTouchDevice } = useIsTouchMobileView();
  const { countries } = useCountries();

  const { isDialogOpen } = useIsDialogOpen(ref);

  const { showError, showAlert } = useAlert();

  const { activeModalData, setActiveModalData } = useNavigation();

  const [code, setCode] = useState<string>('');
  const [country, setCountry] = useState<string>(
    ct.getCountryForTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)?.id || '',
  );
  const [dialCode, setDialCode] = useState<string>(
    countries.find((c: any) => c.code === country)?.dial_code || '',
  );
  const [phone, setPhone] = useState<string>('');
  const [screen, setScreen] = useState<'phone' | 'confirm' | 'reset_password'>('phone');
  const [inProgress, setInProgress] = useState(false);

  const timerExpiresKey =
    screen === 'reset_password' ? 'resetPasswordTimerExpires' : 'phoneConfirmTimerExpires';
  const codeSentCountKey =
    screen === 'reset_password' ? 'resetPasswordCodeSentCount' : 'phoneConfirmCodeSentCount';

  const resendCodeTimerExpiresFromLS = getFromLocalStorage(timerExpiresKey);

  const [resendTimerSeconds, setResendTimerSeconds] = useState(() =>
    resendCodeTimerExpiresFromLS
      ? Math.trunc((resendCodeTimerExpiresFromLS - Date.now()) / 1000)
      : FIRST_PHONE_CODE_TIMER_SEC,
  );

  const codeSentCountFromLS = getFromLocalStorage(codeSentCountKey);

  const [codeSentCount, setCodeSentCount] = useState<null | 0 | 1 | 2>(codeSentCountFromLS); // | 3

  const removeRequestConfirmError = () => {
    if (screen === 'confirm' && activeModalData?.isRequestConfirmError) {
      setActiveModalData(
        (prev: { isRequestConfirmError?: boolean; isVerified?: boolean; modalId: string }) =>
          prev
            ? {
                ...prev,
                isRequestConfirmError: false,
              }
            : null,
      );
    }
  };

  useEffect(() => {
    setResendTimerSeconds(
      resendCodeTimerExpiresFromLS
        ? Math.trunc((resendCodeTimerExpiresFromLS - Date.now()) / 1000)
        : FIRST_PHONE_CODE_TIMER_SEC,
    );
    setCodeSentCount(codeSentCountFromLS);
  }, [resendCodeTimerExpiresFromLS, codeSentCountFromLS]);

  useEffect(() => {
    if (isDialogOpen) {
      const screenAttr = ref?.current?.getAttribute('data-active-screen-verify');
      if (screenAttr) {
        setScreen(screenAttr as 'phone' | 'confirm' | 'reset_password');
      }

      const verifyData = ref?.current?.getAttribute('verify-data');
      if (verifyData) {
        const phoneParsed = parsePhoneNumber(verifyData || '');
        phoneParsed?.countryCallingCode ? setDialCode(`+${phoneParsed.countryCallingCode}`) : null;
        phoneParsed?.nationalNumber ? setPhone(phoneParsed.nationalNumber) : null;
        phoneParsed?.country ? setCountry(phoneParsed.country) : null;
      }
    } else {
      ref?.current?.setAttribute('data-active-screen-verify', 'phone');
      removeRequestConfirmError();
    }
  }, [isDialogOpen]);

  const getIsCodeVerified = async (code: string) => {
    setInProgress(true);
    const requestData = {
      code,
      code_type: (screen === 'reset_password'
        ? 'reset_password_by_phone'
        : 'confirm_phone') as VerifyData['code_type'],
      isPhone: true,
    };
    // console.log('verify phone code');
    const result = await verifyCode(requestData);
    // const result = { result: 'ok' };
    // console.log(result);

    if (!result || result.error) {
      showError(t('invalidCode'));
      setInProgress(false);
      return;
    }

    if (result.result === 'ok') {
      removeFromLocalStorage(timerExpiresKey);
      removeFromLocalStorage(codeSentCountKey);

      if (screen === 'reset_password') {
        closeModal('phoneVerificationModal', false);
        document
          .getElementById('resetPasswordModal')
          ?.setAttribute('data-active-screen-verify', 'password');
        document.getElementById('resetPasswordModal')?.setAttribute('verify-code', code);
        openModal('resetPasswordModal', false);
      }

      if (screen === 'confirm') {
        setActiveModalData({
          modalId: 'phoneVerificationModal',
          isVerified: true,
          isRequestConfirmError: false,
        });
        closeModal('phoneVerificationModal');
        showAlert({
          content: t('phoneConfirmed'),
          id: Date.now().toString(),
          type: 'success',
          timeout: 3000,
        });
      }

      setInProgress(false);
    }
  };

  const onResendCode = async () => {
    setInProgress(true);

    const resendCodeTimerExpiresFromLS = getFromLocalStorage(timerExpiresKey);

    // Date.now() + 1000 - to prevent 1s lag after timer stops
    if (resendCodeTimerExpiresFromLS && Date.now() + 1000 < resendCodeTimerExpiresFromLS) {
      setInProgress(false);
      return;
    }

    const requestFunction =
      screen === 'reset_password' ? requestResetPassword : requestConfirmPhone;
    const requestData = {
      phone: parsePhoneNumber(dialCode + phone)?.number || dialCode + phone,
      phone_type: 'call',
    } as { phone: string; phone_type?: 'call' }; // 'sms'

    // if (codeSentCount === 3 || activeModalData?.isRequestConfirmError) {
    if (codeSentCount === 0 || codeSentCount === 2 || activeModalData?.isRequestConfirmError) {
      delete requestData.phone_type;
    }

    const result = await requestFunction(requestData);
    // const result = { result: 'error' };

    // console.log('resend code', result);

    if (!result || result.error) {
      setInProgress(false);
      showError(t('failedToSendCode'));
      // if error is when requesting Telegram + voice call, don't continue the flow, try again
      if (!codeSentCount) {
        return;
      } else if (codeSentCount === 2) {
        // if error is when requesting Telegram + voice call again, change text, try again
        setCodeSentCount(0);
        saveToLocalStorage(codeSentCountKey, 0);
        return;
      } else {
        // if error is when requesting voice call only (unsupported geo), restart the flow without timer
        setCodeSentCount(0);
        saveToLocalStorage(codeSentCountKey, 0);
        return;
      }
    } else {
      showAlert({
        content: t('codeResent'),
        id: Date.now().toString(),
        type: 'success',
        timeout: 3000,
      });
    }

    const isResendAfterError = activeModalData?.isRequestConfirmError;

    // // after 2 SMS sent, start from the beginning with codeSentCount = null
    // const newCodeSentCount = isResendAfterError
    //   ? 0
    //   : codeSentCount === null
    //     ? 1
    //     : codeSentCount === 3
    //       ? null
    //       : codeSentCount + 1;

    const newCodeSentCount =
      isResendAfterError || codeSentCount === 2 ? 1 : (codeSentCount || 0) + 1;

    setCodeSentCount(newCodeSentCount as null | 0 | 1 | 2); // | 3
    saveToLocalStorage(codeSentCountKey, newCodeSentCount);

    const newTimerSeconds =
      !newCodeSentCount || newCodeSentCount === 1
        ? FIRST_PHONE_CODE_TIMER_SEC
        : LAST_PHONE_CODE_TIMER_SEC;
    //  newCodeSentCount === 1
    //  SECOND_PHONE_CODE_TIMER_SEC
    //  LAST_PHONE_CODE_TIMER_SEC;
    setResendTimerSeconds(newTimerSeconds);
    saveToLocalStorage(timerExpiresKey, Date.now() + newTimerSeconds * 1000);

    removeRequestConfirmError();
    setInProgress(false);
  };

  return (
    <ModalDialog id='phoneVerificationModal' ref={ref} className={screen}>
      {/*this case is used when we ask user to confirm phone number from the place where his number is not present (not from his profile)*/}
      {screen === 'phone' ? (
        <div className='verificationContainer h-full max-sm:px-2 max-sm:py-3 sm:p-6'>
          <div className='flex h-full flex-col'>
            <Typography type='h1' text={t('verifyYourPhone')} className='max-sm:!mb-5 sm:!mb-6' />
            <PhoneInput
              autoFocus={!isTouchDevice}
              name='phone'
              labelTop={t('phone')}
              key='phone'
              onChange={(data) => {
                setCountry(data.country);
                setDialCode(data.dialCode);
                setPhone(data.inputValue);
              }}
              fullWidth
              initialCountry={country}
              initialCode={dialCode}
              initialValue={phone}
              t={t}
            />
            <div className='buttonContainer flex justify-end gap-2 max-sm:mt-auto max-sm:flex-col sm:mt-6 sm:gap-4'>
              <Button
                className='max-sm:w-full sm:order-2'
                text={t('confirm')}
                onClick={(e: any) => {
                  e.preventDefault();
                  setScreen('confirm');
                }}
              />
              <Button
                variant='secondary'
                text={t('cancel')}
                className='max-sm:w-full sm:order-1'
                onClick={(e: any) => {
                  e.preventDefault();
                  setScreen('phone');
                  closeModal('phoneVerificationModal');
                }}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className='verificationContainer h-full max-sm:px-2 max-sm:pb-3 max-sm:pt-3 sm:p-6'>
          <div className='flex h-full flex-col'>
            <Typography
              type='h1'
              text={t('enterCode')}
              className='cursor-pointer max-sm:!mb-5 sm:!mb-6'
              iconName='chevronDown'
              iconProps={{
                className: 'rotate-90 py-3 px-2',
                width: 15,
                height: 9,
                margin: 0,
              }}
              onClick={(e: any) => {
                e.preventDefault();
                closeModal('phoneVerificationModal');
                setTimeout(() => {
                  openModal('resetPasswordModal');
                  document
                    .getElementById('resetPasswordModal')
                    ?.setAttribute('data-active-screen-verify', 'phone');
                }, 0);
              }}
            />
            <Input
              name='code'
              key='code'
              inputMode='numeric'
              pattern='\d*'
              fullWidth
              // labelTop={t(
              //   codeSentCount === 0 ||
              //     codeSentCount === null
              //     activeModalData?.isRequestConfirmError
              //     ? 'enterCodeSent'
              //     : codeSentCount === 1
              //       ? 'smsSent1'
              //       : 'smsSent2',
              //   { phone: dialCode + phone },
              // )}
              labelTop={t(
                codeSentCount === 0 || codeSentCount === 1 || activeModalData?.isRequestConfirmError
                  ? 'enterCodeSent'
                  : 'voiceCallWasMade',
                { phone: dialCode + phone },
              )}
              labelBottom={t('4digitCode')}
              onChange={(e: any) => {
                const digitsOnly = e.target.value.replace(/\D/g, '').slice(0, 4);
                setCode(digitsOnly);
              }}
              value={code}
              className='whitespace-pre-line'
            />
            <div className='buttonContainer flex justify-end gap-2 max-sm:mt-auto max-sm:flex-col sm:mt-6 sm:gap-4'>
              <Button
                className='max-sm:w-full sm:order-2'
                text={t('confirm')}
                onClick={(e: any) => {
                  e.preventDefault();
                  getIsCodeVerified(code);
                }}
                disabled={!code || code.length !== 4 || inProgress}
              />
              <ButtonWithCountdownTimer
                // buttonTranslationKey={
                //   activeModalData?.isRequestConfirmError || codeSentCount === 2
                //     ? 'sendCodeAgain'
                //     : codeSentCount === 0 || codeSentCount === null
                //       ? 'sendSms'
                //       : 'resendSms'
                // }
                buttonTranslationKey='getCode'
                buttonProps={{
                  disabled: inProgress,
                  onClick: (e: any) => {
                    e.preventDefault();
                    onResendCode();
                  },
                  className: 'max-sm:w-full sm:order-1',
                  variant: 'secondary',
                }}
                secondsInitial={activeModalData?.isRequestConfirmError ? 0 : resendTimerSeconds}
                t={t}
              />
            </div>
          </div>
        </div>
      )}
    </ModalDialog>
  );
};

PhoneVerificationModal.displayName = 'PhoneVerificationModal';
export default PhoneVerificationModal;
