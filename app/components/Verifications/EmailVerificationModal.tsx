'use client';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { Button, Icon, Input, Typography } from '@/atomic-design-components';
import useClickOutside from '@/hooks/useClickOutside';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import { closeModal } from '@/utils/closeModal';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';

const EmailVerificationModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const ref = useRef<HTMLDialogElement>(null);
  useClickOutside(ref);

  const [email, setEmail] = useState('');
  const [screen, setScreen] = useState<'email' | 'confirm'>('email');
  // const [inProgress, setInProgress] = useState(false);
  const { isDialogOpen } = useIsDialogOpen(ref);
  const { isTouchDevice } = useIsTouchMobileView();

  useEffect(() => {
    if (isDialogOpen) {
      const screenAttr = ref?.current?.getAttribute(
        'data-active-screen-verify',
      );
      if (screenAttr) setScreen(screenAttr as 'email' | 'confirm');
    } else {
      ref?.current?.setAttribute('data-active-screen-verify', 'email');
    }
  }, [isDialogOpen]);

  return (
    <ModalDialog id='emailVerificationModal' ref={ref} className={screen}>
      {screen === 'email' ? (
        <div className='verificationContainer h-full max-sm:px-2 max-sm:py-3 sm:p-6'>
          <div className='flex h-full flex-col'>
            <Typography
              type='h1'
              text={t('verifyYourEmail')}
              className='max-sm:!mb-5 sm:!mb-6'
            />
            <Input
              name='email'
              key='email'
              fullWidth
              labelTop={t('email')}
              // labelBottom={parse(t('confirmEmailAndGet'))}
              value={email}
              onChange={(e: any) => {
                const { value } = e.target;
                setEmail(value);
              }}
              // disabled={inProgress}
            />
            <div className='buttonContainer flex justify-end gap-2 max-sm:mt-auto max-sm:flex-col sm:mt-6 sm:gap-4'>
              <Button
                className='max-sm:w-full sm:order-2'
                text={t('confirm')}
                onClick={(e: any) => {
                  e.preventDefault();
                  setScreen('confirm');
                }}
                // disabled={inProgress}
              />
              <Button
                variant='secondary'
                text={t('cancel')}
                className='max-sm:w-full sm:order-1'
                onClick={(e: any) => {
                  e.preventDefault();
                  closeModal('emailVerificationModal');
                }}
                // disabled={inProgress}
              />
            </div>
          </div>
        </div>
      ) : (
        <div className='verificationContainer h-full max-sm:px-2 max-sm:py-3 sm:p-6'>
          <div className='flex h-full w-full flex-col items-center justify-center'>
            <div
              className={clsx(
                'mb-4 flex max-w-[360px] flex-col items-center max-sm:h-full max-sm:justify-center',
                isTouchDevice && 'h-full justify-center',
              )}
            >
              <Icon name='emailIncoming' />
              <Typography
                type='sub2'
                text={t('checkEmail')}
                className='!mb-1 !mt-2'
              />
              <Typography text={t('linkSent')} textAlign='center' />
            </div>
            <Button
              text={t('ok')}
              className={clsx(
                'mt-auto w-[120px] max-sm:w-full',
                isTouchDevice && 'w-full',
              )}
              onClick={(e: any) => {
                e.preventDefault();
                closeModal('emailVerificationModal');
              }}
            />
          </div>
        </div>
      )}
    </ModalDialog>
  );
};

EmailVerificationModal.displayName = 'EmailVerificationModal';
export default EmailVerificationModal;
