import { useEffect, useState, useRef } from 'react';
import { useTranslation } from '@/app/i18n/client';
import { useParams } from 'next/navigation';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import ModalDialog from '../ModalDialog/ModalDialog';
import { closeModal } from '@/utils/closeModal';
import useClickOutside from '@/hooks/useClickOutside';
import { Typography } from '@/atomic-design-components';
import ReCAPTCHA from 'react-google-recaptcha';
import { theme } from '@/theme';

export const ReCaptcha = ({
  showReCaptcha,
  onClose,
  onTokenChange,
  className,
  useModal = true,
}: {
  showReCaptcha: boolean;
  // eslint-disable-next-line no-unused-vars
  onTokenChange: (token: string | null) => void;
  onClose?: () => void;
  className?: string;
  useModal?: boolean;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const [isTouchDeviceState, setIsTouchDeviceState] = useState(false);
  const ref = useRef(null);
  useClickOutside(ref);

  const { isTouchDevice } = useIsTouchMobileView();

  useEffect(() => {
    const updateDeviceType = () => {
      const isMobile = window.innerWidth <= theme.breakpoints?.sm;
      setIsTouchDeviceState(isTouchDevice || isMobile);
    };

    updateDeviceType();
    window.addEventListener('resize', updateDeviceType);
    return () => {
      window.removeEventListener('resize', updateDeviceType);
    };
  }, []);

  return (
    <>
      {isTouchDeviceState && useModal ? (
        <>
          <div
            className={`reCaptchaShadowScreen fixed bottom-0 left-0 right-0 top-0 z-[110] bg-general-darkest bg-opacity-90 ${showReCaptcha ? 'block' : 'hidden'}`}
          />
          <ModalDialog
            id='reCaptchaModal'
            ref={ref}
            onClose={() => {
              onClose?.();
              closeModal('reCaptchaModal');
            }}
            contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
            className='pb-6 pt-12'
          >
            <div
              className={`flex flex-col items-center justify-center gap-y-2 transition-opacity duration-1000 ${showReCaptcha ? 'opacity-100' : 'opacity-0'} ${className}`}
            >
              <ReCAPTCHA
                sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_V2_PUBLIC_KEY || ''}
                onChange={onTokenChange}
                theme='dark'
              />
              <Typography type='body1' text={t('recaptchaRequired')} />
            </div>
          </ModalDialog>
        </>
      ) : (
        <div
          className={`flex flex-col items-center justify-center gap-y-2 transition-opacity duration-1000 ${showReCaptcha ? 'opacity-100 pointer-events-auto cursor-default' : 'opacity-0'} ${className}`}
        >
          <ReCAPTCHA
            sitekey={process.env.NEXT_PUBLIC_RECAPTCHA_V2_PUBLIC_KEY || ''}
            onChange={onTokenChange}
            theme='dark'
            className={`${showReCaptcha ? 'block' : 'hidden'}`}
          />
          <Typography type='body1' text={t('recaptchaRequired')} />
        </div>
      )}
    </>
  );
};

// possible privacy policy messages to use:
{
  /* <Typography type='label2' className='flex-wrap gap-x-1'>
        <Trans
          i18nKey='recaptchaNotice'
          components={{
            privacyPolicy: (
              <Link
                isOuterLink
                fontSize='12px'
                href='https://policies.google.com/privacy'
              />
            ),
            termsOfService: (
              <Link
                isOuterLink
                fontSize='12px'
                href='https://policies.google.com/terms'
              />
            ),
          }}
        />
      </Typography> */
}
