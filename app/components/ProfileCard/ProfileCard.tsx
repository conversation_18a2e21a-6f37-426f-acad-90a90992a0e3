'use client';
import { Icon, Typography } from '@/atomic-design-components';
import { useTranslation } from '@/app/i18n/client';
import ProgressBar from '@/app/components/ProgressBar';
import { ITranslations, LanguagesType } from '@/types/global';
import { useParams } from 'next/navigation';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';

const ProfileCard = ({
  currentProgress,
  levelNumber,
  levelTitle,
  total,
  type = 'header',
}: {
  currentProgress: number;
  levelNumber: number;
  levelTitle?: ITranslations;
  total: number;
  type: 'header' | 'profile';
}) => {
  const { lng }: { lng: LanguagesType } = useParams();
  const { t } = useTranslation(lng);

  const getProgressBar = () => (
    <ProgressBar
      currentProgress={currentProgress}
      total={total}
      // text={t('yourProgress')}
    >
      {/*{isObjectEmpty(levelTitle) ? null : (*/}
      {levelTitle && (
        <Typography
          type='body2'
          fontSize='10px'
          lineHeight='10px'
          component='div'
          margin='10px 0 -2px 0'
          textTransform='uppercase'
          justifyContent='space-between'
        >
          {t('player')}
          <div>{getAvailableTranslation(levelTitle, lng)}</div>
        </Typography>
      )}
      {/*)}*/}
    </ProgressBar>
  );

  return (
    <>
      {type === 'header' && (
        <>
          <div className='flex rounded-[4px] bg-[#2D4672] pl-[5px]'>
            <Icon name='levelBadge' level={levelNumber.toString()} />
            <div className='block pb-[22px] pl-[11px] pt-[19px]'>
              <Typography
                fontSize='10px'
                type='label2'
                text={t('welcomeBack')}
                margin='0 0 9px 0'
              />
            </div>
          </div>
          {getProgressBar()}
        </>
      )}

      {type === 'profile' && (
        <div className='flex items-center pb-[15px] pt-[12px]'>
          <Icon name='levelBadge' level={levelNumber?.toString()} />
          <div className='flex flex-1 flex-col justify-center pl-[19px] pr-[19px]'>
            {getProgressBar()}
          </div>
          <Icon name='levelBadge' level={(levelNumber + 1).toString()} />
          <Typography type='label2' fontSize='12px' margin='0 0 0 7px'>
            {t('level')} {levelNumber + 1}
          </Typography>
        </div>
      )}
    </>
  );
};

export default ProfileCard;
