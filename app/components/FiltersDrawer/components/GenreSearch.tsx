'use client';
import { useTranslation } from '@/app/i18n/client';
import { LANGUAGES } from '@/app/i18n/settings';
import { Checkbox, Typography, List } from '@/atomic-design-components';
import Search from '@/atomic-design-components/molecules/Search/Search';
import { GENRES } from '@/constants';
import { ITranslations, LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { isObjectEmpty } from '@/utils/object';
import { sortByActiveId } from '@/utils/sortItems';
import { useEffect, useState } from 'react';
import { StyledGenreCard } from '../styled';
import { usePrevious } from '@/hooks/useReact';

interface IGenre {
  id: string;
  label: ITranslations;
}
const SEARCH_QUERY_MIN_LENGTH = 2;

const GenreSearch = ({
  lng,
  facets,
  setFilterSelected,
  filterSelected,
  changeUrl,
  isOpen,
}: {
  lng: LanguagesType;
  facets: any;
  setFilterSelected: Function;
  filterSelected: string | null;
  changeUrl: Function;
  isOpen: boolean;
}) => {
  const { t } = useTranslation(lng);
  const prevDrawerState: any = usePrevious(isOpen);

  const [inputValue, setInputValue] = useState('');
  const [genresList, setGenresList] = useState<IGenre[]>([]);
  const [initialGenresList, setInitialGenresList] = useState<IGenre[]>([]);

  const filterSplit = filterSelected?.split('=');
  const isGenreSelected = filterSplit?.[0] === 'genres';
  const selectedGenreId = isGenreSelected && filterSplit?.[1];

  const getInitialGenresList = () => {
    if (isObjectEmpty(facets)) {
      setGenresList(GENRES);
      setInitialGenresList(GENRES);
    } else {
      const facetsGenres = facets?.genres //   : //   ? allFacets // !isBonusFilterActive && !isObjectEmpty(allFacets)
        ?.map((el: any) => el.id);
      const genresFiltered =
        GENRES.filter((el: any) => facetsGenres.includes(el.id)) || [];
      setGenresList(genresFiltered);
      setInitialGenresList(genresFiltered);
    }
  };

  useEffect(() => {
    getInitialGenresList();
    setInputValue('');
  }, [facets]);

  function filterGenresForSearch(labelValue: any, languages: any) {
    return initialGenresList?.filter((genre: any) =>
      languages.some((language: any) =>
        genre.label[language]?.toLowerCase().includes(labelValue.toLowerCase()),
      ),
    );
  }

  const getSearchedGenres = async () => {
    const searchedGenres = filterGenresForSearch(inputValue, LANGUAGES);
    setGenresList(searchedGenres);
  };

  const onGenreChange = (newGenreId: string) => {
    const newId = newGenreId === selectedGenreId ? '' : newGenreId;

    const newFilterSelected = newId ? `genres=${newId}` : null;
    setFilterSelected(newFilterSelected);
    changeUrl(newFilterSelected);
  };

  useEffect(() => {
    if (!isOpen) {
      setInputValue('');
    }

    if ((!isOpen && prevDrawerState) || (isOpen && !prevDrawerState)) {
      //set active id card to top only when drawer is reopened
      const sortedGenres = selectedGenreId
        ? sortByActiveId(initialGenresList, selectedGenreId)
        : initialGenresList;
      setGenresList(sortedGenres);
    }
  }, [prevDrawerState, isOpen]);

  useEffect(() => {
    if (!inputValue) {
      setGenresList(initialGenresList);
      return;
    }
    if (inputValue.length < SEARCH_QUERY_MIN_LENGTH) return;
    getSearchedGenres();
  }, [inputValue]);

  if (!genresList.length && !inputValue.length) return null;

  return (
    <div className='flex flex-col px-2 pb-4 md:px-4'>
      <Typography text={t('genre')} type='h3' />
      <Search
        lng={lng}
        withCross={true}
        inputValue={inputValue}
        setInputValue={setInputValue}
        width='100%'
        colorType='primary'
        isAbsolutePositioned={false}
        className='mb-2 mt-1'
      />

      <List className='bg-[#1E293B] px-2 py-1'>
        {genresList.map((genre: IGenre) => (
          <StyledGenreCard key={genre.id}>
            <Checkbox
              type='radio'
              checked={selectedGenreId === genre.id}
              handleChange={(checked: boolean) =>
                onGenreChange(checked ? genre.id : '')
              }
              label={getAvailableTranslation(genre.label, lng)}
              labelType='body2'
              name={genre.id}
              value={genre.id}
            />
          </StyledGenreCard>
        ))}
      </List>
    </div>
  );
};
export default GenreSearch;
