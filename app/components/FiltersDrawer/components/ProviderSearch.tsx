'use client';
import { IProvider } from '@/app/api/getProvidersServer.ts';
import { useTranslation } from '@/app/i18n/client';
import { useSystemData } from '@/app/wrappers/systemDataProvider';
import { Button, TilesGrid, Typography } from '@/atomic-design-components';
import Search from '@/atomic-design-components/molecules/Search/Search';
import { GamePageType, LanguagesType } from '@/types/global';
import { isObjectEmpty } from '@/utils/object';
import { sortByActiveId, sortItems } from '@/utils/sortItems';
import { useEffect, useState } from 'react';
import ProviderCard from '../../Providers/ProviderCard';
import { StyledProvidersList } from '../styled';
import { usePrevious } from '@/hooks/useReact';

const SEARCH_QUERY_MIN_LENGTH = 2;

const ProviderSearch = ({
  lng,
  facets,
  setFilterSelected,
  changeUrl,
  filterSelected,
  gamePageType,
  isOpen,
}: {
  lng: LanguagesType;
  facets: any;
  changeUrl: Function;
  setFilterSelected: Function;
  gamePageType?: GamePageType;
  filterSelected: string | null;
  isOpen: boolean;
}) => {
  const { t } = useTranslation(lng);
  const prevDrawerState: any = usePrevious(isOpen);

  const [{ providers = [], constants }] = useSystemData();

  const providersOrderResult = constants?.find(
    (constant) => constant.key === 'providers_order',
  );
  const providersOrderArr = providersOrderResult
    ? JSON.parse(providersOrderResult.value)
    : [];

  const [inputValue, setInputValue] = useState('');

  const [providersList, setProvidersList] = useState<IProvider[]>();
  const [initialProvidersList, setInitialProvidersList] =
    useState<IProvider[]>();
  const [provToShowCount, setProvToShowCount] = useState(6);
  // const [isShowLessClicked, setIsShowLessClicked] = useState(false);

  const filterSplit = filterSelected?.split('=');
  const isProviderSelected = filterSplit?.[0] === 'provider_id';
  const selectedProviderId = isProviderSelected && filterSplit?.[1];

  const getInitialProvidersList = () => {
    // console.log('getInitialProvidersList', selectedProviderId);
    const currProviders =
      providers?.filter(
        (provider) =>
          (gamePageType === 'live-casino' && provider.is_live) ||
          !provider.is_live,
      ) || [];

    if (
      isObjectEmpty(facets)
      // (!isBonusFilterActive && isObjectEmpty(allFacets))
    ) {
      const sortedProviders = sortItems({
        items: currProviders,
        order: providersOrderArr,
      });

      setProvidersList(sortedProviders);
      setInitialProvidersList(sortedProviders);
    } else {
      const providerFacets = facets?.provider_id //   : //   ? allFacets // !isBonusFilterActive && !isObjectEmpty(allFacets)
        ?.map((el: any) => el.id);
      const currProv =
        currProviders?.filter((el: any) => providerFacets.includes(el.id)) ||
        [];

      const sortedProviders = sortItems({
        items: currProv,
        order: providersOrderArr,
      });

      setInitialProvidersList(sortedProviders);

      const sortedByActiveId = sortByActiveId(
        sortedProviders,
        selectedProviderId,
      );

      setProvidersList(sortedByActiveId);
    }
  };

  const getSearchedProviders = async () => {
    const searchedProviders = initialProvidersList?.filter((prov: any) =>
      prov.name.toLowerCase().includes(inputValue.toLowerCase()),
    );
    const sortedProviders = sortItems({
      items: searchedProviders || [],
      order: providersOrderArr,
    });
    setProvidersList(sortedProviders);
  };

  const onProviderChoose = (e: any) => {
    const id = +e.target.id;
    const newActiveId = +selectedProviderId === id ? 0 : id;
    const newFilterSelected = newActiveId ? `provider_id=${newActiveId}` : null;

    setFilterSelected(newFilterSelected);
    changeUrl(newFilterSelected);
  };

  useEffect(() => {
    // console.log('get prov facets initial');
    getInitialProvidersList();
    setInputValue('');
  }, [facets, providers]);

  useEffect(() => {
    if (!isOpen) {
      setInputValue('');
    }

    if ((!isOpen && prevDrawerState) || (isOpen && !prevDrawerState)) {
      // //set active id card to top only when 'show less' is clicked and drawer is reopened
      const prov =
        // isDrawerChangeState || isShowLessClicked
        selectedProviderId
          ? sortByActiveId(initialProvidersList, selectedProviderId)
          : initialProvidersList;

      setProvidersList(prov);
    }
  }, [prevDrawerState, isOpen]);

  useEffect(() => {
    if (!inputValue) {
      setProvidersList(initialProvidersList);
      return;
    }
    if (inputValue.length < SEARCH_QUERY_MIN_LENGTH) return;
    getSearchedProviders();
  }, [inputValue]);

  // useEffect(() => {
  //   if (providersList && provToShowCount >= providersList?.length)
  //     setIsShowLessClicked(false);
  // }, [provToShowCount]);

  if (!providersList?.length && !inputValue) return null;

  return (
    <div className='mt-4 flex flex-col px-2 md:px-4'>
      <Typography text={t('providers')} type='h3' />
      <Search
        lng={lng}
        withCross={true}
        inputValue={inputValue}
        setInputValue={setInputValue}
        width='100%'
        colorType='primary'
        disabled={!providersList?.length && !inputValue}
        isAbsolutePositioned={false}
        className='mb-2 mt-1'
      />
      <StyledProvidersList flexDirection='column' gap='12px'>
        <TilesGrid
          itemsInRow={2}
          rowGap='10px'
          breakpoints={{
            '(min-width: 1430px)': {
              perView: 2,
              spacing: 8,
            },
            '(max-width: 1430px)': {
              perView: 2,
              spacing: 8,
            },
          }}
        >
          {providersList
            ?.slice(0, provToShowCount)
            .map((provider: IProvider, idx: number) => (
              <ProviderCard
                item={provider}
                index={idx}
                key={idx}
                isLink={false}
                onClick={onProviderChoose}
                className={+selectedProviderId === +provider.id ? 'active' : ''}
              />
            ))}
        </TilesGrid>
        {!!providersList?.length && providersList.length > 6 && (
          <Button
            text={
              provToShowCount < providersList.length
                ? t('showMore')
                : t('showLess')
            }
            fullWidth
            onClick={() => {
              if (provToShowCount < providersList.length) {
                setProvToShowCount(provToShowCount + 6);
              } else {
                setProvToShowCount(6);
                // setIsShowLessClicked(true);
              }
            }}
            variant='transparent'
            withBorder
          />
        )}
      </StyledProvidersList>
    </div>
  );
};
export default ProviderSearch;
