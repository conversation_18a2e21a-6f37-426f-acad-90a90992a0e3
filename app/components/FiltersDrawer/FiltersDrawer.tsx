'use client';
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from 'next/navigation';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

import { IFacet, getGamesFacets } from '@/app/api/getGamesFacets';
import { useTranslation } from '@/app/i18n/client';
import { useDrawerById } from '@/app/wrappers/drawerProvider';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button } from '@/atomic-design-components';
import useClickOutsideElement from '@/hooks/useClickOutsideElement';
import { usePrevious } from '@/hooks/useReact';
import { GamePageType, LanguagesType } from '@/types/global';
import GenreSearch from './components/GenreSearch';
import ProviderSearch from './components/ProviderSearch';
import { StyledDrawer } from './styled';
import { useCookies } from 'react-cookie';

const FiltersDrawer = () => {
  const [cookies] = useCookies(['userCurrency']);

  const { isBonusFilterActive } = useUser();
  const { isOpen, close } = useDrawerById('filtersPanel');

  const pathname = usePathname();
  const router = useRouter();
  const {
    lng,
    gamePageType,
  }: { lng: LanguagesType; gamePageType: GamePageType } = useParams();
  const searchParams = useSearchParams();
  const tournamentParams = searchParams.get('tournament');

  const filterFromUrl = searchParams.get('filters');

  const prevPageType: string = usePrevious(gamePageType) || '';
  const prevPathname: any = usePrevious(pathname) || '';
  const prevIsBonusActive: any = usePrevious(isBonusFilterActive);

  const { t } = useTranslation(lng);

  const ref = useRef<HTMLElement>(null);
  useClickOutsideElement(ref, close);

  const [facets, setFacets] = useState<IFacet>({});
  const [filterSelected, setFilterSelected] = useState<string | null>(
    filterFromUrl,
  );

  const isGamePage = pathname.includes('/game/');

  const changeUrl = (newFilterSelected: string | null) => {
    let url = pathname;
    if (newFilterSelected) {
      url = `${url}?filters=${newFilterSelected}`;
    }
    if (tournamentParams) {
      if (url.includes('?')) {
        url = `${url}&tournament=${tournamentParams}`;
      } else {
        url = `${url}?tournament=${tournamentParams}`;
      }
    }

    router.replace(url);
  };

  const getFacetsInitial = async () => {
    const bonusFilter =
      gamePageType === 'casino' && isBonusFilterActive
        ? { wager_percentage__gte: [1] }
        : undefined;

    const facetsResult = await getGamesFacets(cookies?.userCurrency, {
      is_live: [(gamePageType === 'live-casino').toString()],
      ...bonusFilter,
    });

    setFacets(facetsResult);
    return facetsResult;
  };

  useEffect(() => {
    if (filterFromUrl !== filterSelected) {
      setFilterSelected(filterFromUrl);
    }
  }, [filterFromUrl]);

  useEffect(() => {
    if (
      (prevPageType === 'live-casino' &&
        (gamePageType === 'casino' || gamePageType === undefined)) ||
      ((prevPageType === 'casino' || !prevPageType) &&
        gamePageType === 'live-casino')
    ) {
      getFacetsInitial();
      setFilterSelected(null);
    }
  }, [gamePageType, prevPageType]);

  useEffect(() => {
    getFacetsInitial();
  }, []);

  useEffect(() => {
    if (
      (isBonusFilterActive && !prevIsBonusActive) ||
      (!isBonusFilterActive && prevIsBonusActive)
    ) {
      getFacetsInitial();
    }
  }, [prevIsBonusActive, isBonusFilterActive]);

  useEffect(() => {
    if (
      prevPathname &&
      filterFromUrl &&
      pathname !== prevPathname &&
      !pathname.includes('/games/all')
    ) {
      changeUrl(null);
      close();
    }
  }, [filterFromUrl, pathname, prevPathname]);

  if (isGamePage) return null;

  return (
    <StyledDrawer
      headerHeight='0px'
      maxWidth='430px'
      openedValue={isOpen}
      title={t('filters')}
      withCloseIcon={true}
      ref={ref}
      id='filtersPanel'
      opened={isOpen}
      close={close}
      className='!h-[100dvh] !overflow-y-hidden !pb-0'
    >
      <div
        className={clsx(
          'relative mb-12 flex h-[calc(100dvh-48px)] flex-col gap-4 overflow-y-auto md:h-[calc(100dvh-57px)]',
          filterSelected && 'pb-[110px] md:pb-[66px]',
        )}
      >
        <ProviderSearch
          lng={lng}
          facets={facets}
          setFilterSelected={setFilterSelected}
          filterSelected={filterSelected}
          gamePageType={gamePageType}
          changeUrl={changeUrl}
          isOpen={isOpen}
        />
        {gamePageType !== 'live-casino' && (
          <GenreSearch
            lng={lng}
            facets={facets}
            setFilterSelected={setFilterSelected}
            filterSelected={filterSelected}
            changeUrl={changeUrl}
            isOpen={isOpen}
          />
        )}
        {filterSelected && (
          <div className='fixed bottom-0 flex w-full gap-2 border-t border-[#334155] bg-[#0F172A] px-2 pb-3 pt-2 max-md:flex-col md:p-3'>
            <Button
              text={t('apply')}
              fullWidth
              onClick={close}
              className='md:order-1'
            />
            <Button
              text={t('clear')}
              fullWidth
              onClick={() => {
                changeUrl(null);
                setFilterSelected(null);
              }}
              variant='secondary'
              className='md:order-none'
            />
          </div>
        )}
      </div>
    </StyledDrawer>
  );
};

export default FiltersDrawer;
