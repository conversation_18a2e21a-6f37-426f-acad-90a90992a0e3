'use client';
import { Drawer, FlexRow } from '@/atomic-design-components';
import styled from 'styled-components';

export const StyledGenreCard: any = styled.div`
  padding: 8px 0;
  .checkbox {
    flex-grow: 1;
  }

  &:not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
  }
`;

export const StyledProvidersList: any = styled(FlexRow)`
  .providerCard {
    width: 100%;
    &.active {
      background-color: ${({ theme }) => theme.color?.primary.main};
    }
    &.disabled {
      img {
        filter: grayscale(100%);
      }
      cursor: not-allowed;
    }
  }
`;

export const StyledDrawer: any = styled(Drawer)`
  border-left: 1px solid ${({ theme }) => theme.color?.general.dark};
`;
