'use client';
import React, { ReactNode } from 'react';
import { useParams } from 'next/navigation';
import clsx from 'clsx';
import ReactTexty from 'react-texty';
import { IBonus } from '@/app/api/getBonuses.ts';
import { Icon, Typography, Image } from '@/atomic-design-components';
import { useTranslation } from '@/app/i18n/client';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const LevelBonus = ({
  bonus,
  isActiveLevel,
  currencySymbol,
  className,
  children,
}: {
  bonus?: IBonus;
  className?: string;
  children?: ReactNode;
  isActiveLevel: boolean;
  currencySymbol: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const betPerSpin =
    bonus?.bonus_type === 'freespins_no_deposit' && bonus.freespins_amount
      ? Math.round((bonus.bonus_amount / bonus.freespins_amount) * 100) / 100
      : 0;

  return (
    <div className={clsx(className, 'flex items-center')}>
      {isActiveLevel && (
        <svg
          width='187'
          height='156'
          viewBox='0 0 187 156'
          fill='none'
          className='absolute left-0 top-0 z-10 rounded-[8px]'
        >
          <g filter='url(#filter0_f_5203_118778)'>
            <circle cx='68' cy='37' r='69' fill='#0083FF' fillOpacity='0.3' />
          </g>
          <defs>
            <filter
              id='filter0_f_5203_118778'
              x='-51'
              y='-82'
              width='238'
              height='238'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='BackgroundImageFix'
                result='shape'
              />
              <feGaussianBlur
                stdDeviation='25'
                result='effect1_foregroundBlur_5203_118778'
              />
            </filter>
          </defs>
        </svg>
      )}

      {bonus &&
        (bonus.photo_round_url ? (
          <Image
            src={bonus.photo_round_url}
            alt={bonus.name}
            width={48}
            height={48}
            className='h-48px z-20 mr-3 w-[48px] rounded-[50%]'
          />
        ) : (
          <Icon
            name='levelBonusDefault'
            className='z-20 mr-3'
            borderRadius='50%'
            wrapperColor='rgba(15, 23, 42, 0.4)'
            wrapperHeight='48px'
            wrapperWidth='48px'
            width={34}
            height={34}
          />
        ))}

      <div className='bonusTexts z-20'>
        {bonus?.bonus_type === 'wager_no_deposit' && bonus?.bonus_amount && (
          <Typography type='sub2' as={ReactTexty}>
            <span className='text-secondary-main'>
              {currencySymbol}
              {addSpacesToLongNumbers(bonus.bonus_amount)}
            </span>
          </Typography>
        )}
        {bonus?.bonus_type === 'wager_no_deposit' &&
          bonus?.wager_multiplier && (
            <Typography
              type='body1'
              className='text-sm text-general-light'
              as={ReactTexty}
            >
              {t('wager')} x&nbsp;{bonus.wager_multiplier}
            </Typography>
          )}

        {bonus?.bonus_type === 'freespins_no_deposit' &&
          bonus?.freespins_amount && (
            <Typography type='sub2' as={ReactTexty}>
              <span className='text-secondary-main'>
                {bonus.freespins_amount} FS
              </span>
              &nbsp;x {currencySymbol}
              {addSpacesToLongNumbers(betPerSpin)}
            </Typography>
          )}
        {bonus?.bonus_type === 'freespins_no_deposit' && bonus.game_name && (
          // <Link
          //   href={`/${lng}/casino/${bonus.game_provider_slug}/game/${bonus.game_slug}`}
          // >
          <Typography
            type='body1'
            className='max-w-[160px] text-sm text-general-light'
            as={ReactTexty}
          >
            {bonus.game_name}
          </Typography>
          // </Link>
        )}
      </div>
      {children}
    </div>
  );
};

export default LevelBonus;
