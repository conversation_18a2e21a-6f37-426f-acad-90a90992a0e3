'use client';
import styled from 'styled-components';
import { Typography } from '@/atomic-design-components';

export const StyledTypography = styled(Typography)`
  p {
    margin-bottom: 3px;
  }
`;

export const StyledLevelCard = styled.div`
  width: max-content;

  .chevronCard {
    width: 250px; //270px
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    background-color: #2b3a52;

    &.active {
      background: #284171;
      border: 1px solid #f8fafc1a;
      border-bottom: none;
    }

    .activeRow {
      background-color: ${({ theme }) => theme.color?.status.success};
    }
  }

  .bgBottom {
    margin-bottom: 7px;
    width: 250px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='218' height='312' viewBox='0 0 218 312' fill='none'%3E%3Cpath d='M0 7.99999C0 3.58171 3.58172 0 8 0H210C214.418 0 218 3.58172 218 8V258.782C218 261.953 216.127 264.825 213.224 266.103L112.224 310.58C110.17 311.485 107.83 311.485 105.776 310.58L4.77584 266.103C1.8734 264.825 0 261.953 0 258.782V7.99999Z' fill='%232B3A52'/%3E%3C/svg%3E");
    background-size: cover;
    height: 61px;
    margin-top: -1px;
    background-position-y: bottom;

    &.active {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='218' height='312' viewBox='0 0 218 312' fill='none'%3E%3Cpath d='M0 7.99999C0 3.58171 3.58172 0 8 0H210C214.418 0 218 3.58172 218 8V258.782C218 261.953 216.127 264.825 213.224 266.103L112.224 310.58C110.17 311.485 107.83 311.485 105.776 310.58L4.77584 266.103C1.8734 264.825 0 261.953 0 258.782V7.99999Z' fill='%23284171'/%3E%3Cpath d='M0.5 7.99999C0.5 3.85786 3.85786 0.5 8 0.5H210C214.142 0.5 217.5 3.85786 217.5 8V258.782C217.5 261.755 215.744 264.447 213.023 265.646L112.023 310.123C110.097 310.971 107.903 310.971 105.977 310.123L4.97735 265.646C2.25631 264.447 0.5 261.755 0.5 258.782V7.99999Z' stroke='%23F8FAFC' stroke-opacity='0.1'/%3E%3C/svg%3E");
    }
  }

  & .past {
    opacity: 0.5;
    margin-top: 0;
  }
`;

export const StyledArrows = styled.div`
  .arrow {
    width: 40px;
    height: 40px;
    background-color: #1e293b;
    border-radius: 8px;
    position: absolute;
    top: 50%;
    fill: #fff;
    cursor: pointer;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
    border: 1px solid ${({ theme }) => theme.color?.general.dark};

    &.arrow--disabled {
      fill: #8b8d8fd9;
      background-color: #1e293b;
    }
  }
`;

export const StyledBar = styled.div`
  &.statuses-user-progress {
    flex-direction: row;
    bottom: 0;
    display: flex;
    left: 110px; //120
    height: 8px;
    position: absolute;
    margin-bottom: 45px;
    background-color: ${({ theme }) => theme.color?.general.dark};
  }

  .statuses-user-progress__level-progress {
    background-color: ${({ theme }) => theme.color?.general.dark};
    height: 8px;
    position: relative;
    width: 226px; // 246
    @media only screen and (min-width: ${({ theme }) =>
        theme.breakpoints?.md}px) {
      width: 242px; // 262
    }

    .statuses-user-progress__percentage {
      display: flex;
      height: 100%;
      left: -1px;
      position: absolute;
      border-bottom-right-radius: 80px;
      border-top-right-radius: 80px;
    }
  }

  .statuses-user-progress__dot {
    align-items: center;
    background-color: ${({ theme }) => theme.color?.general.dark};
    border-radius: 50%;
    display: flex;
    height: 32px;
    justify-content: center;
    position: relative;
    width: 32px;
    z-index: 3;
    transform: translate(0, -13px);

    &.statuses-user-progress__dot-check {
      background-color: ${({ theme }) => theme.color?.status.success};
    }
  }

  .statuses-user-progress__level-progress:first-child {
    width: 0;

    .statuses-user-progress__percentage {
      width: 0 !important;
    }
  }

  .statuses-user-progress__level-progress_active {
    .statuses-user-progress__percentage {
      background-color: ${({ theme }) => theme.color?.status.success};
    }
  }
`;
