'use client';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { StyledArrows } from './styled';

const SliderArrowsSimple = ({
  elementRef,
  activeRef,
  elementWidth,
  scrollElementsCount,
  isLastElementFullyVisible,
}: {
  elementRef: any;
  activeRef?: any;
  elementWidth: number;
  scrollElementsCount?: number;
  isLastElementFullyVisible: boolean;
}) => {
  const [sliderPosition, setSliderPosition] = useState(0);

  useEffect(() => {
    if (activeRef?.current) {
      scrollToElement(activeRef.current);
    }
  }, [activeRef]);

  const scrollToElement = (element: any) => {
    if (element && elementRef?.current) {
      elementRef.current?.scrollTo({
        left: element.offsetLeft,
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (elementRef?.current) {
      elementRef.current?.scrollBy({
        left: elementWidth * (scrollElementsCount || 2),
        behavior: 'smooth',
      });
    }
  };

  const scrollLeft = () => {
    if (elementRef?.current) {
      elementRef.current?.scrollBy({
        left: -elementWidth * (scrollElementsCount || 2),
        behavior: 'smooth',
      });
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      setSliderPosition(elementRef?.current?.scrollLeft);
    };
    elementRef?.current?.addEventListener('scroll', handleScroll);
    return () => {
      elementRef?.current?.removeEventListener('scroll', handleScroll);
    };
  }, [elementRef]);

  if (!elementRef) return null;

  return (
    <StyledArrows className='ml-auto flex gap-2 max-md:hidden'>
      <div
        role='button'
        tabIndex={0}
        className={clsx(
          'arrow left !relative',
          sliderPosition === 0 && 'arrow--disabled',
        )}
        onClick={scrollLeft}
        onKeyDown={undefined}
      >
        <svg
          viewBox='0 0 13 7'
          style={{ transform: 'rotate(270deg)' }}
          width={16}
          height={16}
        >
          <path d='M6.47198 0.000653754C6.25005 0.00572058 6.03793 0.0909997 5.87668 0.239983L0.273754 5.49157C0.102768 5.65315 0.00437488 5.87434 0.000141889 6.10664C-0.00409111 6.33894 0.0861809 6.56339 0.251172 6.73082C0.416162 6.89824 0.642411 6.99498 0.880325 6.99981C1.11824 7.00464 1.34841 6.91717 1.52038 6.75658L6.5 2.08624L11.4796 6.75658C11.6516 6.91717 11.8818 7.00464 12.1197 6.99981C12.3576 6.99498 12.5838 6.89824 12.7488 6.73082C12.9138 6.56339 13.0041 6.33894 12.9999 6.10664C12.9956 5.87434 12.8972 5.65316 12.7262 5.49157L7.12331 0.239983C7.03657 0.15972 6.93426 0.0971926 6.8224 0.0560922C6.71055 0.0149918 6.59141 -0.0038524 6.47198 0.000653754Z' />
        </svg>
      </div>
      <div
        role='button'
        tabIndex={0}
        className={clsx(
          'arrow right !relative',
          isLastElementFullyVisible && 'arrow--disabled',
        )}
        onClick={scrollRight}
        onKeyDown={undefined}
      >
        <svg
          viewBox='0 0 13 7'
          style={{ transform: 'rotate(90deg)' }}
          width={16}
          height={16}
        >
          <path d='M6.47198 0.000653754C6.25005 0.00572058 6.03793 0.0909997 5.87668 0.239983L0.273754 5.49157C0.102768 5.65315 0.00437488 5.87434 0.000141889 6.10664C-0.00409111 6.33894 0.0861809 6.56339 0.251172 6.73082C0.416162 6.89824 0.642411 6.99498 0.880325 6.99981C1.11824 7.00464 1.34841 6.91717 1.52038 6.75658L6.5 2.08624L11.4796 6.75658C11.6516 6.91717 11.8818 7.00464 12.1197 6.99981C12.3576 6.99498 12.5838 6.89824 12.7488 6.73082C12.9138 6.56339 13.0041 6.33894 12.9999 6.10664C12.9956 5.87434 12.8972 5.65316 12.7262 5.49157L7.12331 0.239983C7.03657 0.15972 6.93426 0.0971926 6.8224 0.0560922C6.71055 0.0149918 6.59141 -0.0038524 6.47198 0.000653754Z' />
        </svg>
      </div>
    </StyledArrows>
  );
};

export default SliderArrowsSimple;
