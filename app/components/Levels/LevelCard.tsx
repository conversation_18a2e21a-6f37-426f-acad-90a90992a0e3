'use client';
import { IBonus } from '@/app/api/getBonuses.ts';
import { ILevel } from '@/app/api/getLevels';
import LevelBonus from '@/app/components/Levels/LevelBonus.tsx';
import { useTranslation } from '@/app/i18n/client';
import { Icon, Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { changeZerosToText } from '@/utils/changeNumberZerosToText.ts';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import clsx from 'clsx';
import { StyledLevelCard } from './styled';

const LevelCard = ({
  item,
  lng,
  userLevel,
  activeRef,
  bonuses,
  currencySymbol,
  id,
}: {
  item: ILevel;
  lng: LanguagesType;
  userLevel: number;
  activeRef: any;
  bonuses: IBonus[];
  currencySymbol: string;
  id?: string;
}) => {
  const { t } = useTranslation(lng);

  const levelUpBonus = bonuses.find(
    (bonus: IBonus) => bonus.external_id === item.bonus_level_up_external_id,
  );

  const birthdayBonus = bonuses.find(
    (bonus: IBonus) => bonus.external_id === item.bonus_birthday_external_id,
  );

  const prevBirthdayBonus = bonuses.find((bonus: IBonus) => {
    return bonus.external_id === item.prev_birthday_bonus_id;
  });

  const getIcon = (
    value?: number,
    prevValue?: number,
    shouldValueDescend?: boolean,
  ) => {
    if (!value) {
      return 'cross';
    }
    if (
      value === prevValue ||
      (value && !prevValue && item.name === 'level0')
    ) {
      return 'check';
    }
    if (shouldValueDescend) {
      if (value < (prevValue || 0)) {
        return 'arrow';
      }
    }
    if (!shouldValueDescend && value > (prevValue || 0)) {
      return 'arrow';
    }
    return 'check';
  };

  const ICONS: any = {
    cashback: getIcon(item.cashback_percentage, item.prev_cashback_percentage),
    cashbackTimer: getIcon(
      item.cashback_interval_days,
      item.prev_cashback_interval_days,
      true,
    ),
    birthdayPresent: getIcon(
      birthdayBonus?.bonus_amount,
      prevBirthdayBonus?.bonus_amount,
    ),
    withdrawalLimit: getIcon(
      item.max_daily_withdrawal,
      item.prev_max_daily_withdrawal,
    ),
  };

  const getIconProps = (name: string) =>
    name === 'cross'
      ? {
          margin: '4px 0 4px 4px',
          width: 12,
          height: 12,
          wrapperWidth: 16,
          wrapperHeight: 16,
        }
      : {
          margin: '4px 0 4px 4px',
          wrapperWidth: 16,
          wrapperHeight: 16,
        };
  const isActiveLevel =
    (item.number || +item.name.replace('level', '')) === userLevel;
  const isPastLevel =
    (item.number || +item.name.replace('level', '')) < userLevel;

  return (
    <StyledLevelCard
      id={id}
      className='relative flex flex-col'
      ref={isActiveLevel ? activeRef : null}
    >
      <div
        className={clsx(
          isPastLevel && 'past',
          isActiveLevel && 'active',
          'chevronCard z-20',
        )}
      >
        <Typography
          type='h3'
          text={getAvailableTranslation(item.title, lng) || item.name}
          className='relative z-20 px-4 pb-1 pt-4'
        />
        <LevelBonus
          isActiveLevel={isActiveLevel}
          bonus={levelUpBonus}
          currencySymbol={currencySymbol}
          className='h-[64px] w-full border-b border-t border-[#f8fafc1a] px-4 py-2'
        />
        <div className='mt-2 flex flex-col items-start'>
          <div
            className={clsx(
              ICONS.cashback === 'arrow' &&
                !isPastLevel &&
                !isActiveLevel &&
                'activeRow',
              'mx-4 my-1 flex gap-1 rounded pr-2',
            )}
          >
            <Icon
              name={ICONS.cashback}
              toTop
              {...getIconProps(ICONS.cashback)}
            />
            <Typography>
              {t('cashback')} - {item.cashback_percentage}%
            </Typography>
          </div>
          <div
            className={clsx(
              (ICONS.cashbackTimer === 'arrow' ||
                (ICONS.cashbackTimer === 'check' &&
                  !item.prev_cashback_interval_days)) &&
                !isPastLevel &&
                !isActiveLevel &&
                'activeRow',
              'mx-4 my-1 flex gap-1 whitespace-nowrap rounded pr-2',
            )}
          >
            <Icon
              name={ICONS.cashbackTimer}
              toTop
              style={
                ICONS.cashbackTimer === 'arrow'
                  ? { transform: 'rotate(90deg)' }
                  : undefined
              }
              {...getIconProps(ICONS.cashbackTimer)}
            />
            <Typography>
              {t('cashbackTimer')} - {item.cashback_interval_days}{' '}
              {t('day', { count: +item.cashback_interval_days })}
            </Typography>
          </div>
          <div
            className={clsx(
              ICONS.birthdayPresent === 'arrow' &&
                !isPastLevel &&
                !isActiveLevel &&
                'activeRow',
              'mx-4 my-1 flex gap-1 rounded pr-2',
            )}
          >
            <Icon
              name={ICONS.birthdayPresent}
              toTop
              {...getIconProps(ICONS.birthdayPresent)}
            />
            <Typography>
              {t('birthdayGift')} - {currencySymbol}
              {changeZerosToText(birthdayBonus?.bonus_amount || 0, t)}
            </Typography>
          </div>
          <div
            className={clsx(
              ICONS.withdrawalLimit === 'arrow' &&
                !isPastLevel &&
                !isActiveLevel &&
                'activeRow',
              'mx-4 my-1 flex gap-1 rounded pr-2',
            )}
          >
            <Icon
              name={ICONS.withdrawalLimit}
              toTop
              {...getIconProps(ICONS.withdrawalLimit)}
            />
            <Typography>
              {t('withdrawalLimit')} - {currencySymbol}
              {changeZerosToText(item.max_daily_withdrawal, t) || 0}
            </Typography>
          </div>
        </div>
      </div>
      <div
        className={clsx(
          isActiveLevel && 'active',
          isPastLevel && 'past',
          'bgBottom',
        )}
      />
    </StyledLevelCard>
  );
};

export default LevelCard;
