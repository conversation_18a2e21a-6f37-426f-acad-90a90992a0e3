'use client';
import { Fragment } from 'react';
import clsx from 'clsx';
import { ILevel } from '@/app/api/getLevels';
import { Icon, Typography } from '@/atomic-design-components';
import { StyledBar } from './styled';
import { changeZerosToText } from '@/utils/changeNumberZerosToText.ts';

const ProgressBarSimple = ({
  levels,
  currProgress,
  currLevel,
  isUserAuthorized,
  pointValue,
  t,
}: {
  levels: ILevel[];
  currProgress: number;
  currLevel: number;
  isUserAuthorized: boolean;
  pointValue: number;
  t: any;
}) => {
  return (
    <StyledBar className='statuses-user-progress'>
      {levels.map((level: ILevel) => {
        const levelNumberFromName = +level.name.replace('level', '');
        const nextLevelLikes = level.next_level_points / pointValue;
        const isFull = currLevel >= (level.number || levelNumberFromName);

        const progress = isFull
          ? 100
          : currLevel + 1 === (level.number || levelNumberFromName)
            ? (currProgress / nextLevelLikes) * 100
            : 0;
        const isChecked = isUserAuthorized && isFull;
        const isCurrent = currLevel === (level.number || levelNumberFromName);

        const likesTotal = changeZerosToText(Math.round(nextLevelLikes * 100) / 100, t);
        const likesLeft = changeZerosToText(
          Math.round((nextLevelLikes - currProgress) * 100) / 100,
          t,
        );

        const likesCount =
          currLevel === (level.number || levelNumberFromName) - 1 ? likesLeft : likesTotal;

        const text =
          !isFull &&
          levelNumberFromName !== 0 &&
          t(+likesCount === 1 ? 'likeLeft' : 'likesLeft', {
            likes: likesCount,
          });

        return (
          <Fragment key={level.name}>
            <div
              className={clsx(
                'statuses-user-progress__level-progress',
                progress > 0 && 'statuses-user-progress__level-progress_active',
              )}
            >
              {!!progress && (
                <div
                  className='statuses-user-progress__percentage'
                  style={{
                    width: `calc(${progress}% + 4px)`,
                  }}
                ></div>
              )}
            </div>
            <div
              className={clsx(
                isChecked && 'statuses-user-progress__dot-check',
                'statuses-user-progress__dot',
              )}
            >
              <div className='absolute top-2 flex w-max flex-col'>
                <Icon name={isCurrent ? 'star2' : isChecked ? 'check' : 'lock'} />
                <Typography text={text} className='!mt-4' />
              </div>
            </div>
          </Fragment>
        );
      })}
    </StyledBar>
  );
};

export default ProgressBarSimple;
