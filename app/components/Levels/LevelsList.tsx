'use client';
import { IBonus } from '@/app/api/getBonuses.ts';
import { getLevels, ILevel } from '@/app/api/getLevels';
import LevelBonusCard from '@/app/components/Levels/LevelBonusCard.tsx';
import Slider from '@/app/components/Slider/Slider.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Typography } from '@/atomic-design-components';
import { DEFAULT_POINT_VALUE } from '@/constants.ts';
import { usePrevious } from '@/hooks/useReact';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { changeZerosToText } from '@/utils/changeNumberZerosToText.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { useEffect, useRef, useState } from 'react';
import LevelsFaqList from './LevelsFaqList';
import LevelCard from './LevelCard';
import ProgressBarSimple from './ProgressBarSimple';
import SliderArrowsSimple from './SliderArrowsSimple';
import { transformLevelPointsToLikes } from '@/utils/transformLevelPointsToLikes.ts';
import { useIsLastSliderElementFullyVisible } from '@/hooks/useCheckIsLastSliderElementFullyVisible.ts';

const MAX_BONUSES: any = {
  level: {
    title: 'levelUpBonus',
    description: 'levelUpBonusText',
    iconName: 'levelUp',
    bonus: '',
  },
  cashback: {
    title: 'cashback',
    description: 'cashbackBonusText',
    iconName: 'percentIcon',
    bonus: '',
  },
  birthday: {
    title: 'birthdayGift',
    description: 'birthdayGiftText',
    iconName: 'giftBonus',
    iconProps: {
      width: 16,
      height: 16,
      wrapperWidth: 24,
      wrapperHeight: 24,
      fill: theme.color?.general.lightest,
    },
    bonus: '',
  },
  vip: {
    title: 'vipSupport',
    description: 'vipSupportText',
    iconName: 'crown',
    bonus: '',
  },
};

const BONUSES_SLIDER_BREAKPOINTS = {
  '(min-width: 1200px)': {
    slides: {
      perView: 4,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 1200px)': {
    slides: {
      perView: 3.3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(min-width: 960px) and (max-width: 1050px)': {
    slides: {
      perView: 2.9,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 920px)': {
    slides: {
      perView: 3.3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 720px)': {
    slides: {
      perView: 2.9,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 620px)': {
    slides: {
      perView: 2.3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 520px)': {
    slides: {
      perView: 1.9,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 420px)': {
    slides: {
      perView: 1.5,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
};

const LevelsList = ({
  lng,
  items,
  currency,
  bonusesForLevels,
}: {
  lng: LanguagesType;
  items: ILevel[];
  currency: string;
  bonusesForLevels: IBonus[];
}) => {
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const prevUser = usePrevious(user);
  const level = user?.level_number || 0;

  const currencySymbol = getCurrencySymbol(currency);

  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;
  const isSmallScreen = !!width && width < theme.breakpoints?.sm;

  const [data, setData] = useState<ILevel[]>(items);
  const ref = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  const isLastElementFullyVisible =
    useIsLastSliderElementFullyVisible('lastLevel');

  // TODO: refactor
  useEffect(() => {
    if (!prevUser && user) {
      getLevels(0, 100, {
        currency: [user.currency],
      }).then((res) => {
        if (!res?.items) {
          return;
        }
        const itemsFiltered = res.items
          .filter((level) => level.next_level_points || level.name === 'level0')
          .sort((a: ILevel, b: ILevel) => {
            const levelA = a.number || +a.name.replace('level', '');
            const levelB = b.number || +b.name.replace('level', '');
            if (levelA > levelB) {
              return 1;
            }
            if (levelA < levelB) {
              return -1;
            }
            return 0;
          });
        setData(
          itemsFiltered.map((item, index) => {
            const newItem =
              index > 0
                ? {
                    ...item,
                    prev_cashback_interval_days:
                      itemsFiltered[index - 1].cashback_interval_days,
                    prev_cashback_percentage:
                      itemsFiltered[index - 1].cashback_percentage,
                    prev_max_daily_withdrawal:
                      itemsFiltered[index - 1].max_daily_withdrawal,
                    curr_level_points:
                      itemsFiltered[index - 1].next_level_points,
                    prev_birthday_bonus_id:
                      itemsFiltered[index - 1].bonus_birthday_external_id || 0,
                  }
                : {
                    ...item,
                  };
            return newItem;
          }),
        );
      });
    }
  }, [user, prevUser]);

  const likeValue = items?.[0]?.point_value || DEFAULT_POINT_VALUE;

  const maxBonuses: any = Object.keys(MAX_BONUSES).map((key: string) => {
    const lastLevel = items[items.length - 1];
    const levelUpBonus = bonusesForLevels.find(
      (bonus) => bonus.external_id === lastLevel?.bonus_level_up_external_id,
    );
    const birthdayBonus = bonusesForLevels.find(
      (bonus) => bonus.external_id === lastLevel?.bonus_birthday_external_id,
    );

    return {
      ...MAX_BONUSES[key],
      bonus:
        key === 'level'
          ? `${changeZerosToText(levelUpBonus?.bonus_amount || 0, t)} ${currencySymbol}`
          : key === 'cashback'
            ? `${lastLevel?.cashback_percentage || 0}%`
            : key === 'birthday'
              ? `${changeZerosToText(birthdayBonus?.bonus_amount || 0, t)} ${currencySymbol}`
              : '',
    };
  });

  return (
    <>
      <Slider
        className='bonuses'
        items={maxBonuses}
        total={4}
        Slide={LevelBonusCard}
        perView={4}
        withDots={!width || isSmallScreen}
        breakpoints={BONUSES_SLIDER_BREAKPOINTS}
        withArrows={false}
      />
      <div className='rounded-[6px] bg-[#1E293B] px-2 py-3 sm:max-md:mt-2 md:p-6 md:pb-[14px]'>
        <div
          className='gameCarousel levelsCarousel flex flex-col'
          ref={containerRef}
        >
          <div className='mb-4 flex gap-4'>
            <Typography type='h2' lineHeight='40px' text={t('progressLevel')} />
            <Typography
              type='label1'
              text={`1 Like = ${currencySymbol}${likeValue}`}
            />
            <SliderArrowsSimple
              elementRef={ref}
              elementWidth={isMobile ? 226 : 230}
              activeRef={cardRef}
              isLastElementFullyVisible={isLastElementFullyVisible}
            />
          </div>
          <div
            className='relative flex flex-col overflow-y-hidden overflow-x-scroll'
            ref={ref}
          >
            <div className='mb-[68px] flex gap-6 max-md:gap-2'>
              {data.map((item, i) => {
                return (
                  <LevelCard
                    id={i === data.length - 1 ? 'lastLevel' : ''}
                    currencySymbol={currencySymbol}
                    key={item.name}
                    item={item}
                    lng={lng}
                    userLevel={level}
                    bonuses={bonusesForLevels}
                    activeRef={
                      (item.number || +item.name.replace('level', '')) === level
                        ? cardRef
                        : null
                    }
                  />
                );
              })}
            </div>
            <ProgressBarSimple
              levels={data}
              currProgress={
                user?.level_current_points
                  ? transformLevelPointsToLikes(
                      user.level_current_points,
                      likeValue,
                    )
                  : 0
              }
              currLevel={level}
              pointValue={likeValue}
              isUserAuthorized={!!user?.id}
              t={t}
            />
          </div>
        </div>
      </div>
      <LevelsFaqList lng={lng} t={t} isMobile={isMobile} />
    </>
  );
};

export default LevelsList;
