'use client';
import parse from 'html-react-parser';
import { LanguagesType } from '@/types/global';
import FaqItem from '@/app/components/HelpCenter/FaqItem.tsx';
import { Typography } from '@/atomic-design-components';
import { LEVELS_FAQ_LIST } from '@/app/data/faqs-levels.ts';
import { theme } from '@/theme.ts';
import { StyledTypography } from '@/app/components/Levels/styled';

const LevelsFaqList = ({
  lng,
  isMobile,
  t,
}: {
  lng: LanguagesType;
  isMobile: boolean;
  t: Function;
}) => {
  // Convert our custom FAQ items to the format expected by FaqItem component
  const convertedFaqItems = LEVELS_FAQ_LIST.map((item, i) => ({
    id: i,
    name: item.id,
    translations: item.translations,
    body: item.body,
    // Add other required properties from IPost interface
    slug: item.id,
    state: 'published',
    topic_id: 0,
    created_at: '',
    post_type: 'faq' as const,
    updated_at: '',
    icon: null,
    photos: null,
  }));

  return (
    <>
      <div className='mt-3 md:mt-1'>
        <StyledTypography
          text={parse(t('levelsTextBlock'))}
          type='body1'
          className='postContent'
          color={theme.color?.general.lighter}
          displayCssProp='block'
        />
      </div>
      <div className='mt-[6px] md:mt-[-4px]'>
        <Typography type='h3' text='FAQ' className='mb-2 md:mb-4' />
        <div className='flex flex-wrap items-start gap-2'>
          {convertedFaqItems.map((faq) => (
            <FaqItem key={faq.id} faq={faq} lng={lng} isMobile={isMobile} />
          ))}
        </div>
      </div>
    </>
  );
};

export default LevelsFaqList;
