'use client';
import { useTranslation } from '@/app/i18n/client';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { useParams } from 'next/navigation';

const LevelBonusCard = ({
  item,
}: {
  item: {
    title: string;
    description: string;
    iconName: string;
    iconProps?: any;
    bonus: string;
  };
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  return (
    <div className='flex h-full flex-col items-start gap-1 rounded-lg bg-[#1E293B] p-4'>
      <Icon name={item.iconName} {...item.iconProps} />
      <Typography type='sub2' text={t(item.title)} />
      <Typography
        color={theme.color?.general.lighter}
        text={t(item.description, {
          bonus: item.bonus,
        })}
      />
    </div>
  );
};

export default LevelBonusCard;
