'use client';
import clsx from 'clsx';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { useDrawerById } from '@/app/wrappers/drawerProvider';
import { useUser } from '@/app/wrappers/userProvider';
import {
  Badge,
  Icon,
  Typography,
  HeaderMenuIcon,
} from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { openModal } from '@/utils/openModal';
import { StyledItem, StyledMobileMenu } from './styled';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { useEffect } from 'react';

const MobileMenuBottom = () => {
  const { isTouchDevice } = useIsTouchMobileView();
  const pathname = usePathname();
  const { lng }: { lng: LanguagesType } = useParams();
  const isGamePage = pathname.includes('/game/');
  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const { t } = useTranslation(lng);
  const router = useRouter();
  const { setActiveAuthTab } = useNavigation();
  const { isOpen, toggle } = useDrawerById('menuPanel');

  useEffect(() => {
    const body = window.document.body;
    const html = document.documentElement;
    if (isOpen) {
      body?.classList.add('blocked');
      html?.classList.add('blocked');
    } else {
      body?.classList.remove('blocked');
      html?.classList.remove('blocked');
    }
  }, [isOpen]);

  if (isGamePage) return null;

  return (
    <>
      <div className='h-[calc(56px+env(safe-area-inset-bottom))] w-full'></div>
      <StyledMobileMenu className={clsx(isTouchDevice && 'isTouchDevice')}>
        <StyledItem onClick={toggle}>
          <HeaderMenuIcon
            fill={theme.color?.general.lightest}
            opened={isOpen}
            width={18}
          />
          <Typography text={t('menu')} fontSize='10px' lineHeight='12px' />
        </StyledItem>
        <StyledItem
          onClick={(e: any) => {
            e.preventDefault();
            openModal('searchModal');
          }}
        >
          <Icon
            name='search'
            width={18}
            height={18}
            wrapperWidth={24}
            wrapperHeight={24}
          />
          <Typography text={t('search')} fontSize='10px' lineHeight='12px' />
        </StyledItem>
        <StyledItem
          onClick={(e: any) => {
            e.preventDefault();
            if (isUserAuthorized) {
              openModal('cashierModal');
            } else {
              setActiveAuthTab('signUp');
              openModal('authModal');
            }
          }}
        >
          <Icon
            name='plusInCircle'
            width={32}
            height={32}
            fill={theme.color?.secondary.main}
            className='depositIcon'
          />
          <div className='depositIconWrapper'>
            <svg
              width='50'
              height='50'
              xmlns='http://www.w3.org/2000/svg'
              style={{ overflow: 'visible' }}
            >
              <defs>
                <clipPath id='clipPath'>
                  <rect x='0' y='-1' width='50' height='13' />
                </clipPath>
              </defs>
              <circle
                cx='24'
                cy='24'
                r='24'
                stroke={theme.color?.general.dark}
                strokeWidth='1'
                fill='none'
                clipPath='url(#clipPath)'
              />
            </svg>
          </div>
          <Typography
            text={t('deposit')}
            fontSize='10px'
            lineHeight='12px'
            className='pt-[28px]'
          />
        </StyledItem>
        <StyledItem onClick={() => router.push(`/${lng}/casino/games/all`)}>
          <Icon
            name='cherry'
            fill={theme.color?.general.lightest}
            width={18}
            height={18}
            wrapperWidth={24}
            wrapperHeight={24}
          />
          <Typography text={t('slots')} fontSize='10px' lineHeight='12px' />
        </StyledItem>
        {isUserAuthorized ? (
          <StyledItem onClick={() => router.push(`/${lng}/casino/favourites`)}>
            <Icon
              name='heartShape'
              fill={theme.color?.general.lightest}
              width={18}
              height={18}
              wrapperWidth={24}
              wrapperHeight={24}
            />
            <Typography
              text={t('favourite')}
              fontSize='10px'
              lineHeight='12px'
            />
          </StyledItem>
        ) : (
          <StyledItem
            onClick={(e: any) => {
              e.preventDefault();
              setActiveAuthTab('signUp');
              openModal('authModal');
            }}
          >
            <Badge
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              badgeContent={3}
              backgroundColor={theme.color?.secondary.dark}
              borderColor={theme.color?.general.darker}
              className='notificationBadge inline-flex'
              color='white'
              margin='0'
            >
              <Icon
                name='giftBonus'
                width={18}
                height={18}
                wrapperWidth={24}
                wrapperHeight={24}
                fill={theme.color?.general.lightest}
              />
            </Badge>
            <Typography text={t('bonuses')} fontSize='10px' lineHeight='12px' />
          </StyledItem>
        )}
      </StyledMobileMenu>
    </>
  );
};

export default MobileMenuBottom;
