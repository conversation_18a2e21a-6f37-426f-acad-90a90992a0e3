'use client';
import { HEADER_HEIGHT } from '@/constants.ts';
import styled from 'styled-components';

export const StyledMobileMenu: any = styled.nav`
  height: calc(${HEADER_HEIGHT} + env(safe-area-inset-bottom));
  padding-bottom: env(safe-area-inset-bottom);
  width: 100%;
  position: fixed;
  z-index: 100;
  bottom: 0;
  border-top: 1px solid ${({ theme }) => theme.color?.general.dark};
  background-color: ${({ theme }) => theme.color?.general.darker};
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    &:not(.isTouchDevice) {
      display: none;
    }
  }
`;

export const StyledItem: any = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 20%;
  .depositIcon {
    padding: 8px;
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 110;
  }
  .depositIconWrapper {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background-color: ${({ theme }) => theme.color?.general.darker};
    // border: 1px solid ${({ theme }) => theme.color?.general.dark};
    border-radius: 50%;
    width: 48px;
    height: 48px;
    z-index: 100;
  }
  .anchorBottomRight {
    bottom: 8px !important;
  }
`;
