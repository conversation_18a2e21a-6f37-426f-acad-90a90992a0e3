'use client';
import { Button, getTokens, Typography } from '@/atomic-design-components';
import ProgressBar from 'react-customizable-progressbar';
import styled from 'styled-components';
import { HEADER_HEIGHT } from '@/constants.ts';

export const HEADER_LINK_LINE_HEIGHT = 18;

export const StyledHeader = styled.header<any>`
  height: ${HEADER_HEIGHT};
  padding: 8px 0;

  #afterNavLinks {
    gap: 16px;
  }

  &.notLoggedIn {
    padding: 7px 8px;

    #afterNavLinks {
      gap: 8px;
    }
  }

  &:not(.notLoggedIn) {
    padding: 8px 10px 8px 8px;
  }

  &.isGamePage {
    display: none;
  }

  &.isGamePage:not(.isDemo) {
    #afterNavLinks {
      margin-left: auto;
    }
  }

  &.scrolled {
    border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
  }

  .logo {
    width: 53px;
    height: 24px;
  }

  .mobileBalance {
    display: block;
  }

  button.login {
    background: none !important;
    border: none;

    &:hover {
      opacity: 0.8;
    }
  }

  .bonusBadge,
  .notificationBadge {
    .anchorTopRight {
      width: 15px;
      height: 15px;
    }
  }

  @media only screen and (min-width: ${({ theme }) =>
      theme.isTouchDevice ? theme.breakpoints?.xxxl : theme.breakpoints?.md}px) {
    .mobileBalance {
      display: none;
    }

    &.notLoggedIn {
      padding: 7px 8px;
    }

    &.isGamePage {
      display: block;
    }
  }

  @media only screen and (max-width: 374px) {
    #afterNavLinks {
      gap: 8px;
    }
  }
`;

export const StyledNavLinks = styled.nav`
  display: none;
  position: relative;
  align-items: center;
  justify-content: start;
  width: 100%;

  .menu.isLoading {
    position: absolute;
    right: 0;
    left: 0;
  }

  @media only screen and (min-width: ${({ theme }) =>
      theme.isTouchDevice ? theme.breakpoints?.xxxl : theme.breakpoints?.md}px) {
    display: flex;
  }

  .menu {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    opacity: 0;
  }

  .menuLoading {
    flex-wrap: nowrap;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .menuItem {
    display: flex;
    align-items: center;
  }

  #more {
    display: none;
    cursor: pointer;
    margin-left: 10px;
    align-items: center;
    padding: 0 16px;
  }

  #moreItemsDropdown {
    position: absolute;
    display: none;
    top: 100%;
    z-index: 20;
    background: ${({ theme }) => theme.color?.general.darker};
    right: 0;
    border-radius: ${({ theme }) => theme.size?.border.radius.big};
    border: 1px solid ${({ theme }) => theme.color?.general.dark};

    a {
      white-space: nowrap;
      width: 100%;
    }
  }

  #moreItems {
    position: relative;

    &:hover > #moreItemsDropdown {
      display: block;
    }
  }

  @media (max-width: 1200px) {
    .menu {
      opacity: 0;
    }
  }
`;

export const StyledTreeDots = styled.div`
  height: 40px; // must be the same as StyledNavLink height
  color: ${({ theme }) => theme.color?.general.lightest};
  background-color: ${({ theme }) => theme.color?.general.darker};
  border-radius: 48px;
  border: 1px solid ${({ theme }) => theme.color?.general.dark};
`;

export const StyledDot = styled.div`
  width: 5px;
  height: 5px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.color?.general.lightest};

  &:not(:last-child) {
    margin-right: 3px;
  }
`;

export const StyledNavLink = styled(Typography)`
  position: relative;
  padding: 8px 12px;
  border-radius: 48px;
  height: 40px; // must be the same as StyledTreeDots height

  :not(:last-child) {
    margin-right: 8px;
  }

  &:hover,
  &.selected {
    background-color: ${({ theme }) => theme.color?.general.darker};
  }
`;

export const StyledDepositButtons = styled.div`
  ${({ theme }) => getTokens('typography-sub3-black-large', theme)};
  display: none;
  @media only screen and (min-width: ${({ theme }) =>
      theme.isTouchDevice ? theme.breakpoints?.xxxl : theme.breakpoints?.md}px) {
    display: flex;
  }
`;

export const StyledPromoAndBonuses = styled(Button)`
  position: relative;
  cursor: default;

  && {
    font-weight: ${({ theme }) => theme.font.weight.medium};
    font-size: 13px;
  }

  .icon {
    position: absolute;
    left: -1px;
    top: -1px;
  }

  img {
    position: absolute;
    right: 8px;
    top: -7px;
  }
`;

export const StyledProgressCircle = styled(ProgressBar)`
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0px -19px;
  cursor: pointer;

  .progressCircleContent {
    position: absolute;

    &.percent {
      background-color: ${({ theme }) => theme.color?.secondary.dark};
      font-size: 10px;
      line-height: 10px;
      font-weight: ${({ theme }) => theme.font?.weight.bold};
      border-radius: 50%;
      width: 24px;
      height: 24px;
    }
  }
`;
