import i18next from 'i18next';
import dynamic from 'next/dynamic';
import { Button } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { openModal } from '@/utils/openModal.ts';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
const LanguagesMenu = dynamic(
  () => import('@/app/components/LanguagesMenu/LanguagesMenu.tsx'),
  {
    ssr: false,
  },
);

const NotAuthorizedRightPart = ({
  t,
  lng,
  isGamePage,
}: {
  t: typeof i18next.t;
  lng: LanguagesType;
  isGamePage?: boolean;
}) => {
  const { setActiveAuthTab } = useNavigation();
  return (
    <>
      {!isGamePage && (
        <LanguagesMenu lng={lng} openToTop={false} width='70px' />
      )}
      <Button
        className='login'
        variant='transparent'
        hoverType='lighter'
        margin='0 8px 0 0'
        onClick={(e: any) => {
          e.preventDefault();
          // window.scrollTo(0, 0);
          setActiveAuthTab('login');
          openModal('authModal');
        }}
      >
        {t('login')}
      </Button>
      <Button
        variant='primary'
        text={t('signup')}
        onClick={(e: any) => {
          e.preventDefault();
          // window.scrollTo(0, 0);
          setActiveAuthTab('signUp');
          openModal('authModal');
        }}
      />
    </>
  );
};

export default NotAuthorizedRightPart;
