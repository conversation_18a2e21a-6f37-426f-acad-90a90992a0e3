import { Icon } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import Link from 'next/link';

const Logo = ({
  lng,
  isGamePage,
}: {
  lng: LanguagesType;
  isGamePage: boolean;
}) => {
  return (
    <Link href={`/${lng}`} target='_self' className='shrink-0'>
      <Icon
        name='logo'
        height={24}
        width={53}
        margin={isGamePage ? '2px 0 0 0' : '8px 24px 8px 0'}
        className={isGamePage ? '' : 'max-md:!mr-4'}
      />
    </Link>
  );
};

export default Logo;
