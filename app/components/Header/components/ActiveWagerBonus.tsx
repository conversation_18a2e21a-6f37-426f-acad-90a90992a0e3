import { StyledProgressCircle } from '@/app/components/Header/styled.ts';
import { UserType } from '@/app/wrappers/userProvider.tsx';
import { theme } from '@/theme.ts';
import Link from 'next/link';

const ActiveWagerBonus = ({
  user,
  href = '',
}: {
  user: UserType;
  href?: string;
}) => {
  if (!user.active_wager) {
    return null;
  }
  const activeBonusProgressInPercent = user.active_wager.current_progress
    ? Math.round(
        (user.active_wager.current_progress / user.active_wager.goal_sum) * 100,
      )
    : 0;

  return (
    <Link href={href}>
      <StyledProgressCircle
        progress={activeBonusProgressInPercent}
        radius={19}
        strokeWidth={3}
        trackStrokeWidth={3}
        strokeColor={theme.color?.secondary.dark}
        trackStrokeColor={theme.color?.general.darker}
      >
        <div className='progressCircleContent percent'>
          <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>
            <text
              x='50%'
              y='54%'
              dominantBaseline='middle'
              textAnchor='middle'
              fill={theme.color?.general.lightest}
            >
              {activeBonusProgressInPercent}%
            </text>
          </svg>
        </div>
      </StyledProgressCircle>
    </Link>
  );
};

export default ActiveWagerBonus;
