'use client';
import i18next from 'i18next';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { theme } from '@/theme.ts';
import Switch from '@/atomic-design-components/molecules/Switch/Switch';

const GamePageIsDemoSwitch = ({ t }: { t: typeof i18next.t }) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const isGameDemo = searchParams.get('demo') === 'true';

  const [isActive, setIsActive] = useState(!isGameDemo);

  useEffect(() => {
    setIsActive(!isGameDemo);
  }, [isGameDemo]);

  if (!isGameDemo) return null;

  return (
    <div className='mx-auto max-md:my-4'>
      <Switch
        checked={isActive}
        className='mx-auto'
        leftLabel={t('playForRealMoney')}
        labelColor={theme.color?.general.white}
        onChange={(checked: boolean) => {
          // if (checked && !isUserAuthorized) {
          //   openModal('authModal', true);
          //   document
          //     .getElementById('authModal')
          //     ?.setAttribute('data-active-auth-tab', 'login');
          //   return;
          // }

          setIsActive(checked);

          const params = new URLSearchParams(searchParams.toString());
          if (checked) {
            params.delete('demo');
          } else {
            params.set('demo', 'true');
          }
          router.push(`${pathname}?${params.toString()}`);
        }}
      />
    </div>
  );
};

export default GamePageIsDemoSwitch;
