import { useEffect } from 'react';
import Link from 'next/link';
import LoadingCircle from '@/app/components/Skeletons/LoadingCircle.tsx';
import { Badge, Icon } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { openModal } from '@/utils/openModal.ts';
import ActiveWagerBonus from '@/app/components/Header/components/ActiveWagerBonus.tsx';
import { StyledProgressCircle } from '@/app/components/Header/styled.ts';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { GamePageType, LanguagesType } from '@/types/global';
import { UserType } from '@/app/wrappers/userProvider.tsx';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';

const NotificationsBonusesProfileLinks = ({
  lng,
  gamePageTypeChecked,
  user,
  pathname,
}: {
  lng: LanguagesType;
  gamePageTypeChecked: GamePageType;
  user: UserType;
  pathname: string;
}) => {
  const { userBonuses, setInProgressBonuses, getFilteredBonuses } =
    useUserBonuses();
  const isActiveBonusShown = !!(user?.active_bonus_id && user.active_wager);
  const bonusesHref = `/${lng}/${gamePageTypeChecked}/profile/my-bonuses`;

  const {
    newNotificationsCount,
    allNotificationsCount,
    getAllNotifications,
    allNotifications,
    socketReadyState,
    inProgress: inProgressNotifications,
  } = useNewNotification();

  useEffect(() => {
    //|| socketReadyState === -1
    if (user?.id) {
      if (
        socketReadyState > 0 &&
        userBonuses === null &&
        !inProgressNotifications &&
        !pathname.includes('my-bonuses')
      ) {
        setInProgressBonuses(true);
        // console.log('getFilteredBonuses on login in header');
        getFilteredBonuses();
      }
    }
  }, [
    user,
    userBonuses,
    pathname,
    socketReadyState,
    inProgressNotifications,
    getFilteredBonuses,
  ]);

  useEffect(() => {
    if (user?.id && !allNotifications) {
      getAllNotifications();
    }
  }, [allNotifications, user?.id]);

  return (
    <>
      {allNotificationsCount === null && inProgressNotifications ? (
        <LoadingCircle />
      ) : (
        <Badge
          badgeContent={newNotificationsCount}
          backgroundColor={theme.color?.primary.main}
          className='notificationBadge inline-flex'
          color='white'
          margin='0'
          onClick={() => {
            openModal('notificationsModal');
          }}
        >
          <Icon
            wrapperWidth={40}
            wrapperHeight={40}
            name='bell'
            borderRadius='50%'
            wrapperColor={theme.color?.general.darker}
            onClick={
              newNotificationsCount
                ? undefined
                : () => {
                    openModal('notificationsModal');
                  }
            }
          />
        </Badge>
      )}
      {isActiveBonusShown ? (
        <ActiveWagerBonus user={user} href={bonusesHref} />
      ) : userBonuses === null ? (
        <LoadingCircle />
      ) : (
        <Link href={bonusesHref}>
          <Badge
            badgeContent={userBonuses?.length}
            backgroundColor={theme.color?.secondary.dark}
            className='bonusBadge inline-flex'
            color='white'
            margin='0'
          >
            <Icon
              name='giftBonus'
              fill={theme.color?.general.lightest}
              width={16}
              height={16}
              wrapperColor={theme.color?.general.darker}
              wrapperWidth={40}
              wrapperHeight={40}
              borderRadius='50%'
            />
          </Badge>
        </Link>
      )}

      <Link href={`/${lng}/${gamePageTypeChecked}/profile`}>
        <StyledProgressCircle
          progress={(user.level_current_points / user.next_level_points) * 100}
          radius={19}
          strokeWidth={3}
          trackStrokeWidth={3}
          strokeColor={theme.color?.primary.main}
          trackStrokeColor={theme.color?.general.darker}
        >
          <div className='progressCircleContent'>
            <Icon
              name='profile'
              width={14}
              wrapperColor={theme.color?.primary.main}
              wrapperWidth={24}
              wrapperHeight={24}
              borderRadius='50%'
            />
          </div>
        </StyledProgressCircle>
      </Link>
    </>
  );
};

export default NotificationsBonusesProfileLinks;
