import { StyledProgressCircle } from '@/app/components/Header/styled.ts';
import { UserType } from '@/app/wrappers/userProvider.tsx';
import { Icon } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { GamePageType, LanguagesType } from '@/types/global';
import Link from 'next/link';

const UserProfileTooltip = ({
  lng,
  user,
  gamePageType,
  onClick,
}: {
  lng: LanguagesType;
  isGamePage: boolean;
  user: UserType;
  gamePageType: GamePageType;
  isMobile?: boolean;
  onClick?: any;
}) => {
  return (
    <Link href={`/${lng}/${gamePageType}/profile`} onClick={onClick}>
      <StyledProgressCircle
        progress={(user.level_current_points / user.next_level_points) * 100}
        radius={20}
        strokeWidth={3}
        trackStrokeWidth={3}
        strokeColor={theme.color?.primary.main}
        trackStrokeColor={theme.color?.general.darker}
      >
        <div className='progressCircleContent'>
          <Icon
            name='profile'
            wrapperColor={theme.color?.primary.main}
            wrapperWidth={24}
            wrapperHeight={24}
            borderRadius='50%'
          />
        </div>
      </StyledProgressCircle>
    </Link>
  );
};

export default UserProfileTooltip;
