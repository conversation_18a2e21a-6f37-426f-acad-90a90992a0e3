'use client';
import clsx from 'clsx';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Fragment } from 'react';

import { StyledNavLink } from '@/app/components/Header/styled.ts';
import { StyledVerticalLine } from '@/app/components/NavMenu/styled.ts';
import { HEADER_NAV_LINKS } from '@/app/config/headerNavLinks.ts';
import { theme } from '@/theme.ts';
import { GamePageType, LanguagesType } from '@/types/global';

const NavLinks = ({
  t,
  lng,
  gamePageType,
}: {
  t: Function;
  lng: LanguagesType;
  gamePageType: GamePageType;
}) => {
  const pathname = usePathname();
  const isLiveCasinoPages =
    gamePageType === 'live-casino' || pathname.includes('/live-casino');

  return HEADER_NAV_LINKS.map((link) => {
    const selected =
      (link.title === 'liveCasino' && isLiveCasinoPages) ||
      (link.title === 'slots' &&
        (pathname === `/${lng}` || pathname.includes('/casino'))) ||
      (link.title !== 'slots' &&
        link.title !== 'liveCasino' &&
        pathname.includes(link.href));

    return (
      <Fragment key={link.href}>
        <StyledNavLink
          component={Link}
          className={clsx('menuItem', selected && 'selected')}
          type='body2'
          href={
            ['liveCasino', 'slots'].includes(link.title)
              ? '/' + lng + link.href
              : '/' + lng + '/' + gamePageType + link.href
          }
          key={link.href}
          iconName={link.withIcon ? link.iconName : ''}
          iconProps={{
            height: 16,
            width: 16,
            fill: theme.color?.general.lightest,
            margin: '0 8px 0 0',
          }}
        >
          {t(link.title)}
        </StyledNavLink>
        {link.withLineAfter && <StyledVerticalLine className='mr-2 h-6' />}
      </Fragment>
    );
  });
};

export default NavLinks;
