'use client';
import clsx from 'clsx';
import { useParams, usePathname, useSearchParams } from 'next/navigation';
import { memo, useEffect, useRef, useState } from 'react';
import GamePageIsDemoSwitch from '@/app/components/Header/components/GamePageIsDemoSwitch';
import Logo from '@/app/components/Header/components/Logo.tsx';
import NavLinks from '@/app/components/Header/components/NavLinks.tsx';
import NotAuthorizedRightPart from '@/app/components/Header/components/NotAuthorizedRightPart.tsx';
import ThreeDots from '@/app/components/Header/components/ThreeDots.tsx';
import {
  StyledDepositButtons,
  StyledHeader,
  StyledNavLinks,
} from '@/app/components/Header/styled.ts';
import { useTranslation } from '@/app/i18n/client';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { Button, FlexRow, Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { GamePageType, LanguagesType } from '@/types/global';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { getGamePageType } from '@/utils/getGamePageType.ts';
import { openModal } from '@/utils/openModal';
import { populateHeaderWithLinks } from '@/utils/populateHeaderWithLinks.ts';
import NotificationInHeader from '../Notifications/components/NotificationInHeader';
import NotificationsBonusesProfileLinks from '@/app/components/Header/components/NotificationsBonusesProfileLinks.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

function Header() {
  const { lng, gamePageType }: { lng: LanguagesType; gamePageType: GamePageType } = useParams();
  const { t } = useTranslation(lng);
  const { isTouchDevice } = useIsTouchMobileView();
  const pathname = usePathname();
  const { previousRoute } = useNavigation();

  const searchParams = useSearchParams();
  const ref = useRef<HTMLElement>(null);
  const [isLoading, setIsLoading] = useState(true);

  const { user, isFirstLoadDone, inProgressCodeCheck } = useUser();
  const isUserAuthorized = !!user?.id;
  const isGamePage = pathname.includes('/game/');
  const isDemo = searchParams.get('demo') === 'true';

  const gamePageTypeChecked = getGamePageType(gamePageType);

  useEffect(() => {
    if (isFirstLoadDone && !isLoading && !isTouchDevice) {
      populateHeaderWithLinks();
    }
  }, [user, isFirstLoadDone, isTouchDevice, isLoading]);

  useEffect(() => {
    if (
      !isTouchDevice &&
      previousRoute &&
      pathname !== previousRoute &&
      previousRoute.includes('/game/')
    ) {
      populateHeaderWithLinks();
    }
  }, [pathname, previousRoute, isTouchDevice]);

  useEffect(() => {
    if (!isTouchDevice && isLoading && isFirstLoadDone) {
      // setTimeout(() => {
      populateHeaderWithLinks();
      setIsLoading(false);
      window.addEventListener('resize', () => {
        populateHeaderWithLinks();
      });
      // }, 0);
    }
  }, [isFirstLoadDone, isLoading, isTouchDevice]);

  useEffect(() => {
    const handleScroll = () => {
      if (ref && ref.current) {
        if (window.scrollY > 0) {
          ref.current.classList.add('scrolled');
        } else {
          ref.current.classList.remove('scrolled');
        }
      }
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  if (inProgressCodeCheck) return null;

  return (
    <>
      {!isGamePage && <NotificationInHeader />}
      <StyledHeader
        ref={ref}
        className={clsx(
          isGamePage && 'isGamePage',
          isDemo && 'isDemo',
          !isUserAuthorized && 'notLoggedIn',
          'sticky top-0 z-[100] bg-[#0F172A] text-white xxl:mx-auto xxl:w-[1680px]',
        )}
      >
        <div className='flex items-center md:mx-4'>
          <Logo lng={lng} isGamePage={isGamePage} />
          {isGamePage && (
            <div
              role='button'
              tabIndex={0}
              onClick={(e: any) => {
                e.preventDefault();
                openModal('searchModal');
              }}
              className='ml-6 flex w-[240px] gap-2 rounded-lg border border-[#334155] bg-[#1E293B] px-3 py-[7px]'
            >
              <Icon name='search' width={16} height={16} fill={theme.color?.general.lighter} />
              <Typography text={t('searchGame')} type='body2' color={theme.color?.general.light} />
            </div>
          )}
          {isUserAuthorized && (
            <div
              role='button'
              tabIndex={0}
              className='mobileBalance cursor-pointer rounded-[8px] border-[1px] border-transparent bg-[#1E293B] px-[16px] py-[8px] text-[14px] font-semibold max-md:py-[7px]'
              onClick={(e: any) => {
                e.preventDefault();
                // window.scrollTo(0, 0);
                openModal('cashierModal');
              }}
            >
              {getCurrencySymbol(user?.currency)}{' '}
              {addSpacesToLongNumbers(user.account_balance.toFixed(2))}
            </div>
          )}
          {/*{inProgress && <HeaderSkeleton />}*/}
          {isFirstLoadDone && (
            <>
              {!isGamePage && (
                <StyledNavLinks id='headerNavMenu'>
                  {isLoading && (
                    <div className='menuLoading absolute left-[0px] right-[8px] flex flex-nowrap overflow-hidden text-ellipsis whitespace-nowrap'>
                      <NavLinks t={t} lng={lng} gamePageType={gamePageTypeChecked} />
                    </div>
                  )}
                  <div id='menu' className={clsx('menu', isLoading && 'isLoading')}>
                    <NavLinks t={t} lng={lng} gamePageType={gamePageTypeChecked} />
                    <div id='moreItems'>
                      <ThreeDots id='more' />
                      <div id='moreItemsDropdown'></div>
                    </div>
                  </div>
                </StyledNavLinks>
              )}

              {isGamePage && <GamePageIsDemoSwitch t={t} />}

              <FlexRow id='afterNavLinks' margin={isGamePage ? 0 : '0 0 0 auto'}>
                {!isUserAuthorized && <NotAuthorizedRightPart t={t} lng={lng} />}
                {isUserAuthorized && (
                  <>
                    {isGamePage ? (
                      <Button
                        variant='primary'
                        size='medium'
                        onClick={(e: any) => {
                          e.preventDefault();
                          // window.scrollTo(0, 0);
                          openModal('cashierModal');
                        }}
                      >
                        +
                        <span className='ml-[5px] hidden sm:inline'>
                          {t('deposit').toUpperCase()}
                        </span>
                      </Button>
                    ) : (
                      <StyledDepositButtons>
                        <div className='relative flex items-center gap-4 overflow-hidden whitespace-nowrap rounded-lg bg-[#1E293B] py-1 pl-4 pr-2'>
                          {getCurrencySymbol(user?.currency)}
                          {addSpacesToLongNumbers(user.account_balance.toFixed(2))}
                          <Button
                            variant='primary'
                            size='small'
                            onClick={(e: any) => {
                              e.preventDefault();
                              // window.scrollTo(0, 0);
                              openModal('cashierModal');
                            }}
                          >
                            +
                            <span className='ml-[5px] hidden sm:inline'>
                              {t('deposit').toUpperCase()}
                            </span>
                          </Button>
                        </div>
                      </StyledDepositButtons>
                    )}
                    <NotificationsBonusesProfileLinks
                      lng={lng}
                      gamePageTypeChecked={gamePageTypeChecked}
                      user={user}
                      pathname={pathname}
                    />
                  </>
                )}
              </FlexRow>
            </>
          )}
        </div>
      </StyledHeader>
    </>
  );
}

export default memo(Header);
