'use client';
import clsx from 'clsx';
import { theme } from '@/theme';
import { useEffect, useState } from 'react';
import Slider from '@/app/components/Slider/Slider.tsx';
import Banner from '@/app/components/Banner/Banner.tsx';
import BannerMobile from '@/app/components/Banner/BannerMobile.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { filterBanners } from '@/utils/filterBanners.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import useWindowSize from '@/hooks/useWindowSize';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';

const BannerClientComponent = () => {
  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isTouchDeviceOrMd = isTouchDevice || (width && width < theme.breakpoints?.md);

  const [{ banners: bannersAll }] = useSystemData();
  const bannersMain = bannersAll?.filter((banner) => banner.banner_type === 'landing') || [];

  const getFilteredBanners = () => {
    const { items } = filterBanners(bannersMain, isUserAuthorized);
    const mode = isTouchDeviceOrMd ? 'mobile' : 'desktop';
    const filteredItems = items.filter((item) => item.display_mode === mode);
    return {
      items: filteredItems,
    };
  };

  const initialBanners = getFilteredBanners();
  const [banners, setBanners] = useState(initialBanners.items);

  useEffect(() => {
    const { items } = getFilteredBanners();
    setBanners(items);
  }, [isUserAuthorized, isTouchDeviceOrMd, bannersAll]);

  // useEffect(() => {
  //   // preload banner images for smooth transitions
  //   banners.forEach((banner, index) => {
  //     if (index !== 0) return;
  //     const imgBg = new Image();
  //     const imgMain = new Image();
  //     const bgSrcArr = banner.photo_background_url.replace('https://', '').split('/');
  //     const bgSrc = bgSrcArr.slice(1).join('/');
  //     const mainSrcArr = banner.photo_main_url.replace('https://', '').split('/');
  //     const mainSrc = mainSrcArr.slice(1).join('/');
  //     imgBg.src = `https://cdn-image-store-dev.b-cdn.net/${bgSrc}?format=auto`;
  //     imgMain.src = `https://cdn-image-store-dev.b-cdn.net/${mainSrc}?format=auto`;
  //     console.log(imgBg, imgMain);
  //   });
  // }, [banners]);

  // console.log(bannersAll, bannersMain, banners);
  return (
    <div id='landing-banner'>
      <Slider
        autoplay
        className={clsx('banners', isTouchDeviceOrMd ? '!h-[344px]' : '!h-[320px]')}
        isArrowShownOnHover
        isArrowShownOnMobile={true}
        items={banners || []}
        loop={banners?.length > 1}
        mode='snap'
        renderMode='performance'
        total={banners?.length || 0}
        Slide={isTouchDeviceOrMd ? BannerMobile : Banner}
        opacitiesInitial={banners.map((_, index) => (index === 0 ? 1 : 0)) || []}
        perView={1}
        withDots
      />
    </div>
  );
};

export default BannerClientComponent;
