'use client';
import { memo } from 'react';
import { useParams } from 'next/navigation';
import { IBanner } from '@/app/api/getBannersServer.ts';
import { Typography, Image } from '@/atomic-design-components';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import TargetUrlButton from '@/app/components/TargetUrlButton';

type BannerProps = {
  item: IBanner;
  index: number;
};

const BannerMobile = ({ item, index }: BannerProps) => {
  const { lng } = useParams();
  const { photo_main_url, photo_background_url, title, button_text = '', target_url } = item;

  const bannerClassName = 'h-[344px]';
  const buttonText = getAvailableTranslation(button_text, lng);
  const titleText = getAvailableTranslation(title, lng);

  return (
    <>
      <div className='absolute bottom-0 top-0 z-10 flex w-full flex-col items-center justify-center p-4 pb-9 text-center'>
        {titleText && <Typography type='h1' color='white' text={titleText} />}
        <div className='flex-grow' />
        {target_url && <TargetUrlButton target_url={target_url} buttonText={buttonText} />}
      </div>
      <div className='relative h-[344px] w-full'>
        {photo_background_url && (
          <div
            className={bannerClassName}
            style={{
              width: 'calc(100% + 40px)',
              transform: 'translateZ(0)',
              animation: '30s infinite linear banners-bg-move',
              position: 'absolute',
              right: '-20px',
              left: '-20px',
            }}
          >
            <Image
              src={photo_background_url}
              alt={`bg-${titleText}`}
              fill={true}
              style={{
                objectFit: 'cover',
                objectPosition: '50% 25%',
              }}
              sizes='100vw'
              priority={index === 0}
            />
          </div>
        )}
        {photo_main_url && (
          <div
            className='absolute bottom-0 h-full w-full'
            style={{
              transform: 'translateZ(0)',
              animation: '30s infinite linear banners-main-move',
            }}
          >
            <Image
              src={photo_main_url}
              alt={titleText}
              fill={true}
              style={{
                objectFit: 'contain',
                objectPosition: 'bottom',
              }}
              sizes='100vw'
              priority={index === 0}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default memo(BannerMobile);
