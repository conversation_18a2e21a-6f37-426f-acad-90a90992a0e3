'use client';
import { LOGO_PLACEHOLDER_HORIZONTAL } from '@/constants';
import clsx from 'clsx';
import { useState } from 'react';
import { Image } from '@/atomic-design-components';

const PartnerCard = ({
  item,
  index,
  className,
}: {
  item: string;
  index: number;
  className: string;
}) => {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div
      className={clsx(
        'gameCard partnerCard align-center relative flex flex-col justify-center',
        !item && 'placeholder',
        !isLoaded && 'loading',
        className,
      )}
    >
      {(!isLoaded || !item) && (
        <Image
          alt={item || `Partner ${index} placeholder`}
          placeholder={undefined}
          src={LOGO_PLACEHOLDER_HORIZONTAL}
          fill
          sizes='357px'
          style={{ objectFit: 'contain' }}
          className='placeholder'
          unoptimized
        />
      )}
      {item && (
        <Image
          alt={item || `Partner ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={`/partners/${item}.png`}
          fill
          sizes='357px'
          style={{ objectFit: 'contain' }}
          unoptimized
        />
      )}
    </div>
  );
};

export default PartnerCard;
