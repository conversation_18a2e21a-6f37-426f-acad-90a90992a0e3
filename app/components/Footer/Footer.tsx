'use client';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import dynamic from 'next/dynamic';

import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Button, Icon, Typography, Image } from '@/atomic-design-components';
import FlexRow from '@/atomic-design-components/atoms/FlexRow/FlexRow';
import { GamePageType, LanguagesType } from '@/types/global';
import { getGamePageType } from '@/utils/getGamePageType.ts';
import { StyledFooter } from './styled';

const LanguagesMenu = dynamic(
  () => import('@/app/components/LanguagesMenu/LanguagesMenu.tsx'),
  {
    ssr: false,
  },
);

export default function Footer({ className }: { className?: string }) {
  const pathname = usePathname();
  const {
    lng,
    gamePageType,
  }: { lng: LanguagesType; gamePageType: GamePageType } = useParams();
  const { t } = useTranslation(lng);

  const { user } = useUser();
  const isUserAuthorized = !!user?.currency;

  const isGamePage = pathname.includes('/game/');

  if (isGamePage) {
    return null;
  }

  const gamePageTypeChecked = getGamePageType(gamePageType);

  return (
    <StyledFooter className={className}>
      <div className='flex flex-col gap-4 border-t border-[#334155] px-4 py-6'>
        <div className='flex w-full flex-col items-start justify-between gap-4 max-md:hidden'>
          <FlexRow className='w-full' gap='20px' alignItems='start'>
            <Link href={`/${lng}`} target='_self' className='shrink-0'>
              <Icon name='logo' height={24} width={53} />
            </Link>
          </FlexRow>
        </div>
        <div className='infoLinks flex w-full items-center justify-between max-sm:flex-col max-sm:gap-4'>
          <div className='flex gap-4 max-sm:w-full max-sm:justify-between sm:flex-col'>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/faq`}
              className='w-full'
            >
              <Typography
                text='FAQ'
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/privacy-policy`}
              className='w-full'
            >
              <Typography
                text={t('privacyPolicy')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/terms-conditions`}
              className='w-full'
            >
              <Typography
                text={t('termsAgree')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
          </div>
          {/*<div className='flex gap-4 max-sm:w-full max-sm:justify-between sm:flex-col'></div>*/}
          <div className='flex gap-4 max-sm:w-full max-sm:justify-between sm:flex-col'>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/bonus-terms`}
              className='w-full'
            >
              <Typography
                text={t('bonusTerms')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/payment-methods`}
              className='w-full'
            >
              <Typography
                text={t('paymentMethods')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/kyc-policy`}
              className='w-full'
            >
              <Typography
                text={t('KYCpolicy')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
          </div>
          <div className='flex gap-4 max-sm:w-full max-sm:justify-between sm:flex-col'>
            <a href='https://like.partners/' target='_blank' className='w-full'>
              <Typography
                text={t('affiliateProgram')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </a>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/loyalty-program`}
              className='w-full'
            >
              <Typography
                text={t('loyaltyProgram')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/responsible-gaming`}
              className='w-full'
            >
              <Typography
                text={t('responsibleGaming')}
                type='body1'
                className='w-full max-sm:justify-center max-sm:text-center'
              />
            </Link>
            {/*<Link*/}
            {/*  href={`/${lng}/${gamePageTypeChecked}/contacts`}*/}
            {/*  className='w-full'*/}
            {/*>*/}
            {/*  <Typography*/}
            {/*    text={t('contacts')}*/}
            {/*    type='body1'*/}
            {/*    className='w-full max-sm:justify-center max-sm:text-center'*/}
            {/*  />*/}
            {/*</Link>*/}
          </div>
          {isUserAuthorized && (
            <LanguagesMenu
              lng={lng}
              openToTop={true}
              isFullNamed={true}
              width='228px'
              withFlag
              className='max-md:hidden'
            />
          )}
        </div>
      </div>
      <div className='mb-2 flex w-full flex-col items-center justify-between border-t border-[#334155] px-2 py-6 md:hidden'>
        <div className='mb-6 flex w-full flex-col items-center gap-2'>
          <Typography
            text={t('installOnPhone')}
            type='body1'
            className='justify-center'
          />
          <div className='flex w-full gap-2'>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/apple`}
              className='w-full'
            >
              <Button
                variant='secondary'
                size='small'
                className='gap-1'
                fullWidth
              >
                <Icon name='apple' width={16} height={16} />
                Apple
              </Button>
            </Link>
            <Link
              href={`/${lng}/${gamePageTypeChecked}/info/android`}
              className='w-full'
            >
              <Button
                variant='secondary'
                size='small'
                className='gap-1'
                fullWidth
              >
                <Icon name='android' width={16} height={16} />
                Android
              </Button>
            </Link>
          </div>
        </div>
        <div className='flex w-full justify-between'>
          <div>
            <Typography text={t('allRightsReserved')} type='body1' />
            <Typography
              text={`© ${new Date().getFullYear()} Like`}
              type='body1'
            />
          </div>
          <div className='flex gap-3 px-2'>
            <div className='flex gap-4'>
              {/*<Link href='insta'>*/}
              {/*  <Icon name='instagram' width={24} height={24} />*/}
              {/*</Link>*/}
              <a
                href='https://t.me/likecasino777'
                target='_blank'
                className='w-full'
              >
                <Icon name='telegram' width={24} height={24} />
              </a>
            </div>
            <Image
              alt='colorfull'
              src='/colorFullIcon.png'
              width={24}
              height={24}
              className='h-fit w-fit'
              unoptimized
            />
            <Image
              alt='18+'
              src='/18icon.png'
              width={24}
              height={24}
              className='h-fit w-fit'
              unoptimized
            />
          </div>
        </div>
      </div>
      <div className='flex w-full justify-between border-t border-[#334155] px-4 py-6 max-md:hidden'>
        <Typography
          text={`${t('allRightsReserved')} © ${new Date().getFullYear()} Like`}
          type='body1'
        />
        <div className='flex gap-2 md:gap-4'>
          <Typography
            text={t('installOnPhone')}
            type='body1'
            className='max-md:justify-center'
          />
          <div className='flex gap-2'>
            <Link href={`/${lng}/${gamePageTypeChecked}/info/apple`}>
              <Button variant='secondary' size='small' className='gap-1'>
                <Icon name='apple' width={16} height={16} />
                Apple
              </Button>
            </Link>
            <Link href={`/${lng}/${gamePageTypeChecked}/info/android`}>
              <Button variant='secondary' size='small' className='gap-1'>
                <Icon name='android' width={16} height={16} />
                Android
              </Button>
            </Link>
          </div>
        </div>
        <div className='flex h-6 gap-3'>
          <div className='flex gap-3'>
            {/*<Link href='insta'>*/}
            {/*  <Icon name='instagram' width={24} height={24} />*/}
            {/*</Link>*/}
            <a
              href='https://t.me/likecasino777'
              target='_blank'
              className='w-full'
            >
              <Icon name='telegram' width={24} height={24} />
            </a>
          </div>
          <Image
            alt='colorfull'
            src='/colorFullIcon.png'
            width={24}
            height={24}
            className='h-auto w-auto'
            unoptimized
          />
          <Image
            alt='18+'
            src='/18icon.png'
            width={24}
            height={24}
            className='h-auto w-auto'
            unoptimized
          />
        </div>
      </div>
    </StyledFooter>
  );
}
