'use client';
import styled from 'styled-components';

export const StyledFooter = styled.footer`
  background-color: ${({ theme }) => theme.color?.general.darkest};

  &.fullWidth {
    width: 100%;
    padding: 8px 24px 0 0;
  }

  .languagesMenu {
    margin-left: 0;
  }

  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    padding: 8px 24px 0 0;
    ${({ theme }) => theme.isTouchDevice && 'padding-left: 24px;'}
  }
`;

export const StyledLine: any = styled.hr`
  background-color: ${({ theme, color }) => color || theme.color?.general.dark};
  border-width: 0;
  height: 1px;
  margin: ${({ margin }: { margin: string }) => margin || '0'};
  width: 100%;
`;
