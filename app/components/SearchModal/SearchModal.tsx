'use client';
import { useParams, usePathname } from 'next/navigation';
import { useEffect, useRef } from 'react';

import { StyledModalDialog } from '@/app/components/ModalDialog/styled.ts';
import SearchContainer from '@/atomic-design-components/molecules/Search/SearchContainer';
import useClickOutsideElement from '@/hooks/useClickOutsideElement';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { closeModal } from '@/utils/closeModal';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';

const SearchModal = () => {
  const { isTouchDevice } = useIsTouchMobileView();

  const { lng } = useParams();
  const pathname = usePathname();

  const { width } = useWindowSize();
  const isMobile = width && width < theme.breakpoints?.sm;

  const ref = useRef<HTMLDialogElement>(null);
  useClickOutsideElement(ref, () => {
    if (
      ref?.current?.hasAttribute('open') &&
      !document.getElementById('continueGameWithZeroWager')?.dataset
        ?.gameUrlClicked &&
      !document
        .getElementById('activeFreespinsInGame')
        ?.classList?.contains('opened') &&
      !document
        .getElementById('unfinishedGameModal')
        ?.classList?.contains('opened') &&
      !document.getElementById('gameModal')?.classList?.contains('opened')
    ) {
      closeModal('searchModal');
    }
  });

  const { register } = useRegisterModalToOpenByUrl();
  useEffect(() => {
    if (ref.current) {
      register('searchModal');
    }
  }, [ref?.current]);

  useEffect(() => {
    closeModal('searchModal');
  }, [pathname]);

  const { isDialogOpen } = useIsDialogOpen(ref);

  return (
    <StyledModalDialog
      id='searchModal'
      ref={ref}
      className={isMobile || isTouchDevice ? 'mobileView' : ''}
    >
      <SearchContainer
        lng={lng}
        isAlwaysOpened
        width={isMobile || isTouchDevice ? '100vw' : '65vw'}
        isDialogOpen={isDialogOpen}
        isTouchDevice={isTouchDevice}
      />
    </StyledModalDialog>
  );
};

SearchModal.displayName = 'SearchModal';
export default SearchModal;
