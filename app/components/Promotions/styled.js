'use client';

import styled from 'styled-components';

export const StyledPromoCard = styled.div`
  position: relative;
  min-width: ${({ width }) => width}px;

  .infoIcon {
    position: absolute;
    top: 8px;
    right: 8px;
  }

  .infoContainer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 8px;
    background-color: ${({ theme }) => `${theme.color?.general.darkest}e6`};
    @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      padding: 16px;
    }
  }

  img {
    border-radius: ${({ theme }) => theme.size.border.radius.main};
  }

  .bannerText {
    color: #fff;
    text-align: center;
    text-shadow: 0 0 7.772px #3da0ff;
    font-weight: 900;
    line-height: 70px;
    text-transform: uppercase;
    position: absolute;
    left: 30px;

    &.smallCardText {
      left: 20px;
      line-height: 40px;
    }
  }
`;

export const StyledBadgeText = styled.div`
  background-color: ${({ theme }) => theme.color?.secondary.dark};
  border-radius: 5px;
  padding: 2px 10px;
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    top: 16px;
    left: 16px;
  }
`;
