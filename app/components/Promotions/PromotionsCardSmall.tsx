'use client';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { IPromotion } from '@/app/api/getPromotions';
import { FlexRow, Typography, Image } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { openModal } from '@/utils/openModal';
import { StyledBadgeText, StyledPromoCard } from './styled';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

const PromotionsCardSmall = ({
  item,
  index,
  className,
  id,
  isSmallerTitle,
  cardWidth,
}: {
  item: IPromotion;
  index: number;
  className?: string;
  id?: string;
  cardWidth: number;
  isSmallerTitle: boolean;
}) => {
  const { lng }: { lng: LanguagesType } = useParams();

  const { setActiveModalData } = useNavigation();

  const [isLoaded, setIsLoaded] = useState(false);

  const badgeText = getAvailableTranslation(item.badge_text || '', lng);

  return (
    <StyledPromoCard
      id={id}
      // @ts-ignore
      width={cardWidth}
      className={clsx(
        className,
        'gameCard promotionCardSmall align-center flex cursor-pointer flex-col justify-center',
        !item.photo_small_url && 'placeholder',
        !isLoaded && 'loading',
      )}
      role='presentation'
      onClick={(e: any) => {
        e.preventDefault();
        setActiveModalData(item);
        openModal('promotionModal');
      }}
    >
      {badgeText && isLoaded && (
        <StyledBadgeText>
          <Typography type='sub2' text={badgeText} />
        </StyledBadgeText>
      )}
      {(!isLoaded || !item.photo_small_url) && (
        <Image
          alt={item.slug || `Promotion ${index} placeholder`}
          placeholder={undefined}
          src={LOGO_PLACEHOLDER}
          fill
          sizes='357px'
          style={{ objectFit: 'contain' }}
          className='placeholder'
          unoptimized
        />
      )}
      {item.photo_small_url && (
        <Image
          alt={item.slug || `Promotion ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={item.photo_small_url}
          // priority={index <= 3}
          fill
          sizes='450px'
          style={{ objectFit: 'cover' }}
        />
      )}
      {isLoaded && (
        <FlexRow className='infoContainer' flexDirection='column' alignItems='start'>
          <Typography
            text={getAvailableTranslation(item.title, lng)}
            type={isSmallerTitle ? 'sub2' : 'h3'}
            className='clamp-1-line'
          />
          <Typography
            text={getAvailableTranslation(item.text, lng)}
            type='body1'
            className='clamp-1-line'
          />
        </FlexRow>
      )}
    </StyledPromoCard>
  );
};

export default PromotionsCardSmall;
