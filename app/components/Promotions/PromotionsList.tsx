'use client';
import React, { useEffect } from 'react';

import { IPromotion } from '@/app/api/getPromotionsServer.ts';
import { Container, TilesGrid } from '@/atomic-design-components';
import PromotionsCard from './PromotionsCard.tsx';
import { MAX_CONTAINER_WIDTH } from '@/constants.ts';

// To implement load more we need to change the filtering logic (move it to BE side, probably)

const PromotionsList = ({
  // limit,
  initialData,
  // totalItems,
  // promotionsOrderArray,
}: {
  limit: number;
  initialData: IPromotion[];
  totalItems: number;
  promotionsOrderArray: number[];
}) => {
  // const loadMoreRef = React.useRef<HTMLDivElement>(null);

  const [tableData, setTableData] = React.useState(initialData);
  // const [skip, setSkip] = React.useState(limit);

  useEffect(() => {
    setTableData(initialData);
  }, [initialData]);

  // const loadNextPage = async () => {
  //   if (totalItems <= tableData?.length) return;
  //   // setInProgress(true);
  //   const { items } = await getPromotions(skip, limit);
  //   const newData = sortItems({
  //     items: [...tableData, ...items],
  //     order: promotionsOrderArray,
  //   });
  //   setTableData(newData);
  //   setSkip((prev) => prev + limit);
  //   // setInProgress(false);
  // };

  // useLoadMoreObserver({ loadNextPage, loadMoreRef, withLoadMore: true });

  return (
    <>
      <Container
        flexDirection='column'
        fullWidth
        centered
        maxWidth={MAX_CONTAINER_WIDTH}
        dataLength={tableData.length}
        noPadding
        className='relative !mt-4'
      >
        <TilesGrid
          itemsInRow={2}
          rowGap='16px'
          breakpoints={{
            '(min-width: 621px)': {
              perView: 2,
              spacing: 16,
            },
            '(max-width: 620px)': {
              perView: 1,
              spacing: 8,
            },
          }}
        >
          {tableData.map((promo, index) => (
            <PromotionsCard item={promo} key={index} index={index} />
          ))}
        </TilesGrid>
        {/*<div ref={loadMoreRef} className='absolute bottom-[30vh]' />*/}
      </Container>
    </>
  );
};

export default PromotionsList;
