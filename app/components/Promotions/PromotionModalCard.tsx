'use client';
import useParseRichText from '@/hooks/useParseRichText.tsx';
import clsx from 'clsx';
import { Image } from '@/atomic-design-components';
import { useParams } from 'next/navigation';
import { useState } from 'react';

import { Typography } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER_HORIZONTAL } from '@/constants';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { StyledLine } from '../Footer/styled';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { LanguagesType } from '@/types/global';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import TargetUrlButton from '@/app/components/TargetUrlButton';

const PromotionModalCard = () => {
  const { parse } = useParseRichText();
  const { lng }: { lng: LanguagesType } = useParams();
  const { isTouchDevice } = useIsTouchMobileView();
  const [isLoaded, setIsLoaded] = useState(false);

  const { activeModalData } = useNavigation();

  const id = activeModalData?.id;

  if (!activeModalData) {
    return null;
  }

  const description = getAvailableTranslation(activeModalData.description, lng);
  const buttonText = getAvailableTranslation(activeModalData.button_text || '', lng);

  return (
    <div className='flex flex-col'>
      <div className='mb-4 flex flex-col overflow-hidden pt-6 max-sm:px-4 sm:px-6'>
        <Typography
          text={getAvailableTranslation(activeModalData.title, lng)}
          type='h1'
          margin='0 32px 0 0'
        />
        <div
          className={clsx(
            'mt-2 flex max-h-[calc(100dvh-165px)] flex-col gap-4 overflow-auto',
            isTouchDevice ? 'max-w-full' : 'md:max-w-[500px]',
          )}
        >
          <div
            className={clsx(
              'gameCard promoLargeCard align-center flex !max-w-full flex-col justify-center',
              !activeModalData.photo_large_url && 'placeholder',
              !isLoaded && 'loading',
            )}
          >
            {(!isLoaded || !activeModalData.photo_large_url) && (
              <Image
                alt={activeModalData.slug || `Promotion ${id} placeholder`}
                placeholder={undefined}
                src={LOGO_PLACEHOLDER_HORIZONTAL}
                fill
                className='placeholder'
                unoptimized
              />
            )}
            {activeModalData.photo_large_url && (
              <Image
                alt={activeModalData.slug || `Promotion ${id}`}
                placeholder={undefined}
                onLoad={() => setIsLoaded(true)}
                src={activeModalData.photo_large_url}
                fill
                style={{ objectFit: 'cover' }}
              />
            )}
          </div>
          <Typography
            text={parse(description)}
            type='body2'
            className='!block pr-1 max-sm:max-h-[calc(100dvh-152px)] sm:max-h-[50dvh]'
          />
        </div>
      </div>
      {activeModalData.target_url && buttonText && (
        <>
          <StyledLine margin='auto 0 0' />
          <div className='w-full max-sm:px-4 max-sm:py-3 sm:p-6 sm:pt-4'>
            <TargetUrlButton
              target_url={activeModalData?.target_url || ''}
              buttonText={buttonText}
              isPromoButton
              isTouchDevice={isTouchDevice}
            />
          </div>
        </>
      )}
    </div>
  );
};

export default PromotionModalCard;
