'use client';
import { useRef } from 'react';

import useClickOutsideElement from '@/hooks/useClickOutsideElement';
import { closeModal } from '@/utils/closeModal';
import ModalDialog from '../ModalDialog';
import PromotionModalCard from './PromotionModalCard';

const PromotionModal = () => {
  const ref = useRef<HTMLDialogElement>(null);

  useClickOutsideElement(ref, () => {
    if (ref.current?.classList.contains('opened')) {
      closeModal('promotionModal');
    }
  });

  return (
    <ModalDialog
      id='promotionModal'
      ref={ref}
      onClose={() => {
        closeModal('promotionModal');
      }}
    >
      <PromotionModalCard />
    </ModalDialog>
  );
};

PromotionModal.displayName = 'PromotionModal';
export default PromotionModal;
