'use client';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { IPromotion } from '@/app/api/getPromotions';
import { FlexRow, Typography, Image } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { openModal } from '@/utils/openModal';
import { StyledBadgeText, StyledPromoCard } from './styled';
import useWindowSize from '@/hooks/useWindowSize';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

const PromotionsCard = ({
  item,
  index,
  className,
}: {
  item: IPromotion;
  index: number;
  className?: string;
}) => {
  const { lng }: { lng: LanguagesType } = useParams();
  const { setActiveModalData } = useNavigation();

  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const onMouseEnter = () => setIsHovered(true);
  const onMouseLeave = () => setIsHovered(false);
  const { width } = useWindowSize();
  const isMobile = width && width < theme.breakpoints?.md;

  const badgeText = getAvailableTranslation(item.badge_text || '', lng);

  return (
    <StyledPromoCard
      className={clsx(
        className,
        'gameCard promotionCard align-center flex cursor-pointer flex-col justify-center',
        !item.photo_large_url && 'placeholder',
        !isLoaded && 'loading',
      )}
      role='presentation'
      onClick={(e: any) => {
        if (className?.includes('getBonusPromoCard')) return;
        e.preventDefault();
        setActiveModalData(item);
        openModal('promotionModal');
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    >
      {badgeText && isLoaded && (
        <StyledBadgeText>
          <Typography type='sub2' text={badgeText} fontWeight='500' />
        </StyledBadgeText>
      )}
      {!isLoaded && item.photo_large_url && (
        <div className='absolute bottom-0 left-0 flex h-[100px] w-full flex-col gap-1 p-4'>
          <div className='absolute top-[-12px] h-6 w-[120px] rounded bg-gray-600'></div>
          <div className='my-2 h-5 w-1/2 rounded bg-gray-600'></div>
          <div className='h-3 w-full rounded bg-gray-600'></div>
          <div className='h-3 w-3/4 rounded bg-gray-600'></div>
        </div>
      )}
      {!item.photo_large_url && (
        <Image
          alt={item.slug || `Promotion ${index} placeholder`}
          placeholder={undefined}
          src={LOGO_PLACEHOLDER}
          fill
          sizes='357px'
          style={{ objectFit: 'contain' }}
          className='placeholder'
          unoptimized
        />
      )}
      {item.photo_large_url && (
        <Image
          alt={item.slug || `Promotion ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={item.photo_large_url}
          // priority={index <= 3}
          fill
          style={{ objectFit: 'cover' }}
          className={isHovered ? 'hovered' : ''}
        />
      )}
      {(isLoaded || !item.photo_large_url) && (
        <FlexRow
          className='infoContainer'
          flexDirection='column'
          alignItems='start'
        >
          <Typography
            text={getAvailableTranslation(item.title, lng)}
            type={isMobile ? 'sub2' : 'h3'}
            className={isMobile ? 'clamp-1-line' : ''}
          />
          <div className='inline-flex w-full items-end'>
            <Typography
              text={getAvailableTranslation(item.text, lng)}
              type='body1'
              className='clamp-1-line'
            />
          </div>
        </FlexRow>
      )}
    </StyledPromoCard>
  );
};

export default PromotionsCard;
