'use client';
import { IPromotionsResponse } from '@/app/api/getPromotions';
import Slider from '@/app/components/Slider/Slider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { filterPromotions } from '@/utils/filterPromotions';
import { useEffect, useRef, useState } from 'react';
import PromotionsCardSmall from './PromotionsCardSmall';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { sortItems } from '@/utils/sortItems.ts';
import { PROMO_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/promoBreakpoints.ts';
import { useTranslation } from '@/app/i18n/client';
import { theme } from '@/theme.ts';
import Link from 'next/link';
import SliderArrowsSimple from '@/app/components/Levels/SliderArrowsSimple.tsx';
import { useIsLastSliderElementFullyVisible } from '@/hooks/useCheckIsLastSliderElementFullyVisible.ts';
import useWindowSize from '@/hooks/useWindowSize.ts';
import LoadingPromoCard from '@/app/components/Skeletons/LoadingPromoCard.tsx';

const PromotionsClientCarousel = ({
  promotionsAll,
  promotionsInitial,
  lng,
  gamePageType,
}: {
  promotionsAll: IPromotionsResponse;
  promotionsInitial: IPromotionsResponse;
  lng: LanguagesType;
  gamePageType: GamePageType;
}) => {
  const promoSliderRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const isLastElementFullyVisible = useIsLastSliderElementFullyVisible('lastPromo');

  const { t } = useTranslation(lng);

  const { width } = useWindowSize();
  const isMobile = width && width <= 520;
  const isTablet = width && width <= theme.breakpoints?.lg;

  const slidesPerView = isMobile ? 1.2 : isTablet ? 2 : 3;
  const spacingSum = isMobile ? 8 : isTablet ? 16 : 32;

  const sliderWidth =
    width && promoSliderRef && promoSliderRef.current ? promoSliderRef.current.offsetWidth : null;

  const [cardWidth, setCardWidth] = useState<number | null>(
    sliderWidth ? (sliderWidth - spacingSum) / slidesPerView : null,
  );

  useEffect(() => {
    if (!sliderWidth) return;
    setCardWidth((sliderWidth - spacingSum) / slidesPerView);
  }, [sliderWidth, spacingSum, slidesPerView]);

  const { user, isFirstLoadDone } = useUser();
  const isUserAuthorized = !!user?.id;

  const [{ constants }] = useSystemData();
  const promotionsConstant = constants?.find((constant) => constant.key === 'promotions_order');
  const promotionsOrderArray = promotionsConstant ? JSON.parse(promotionsConstant.value) : [];

  const [promotions, setPromotions] = useState(
    sortItems({
      items: promotionsInitial.items,
      order: promotionsOrderArray,
    }),
  );

  useEffect(() => {
    if (!isFirstLoadDone) return;
    const { items } = filterPromotions(promotionsAll.items, isUserAuthorized, promotionsOrderArray);
    setPromotions(items);
  }, [isUserAuthorized, isFirstLoadDone]);

  return (
    <div className='promotionsCarousel flex flex-col gap-y-1 md:gap-y-4' ref={promoSliderRef}>
      {/*<Typography type='h1' text={t('promotions')} iconName='loudspeaker' />*/}
      <div className='flex w-full justify-between'>
        <Typography type='h1' text={t('promotions')} iconName='loudspeaker' />
        <div className='flex gap-2'>
          <Link href={`/${lng}/${gamePageType}/promotions`} className='ml-auto'>
            <Button text={t('all')} variant='secondary' className='max-md:!hidden' />
            <Button text={t('all')} size='small' variant='secondary' className='md:!hidden' />
          </Link>
          <SliderArrowsSimple
            elementRef={cardRef}
            elementWidth={(cardWidth || 0) + (isMobile ? 8 : 16)}
            scrollElementsCount={2}
            isLastElementFullyVisible={isLastElementFullyVisible}
          />
        </div>
      </div>
      {!cardWidth && (
        <Slider
          items={Array(8).fill(<LoadingPromoCard />)}
          total={8}
          Slide={LoadingPromoCard}
          isCarousel
          perView={3}
          buttonText='all'
          buttonHref='/casino/promotions'
          breakpoints={PROMO_SLIDER_BREAKPOINTS}
        />
      )}
      {cardWidth && (
        <div className='promoSliderWrapper relative flex h-full overflow-y-auto' ref={cardRef}>
          {promotions.map((promo, index) => (
            <PromotionsCardSmall
              item={promo}
              key={index}
              index={index}
              id={index === promotions.length - 1 ? 'lastPromo' : ''}
              cardWidth={cardWidth}
              isSmallerTitle={width ? width <= theme.breakpoints?.md : false}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default PromotionsClientCarousel;
