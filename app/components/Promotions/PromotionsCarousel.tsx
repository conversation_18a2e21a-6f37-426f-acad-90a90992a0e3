import { getPromotions } from '@/app/api/getPromotions';
import { GamePageType, LanguagesType } from '@/types/global';
import { filterPromotions } from '@/utils/filterPromotions';
import 'keen-slider/keen-slider.min.css';
import { cookies } from 'next/headers';
import PromotionsClientCarousel from './PromotionsClientCarousel';

const PromotionsCarousel = async ({
  lng,
  gamePageType,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
}) => {
  const isUserAuthorized = !!cookies()?.get('token')?.value;
  const promotionsAll = await getPromotions(0, 50);
  const promotionsFiltered = filterPromotions(
    promotionsAll.items,
    isUserAuthorized,
  );

  return (
    <PromotionsClientCarousel
      promotionsInitial={promotionsFiltered}
      promotionsAll={promotionsAll}
      lng={lng}
      gamePageType={gamePageType}
    />
  );
};

export default PromotionsCarousel;
