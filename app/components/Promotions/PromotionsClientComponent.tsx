'use client';
import { IPromotionsResponse } from '@/app/api/getPromotionsServer.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { filterPromotions } from '@/utils/filterPromotions';
import { useEffect, useState } from 'react';
import PromotionsList from './PromotionsList';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { sortItems } from '@/utils/sortItems.ts';
import { useTranslation } from '@/app/i18n/client';

const PromotionsClientComponent = ({
  promotionsAll,
  promotionsInitial,
  lng,
  limit,
}: {
  promotionsAll: IPromotionsResponse;
  promotionsInitial: IPromotionsResponse;
  lng: LanguagesType;
  limit: number;
}) => {
  const { t } = useTranslation(lng);

  const { user, isFirstLoadDone } = useUser();
  const isUserAuthorized = !!user?.id;

  const [{ constants }] = useSystemData();
  const promotionsConstant = constants?.find(
    (constant) => constant.key === 'promotions_order',
  );
  const promotionsOrderArray = promotionsConstant
    ? JSON.parse(promotionsConstant.value)
    : [];

  const [promotions, setPromotions] = useState(
    sortItems({
      items: promotionsInitial.items,
      order: promotionsOrderArray,
    }),
  );

  useEffect(() => {
    if (!isFirstLoadDone) return;
    const { items } = filterPromotions(
      promotionsAll.items,
      isUserAuthorized,
      promotionsOrderArray,
    );
    setPromotions(items);
  }, [isUserAuthorized, isFirstLoadDone]);

  return (
    <div className='promotionsPage'>
      <Typography text={t('promotions')} type='h1' iconName='loudspeaker' />

      <PromotionsList
        initialData={promotions}
        limit={limit}
        totalItems={promotions.length}
        promotionsOrderArray={promotionsOrderArray}
      />
    </div>
  );
};

export default PromotionsClientComponent;
