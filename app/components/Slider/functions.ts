import { KeenSliderInstance } from 'keen-slider';

export const autoplayInit = (
  slider: KeenSliderInstance,
  autoplayTimeout: number,
) => {
  if (!slider?.track) {
    return;
  }

  let timeout: ReturnType<typeof setTimeout>;
  let mouseOver = false;
  const clearNextTimeout = () => {
    clearTimeout(timeout);
  };
  const nextTimeout = () => {
    clearTimeout(timeout);
    if (mouseOver || !slider.track?.details) return;
    timeout = setTimeout(() => {
      slider?.next();
    }, autoplayTimeout);
  };

  slider.on('created', () => {
    slider.container.addEventListener('mouseover', () => {
      mouseOver = true;
      clearNextTimeout();
    });
    slider.container.addEventListener('mouseout', () => {
      mouseOver = false;
      nextTimeout();
    });
    nextTimeout();
  });
  slider.on('dragStarted', clearNextTimeout);
  slider.on('animationEnded', nextTimeout);
  slider.on('updated', nextTimeout);
  slider.on('slideChanged', nextTimeout);
};

export const wheelEventInit = (slider: KeenSliderInstance) => {
  let touchTimeout: ReturnType<typeof setTimeout>;
  let position: {
    x: number;
    y: number;
  };
  let wheelActive: boolean;

  const dispatch = (e: WheelEvent, name: string) => {
    position.x -= e.deltaX;
    position.y -= e.deltaY;
    slider.container.dispatchEvent(
      new CustomEvent(name, {
        detail: {
          x: position.x,
          y: position.y,
        },
      }),
    );
  };

  const wheelStart = (e: WheelEvent) => {
    position = {
      x: e.pageX,
      y: e.pageY,
    };
    dispatch(e, 'ksDragStart');
  };

  const wheel = (e: WheelEvent) => {
    dispatch(e, 'ksDrag');
  };

  const wheelEnd = (e: WheelEvent) => {
    dispatch(e, 'ksDragEnd');
  };

  const eventWheel = (e: WheelEvent) => {
    e.stopPropagation();

    if (Math?.abs(e.deltaX) < Math?.abs(e.deltaY)) {
      return;
    }
    e.preventDefault();
    if (!wheelActive) {
      wheelStart(e);
      wheelActive = true;
    }
    wheel(e);
    clearTimeout(touchTimeout);
    touchTimeout = setTimeout(() => {
      wheelActive = false;
      wheelEnd(e);
    }, 50);
  };

  slider.on('created', () => {
    slider.container.addEventListener('wheel', eventWheel, {
      passive: false,
    });
  });
};
