'use client';
import Arrow from '@/app/components/Slider/Arrow.tsx';
import clsx from 'clsx';
import { useKeenSlider } from 'keen-slider/react';
import { useEffect, useState } from 'react';

import { getGames } from '@/app/api/getGames.ts';
import {
  autoplayInit,
  wheelEventInit,
} from '@/app/components/Slider/functions.ts';
import { StyledSlider } from '@/app/components/Slider/styled.ts';
import { Button } from '@/atomic-design-components';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import 'keen-slider/keen-slider.min.css';
import { usePathname, useRouter } from 'next/navigation';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { useCookies } from 'react-cookie';

// import './styles.css';

export interface ISliderProps {
  autoplay?: boolean;
  breakpoints?: any;
  className?: string;
  filters?: any;
  isArrowShownOnHover?: boolean;
  isArrowShownOnMobile?: boolean;
  isCarousel?: boolean;
  items: any[];
  limit?: number;
  loop?: boolean;
  loopedSlides?: number;
  maxLimit?: number;
  mode?: 'free' | 'snap' | 'free-snap';
  opacitiesInitial?: number[] | null;
  perView?: number | 'auto';
  Slide: any;
  slideProps?: { [key: string]: any };
  sort?: string;
  spacing?: number;
  startFromZeroIndex?: boolean;
  systemData?: any;
  total: number;
  withArrows?: boolean;
  withAutosizeItems?: boolean;
  withDots?: boolean;
  withLoadMore?: boolean;
  renderMode?: 'performance' | 'precision' | 'custom' | undefined;
  buttonText?: string;
  buttonHref?: string;
  isBadgeShown?: boolean;
  selector?: string;
  isSliding?: boolean;
  customLoadMore?: any;
  onButtonClick?: Function;
  isUpdateTriggered?: boolean;
}

const Slider = ({
  autoplay = false,
  breakpoints,
  className = '',
  filters,
  isArrowShownOnHover = false,
  isArrowShownOnMobile = false,
  isCarousel = false,
  startFromZeroIndex,
  items: itemsInitial = [],
  limit = 36,
  loop = false,
  loopedSlides = 0,
  maxLimit = 750,
  mode = 'free',
  onButtonClick,
  opacitiesInitial = null,
  perView = 1,
  Slide,
  slideProps,
  sort,
  spacing = 16,
  systemData,
  total,
  renderMode,
  buttonText,
  buttonHref,
  isBadgeShown,
  selector = '.keen-slider__slide',
  withArrows = true,
  withAutosizeItems = false,
  withDots = false,
  withLoadMore = false,
  isSliding = false,
  isUpdateTriggered,
  customLoadMore,
}: ISliderProps) => {
  const pathname = usePathname();
  const { isTouchDevice } = useIsTouchMobileView();

  const { width } = useWindowSize();
  const isMobile = (width && width < theme.breakpoints?.md) || isTouchDevice;

  const [cookies] = useCookies(['userCurrency']);

  const [loaded, setLoaded] = useState(false);

  const [currentSlide, setCurrentSlide] = useState(0);
  const [sliding, setSliding] = useState(false);
  const [opacities, setOpacities] = useState<number[] | null>(opacitiesInitial);

  const [initSlideIdx, setInitSlideIdx] = useState(0);
  const [items, setItems] = useState(itemsInitial);
  const [skip, setSkip] = useState(limit);
  const [inProgress, setInProgress] = useState(false);

  const circularAnimation = { duration: 70000, easing: (t: number) => t };
  const linearAnimation = { duration: 500, easing: (t: number) => t };

  const router = useRouter();

  const maxTotal = maxLimit < total ? maxLimit : total;

  useEffect(() => {
    if (isSliding) {
      const currSlide = items?.findIndex((item: any) =>
        pathname.includes(item.route),
      );

      if (currSlide !== -1) {
        setInitSlideIdx(currSlide);
      } else {
        setInitSlideIdx(0);
      }
      setSliding(true);
    }
  }, [pathname]);

  useEffect(() => {
    setItems(itemsInitial);
  }, [itemsInitial]);

  const loadMore = async (customLimit?: number) => {
    if (maxTotal <= skip) {
      return;
    }
    setInProgress(true);

    if (customLoadMore) {
      const res = await customLoadMore(skip);
      setItems((prev) => {
        return [...prev, ...res];
      });
    } else {
      const { items: newItemsData } = await getGames(
        skip,
        customLimit || limit,
        cookies?.userCurrency,
        filters,
        sort,
      );

      const newItems =
        maxLimit < total ? newItemsData.slice(0, maxLimit) : newItemsData;
      setItems((prev) => {
        return [...prev, ...newItems];
      });
    }
    setSkip((prev) => prev + (customLimit || limit));
    setInProgress(false);
  };

  const [sliderRef, instanceRef] = useKeenSlider<HTMLDivElement>(
    {
      drag: true,
      dragSpeed: 0,
      initial: initSlideIdx,
      rubberband: false,
      slides: {
        perView: withAutosizeItems ? 'auto' : perView,
        number: items?.length,
        spacing,
      },
      selector,
      loop,
      mode: mode,
      renderMode: withAutosizeItems ? 'custom' : renderMode,
      defaultAnimation: { duration: 2000 },
      detailsChanged(s) {
        if (withAutosizeItems) {
          if (!s?.container) {
            return;
          }
          const containerWidth = s.container.offsetWidth;
          if (!s.slides?.length) {
            return;
          }
          s.slides.forEach((slide, idx) => {
            if (!slide || !s?.track?.details?.slides?.[idx]) {
              return;
            }
            slide.style.left =
              s.track.details.slides[idx].distance * containerWidth + 'px';
          });
        }

        if (!opacitiesInitial || !s?.track?.details?.slides) {
          return;
        }
        const new_opacities = s.track.details.slides.map(
          (slide) => slide?.portion,
        );
        setOpacities(new_opacities);
      },
      slideChanged(slider) {
        if (slider?.track) {
          setCurrentSlide(slider.track.details.rel);
        }
      },
      created(s) {
        setLoaded(true);

        if (loop && s) {
          s.moveToIdx(loopedSlides, true, circularAnimation);
        }
      },

      updated(s) {
        if (loop && s?.track?.details) {
          s.moveToIdx(
            s.track.details?.abs + loopedSlides,
            true,
            circularAnimation,
          );
        }
      },
      animationStopped() {
        if (withLoadMore && !inProgress && maxTotal >= skip!) {
          (async () => {
            await loadMore();
          })();
        }
      },
      animationEnded(s) {
        if (withLoadMore && !inProgress && maxTotal >= skip!) {
          (async () => {
            await loadMore();
          })();
        }

        if (loop && s?.track?.details && !autoplay) {
          s.moveToIdx(
            s.track.details?.abs + loopedSlides,
            true,
            circularAnimation,
          );
        }
      },
      breakpoints,
    },
    [
      (slider) => {
        if (autoplay) {
          autoplayInit(slider, 6500);
        } else {
          wheelEventInit(slider);
        }
      },
    ],
  );

  useEffect(() => {
    if (!sliding || !instanceRef.current?.track?.details) return;
    const idx =
      initSlideIdx > instanceRef.current.track.details.maxIdx
        ? instanceRef.current.track.details.maxIdx
        : initSlideIdx;

    instanceRef.current.moveToIdx(idx, true, linearAnimation);
    setSliding(false);
  }, [sliding]);

  useEffect(() => {
    if (startFromZeroIndex && instanceRef.current) {
      instanceRef.current.moveToIdx(0, true);
    }
  }, [startFromZeroIndex]);

  useEffect(() => {
    if (isUpdateTriggered) {
      instanceRef.current?.update();
      if (startFromZeroIndex && instanceRef.current) {
        instanceRef.current.moveToIdx(0, true);
      }
    }
  }, [isUpdateTriggered]);

  const getSliderWithoutWrapper = () => {
    return (
      <div
        ref={sliderRef}
        className={clsx(
          opacitiesInitial ? 'fader' : 'keen-slider',
          loop && 'keen-slider--loop',
        )}
      >
        {items?.map((item, idx) => {
          return (
            <div
              key={idx}
              className={clsx(
                opacitiesInitial ? 'fader__slide' : 'keen-slider__slide',
                loaded && 'loaded',
                currentSlide === idx && 'active',
              )}
              style={
                opacities
                  ? { opacity: opacities?.[idx] }
                  : withAutosizeItems
                    ? { position: 'absolute', width: 'auto' }
                    : undefined
              }
            >
              <Slide
                item={item}
                index={idx}
                loaded={loaded}
                systemData={systemData}
                isBadgeShown={isBadgeShown}
                {...slideProps}
              />
            </div>
          );
        })}
      </div>
    );
  };
  const getPerView = () => {
    const sl: any = instanceRef?.current?.options?.slides;
    return sl?.perView;
  };

  const isArrowShownMobile = !isTouchDevice || isArrowShownOnMobile;

  const isWithArrows =
    withArrows &&
    isArrowShownMobile &&
    items.length > (getPerView() || perView);

  if (withAutosizeItems) {
    return getSliderWithoutWrapper();
  }

  return (
    <>
      <StyledSlider
        className={clsx(
          'navigation-wrapper',
          className,
          isArrowShownOnHover && 'isArrowShownOnHover',
        )}
        perView={perView}
        breakpoints={breakpoints}
        role='presentation'
      >
        {getSliderWithoutWrapper()}

        {loaded && items?.length > 1 && instanceRef.current && (
          <>
            {buttonText && buttonHref && (
              <Button
                text={buttonText}
                onClick={() => {
                  if (onButtonClick) {
                    onButtonClick();
                  }
                  router.push(buttonHref);
                }}
                size={isMobile ? 'small' : 'medium'}
                variant='secondary'
                className={clsx(
                  'button',
                  isCarousel && 'carousel',
                  isMobile && 'mobile',
                  isWithArrows && 'withArrows',
                )}
              />
            )}
            {isWithArrows && (
              <>
                <Arrow
                  left
                  disabled={!loop && currentSlide === 0}
                  isCarousel={isCarousel}
                  instanceRef={instanceRef}
                  loop={loop}
                  currentSlide={currentSlide}
                  // @ts-ignore
                  perView={instanceRef?.current?.options?.slides?.perView}
                  totalCurrent={items.length}
                  isArrowShownOnHover={isArrowShownOnHover}
                  // total={total}
                />
                <Arrow
                  disabled={
                    !loop &&
                    currentSlide +
                      // @ts-ignore
                      instanceRef?.current?.options?.slides?.perView >=
                      maxTotal
                  }
                  isCarousel={isCarousel}
                  instanceRef={instanceRef}
                  currentSlide={currentSlide}
                  loop={loop}
                  // @ts-ignore
                  perView={instanceRef?.current?.options?.slides?.perView}
                  totalCurrent={items.length}
                  isArrowShownOnHover={isArrowShownOnHover}
                  // total={total}
                  // onArrowCustomClick={withLoadMore ? loadMore : undefined}
                  // skip={skip}
                />
              </>
            )}
          </>
        )}

        {withDots && (
          <div className='dots'>
            {items?.map((_, idx) => {
              return (
                <button
                  key={idx}
                  onClick={() => {
                    instanceRef.current?.moveToIdx(idx);
                  }}
                  className={'dot' + (currentSlide === idx ? ' active' : '')}
                />
              );
            })}
          </div>
        )}
      </StyledSlider>
    </>
  );
};
export default Slider;
