'use client';
import styled, { css } from 'styled-components';

export const StyledSlider: any = styled.div<any>`
  width: 100%;

  &.isArrowShownOnHover {
    &:hover {
      .arrow {
        opacity: 1;
      }
    }
  }

  &.navigation-wrapper {
    position: relative;
  }

  &.banners {
    .fader__slide.active {
      z-index: 2;
    }

    .arrow--left {
      left: 16px;
    }

    .arrow--right {
      right: 16px;
    }
  }

  .fader {
    //height: 300px;
    position: relative;
    overflow: hidden;

    img {
      background-color: transparent;
      width: 100%;
      height: 100%;
      object-fit: cover;
      position: absolute;
    }
  }

  .fader__slide {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    will-change: opacity;
    transition: opacity 500ms linear;
    opacity: 0;
  }

  .keen-slider__slide.loaded {
    margin-right: 0;
  }

  .dots {
    display: flex;
    padding: 10px 0;
    justify-content: center;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
  }

  .dot {
    border: none;
    width: 24px;
    height: 6px;
    background: #fff;
    border-radius: 38px;
    margin: 0 12px 6px;
    cursor: pointer;
    animation: narrowing 1s forwards;
    z-index: 5;
  }

  .dot:focus {
    outline: none;
  }

  .dot.active {
    background: #0083ff;
    width: 52px;
    animation: expansion 1s forwards;
  }

  .arrow {
    width: 40px;
    height: 40px;
    background-color: #1e293b;
    border-radius: 8px;
    position: absolute;
    top: 50%;
    fill: #fff;
    cursor: pointer;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;

    &.isArrowShownOnHover {
      opacity: 0;
      transition: opacity 0.3s;
    }
  }

  .button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    fill: #fff;
    cursor: pointer;
    padding: 12px 16px 13px;
  }

  .carousel.arrow {
    top: -24px;
    background-color: #1e293b;
    @media screen and (min-width: 960px) {
      top: -36px;
    }
  }

  .carousel.button {
    top: -24px;
    right: 0;

    &.withArrows {
      right: 96px;
    }

    .mobile {
      right: 0;
    }

    @media screen and (min-width: 960px) {
      top: -36px;
    }
  }

  .arrow--left {
    left: -10px;
  }

  .arrow--right {
    left: auto;
    right: -10px;
  }

  .carousel.arrow--left {
    left: auto;
    right: 48px;
  }

  .carousel.arrow--right {
    right: 0;
  }

  .arrow.arrow--disabled {
    fill: #8b8d8fd9;
    background-color: #1e293b;
  }

  .keen-slider__slide:not(.loaded) {
    margin-right: 15px;

    min-width: calc(
      100% / ${({ perView }: { perView: number }) => perView} - calc(
          15px / ${({ perView }: { perView: number }) => perView} *
            (${({ perView }: { perView: number }) => perView} - 1)
        )
    );
    max-width: calc(
      100% / ${({ perView }: { perView: number }) => perView} - calc(
          15px / ${({ perView }: { perView: number }) => perView} *
            (${({ perView }: { perView: number }) => perView} - 1)
        )
    );
    ${({ breakpoints }: { breakpoints: any }) => {
      if (!breakpoints) return;
      return Object.entries(breakpoints).map(([key, value]: [string, any]) => {
        return css`
          @media ${key} {
            min-width: calc(
              100% / ${value.slides.perView} - calc(
                  ${value.slides.spacing}px / ${value.slides.perView} *
                    (${value.slides.perView} - 1)
                )
            );
            max-width: calc(
              100% / ${value.slides.perView} - calc(
                  ${value.slides.spacing}px / ${value.slides.perView} *
                    (${value.slides.perView} - 1)
                )
            );
            margin-right: ${value.slides.spacing}px;
          }
        `;
      });
    }};
  }

  @keyframes expansion {
    0% {
      width: 24px;
    }
    100% {
      width: 52px;
    }
  }

  @keyframes narrowing {
    0% {
      width: 52px;
    }
    100% {
      width: 24px;
    }
  }
`;
