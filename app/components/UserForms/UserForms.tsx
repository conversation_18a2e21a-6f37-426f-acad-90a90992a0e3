'use client';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { ReCaptchaProvider } from '@/app/wrappers/ReCaptchaProvider';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { useUser } from '@/app/wrappers/userProvider';
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from 'next/navigation';
import { verifyCode } from '@/app/api/server-actions/forgotPassword.ts';
import { setUserDataAfterLogin } from '@/app/api/server-actions/login.ts';
import { openModal } from '@/utils/openModal.ts';
import ResetPasswordModal from '@/app/components/ResetPasswordForm/ResetPasswordModal.tsx';
import { useTranslation } from '@/app/i18n/client';

const NotificationsModal = dynamic(
  () => import('@/app/components/Notifications/NotificationsModal.tsx'),
  { ssr: false },
);
const CashierModal = dynamic(() => import('../Cashier/CashierModal'));
const GetBonusModal = dynamic(() => import('../GetBonusModal/GetBonusModal'));
const EmailVerificationModal = dynamic(
  () => import('../Verifications/EmailVerificationModal'),
  { ssr: false },
);
const PhoneVerificationModal = dynamic(
  () => import('../Verifications/PhoneVerificationModal'),
  { ssr: false },
);
const AuthModal = dynamic(() => import('./AuthModal'));

export default function UserForms() {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user, setUser } = useUser();
  const isUserAuthorized = !!user?.id;

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const action = searchParams.get('action') || '';
  const paramsCode = searchParams.get('code') || '';
  const { showError, showAlert } = useAlert();

  const { setInProgressCodeCheck } = useUser();

  const [tokenDataAfterCodeVerified, setTokenDataAfterCodeVerified] =
    useState<any>('');

  const getIsEmailCodeVerified = async (
    code: string,
    code_type: 'confirm_email' | 'reset_password',
  ) => {
    const requestData = { code, code_type };
    const result = await verifyCode(requestData);

    if (!result || result.error) {
      showError(t('invalidCode'));
      setInProgressCodeCheck(false);
      return;
    }

    setInProgressCodeCheck(false);

    if (code_type === 'confirm_email') {
      if (!isUserAuthorized) {
        const userData = await setUserDataAfterLogin(result.auth);
        setUser(userData);
      }
      showAlert({
        content: t('emailConfirmed'),
        id: Date.now().toString(),
        type: 'success',
        timeout: 3000,
      });
      router.push(`/${lng}`);
    }

    if (code_type === 'reset_password') {
      setTokenDataAfterCodeVerified(result.auth);
      const resetPasswordModal = document.getElementById('resetPasswordModal');

      if (resetPasswordModal) {
        resetPasswordModal.setAttribute(
          'data-active-screen-verify',
          'password',
        );
        setTimeout(() => {
          openModal('resetPasswordModal');
        }, 1000);
      }
    }

    return result;
  };

  useEffect(() => {
    if (paramsCode && !action) {
      setInProgressCodeCheck(false);
      showError(t('invalidLink'));
      return;
    }
    if (paramsCode && ['confirm_email', 'reset_password'].includes(action)) {
      if (action === 'reset_password' && isUserAuthorized) {
        showError(t('youAreAuthorized'));
        setInProgressCodeCheck(false);
        router.replace(pathname);
        return;
      }
      getIsEmailCodeVerified(
        paramsCode,
        action as 'confirm_email' | 'reset_password',
      );
    }
  }, []);

  if (isUserAuthorized)
    return (
      <>
        <NotificationsModal />
        <CashierModal />
      </>
    );

  return (
    <>
      <ReCaptchaProvider>
        <AuthModal />
        <ResetPasswordModal
          tokenDataAfterCodeVerified={tokenDataAfterCodeVerified}
        />
        <GetBonusModal />
        <EmailVerificationModal />
        <PhoneVerificationModal />
      </ReCaptchaProvider>
    </>
  );
}
