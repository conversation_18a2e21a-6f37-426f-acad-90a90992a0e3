'use client';
import { getRecommendedGamesClient } from '@/app/api/user/games/getRecommendedGamesClient.ts';
import GameCard from '@/app/components/Games/GameCard.tsx';
import Slider from '@/app/components/Slider/Slider.tsx';
import { useTranslation } from '@/app/i18n/client';
import { Icon, Typography } from '@/atomic-design-components';
import { GAMES_SLIDER_IN_MODAL_BREAKPOINTS } from '@/atomic-design-components/molecules/Search/ProposedGames';
import { LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal.ts';
import { useEffect, useState } from 'react';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';

const LIMIT = 24;

const RecommendedGamesCarousel = ({
  lng,
  filters,
  isDialogOpen,
}: {
  lng: LanguagesType;
  filters?: any;
  isDialogOpen: boolean;
}) => {
  const { t } = useTranslation(lng);

  const [games, setGames] = useState<any>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (!games && isDialogOpen) {
      getRecommendedGamesClient(0, LIMIT, filters)
        .then((data) => {
          setGames({ items: data.items, total: data.total });
        })
        .catch((error) => {
          console.error(error);
          setGames({ items: [], total: 0 });
        })
        .finally(() => {
          setIsLoaded(true);
        });
    }
  }, [games, isDialogOpen]);

  return (
    <div className='gameCarousel gamesRecommendedCarousel flex flex-col'>
      <div className='mb-[6px] flex md:mb-4'>
        <Icon
          name='star'
          width={18}
          height={18}
          className='mr-[5px] mt-[-5px]'
        />
        <Typography type='h3' lineHeight='40px' text={t('recommended')} />
      </div>
      <Slider
        items={isLoaded ? games.items : Array(6).fill(<LoadingGameCard />)}
        total={isLoaded ? games.total : 6}
        Slide={isLoaded ? GameCard : LoadingGameCard}
        isBadgeShown={true}
        filters={filters}
        isCarousel
        startFromZeroIndex={isDialogOpen}
        limit={LIMIT}
        isUpdateTriggered={isDialogOpen}
        spacing={8}
        buttonText={t('allGames')}
        buttonHref={`/${lng}/casino/games/top`}
        onButtonClick={() => {
          closeModal('continueGameWithZeroWager');
          closeModal('searchModal');
          closeModal('gamePageOverlayModal');
        }}
        breakpoints={GAMES_SLIDER_IN_MODAL_BREAKPOINTS}
      />
    </div>
  );
};

export default RecommendedGamesCarousel;
