'use client';
import { useTranslation } from '@/app/i18n/client';
import { Button, Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { useEffect, useState } from 'react';
interface IProps {
  lng: LanguagesType;
}
const STATUSES = ['notStarted', 'inProgress', 'approved'];

const IdentityVerificationCard = ({ lng }: IProps) => {
  const { t } = useTranslation(lng);
  const [verificationStep, setVerificationStep] = useState(0);
  useEffect(() => {
    setVerificationStep(0);
  }, []);

  return (
    <div className='flex w-full flex-col gap-4'>
      <div className='flex w-full gap-2 rounded-lg bg-[#334155] p-2'>
        <Icon
          name={verificationStep === 2 ? 'checkboxChecked' : 'infoIcon'}
          fill={verificationStep === 2 && theme.color?.status.new}
          stroke={verificationStep === 2 && '#272C39'}
          width={24}
          height={24}
        />
        <Typography
          text={
            verificationStep === 2
              ? t('verificationSuccess')
              : t('verifyIdentity')
          }
        />
      </div>
      <div className='flex justify-center gap-4'>
        <Icon
          name='verificationPlanet'
          fill={verificationStep === 0 && theme.color?.primary.main}
        />
        <Icon name='chevronDown' style={{ transform: 'rotate(-90deg)' }} />
        <Icon
          name='fingerprint'
          fill={verificationStep === 1 && theme.color?.primary.main}
        />
        <Icon name='chevronDown' style={{ transform: 'rotate(-90deg)' }} />
        <Icon
          name='verificationShield'
          fill={verificationStep === 2 && theme.color?.status.new}
        />
      </div>
      <div className='flex justify-center gap-1'>
        <Typography text={`${t('status')}:`} type='sub1' />
        <Typography
          text={t(STATUSES[verificationStep])}
          type='sub1'
          color={
            verificationStep === 2
              ? theme.color?.status.new
              : theme.color?.primary.main
          }
        />
      </div>

      <Button text={t('startVerification')} fullWidth disabled />
    </div>
  );
};

export default IdentityVerificationCard;
