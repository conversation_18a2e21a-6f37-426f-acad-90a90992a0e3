import { IHistoryItem } from '@/app/api/user/getHistoryClient.ts';
import { useTranslation } from '@/app/i18n/client';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { transformDate } from '@/utils/dates';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const HistoryCard = ({
  item,
  currency,
  lng,
}: {
  item: IHistoryItem;
  currency: string;
  lng: LanguagesType;
}) => {
  const { t } = useTranslation(lng);
  const winText =
    item.win_amount > 0
      ? `+${getCurrencySymbol(currency)}${addSpacesToLongNumbers(item.win_amount)}`
      : addSpacesToLongNumbers(item.win_amount || 0);

  return (
    <div className='flex w-full flex-col gap-2 rounded-lg bg-[#1E293B] p-4'>
      <div className='flex justify-between'>
        <Typography
          text={item.game_external_id}
          color={theme.color?.general.white}
        />
        <Typography
          text={transformDate(item.date, 'DD.MM.YYYY HH:mm')}
          color={theme.color?.general.white}
        />
      </div>
      <div className='flex justify-between'>
        <Typography
          type='label1'
          text={`${t('bet')}: ${getCurrencySymbol(currency)}${addSpacesToLongNumbers(item.bet_amount)}`}
          className='w-[28%]'
        />
        <Typography
          type='label1'
          text={`${t('win')}: ${winText}`}
          color={
            item.win_amount > 0
              ? theme.color?.status.success
              : theme.color?.general.lightest
          }
          className='w-[28%]'
        />
        <Typography
          type='label1'
          text={`${t('balance')}: ${getCurrencySymbol(currency)}${addSpacesToLongNumbers(item.balance)}`}
          className='w-[46%]'
          justifyContent='end'
        />
      </div>
    </div>
  );
};

export default HistoryCard;
