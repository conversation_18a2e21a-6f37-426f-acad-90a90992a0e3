'use client';
import { useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import Loading from '@/app/[lng]/[gamePageType]/profile/(dashboard)/loading';
import { signOut } from '@/app/api/server-actions/signOut.ts';
import ModalDialog from '@/app/components/ModalDialog';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import useClickOutside from '@/hooks/useClickOutside';
import { closeModal } from '@/utils/closeModal';
import { Button, FlexRow } from '@/atomic-design-components';

const SignOutModal = () => {
  const router = useRouter();
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const ref = useRef(null);
  useClickOutside(ref);

  const { user } = useUser();
  // const [, setSystemData] = useSystemData();

  useEffect(() => {
    // Prefetch the home page
    router.prefetch(`/${lng}`);
  }, [router, lng]);

  if (!user) {
    return <Loading />;
  }

  return (
    <ModalDialog
      id='signOutModal'
      ref={ref}
      title={t('wantSignOut')}
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-6'
    >
      <FlexRow
        gap='10px'
        className='buttonContainer px-4 pb-3 sm:pb-6'
        alignItems='center'
        justifyContent='end'
      >
        <Button
          className='screenButton'
          textTransform='uppercase'
          type='label2'
          text={t('no')}
          onClick={(e: any) => {
            e.preventDefault();
            closeModal('signOutModal');
          }}
          variant={'secondary'}
        />
        {/*<Link href={`/${lng}`} className='max-sm:w-full' prefetch>*/}
        <Button
          className='screenButton'
          textTransform='uppercase'
          type='label2'
          text={t('yes')}
          onClick={async () => {
            await signOut(lng);
            // setSystemData((prev) => ({ ...prev, favourites: null }));
            closeModal('signOutModal');
            // setUser(null);
            // router.push(`/${lng}`);
          }}
        />
        {/*</Link>*/}
      </FlexRow>
    </ModalDialog>
  );
};

SignOutModal.displayName = 'SignOutModal';
export default SignOutModal;
