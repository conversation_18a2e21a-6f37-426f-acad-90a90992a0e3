'use client';
import { updatePassword } from '@/app/api/server-actions/updatePassword';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { Button, Input } from '@/atomic-design-components';
import { useIsPasswordShown } from '@/hooks/useIsPasswordShown';
import { LanguagesType } from '@/types/global';
import validate, { rule } from '@/utils/formValidationRules';
import { isObjectEmpty } from '@/utils/object';
import { useState } from 'react';
import CustomPasswordChecklist from '@/app/components/Form/CustomPasswordChecklist.tsx';
import { toCamelCase } from '@/utils/toCamelCase.ts';

interface IProps {
  lng: LanguagesType;
}

const SafetyCard = ({ lng }: IProps) => {
  const { t } = useTranslation(lng, 'validation');
  const { showAlert } = useAlert();

  const [password, setPassword] = useState('');
  const [new_password, setNewPassword] = useState('');
  const [isPasswordValid, setIsPasswordValid] = useState(undefined);

  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [errors, setErrors] = useState<any>({});
  const [inProgress, setInProgress] = useState(false);

  const validationRulesInitial = {
    password: ['required'],
    new_password: ['required', 'password'],
  };

  const initialValuesChangedBeforeCheck = {
    password: password,
    new_password: new_password,
  };

  const submitForm = async () => {
    setIsSubmitPressed(true);
    const errors = validate(validationRulesInitial)(
      initialValuesChangedBeforeCheck,
      {
        values: initialValuesChangedBeforeCheck,
      },
    );
    setErrors(errors);

    if (isObjectEmpty(errors)) {
      setInProgress(true);
      let requestBody: { old_password: string; new_password: string } = {
        old_password: password,
        new_password: new_password,
      };

      const result: any = await updatePassword(requestBody);
      setInProgress(false);

      if (result.detail?.message) {
        setErrors((prev: any) => ({
          ...prev,
          password: toCamelCase(result.detail.parameters.error_text), // wrong password
        }));
        return;
      }

      showAlert({
        content: t('passwordUpdated'),
        id: Date.now().toString(),
        type: 'success',
        timeout: 3000,
      });

      setPassword('');
      setNewPassword('');
      setIsPasswordValid(undefined);
      setIsSubmitPressed(false);
    }
  };

  const onChangeField = (name: string, value: string, setState: Function) => {
    const error = rule(name, validationRulesInitial)(
      value,
      initialValuesChangedBeforeCheck,
    );
    let newErrors = { ...errors };
    if (error[name]) {
      setErrors({ ...newErrors, [name]: error[name] });
    } else {
      delete newErrors[name];
      setErrors({ ...newErrors });
    }

    setState(value);
  };

  return (
    <div className='flex w-full flex-col gap-4'>
      <div className='flex w-full flex-col gap-4'>
        <Input
          fullWidth
          name='password'
          labelTop={t('currPassword')}
          {...useIsPasswordShown()}
          onChange={(e: any) => {
            const { value } = e.target;
            onChangeField('password', value, setPassword);
          }}
          error={
            (errors.password === 'wrongPassword' || isSubmitPressed) &&
            errors.password
          }
          disabled={inProgress}
          value={password}
        />
        <div>
          <Input
            fullWidth
            name='new_password'
            labelTop={t('newPassword')}
            // labelBottom={t('lettersAndDigits')}
            {...useIsPasswordShown()}
            onChange={(e: any) => {
              const { value } = e.target;
              onChangeField('new_password', value, setNewPassword);
            }}
            error={isSubmitPressed && !!errors.new_password}
            disabled={inProgress}
            value={new_password}
          />
          <CustomPasswordChecklist
            password={new_password}
            setIsPasswordValid={setIsPasswordValid}
            className={isSubmitPressed && !isPasswordValid ? 'passError' : ''}
          />
        </div>
      </div>

      <Button
        text={t('changePassword')}
        fullWidth
        size='medium'
        onClick={(e: any) => {
          e.stopPropagation();
          submitForm();
        }}
        disabled={inProgress || !password || !new_password}
      />
    </div>
  );
};

export default SafetyCard;
