'use client';
import { IHistoryItem, getHistory } from '@/app/api/server-fetches/getHistory.ts';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Select, Typography } from '@/atomic-design-components';
import { DEFAULT_CURRENCY } from '@/constants.ts';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { getEndDateTime, getStartDateTime, transformDate } from '@/utils/dates';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import ReactTexty from 'react-texty';
import EmptyTable from '../TableComponent/EmptyTable';
import TableComponent from '../TableComponent/TableComponent';
import HistoryCard from './HistoryCard';
import Loading from '@/app/[lng]/[gamePageType]/profile/history/loading.tsx';
import { useGlobalNotificationState } from '@/app/wrappers/globalNotificationProvider';
import clsx from 'clsx';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

interface TableProps {
  lng: LanguagesType;
}

interface IRangeProps {
  id: number;
  start: string;
  end: string;
}

const LIMIT = 500;

const AccountHistoryTable = ({ lng }: TableProps) => {
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;
  const { t } = useTranslation(lng);

  const { user } = useUser();

  const [history, setHistory] = useState<IHistoryItem[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [inProgress, setInProgress] = useState(true);

  const [range, setRange] = useState<IRangeProps>({
    id: 7,
    start: getStartDateTime(dayjs().subtract(6, 'day')),
    end: getEndDateTime(dayjs().subtract(0, 'day')),
  });
  const [activeDayFilter, setActiveDayFilter] = useState<null | number>(7);
  const { isGlobalNotificationVisible } = useGlobalNotificationState();

  const lastSevenDaysInDayJsFormat = Array.from({ length: 7 }, (_, i) => {
    return {
      id: i,
      start: getStartDateTime(dayjs().subtract(i, 'day')),
      end: getEndDateTime(dayjs().subtract(i, 'day')),
    };
  });

  const fetchHistory = useCallback(async (range: IRangeProps) => {
    const { items, total } = await getHistory({
      dateFrom: dayjs.utc(range.start).format('YYYY-MM-DD HH:mm:ss'),
      dateTo: dayjs.utc(range.end).format('YYYY-MM-DD HH:mm:ss'),
    });

    setHistory(items);
    setTotalItems(total);
    setInProgress(false);
  }, []);

  useEffect(() => {
    fetchHistory(range);
  }, []);

  if (inProgress) {
    return (
      <Loading
        withoutWrapper
        className={clsx('overflow-hidden',
          isGlobalNotificationVisible
            ? 'h-[calc(100dvh-214px)]'
            : 'h-[calc(100dvh-140px)]',
        )}
      />
    );
  }

  const onFilterChange = (day: any) => {
    if (day && day?.id === activeDayFilter) {
      return;
    }
    if (!day) {
      setRange({
        id: 7,
        start: getStartDateTime(dayjs().subtract(6, 'day')),
        end: getEndDateTime(dayjs().subtract(0, 'day')),
      });
      setActiveDayFilter(7);
      fetchHistory({
        id: 7,
        start: getStartDateTime(dayjs().subtract(6, 'day')),
        end: getEndDateTime(dayjs().subtract(0, 'day')),
      });
    } else {
      setActiveDayFilter(day.id);
      setRange(day);
      fetchHistory(day);
    }
  };

  const columns = [
    {
      game_title: {
        key: 'gameName',
        dataKey: 'game_title',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ game_title }: any) => (
          <Typography type='body2' text={game_title} as={ReactTexty} />
        ),
      },
    },
    {
      date: {
        key: 'date',
        dataKey: 'date',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ date }: any) => (
          <Typography
            type='body2'
            text={transformDate(date, 'DD.MM.YYYY HH:mm')}
          />
        ),
      },
    },

    {
      bet_amount: {
        key: 'bet',
        dataKey: 'bet_amount',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ bet_amount }: any) => (
          <Typography
            type='body2'
            text={`${getCurrencySymbol(user?.currency || DEFAULT_CURRENCY)}${addSpacesToLongNumbers(bet_amount)}`}
          />
        ),
      },
    },
    {
      win_amount: {
        key: 'win',
        dataKey: 'win_amount',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ win_amount }: any) => {
          const text =
            win_amount > 0
              ? `+${getCurrencySymbol(user?.currency || DEFAULT_CURRENCY)}${addSpacesToLongNumbers(win_amount)}`
              : win_amount;
          return (
            <Typography
              type='body2'
              text={text}
              color={
                text !== 0
                  ? theme.color?.status.success
                  : theme.color?.general.lightest
              }
            />
          );
        },
      },
    },
    {
      balance: {
        key: 'balance',
        dataKey: 'balance',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ balance }: any) => (
          <Typography
            type='body2'
            text={`${getCurrencySymbol(user?.currency || DEFAULT_CURRENCY)}${addSpacesToLongNumbers(balance)}`}
          />
        ),
      },
    },
  ];

  return (
    <div className='h-auto w-full'>
      <div className='flex h-full w-full flex-col gap-2 max-md:py-2 md:mt-2 md:rounded-lg md:border md:border-[#334155]'>
        <div
          className={clsx(
            'flex min-h-min flex-col gap-2 overflow-hidden md:gap-4 md:p-4',
            isGlobalNotificationVisible
              ? 'md:h-[calc(100dvh-216px)]'
              : 'md:h-[calc(100dvh-142px)]',
            !history.length && isGlobalNotificationVisible && 'h-[calc(100vh-241px)]',
            !history.length && !isGlobalNotificationVisible && 'h-[calc(100vh-185px)]',
          )}
        >
          <Select
            className='max-md:order-none'
            width={isMobile ? '100%' : '224px'}
            name='day'
            isClearable
            isSearchable={false}
            options={lastSevenDaysInDayJsFormat}
            controlShouldRenderValue
            onChange={(day: any) => onFilterChange(day)}
            placeholder={t('chooseDay')}
            customGetOptionLabel={(option: any) => {
              return (
                <Typography
                  key={option.id}
                  text={dayjs().subtract(option.id, 'day').format('DD.MM.YYYY')}
                />
              );
            }}
            getSelectedOption={(props: any) => {
              const value = props.getValue()[0];
              return (
                <Typography
                  key={value.id}
                  text={dayjs().subtract(value.id, 'day').format('DD.MM.YYYY')}
                  iconName='calendar'
                />
              );
            }}
          />
          {!inProgress && !history.length ? (
            <EmptyTable
              lng={lng}
              className='flex-grow flex items-center justify-center'
              iconName='walletCross'
              header={t('noAccountHistory')}
            />
          ) : (
            <>
              <TableComponent
                className='paymentHistoryTable accountTable mt-4 w-full max-md:hidden'
                columns={columns}
                initialData={history}
                totalItems={totalItems}
                lng={lng}
                emptyIconName='walletTime'
                emptyIconProps={{
                  width: 80,
                  height: 80,
                  fill: theme.color?.general.light,
                }}
                isHeaderSticky
                withInfiniteScroll
                limit={LIMIT}
              />
              <div className='order-1 flex w-full flex-col gap-2 md:hidden'>
                {history.map((item: IHistoryItem, idx: number) => (
                  <HistoryCard
                    key={idx}
                    item={item}
                    currency={user?.currency || DEFAULT_CURRENCY}
                    lng={lng}
                  />
                ))}
              </div>
            </>
          )}
          {!inProgress && !history.length && <div />}
        </div>
      </div>
    </div>
  );
};

export default AccountHistoryTable;
