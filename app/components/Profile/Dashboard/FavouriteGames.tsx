'use client';
import { Typography } from '@/atomic-design-components';
import Slider from '@/app/components/Slider/Slider.tsx';
import GameCard from '@/app/components/Games/GameCard.tsx';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';

const FavouriteGames = () => {
  const { lng, gamePageType } = useParams();
  const { t } = useTranslation(lng);
  const [{ favourites }] = useSystemData();

  if (!favourites?.length) return null;

  return (
    <div className='mt-2 flex h-full w-full flex-col gap-y-2 md:gap-y-4'>
      <Typography
        text={`${t('favourite')} ${t('games')}`}
        iconName='heart'
        iconProps={{ width: 24, height: 24 }}
        type='h1'
      />
      <Slider
        items={favourites}
        total={favourites.length}
        Slide={GameCard}
        isCarousel
        limit={favourites.length}
        perView={6}
        buttonText={t('all')}
        buttonHref={`/${lng}/${gamePageType}/favourites`}
        breakpoints={GAMES_SLIDER_BREAKPOINTS()}
      />
    </div>
  );
};

export default FavouriteGames;
