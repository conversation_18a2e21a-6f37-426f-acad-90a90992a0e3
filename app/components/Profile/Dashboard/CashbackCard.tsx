'use client';
import clsx from 'clsx';
import dayjs from 'dayjs';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import { StyledCashbackPanel } from '@/app/[lng]/[gamePageType]/profile/styled.ts';
import { activateBonus } from '@/app/api/server-actions/bonuses';
import { StyledLine } from '@/app/components/Footer/styled.ts';
import { useUserLevels } from '@/app/components/Profile/Dashboard/UserLevelsProvider.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { useTransformDateFromNowInSeconds } from '@/utils/dates';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { openModal } from '@/utils/openModal.ts';
import CancelBonusToTakeCashbackModal from '@/app/components/Profile/Dashboard/CancelBonusToTakeCashbackModal.tsx';
import MinBalanceForCashbackModal from '@/app/components/Profile/Dashboard/MinBalanceForCashbackModal.tsx';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const CashbackCard = () => {
  const { lng, gamePageType } = useParams();
  const { t } = useTranslation(lng);
  const { user, setUser } = useUser();
  const { showAlert, showError } = useAlert();
  const [inProgress, setInProgress] = useState(false);

  const { currentLevel } = useUserLevels();
  const cashbackPercent = currentLevel?.cashback_percentage;

  const currentDateInUTC = useRef(dayjs().utc());

  const [cashbackNextActivationDate, setCashbackNextActivationDate] =
    useState('');

  useEffect(() => {
    if (cashbackPercent && user?.time_to_cashback) {
      const dateTimeToCashback = currentDateInUTC.current?.add(
        user.time_to_cashback,
        'second',
      );
      setCashbackNextActivationDate(dateTimeToCashback);
    }
  }, [cashbackPercent, user?.time_to_cashback]);

  const activationTimer = useTransformDateFromNowInSeconds(
    cashbackNextActivationDate,
    lng,
    true,
  ) as Array<string>;

  const dateText = ['dayShort', 'hourShort', 'minShort', 'secShort'];
  let currDates = activationTimer || [];
  let currDateText = dateText;
  if (activationTimer[0] === '0') {
    currDates = activationTimer.slice(1);
    currDateText = dateText.slice(1);
  }

  const takeCashback = () => {
    if (!currentLevel?.bonus_cashback_external_id) {
      return;
    }

    if (user?.active_bonus_id) {
      openModal('cancelBonusToTakeCashbackModal');
      return;
    }

    if (user!.min_balance < user!.account_balance) {
      openModal('minBalanceForCashbackModal');
      return;
    }

    setInProgress(true);
    activateBonus(currentLevel?.bonus_cashback_external_id, 'template')
      .then((result) => {
        if (result && !result.error) {
          setUser(result.user);
          showAlert({
            content: t('cashbackIsCreditedToBalance'),
            id: Date.now().toString(),
            type: 'success',
            timeout: 3000,
          });
        } else {
          showError(result?.error?.replace(',', ' ') || t('error'));
        }
      })
      .finally(() => setInProgress(false));
  };

  return (
    <>
      <StyledCashbackPanel>
        <svg
          width='188'
          height='129'
          viewBox='0 0 188 129'
          fill='none'
          className='absolute left-0 top-[-30px] z-0 h-[138px] w-[138px]'
        >
          <g filter='url(#filter0_f_5076_57427)'>
            <circle cx='69' cy='35' r='69' fill='#0083FF' fillOpacity='0.3' />
          </g>
          <defs>
            <filter
              id='filter0_f_5076_57427'
              x='-50'
              y='-84'
              width='238'
              height='238'
              filterUnits='userSpaceOnUse'
              colorInterpolationFilters='sRGB'
            >
              <feFlood floodOpacity='0' result='BackgroundImageFix' />
              <feBlend
                mode='normal'
                in='SourceGraphic'
                in2='BackgroundImageFix'
                result='shape'
              />
              <feGaussianBlur
                stdDeviation='25'
                result='effect1_foregroundBlur_5076_57427'
              />
            </filter>
          </defs>
        </svg>
        <div className='relative z-10 flex items-center justify-between'>
          <Typography type='h2'>{`${t('yourCashback')}: ${cashbackPercent || 0}%`}</Typography>
          <Typography
            as={Link}
            type='label1'
            href={`/${lng}/${gamePageType}/levels`}
          >
            {t('howToIncrease')}
          </Typography>
        </div>

        {!!cashbackPercent && (
          <>
            <StyledLine
              margin='12px 0'
              color={`${theme.color?.general.lightest}1a`}
            />
            <div
              className={clsx(
                'flex flex-wrap items-start justify-between gap-4',
                !cashbackNextActivationDate && 'halfWidth',
              )}
            >
              <div className='cashbackInfo flex flex-col'>
                <Typography text={t('availableCashback')} type='label2' />
                <Typography
                  text={`${getCurrencySymbol(user!.currency)}${addSpacesToLongNumbers(user?.cashback || 0)}`}
                  type='sub2'
                  color={theme.color?.secondary.main}
                />
              </div>

              {!!user?.time_to_cashback &&
                cashbackNextActivationDate &&
                activationTimer && (
                  <div className='cashbackInfo flex flex-col'>
                    <Typography
                      text={t('remainingTimeCashback')}
                      type='label2'
                    />
                    <Typography type='body1' className=''>
                      {currDates.map(
                        (datePart, i) => `${datePart} ${currDateText[i]} `,
                      )}
                    </Typography>
                  </div>
                )}

              <Button
                text={t('takeCashback')}
                className='cashbackButton'
                size='medium'
                onClick={() => takeCashback()}
                disabled={
                  inProgress || user?.time_to_cashback || !user?.cashback
                }
              />
            </div>
          </>
        )}
      </StyledCashbackPanel>
      <CancelBonusToTakeCashbackModal />
      <MinBalanceForCashbackModal />
    </>
  );
};

export default CashbackCard;
