'use client';
import Link from 'next/link';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { useTranslation } from '@/app/i18n/client';
import { useParams } from 'next/navigation';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const MyTournament = ({
  tournament,
  userCurrency,
}: {
  tournament: any;
  userCurrency: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const currencySymbol = getCurrencySymbol(userCurrency);
  const prize =
    userCurrency === tournament.currency
      ? tournament.scoreData.prize
      : +tournament.scoreData.prize * tournament.rates[userCurrency];

  return (
    <Link
      href={`/${lng}/casino/tournaments/${tournament.slug}`}
      className='flex cursor-pointer items-center justify-between rounded-lg bg-[#1E293B] py-4 pl-4 pr-1 sm:pr-2'
    >
      <div className='flex flex-col gap-1'>
        <Typography
          text={
            getAvailableTranslation(tournament.title, lng) || tournament.name
          }
          type='body2'
        />
        <Typography
          type='body1'
          lineHeight='16px'
          color={theme.color?.general.lighter}
        >
          {t('prize')} - {currencySymbol}
          {addSpacesToLongNumbers(prize || 0)},&nbsp;&nbsp;&nbsp;
          {t('place')} - {tournament.scoreData.place}
          ,&nbsp;&nbsp;&nbsp;
          {t('points')} - {addSpacesToLongNumbers(tournament.scoreData.points)}
        </Typography>
      </div>
      <Icon
        name='chevronDown'
        className='rotate-[-90deg]'
        width={15}
        height={9}
        wrapperWidth={40}
        wrapperHeight={40}
      />
    </Link>
  );
};

export default MyTournament;
