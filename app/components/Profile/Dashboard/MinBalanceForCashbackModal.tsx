'use client';
import parse from 'html-react-parser';
import ModalDialog from '@/app/components/ModalDialog';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { Typography } from '@/atomic-design-components';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const MinBalanceForCashbackModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();

  return (
    <ModalDialog
      id='minBalanceForCashbackModal'
      title={t('cashbackActivationNotAvailable')}
      contentChildren={
        <Typography type='body2' displayCssProp='block'>
          {
            parse(
              t('minBalanceForCashbackActivation', {
                minBalance:
                  getCurrencySymbol(user?.currency || '') + addSpacesToLongNumbers(user?.min_balance || 0),
              }) || '',
            ) as string
          }
        </Typography>
      }
      withOneButton
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

export default MinBalanceForCashbackModal;
