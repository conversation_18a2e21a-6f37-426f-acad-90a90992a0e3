import { Typography } from '@/atomic-design-components';
import { getServerTranslation } from '@/app/i18n';
import { LanguagesType } from '@/types/global';
import { getUserTournaments } from '@/app/api/server-fetches/getUserTournamets.ts';
import { cookies } from 'next/headers';
import NoTournaments from '@/app/components/Profile/Dashboard/NoTournaments.tsx';
import MyTournament from '@/app/components/Profile/Dashboard/MyTournament.tsx';

const MyTournaments = async ({ lng }: { lng: LanguagesType }) => {
  const { t } = await getServerTranslation(lng);
  const userCurrency = cookies().get('userCurrency')?.value;

  const userTournaments = await getUserTournaments();

  return (
    <>
      <Typography
        text={t('myTournaments')}
        type='h3'
        className='mt-2 sm:mt-0'
      />
      {userTournaments?.length ? (
        userTournaments
          .slice(0, 3)
          .map((tournament: any) => (
            <MyTournament
              key={tournament.id}
              tournament={tournament}
              userCurrency={userCurrency || ''}
            />
          ))
      ) : (
        <NoTournaments />
      )}
    </>
  );
};

export default MyTournaments;
