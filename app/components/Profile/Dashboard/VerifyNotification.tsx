'use client';
import parse from 'html-react-parser';
import { openModal } from '@/utils/openModal.ts';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

const VERIFY_NOTIFICATION_DATA = {
  email: {
    modal: 'emailVerificationModal',
    textKey: 'verifyEmail',
  },
  phone: {
    modal: 'phoneVerificationModal',
    textKey: 'verifyPhoneForFullAccess',
  },
};

const VerifyNotification = ({ type }: { type: 'email' | 'phone' }) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const modalId = VERIFY_NOTIFICATION_DATA[type].modal;

  const { activeModalData } = useNavigation();

  if (
    type === 'phone' &&
    activeModalData?.modalId === 'phoneVerificationModal' &&
    activeModalData?.isVerified
  ) {
    return null;
  }

  return (
    <div
      className='flex cursor-pointer gap-2 rounded-lg bg-[#334155] p-2'
      role='button'
      tabIndex={0}
      onClick={(e: any) => {
        e.stopPropagation();
        document
          .getElementById(modalId)
          ?.setAttribute('data-active-screen-verify', type);
        openModal(modalId);
      }}
    >
      <Typography
        text={parse(t(VERIFY_NOTIFICATION_DATA[type].textKey))}
        iconName='infoIcon'
        iconProps={{ fill: theme.color?.secondary.main }}
      />
    </div>
  );
};

export default VerifyNotification;
