import { Typography } from '@/atomic-design-components';
import PaymentCard from '@/app/components/Cashier/History/PaymentCard.tsx';
import { getServerTranslation } from '@/app/i18n';
import { IPaymentTransaction } from '@/app/api/payments/getPaymentTransactionsClient.ts';
import CancelWithdrawalModal from '@/app/components/Cashier/History/CancelWithdrawalModal.tsx';
import { LanguagesType } from '@/types/global';
import { getPaymentTransactionsServer } from '@/app/api/payments/getPaymentTransactionsServer.ts';

const PendingWithdrawals = async ({ lng }: { lng: LanguagesType }) => {
  const { t } = await getServerTranslation(lng);

  const withdrawals = await getPaymentTransactionsServer(
    0,
    3,
    'payment_operation=withdrawal,status=pending',
    'created_at=asc',
    ['pending_withdrawals'],
  );

  return (
    <>
      {!!withdrawals?.items?.length && (
        <Typography text={t('youHaveProcessingWithdrawals')} type='h3' className='mt-2 sm:mt-0' />
      )}
      {withdrawals?.items?.map((item: IPaymentTransaction) => {
        return <PaymentCard key={item.transaction_id} lng={lng} item={item} className='py-4' />;
      })}
      <CancelWithdrawalModal modalId='cancelWithdrawalModalProfile' />
    </>
  );
};

export default PendingWithdrawals;
