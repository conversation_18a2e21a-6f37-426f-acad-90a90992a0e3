'use client';
import ModalDialog from '@/app/components/ModalDialog';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';

const CancelBonusToTakeCashbackModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  return (
    <ModalDialog
      id='cancelBonusToTakeCashbackModal'
      title={t('youHaveActiveBonus')}
      text={t('completeOrCancelBonusBeforeCashback')}
      withOneButton
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

export default CancelBonusToTakeCashbackModal;
