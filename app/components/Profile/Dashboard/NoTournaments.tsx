'use client';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { useTranslation } from '@/app/i18n/client';
import { useParams } from 'next/navigation';
import Link from 'next/link';

const NoTournaments = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  return (
    <Link
      href={`/${lng}/casino/tournaments`}
      className='flex cursor-pointer items-center justify-between rounded-lg bg-[#1E293B] py-4 pl-3 pr-2 sm:pr-4'
    >
      <div className='flex flex-col gap-1'>
        {/* <Typography text='tournament name' type='body2' />
            <Typography text='prize points place' type='body2' /> */}
        <Typography
          text={t('joinToTournaments')}
          type='body2'
          iconName='cup'
          iconProps={{
            fill: theme.color?.primary.main,
            wrapperWidth: 40,
            wrapperHeight: 40,
            margin: '0 4px 0 0',
            className: '!flex',
          }}
        />
      </div>
      <Icon
        name='chevronDown'
        className='rotate-[-90deg]'
        width={15}
        height={9}
        wrapperWidth={40}
        wrapperHeight={40}
      />
    </Link>
  );
};

export default NoTournaments;
