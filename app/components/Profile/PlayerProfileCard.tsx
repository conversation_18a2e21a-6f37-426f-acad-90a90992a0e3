'use client';
import { useEffect, useState } from 'react';
import { parsePhoneNumber } from 'react-phone-number-input';
import dayjs from 'dayjs';
import {
  requestConfirmEmail,
  RequestConfirmEmail,
  requestConfirmPhone,
  RequestConfirmPhone,
} from '@/app/api/server-actions/confirmUserData';
import { updateUserProfile } from '@/app/api/server-actions/updateUserProfile.ts';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { Button, Input } from '@/atomic-design-components';
import PhoneInput from '@/atomic-design-components/molecules/PhoneInput/PhoneInput';
import { LanguagesType } from '@/types/global';
import validate, { rule } from '@/utils/formValidationRules.ts';
import { isObjectEmpty } from '@/utils/object.ts';
import { openModal } from '@/utils/openModal';
import {
  getFromLocalStorage,
  removeFromLocalStorage,
  saveToLocalStorage,
} from '@/utils/localStorage.ts';
import { FIRST_PHONE_CODE_TIMER_SEC } from '@/constants.ts';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

interface IProps {
  lng: LanguagesType;
  user: any;
}

const PlayerProfileCard = ({ lng, user }: IProps) => {
  const { t } = useTranslation(lng);
  const { t: validationT } = useTranslation(lng, 'validation');
  const { showAlert, showError } = useAlert();
  const userPhoneParsed = parsePhoneNumber(user?.phone || '');

  const { activeModalData, setActiveModalData } = useNavigation();

  const [initialUser, setInitialUser] = useState(user);
  const [country, setCountry] = useState<string>(userPhoneParsed?.country || '');
  const [dialCode, setDialCode] = useState<string>(`+${userPhoneParsed?.countryCallingCode}` || '');
  const [phone, setPhone] = useState<string>(userPhoneParsed?.nationalNumber || '');
  const [email, setEmail] = useState<string>(user.email);
  const [nickname, setNickname] = useState<string>(user.nickname);
  const [fullName, setFullName] = useState<string>(user.full_name);
  const [birthDate, setBirthDate] = useState<string>(user.birth_date);

  const [inProgress, setInProgress] = useState(false);
  const [isSubmitPressed, setIsSubmitPressed] = useState({
    email: false,
    phone: false,
    nickname: false,
    fullName: false,
    birthDate: false,
  });
  const [errors, setErrors] = useState<any>({});

  const isBirthDateEmpty = !initialUser.birth_date;

  const validationRulesInitial = {
    fullName: ['fullName'],
    birthDate: ['birthDate'],
    nickname: ['nickname'],
    email: ['email'],
    phone: ['phone'],
  };

  useEffect(() => {
    if (activeModalData?.modalId === 'phoneVerificationModal' && activeModalData?.isVerified) {
      setInitialUser({
        ...initialUser,
        is_phone_verified: true,
      });
      setActiveModalData(null);
    }
  }, [activeModalData]);

  const isValueChanged = (fieldName: keyof typeof initialValuesChangedBeforeCheck) => {
    if (fieldName === 'phone') {
      return parsePhoneNumber(dialCode + phone)?.number !== initialUser.phone?.replace('-', '');
    }

    if (fieldName === 'email') {
      return email !== initialUser.email;
    }

    if (fieldName === 'nickname') {
      return nickname !== initialUser.nickname;
    }
  };

  const initialValuesChangedBeforeCheck = {
    email,
    phone: phone && dialCode ? dialCode + phone : '',
    nickname,
    fullName,
    birthDate,
  };

  const onCheckField = (name: string, value: string) => {
    const error = rule(name, validationRulesInitial)(value, initialValuesChangedBeforeCheck);

    let newErrors = { ...errors };
    if (error[name]) {
      setErrors({ ...newErrors, [name]: error[name] });
    } else {
      delete newErrors[name];
      setErrors({ ...newErrors });
    }
  };

  const submitField = async (fieldName: keyof typeof initialValuesChangedBeforeCheck) => {
    setIsSubmitPressed({ ...isSubmitPressed, [fieldName]: true });
    const errors = validate(validationRulesInitial)(
      { [fieldName]: initialValuesChangedBeforeCheck[fieldName] },
      {
        values: { [fieldName]: initialValuesChangedBeforeCheck[fieldName] },
      },
    );

    setErrors((prev: any) => ({ ...prev, ...errors }));

    if (!email && (!phone || !dialCode)) {
      return;
    }
    if (isObjectEmpty(errors)) {
      setInProgress(true);
      const requestBody: {
        email?: string;
        phone?: string;
        nickname?: string;
        full_name?: string;
        birth_date?: string;
      } = {};

      if (fieldName === 'email' && email !== initialUser.email) {
        requestBody.email = email;

        if (!email && initialUser.email) {
          requestBody.email = initialUser.email;
        }
      }
      if (fieldName === 'phone' && dialCode + phone !== initialUser.phone) {
        requestBody.phone = dialCode + phone;
        if (!phone && initialUser.phone) {
          requestBody.phone = initialUser.phone;
        }
      }
      if (fieldName === 'nickname' && nickname !== initialUser.nickname) {
        requestBody.nickname = nickname;
      }
      if (fieldName === 'fullName' && fullName) {
        requestBody.full_name = fullName;
      }
      if (fieldName === 'birthDate' && birthDate) {
        requestBody.birth_date = birthDate.split('-').reverse().join('-');
      }

      const result: any = await updateUserProfile(requestBody);
      setInProgress(false);

      if (result?.detail?.message) {
        const field = result.detail.parameters?.error_text.split(' ')[0].toLowerCase();

        setErrors((prev: any) => ({
          ...prev,
          [field === 'nick' ? 'nickname' : field]: result.detail.parameters?.error_text?.replace(
            / /g,
            '_',
          ),
        }));
        return;
      }

      // if phone is successfully changed, clear local storage
      if (fieldName === 'phone') {
        removeFromLocalStorage('phoneConfirmTimerExpires');
        removeFromLocalStorage('phoneConfirmCodeSentCount');
      }

      setInitialUser({
        ...result,
        birth_date:
          result.birth_date === '0000-00-00' || result.birth_date === '1900-01-01'
            ? ''
            : dayjs(result.birth_date).format('DD-MM-YYYY'),
      });

      if (fieldName === 'email' && !email && initialUser.email) {
        setEmail(initialUser.email);
      }
      if (fieldName === 'phone' && !phone && initialUser.phone) {
        const userPhoneParsed = parsePhoneNumber(initialUser.phone);
        setPhone(userPhoneParsed?.nationalNumber || initialUser.phone.split('-')[1]);
        setDialCode(`+${userPhoneParsed?.countryCallingCode}` || initialUser.phone.split('-')[0]);
      }

      setIsSubmitPressed({
        email: false,
        phone: false,
        nickname: false,
        fullName: false,
        birthDate: false,
      });

      showAlert({
        content: t('changesSaved'),
        id: Date.now().toString(),
        type: 'success',
        timeout: 3000,
      });
    }
  };

  const requestEmailConfirm = async () => {
    setInProgress(true);

    const requestData: RequestConfirmEmail = { email };
    const result = await requestConfirmEmail(requestData);

    if (result.error) {
      setInProgress(false);
      showError(result.error);
      return;
    }

    if (result.result === 'ok') {
      openModal('emailVerificationModal');
      document
        .getElementById('emailVerificationModal')
        ?.setAttribute('data-active-screen-verify', 'confirm');
    }
    setInProgress(false);
  };

  const openPhoneVerificationModal = (phone: string) => {
    openModal('phoneVerificationModal');
    document
      .getElementById('phoneVerificationModal')
      ?.setAttribute('data-active-screen-verify', 'confirm');
    document.getElementById('phoneVerificationModal')?.setAttribute('verify-data', phone);
    // setInProgress(false);
  };

  const requestPhoneConfirm = async () => {
    const resendCodeTimerExpiresFromLS = getFromLocalStorage('phoneConfirmTimerExpires');
    const codeSentCount = getFromLocalStorage('phoneConfirmCodeSentCount');

    const requestData: RequestConfirmPhone = {
      phone: parsePhoneNumber(dialCode + phone)?.number || dialCode + phone,
      // no phone type for now - code will be sent in Telegram and then by phone call
    };

    openPhoneVerificationModal(requestData.phone);

    if (!resendCodeTimerExpiresFromLS && !codeSentCount) {
      setInProgress(true);
      // console.log('send phone confirm request');
      const result = await requestConfirmPhone(requestData);
      // const result = { result: 'ok' };
      // console.log(result);
      saveToLocalStorage(
        'phoneConfirmTimerExpires',
        Date.now() + FIRST_PHONE_CODE_TIMER_SEC * 1000,
      );
      saveToLocalStorage('phoneConfirmCodeSentCount', 1);

      if (!result || result?.result !== 'ok' || result.error) {
        setInProgress(false);
        showError(t('failedToSendCode'));
        removeFromLocalStorage('phoneConfirmCodeSentCount');
        removeFromLocalStorage('phoneConfirmTimerExpires');
        setActiveModalData({
          modalId: 'phoneVerificationModal',
          isRequestConfirmError: true,
        });
        return;
      }

      if (result.result === 'ok') {
        setInProgress(false);
        if (activeModalData?.isRequestConfirmError) {
          setActiveModalData(null);
        }
      }
    }
  };

  return (
    <div className='flex h-full w-full flex-col gap-4'>
      <div className='flex items-start gap-2'>
        <Input
          name='fullName'
          labelTop={t('fullName')}
          value={fullName}
          fullWidth
          onChange={(e: any) => {
            const { value } = e.target;
            onCheckField('fullName', value);
            setFullName(value);
          }}
          rightIconName={!initialUser.full_name && fullName ? 'cross' : undefined}
          onRightIconClick={() => {
            onCheckField('fullName', '');
            setFullName('');
          }}
          error={isSubmitPressed.fullName && validationT(errors.fullName)}
          disabled={inProgress || initialUser.full_name}
        />
        {!initialUser.full_name && (
          <Button
            text={t('save')}
            className='!mt-5 min-w-[132px]'
            onClick={() => submitField('fullName')}
            disabled={inProgress || !fullName}
          />
        )}
      </div>
      <div className='flex items-start gap-2'>
        <Input
          name='birthDate'
          labelTop={t('dateOfBirth')}
          value={birthDate}
          fullWidth
          placeholder='22-01-1900'
          onChange={(e: any) => {
            // Remove all non-digit characters.
            let value = e.target.value.replace(/\D/g, '');

            // Insert dash after day if at least 3 digits are entered.
            if (value.length >= 3) {
              value = value.slice(0, 2) + '-' + value.slice(2);
            }
            // Insert dash after month if at least 5 digits are entered.
            if (value.length >= 6) {
              value = value.slice(0, 5) + '-' + value.slice(5, 9);
            }

            // Limit the total length to 10 characters (DD-MM-YYYY).
            e.target.value = value.slice(0, 10);
            onCheckField('birthDate', value);
            setBirthDate(value);
          }}
          rightIconName={!initialUser.birth_date && birthDate ? 'cross' : undefined}
          onRightIconClick={() => {
            onCheckField('birthDate', '');
            setBirthDate('');
          }}
          error={isSubmitPressed.birthDate && validationT(errors.birthDate)}
          disabled={inProgress || initialUser.birth_date}
        />
        {isBirthDateEmpty && (
          <Button
            text={t('save')}
            className='!mt-5 min-w-[132px]'
            onClick={() => submitField('birthDate')}
            disabled={inProgress || !birthDate}
          />
        )}
      </div>
      <div className='flex items-start gap-2'>
        <Input
          name='nickname'
          labelTop={t('nickname')}
          value={nickname}
          fullWidth
          onChange={(e: any) => {
            const { value } = e.target;
            onCheckField('nickname', value);
            setNickname(value);
          }}
          rightIconName={nickname !== initialUser.nickname ? 'cross' : undefined}
          onRightIconClick={() => {
            onCheckField('nickname', initialUser.nickname);
            setNickname(initialUser.nickname);
          }}
          error={isSubmitPressed.nickname && validationT(errors.nickname)}
          disabled={inProgress}
        />
        <Button
          text={t('save')}
          className='!mt-5 min-w-[132px]'
          onClick={() => submitField('nickname')}
          disabled={inProgress || !isValueChanged('nickname')}
        />
      </div>
      <div className='flex items-start gap-2'>
        <Input
          name='email'
          key='email'
          fullWidth
          labelTop={t('email')}
          labelBottom={
            initialUser.is_email_verified && t('verified')
            // initialUser.is_email_verified
            //   ? t('verified')
            //   : parse(t('confirmEmailAndGet'))
          }
          rightIconName={email !== initialUser.email ? 'cross' : undefined}
          onRightIconClick={() => {
            onCheckField('email', initialUser.email);
            setEmail(initialUser.email);
          }}
          value={email}
          onChange={(e: any) => {
            if (initialUser.is_email_verified) {
              return;
            }
            const { value } = e.target;
            onCheckField('email', value);
            setEmail(value);
          }}
          error={
            isSubmitPressed.email &&
            (!email && (!phone || !dialCode) ? t('emailOrPhone') : validationT(errors.email))
          }
          disabled={inProgress || initialUser.is_email_verified}
        />
        {!initialUser.is_email_verified && (
          <Button
            text={t(isValueChanged('email') ? 'save' : 'confirm')}
            className='!mt-5 min-w-[132px]'
            onClick={() => {
              isValueChanged('email') ? submitField('email') : requestEmailConfirm();
            }}
            disabled={inProgress || !email}
          />
        )}
      </div>
      <div className='flex items-end gap-2'>
        <PhoneInput
          labelTop={t('phone')}
          labelBottom={initialUser.is_phone_verified && t('verified')}
          // labelBottom={
          //   initialUser.is_phone_verified
          //     ? t('verified')
          //     : parse(t('confirmPhoneAndGet'))
          // }
          name='phone'
          placeholder={t('phone')}
          key='phone'
          onChange={(data) => {
            if (initialUser.is_phone_verified) {
              return;
            }
            setPhone(data.inputValue);
            setDialCode(data.dialCode);
            setCountry(data.country);
            onCheckField('phone', data.dialCode + data.inputValue);
          }}
          error={
            isSubmitPressed.phone
              ? !email && (!phone || !dialCode)
                ? t('emailOrPhone')
                : validationT(errors.phone)
              : ''
          }
          fullWidth
          withAutoFocus={false}
          initialValue={userPhoneParsed?.nationalNumber}
          initialCode={dialCode}
          initialCountry={country}
          backToInitialValueOnCrossClick
          onCrossClick={
            phone !== (userPhoneParsed?.nationalNumber || '')
              ? () => {
                  setPhone(userPhoneParsed?.nationalNumber || '');
                  setDialCode(`+${userPhoneParsed?.countryCallingCode}` || '');
                  onCheckField('phone', dialCode + phone);
                }
              : undefined
          }
          t={t}
          disabled={inProgress || initialUser.is_phone_verified}
        />
        {!initialUser.is_phone_verified && (
          <Button
            text={t(isValueChanged('phone') ? 'save' : 'confirm')}
            className='!mt-5 min-w-[132px]'
            onClick={() => {
              isValueChanged('phone') ? submitField('phone') : requestPhoneConfirm();
            }}
            disabled={inProgress || !phone || !dialCode}
          />
        )}
      </div>
      <Button
        text={t('signOut')}
        fullWidth
        variant='transparent'
        onClick={(e: any) => {
          e.preventDefault();
          openModal('signOutModal');
        }}
      />
    </div>
  );
};

export default PlayerProfileCard;
