export const LANDING_GAMES_GROUPS = {
  'live-casino': {
    top: {
      icon: 'starFlying',
      filter: { top__gte: [0] },
      sort: 'top=asc',
      perView: 7,
    },
    roulette: {
      icon: 'roulette',
      filter: { game_type: ['roulette'] },
      sort: 'popularity=asc',
    },
    blackjack: {
      icon: 'cards',
      filter: { game_type: ['blackjack'] },
      sort: 'popularity=asc',
      perView: 7,
    },
    baccarat: {
      icon: 'man',
      filter: { game_type: ['baccarat'] },
      sort: 'popularity=asc',
    },
    videoPoker: {
      icon: 'chipPlay',
      filter: { game_type: ['video_poker'] },
      sort: 'popularity=asc',
    },
    showsGames: {
      icon: 'videoPlay',
      filter: { game_type: ['tv_shows'] },
      sort: 'popularity=asc',
    },
  },
  casino: {
    top: {
      icon: 'star',
      filter: { top__gte: [0] },
      sort: 'top=asc',
      perView: 7,
      iconProps: { fill: '#0083FF' },
    },
    hot: {
      icon: 'fire',
      filter: { hot__gte: [0] },
      sort: 'hot=desc',
    },
    Live: {
      icon: 'chip',
      sort: 'popularity=asc',
      // filter: { is_live: ['true'] },
      perView: 7,
    },
    rtp: {
      icon: 'graph',
      filter: { rtp__gte: [0] },
    },
    new: {
      icon: 'newIcon',
      sort: 'release_date=desc,external_id=desc',
      maxLimit: 100,
      iconProps: { fill: '#489F37', width: 24, height: 24 },
    },
    crash: {
      icon: 'rocket',
      filter: { game_type: ['crash'] },
      sort: 'popularity=asc',
    },
  },
} as any; //'all'?// 'new', 'rtp', 'bonus'
