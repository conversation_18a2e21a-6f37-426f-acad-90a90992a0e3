'use client';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useRef, useState } from 'react';

import { IGame } from '@/app/api/getGames';
import { IProvider } from '@/app/api/getProviders.ts';
import { useTranslation } from '@/app/i18n/client';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Icon, Typography, Image } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import useClickOutsideElement from '@/hooks/useClickOutsideElement';
import useIsGameFavourite from '@/hooks/useIsGameFavourite.ts';
import { theme } from '@/theme';
import { closeModal } from '@/utils/closeModal';
import { getFromLocalStorage, saveToLocalStorage } from '@/utils/localStorage';
import { openModal } from '@/utils/openModal.ts';
import clsx from 'clsx';

const GameCardForMobileModal = ({ item }: { item: IGame }) => {
  const { lng, gamePageType } = useParams();
  const { t } = useTranslation(lng);
  const router = useRouter();

  const ref = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  useClickOutsideElement(ref, (target: any) => {
    if (ref?.current && !cardRef.current?.contains(target)) closeModal('gameModal');
  });

  const gameLaunchCount = getFromLocalStorage('gameLaunchCount');
  // const isGameOverlayModalOpened = document
  //   .getElementById('gamePageOverlayModal')
  //   ?.classList.contains('opened');

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const [isLoaded, setIsLoaded] = useState(false);

  const [{ favourites = [], providers = [] }, setSystemData] = useSystemData();

  const gameProvider = providers?.find(
    (provider: IProvider) => provider.slug === item?.provider_slug,
  );
  const { isGameFavourite, onToggleFavourite } = useIsGameFavourite(
    item,
    favourites,
    setSystemData,
  );

  const gameUrl = `/${lng}/${gamePageType || 'casino'}/${item.provider_slug}/game/${item.slug}`;

  const onGameClick = () => {
    closeModal('gameModal');
    closeModal('gamePageOverlayModal');
    closeModal('searchModal');
    if (!gameLaunchCount) {
      openModal('gameLaunchModal');
      saveToLocalStorage('gameLaunchCount', '1');
    }
  };

  return (
    <>
      <div className='flex h-[calc(100%-208px)] w-full flex-col content-center justify-center'>
        <div
          className={clsx(
            'gameCard modalImg opened m-auto cursor-pointer ',
            !item.photo_url_vertical && 'placeholder',
            !isLoaded && 'loading',
          )}
          role='presentation'
          ref={cardRef}
        >
          {(!isLoaded || !item.photo_url_vertical) && (
            <div>
              <Image
                alt={item.name || `Game ${item.external_id} placeholder`}
                placeholder={undefined}
                src={LOGO_PLACEHOLDER}
                fill
                sizes='357px'
                style={{ objectFit: 'contain' }}
                className='placeholder'
                unoptimized
              />
            </div>
          )}
          {item.photo_url_vertical && (
            <div>
              <Image
                alt={item.name || `Game ${item.external_id}`}
                placeholder={undefined}
                onLoad={() => setIsLoaded(true)}
                src={item.photo_url_vertical}
                fill
                style={{ objectFit: 'cover' }}
                sizes='357px'
              />
            </div>
          )}
        </div>
      </div>
      <div className='gameModalCard' ref={ref}>
        <Icon
          name='cross'
          className='crossIcon gameCrossIcon absolute right-4 top-4 z-10'
          onClick={(e: any) => {
            e.preventDefault();
            closeModal('gameModal');
          }}
          width={38}
          height={16}
          wrapperWidth={32}
          wrapperHeight={32}
          strokeWidth={2}
          fill={theme.color?.general.lightest}
          wrapperColor={theme.color?.general.darker}
        />
        <div className='mb-1 flex gap-2'>
          <Typography
            text={item.name}
            type='h1'
            href={gameUrl}
            onClick={() => {
              onGameClick();
            }}
            component={Link}
          />
          {isUserAuthorized && (
            <Icon
              name='heartShape'
              stroke={isGameFavourite ? theme.color?.primary.main : theme.color?.general.lightest}
              fill={isGameFavourite ? theme.color?.primary.main : 'none'}
              className='favoriteIcon'
              onClick={async (e: Event) => {
                await onToggleFavourite(e);
              }}
            />
          )}
        </div>

        {gameProvider?.id && (
          <Typography
            href={`/${lng}/${gameProvider.is_live ? 'live-casino' : 'casino'}/games/all?filters=provider_id=${gameProvider.id}`}
            text={gameProvider.name}
            type='label1'
            component={Link}
            className='!mb-6'
          />
        )}
        <Link
          href={gameUrl}
          onClick={() => {
            onGameClick();
          }}
        >
          <Button fullWidth size='medium' className='mb-2'>
            {t('play')}
          </Button>
        </Link>
        {item.is_demo ? (
          <Button
            fullWidth
            variant='transparent'
            size='medium'
            onClick={(e: any) => {
              e.stopPropagation();
              onGameClick();
              router.push(`${gameUrl}?demo=true`);
            }}
          >
            {t('demo')}
          </Button>
        ) : (
          <div className='mt-auto' />
        )}
      </div>
    </>
  );
};

export default GameCardForMobileModal;
