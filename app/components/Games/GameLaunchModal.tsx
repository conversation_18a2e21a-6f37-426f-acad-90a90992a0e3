'use client';
import { useRef } from 'react';

import { useTranslation } from '@/app/i18n/client';
import { Button, Typography } from '@/atomic-design-components';
import { closeModal } from '@/utils/closeModal';
import { useParams } from 'next/navigation';
import ModalDialog from '../ModalDialog';

const GameLaunchModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const ref = useRef<HTMLDialogElement>(null);

  return (
    <ModalDialog id='gameLaunchModal' ref={ref}>
      <div className='flex h-dvh w-full flex-col'>
        <div className='relative mt-auto flex flex-col rounded-t-2xl bg-[#1E293B] px-4 pb-3 pt-8'>
          <Typography text={t('easyNavigation')} type='h1' className='!mb-3' />
          <Typography
            text={t('easyNavigationText')}
            type='body2'
            className='!mb-6'
          />
          <Button
            text={t('gotIt')}
            fullWidth
            size='medium'
            onClick={(e: any) => {
              e.preventDefault();
              closeModal('gameLaunchModal');
            }}
          />
        </div>
      </div>
    </ModalDialog>
  );
};

GameLaunchModal.displayName = 'GameLaunchModal';
export default GameLaunchModal;
