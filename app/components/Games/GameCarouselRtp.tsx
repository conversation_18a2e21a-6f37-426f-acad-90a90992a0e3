import { getGames, IGame } from '@/app/api/getGames.ts';
import RtpSliderDoubleGameCard from '@/app/components/Games/RtpSliderDoubleGameCard.tsx';
import Slider from '@/app/components/Slider/Slider.tsx';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import 'keen-slider/keen-slider.min.css';
import { GET_GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';
import { cookies } from 'next/headers';

const LIMIT = 16;

const GameCarouselRtp = async ({ lng }: { lng: LanguagesType }) => {
  const { t } = await getServerTranslation(lng);
  const userCurrency = cookies().get('userCurrency')?.value;

  const hotRtpGames = await getGames(
    0,
    LIMIT,
    userCurrency,
    { rtp_hot__gte: ['0'] },
    'rtp_hot=desc',
  );

  const coldRtpGames = await getGames(
    0,
    LIMIT,
    userCurrency,
    { rtp_cold__gte: ['0'] },
    'rtp_cold=asc',
  );

  if (!hotRtpGames.items?.length && !coldRtpGames.items?.length) {
    return null;
  }

  return (
    <div className='gameCarousel rtpCarousel flex flex-col gap-y-2 md:gap-y-4'>
      <Typography
        type='h1'
        text={t('rtp')}
        iconName='graph'
        iconProps={{ fill: theme.color?.status.error }}
      />
      <Slider
        items={hotRtpGames.items.map((hotGame: IGame, index: number) => ({
          hot: hotGame,
          cold: coldRtpGames.items[index],
        }))}
        total={16}
        Slide={RtpSliderDoubleGameCard}
        isCarousel
        limit={LIMIT}
        perView={7}
        buttonText={t('all')}
        buttonHref={`/${lng}/casino/games/rtp`}
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(7)}
      />
    </div>
  );
};

export default GameCarouselRtp;
