'use client';
import clsx from 'clsx';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { memo, useState } from 'react';

import { IGame } from '@/app/api/getGames.ts';
import { IProvider } from '@/app/api/getProviders.ts';
import { StyledDemoTag } from '@/app/components/Games/styled';
import { useTranslation } from '@/app/i18n/client';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Icon, Typography, Image } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { useGameCheckBeforeOpen } from '@/hooks/useGameCheckBeforeOpen.ts';
import useIsGameFavourite from '@/hooks/useIsGameFavourite.ts';
import { theme } from '@/theme.ts';
import { isClientReady } from '@/utils';
import { closeModal } from '@/utils/closeModal.ts';
import { openModal } from '@/utils/openModal.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import useWindowSize from '@/hooks/useWindowSize.ts';

const GameCard = memo(function GameCard({
  item,
  index,
  page,
  setTableData,
  isBadgeShown = true,
  category,
  className,
}: {
  item: IGame;
  index: number;
  page?: string;
  setTableData?: any;
  isBadgeShown?: boolean;
  category?: string;
  className?: string;
}) {
  const router = useRouter();
  const { lng, gamePageType } = useParams();
  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isTouchDeviceOrMd = isTouchDevice || (!!width && width < theme.breakpoints?.md);

  const pageType = ['casino', 'live-casino'].includes(gamePageType as string)
    ? gamePageType
    : 'casino';

  const { t } = useTranslation(lng);
  const [{ favourites = [], providers = [], newGames = [] }, setSystemData] = useSystemData();

  const { isGameFavourite, onToggleFavourite } = useIsGameFavourite(
    item,
    favourites,
    setSystemData,
    setTableData,
  );

  const gameProvider = providers?.find(
    (provider: IProvider) => provider.slug === item?.provider_slug,
  );

  const isNewGame =
    !!newGames?.length && newGames.find((game: IGame) => game.external_id === item?.external_id);

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const onMouseEnter = () => setIsHovered(true);
  const onMouseLeave = () => setIsHovered(false);

  const gameUrl = `/${lng}/${pageType}/${item?.provider_slug}/game/${item?.slug}`;

  const { onGameClick, canGameBeOpenedByLink } = useGameCheckBeforeOpen(
    gameUrl,
    user,
    item?.wager_percentage,
  );

  if (!item) return null;

  const badges = [];
  if (item.top !== null && (isBadgeShown || category !== 'top')) {
    badges?.push('top');
  }
  if (item.hot !== null && (isBadgeShown || category !== 'hot')) {
    badges?.push('hot');
  }
  if (isNewGame && (isBadgeShown || category !== 'new')) badges?.push('new');

  //TODO set real value
  const isRtpHot = category === 'rtp_hot';
  const isRtpCold = category === 'rtp_cold';
  const isRTP = isRtpHot || isRtpCold;

  const openDesktopGame = (url?: string) => {
    if (url) {
      router.push(url);
    }
    closeModal('searchModal');
    closeModal('continueGameWithZeroWager');
  };

  const openMobileGame = (e: Event) => {
    if (isTouchDeviceOrMd) {
      const isSearchOpened =
        isClientReady() && document.getElementById('searchModal')?.classList.contains('opened');

      const zIndex = isSearchOpened ? '212' : '';
      if (canGameBeOpenedByLink) {
        e.preventDefault();
        document.getElementById('gameModal')?.setAttribute('data-active-game-slug', item.slug);
        closeModal('continueGameWithZeroWager');
        openModal('gameModal', true, true, true, zIndex);
      } else {
        onGameClick();
      }
    }
  };

  return (
    <div
      onMouseEnter={isTouchDeviceOrMd ? undefined : () => onMouseEnter()}
      onMouseLeave={isTouchDeviceOrMd ? undefined : () => onMouseLeave()}
      role='presentation'
      onClick={(e: any) => {
        openMobileGame(e);
      }}
    >
      <div
        className={clsx(
          'gameCard align-center flex cursor-pointer flex-col justify-center',
          !item.photo_url_vertical && 'placeholder',
          !isLoaded && 'loading',
          isRTP && 'rtpCard',
          className,
        )}
        onClick={() => {
          if (!isTouchDeviceOrMd) {
            if (canGameBeOpenedByLink) {
              openDesktopGame(gameUrl);
            }
            onGameClick();
          }
        }}
        role='presentation'
      >
        {(!isLoaded || !item.photo_url_vertical) && (
          <>
            <div className={clsx('hoverImageDiv', isHovered && 'hovered')}>
              <Image
                alt={item.name || `Game ${index} placeholder`}
                placeholder={undefined}
                src={LOGO_PLACEHOLDER}
                style={{
                  objectFit: 'contain',
                }}
                className='placeholder'
                fill
                sizes='357px'
                unoptimized
              />
            </div>
          </>
        )}
        {item.photo_url_vertical && (
          <div className={clsx('hoverImageDiv', isHovered && 'hovered')}>
            <Image
              alt={item.name || `Game ${index}`}
              placeholder={undefined}
              onLoad={() => setIsLoaded(true)}
              src={item.photo_url_vertical}
              fill
              sizes='357px'
              style={{
                objectFit: 'cover',
              }}
            />
          </div>
        )}
        {isRTP && (
          <div
            className={clsx(
              'absolute bottom-0 flex h-[28px] w-full px-2 py-1',
              isRtpHot ? 'rtpHot' : 'rtpCold',
            )}
          >
            <Typography
              text={t(isRtpHot ? 'hotTranslated' : 'cold')}
              type='sub1'
              className='mr-5'
            />
            <div className='flex'>
              <Typography
                text={`${Math.round((isRtpHot ? item.rtp_hot! : item.rtp_cold!) * 100) / 100}%`}
                type='sub1'
                className='pr-1'
              />
              <Icon name='arrow' toTop={isRtpHot} fill={isRtpHot ? '#EB5645' : '#3392EA'} />
            </div>
          </div>
        )}
        {isLoaded && badges?.length > 0 && (
          <div className='absolute left-0 top-[10px] flex flex-col gap-[4px]'>
            {badges.map((badge) => (
              <Typography
                key={badge}
                className='gameCard__badge'
                text={badge}
                fontSize='10px'
                fontWeight={theme.font.weight.bold}
                textAlign='center'
                textTransform='uppercase'
                padding='6px 8px'
                lineHeight='7px'
                backgroundColor={
                  badge === 'hot'
                    ? theme.color?.secondary.dark
                    : badge === 'top'
                      ? theme.color?.primary.main
                      : theme.color?.status.success
                }
                color={theme.color?.general.white}
              />
            ))}
          </div>
        )}
        {isHovered && (
          <div className='gameCard__overlay'>
            {isUserAuthorized && isHovered && (
              <Icon
                name='heartShape'
                fill={isGameFavourite ? theme.color?.primary.main : theme.color?.general.lightest}
                stroke={isGameFavourite ? theme.color?.primary.main : theme.color?.general.lightest}
                className='favoriteIcon'
                onClick={async (e: Event) => {
                  await onToggleFavourite(e);
                }}
              />
            )}
            <Link
              href={canGameBeOpenedByLink ? gameUrl : ''}
              onClick={() => {
                onGameClick();
                if (canGameBeOpenedByLink) {
                  openDesktopGame();
                }
              }}
              className='playIcon'
            >
              <Icon name='play' />
            </Link>
            {item.is_demo ? (
              <StyledDemoTag
                size='small'
                variant='transparent'
                className='!mt-auto'
                text={t('demo')}
                onClick={(e: any) => {
                  e.stopPropagation();
                  if (!isTouchDeviceOrMd) {
                    if (isUserAuthorized) {
                      openDesktopGame(`${gameUrl}?demo=true`);
                    } else {
                      onGameClick();
                    }
                  }
                }}
              />
            ) : (
              <div className='mt-auto' />
            )}
          </div>
        )}
      </div>
      {item.name ? (
        <Typography
          href={canGameBeOpenedByLink ? gameUrl : ''}
          onClick={(e: Event) => {
            openMobileGame(e);
            if (!isTouchDeviceOrMd) {
              if (canGameBeOpenedByLink) {
                openDesktopGame();
              }
              onGameClick();
            }
          }}
          className={clsx('gameCard__title', isTouchDeviceOrMd && 'isMobile')}
          text={item.name}
          fontSize='14px'
          fontWeight={theme.font.weight.medium}
          component={Link}
          textAlign='center'
          margin='5px 0 0 0'
          withEllipsis
        />
      ) : (
        <div className='loadingTitle mx-auto mb-[30px] mt-[5px] h-4 w-1/2 rounded bg-gray-600 max-sm:mb-0' />
      )}

      {page !== 'provider' && gameProvider?.id && (
        <Typography
          href={`/${lng}/${gameProvider.is_live ? 'live-casino' : 'casino'}/games/all?filters=provider_id=${gameProvider.id}`}
          className={clsx(
            'gameCard__provider flex items-center justify-center max-md:!hidden',
            isHovered && 'hovered',
          )}
          text={gameProvider.name}
          fontSize='12px'
          fontWeight={theme.font.weight.regular}
          component={Link}
          onClick={() => {
            openDesktopGame();
          }}
        >
          {gameProvider.photo_url_secondary && (
            <Image
              src={gameProvider.photo_url_secondary}
              alt={`${gameProvider.name}-icon`}
              height={16}
              width={16}
              style={{ margin: '-2px 3px 0 0', width: 16, height: 16 }}
              unoptimized
            />
          )}
          <span className='truncate'>{gameProvider.name}</span>
        </Typography>
      )}
      {/*/!*provider info is still in progress: *!/*/}
      {/*{page !== 'provider' && !gameProvider?.id && (*/}
      {/*  <div className='h-[23px] w-full max-md:!hidden' />*/}
      {/*)}*/}
    </div>
  );
});

export default GameCard;
