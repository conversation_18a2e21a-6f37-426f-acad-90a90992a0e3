'use client';
import { getGames } from '@/app/api/getGames.ts';
import GamesList from '@/app/components/Games/GamesList.tsx';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { useRtpGamesSelector } from '@/app/store/selectors.ts';
import { useEffect } from 'react';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';
import { useTranslation } from '@/app/i18n/client';
import { LanguagesType } from '@/types/global';
import { useCookies } from 'react-cookie';

const RTP_DATA = {
  hot: { filterKey: 'rtp_hot__gte', sort: 'rtp_hot=desc', category: 'rtp_hot' },
  cold: {
    filterKey: 'rtp_cold__gte',
    sort: 'rtp_cold=asc',
    category: 'rtp_cold',
  },
};

const LIMIT = 20;

const GamesListRtp = ({
  type,
  lng,
}: {
  type: 'hot' | 'cold';
  lng: LanguagesType;
}) => {
  const [cookies] = useCookies(['userCurrency']);

  const { t } = useTranslation(lng);
  const { isInitiallyLoaded, saveGames, total, items } =
    useRtpGamesSelector(type);

  useEffect(() => {
    if (!isInitiallyLoaded) {
      getGames(
        0,
        LIMIT,
        cookies?.userCurrency,
        { [RTP_DATA[type].filterKey]: ['0'] },
        RTP_DATA[type].sort,
      ).then((result) => {
        if (saveGames) {
          saveGames({
            category: 'rtp',
            rtpType: type,
            gamesResult: { items: result.items, total: result.total },
          });
        }
      });
    }
  }, [isInitiallyLoaded]);

  if (!isInitiallyLoaded) {
    return <LoadingGameCardsGrid itemsCount={LIMIT} />;
  }

  return !total ? (
    <Typography
      text={t('noMatchedGames')}
      justifyContent='center'
      type='body2'
      color={theme.color?.general.lighter}
    />
  ) : (
    <GamesList
      initialData={items}
      totalItems={16}
      limit={LIMIT}
      // filtersMain={filters}
      category={RTP_DATA[type].category}
      isBadgeShown={false}
      withLoadMore={false}
      isInitiallyLoaded
    />
  );
};

export default GamesListRtp;
