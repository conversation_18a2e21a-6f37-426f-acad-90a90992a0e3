import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import GamesListRtp from '@/app/components/Games/GamesListRtp.tsx';

interface CategoryPageProps {
  params: { lng: LanguagesType };
}

const GamesPageRtp = async ({ params: { lng } }: CategoryPageProps) => {
  const { t } = await getServerTranslation(lng);

  return (
    <>
      <div className='flex items-center gap-[20px]'>
        <Typography
          text={t('gamesWithHighRtp')}
          type='h1'
          iconName='chartIncreasing'
        />
      </div>
      <Typography
        text={t('keepAnEyeOnHighRtpGames')}
        fontWeight={500}
        margin='10px 0 20px 0'
      />

      <GamesListRtp type='hot' lng={lng} />

      <div className='mt-[22px] flex items-center gap-[20px]'>
        <Typography
          text={t('gamesWithLowRtp')}
          type='h2'
          lineHeight='initial'
          iconName='chartDecreasing'
        />
      </div>
      <Typography
        text={t('keepAnEyeOnLowRtpGames')}
        fontWeight={500}
        margin='10px 0 20px 0'
      />

      <GamesListRtp type='cold' lng={lng} />
    </>
  );
};

export default GamesPageRtp;
