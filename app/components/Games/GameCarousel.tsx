import { getGames } from '@/app/api/getGames.ts';
import GameCard from '@/app/components/Games/GameCard';
import Slider from '@/app/components/Slider/Slider.tsx';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import 'keen-slider/keen-slider.min.css';
import { GET_GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';
import { GameGroup } from '@/app/[lng]/[gamePageType]/(landing-pages)/page.tsx';

export const LANDING_GAMES_GROUPS = {
  'live-casino': {
    top: {
      icon: 'starFlying',
      filter: { top__gte: [0] },
      sort: 'top=asc',
      perView: 7,
    },
    roulette: {
      icon: 'roulette',
      filter: { game_type: ['roulette'] },
      sort: '',
    },
    blackjack: {
      icon: 'cards',
      filter: { game_type: ['blackjack'] },
      sort: '',
      perView: 7,
    },
    baccarat: {
      icon: 'man',
      filter: { game_type: ['baccarat'] },
    },
    videoPoker: {
      icon: 'chipPlay',
      filter: { game_type: ['video_poker'] },
    },
    showsGames: {
      icon: 'videoPlay',
      filter: { game_type: ['tv_shows'] },
    },
  },
  casino: {
    top: {
      icon: 'star',
      filter: { top__gte: [0] },
      sort: 'top=asc',
      perView: 7,
      iconProps: { fill: '#0083FF' },
    },
    hot: {
      icon: 'fire',
      filter: { hot__gte: [0] },
      sort: 'hot=desc',
    },
    Live: {
      icon: 'chip',
      sort: 'popularity=asc',
      // filter: { is_live: ['true'] },
      perView: 7,
    },
    rtp: {
      icon: 'graph',
      filter: { rtp__gte: [0] },
    },
    new: {
      icon: 'newIcon',
      sort: 'release_date=desc,external_id=desc',
      maxLimit: 100,
      iconProps: { fill: '#489F37', width: 24, height: 24 },
    },
    crash: {
      icon: 'rocket',
      filter: { game_type: ['crash'] },
    },
  },
} as any; //'all'?// 'new', 'rtp', 'bonus'

const LIMIT = 36;

const GameCarousel = async ({
  gamePageType,
  group,
  icon,
  lng,
  isBadgeShown,
  iconProps,
}: {
  gamePageType: GamePageType;
  group: GameGroup;
  icon: string;
  lng: LanguagesType;
  isBadgeShown?: boolean;
  iconProps?: any;
}) => {
  const { t } = await getServerTranslation(lng);

  const mainFilter = LANDING_GAMES_GROUPS[gamePageType][group]?.filter;
  const sort = LANDING_GAMES_GROUPS[gamePageType][group]?.sort;
  const maxLimit = LANDING_GAMES_GROUPS[gamePageType][group]?.maxLimit;
  const perView = LANDING_GAMES_GROUPS[gamePageType][group]?.perView;

  const filters = {
    ...mainFilter,
    is_live:
      group === 'Live'
        ? ['true']
        : [(gamePageType === 'live-casino').toString()],
  };

  const games = await getGames(0, LIMIT, filters, sort);

  if (!games.items?.length) {
    return null;
  }

  return (
    <div
      className={`gameCarousel ${String(group)}Carousel flex flex-col gap-y-2 md:gap-y-4`}
    >
      <Typography
        type='h1'
        text={t(String(group))}
        iconName={icon}
        iconProps={{
          width: 18,
          height: 18,
          ...iconProps,
        }}
      />
      <Slider
        items={games.items}
        total={games.total}
        Slide={GameCard}
        isBadgeShown={isBadgeShown}
        filters={filters}
        sort={sort}
        isCarousel
        limit={LIMIT}
        maxLimit={maxLimit}
        perView={perView || 8}
        withLoadMore
        buttonText={t('all')}
        buttonHref={
          group === 'Live'
            ? `/${lng}/live-casino/games/all`
            : `/${lng}/${gamePageType}/games/${String(group)}`
        }
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(perView || 8)}
      />
    </div>
  );
};

export default GameCarousel;
