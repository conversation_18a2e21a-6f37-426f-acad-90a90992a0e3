import { getGames } from '@/app/api/getGames.ts';
import GameCard from '@/app/components/Games/GameCard';
import Slider from '@/app/components/Slider/Slider.tsx';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import 'keen-slider/keen-slider.min.css';
import { GET_GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';
import { GameGroup } from '@/app/[lng]/[gamePageType]/(landing-pages)/page.tsx';
import { cookies } from 'next/headers';
import { LANDING_GAMES_GROUPS } from '@/app/components/Games/config.ts';

const LIMIT = 36;

const GameCarousel = async ({
  gamePageType,
  group,
  icon,
  lng,
  isBadgeShown,
  iconProps,
}: {
  gamePageType: GamePageType;
  group: GameGroup;
  icon: string;
  lng: LanguagesType;
  isBadgeShown?: boolean;
  iconProps?: any;
}) => {
  const { t } = await getServerTranslation(lng);
  const userCurrency = cookies().get('userCurrency')?.value;

  const mainFilter = LANDING_GAMES_GROUPS[gamePageType][group]?.filter;
  const sort = LANDING_GAMES_GROUPS[gamePageType][group]?.sort;
  const maxLimit = LANDING_GAMES_GROUPS[gamePageType][group]?.maxLimit;
  const perView = LANDING_GAMES_GROUPS[gamePageType][group]?.perView;

  const filters = {
    ...mainFilter,
    is_live:
      group === 'Live'
        ? ['true']
        : [(gamePageType === 'live-casino').toString()],
  };

  const games = await getGames(0, LIMIT, userCurrency, filters, sort);

  if (!games?.items?.length) {
    return null;
  }

  return (
    <div
      className={`gameCarousel ${String(group)}Carousel flex flex-col gap-y-2 md:gap-y-4`}
    >
      <Typography
        type='h1'
        text={t(String(group))}
        iconName={icon}
        iconProps={{
          width: 18,
          height: 18,
          ...iconProps,
        }}
      />
      <Slider
        items={games.items}
        total={games.total}
        Slide={GameCard}
        isBadgeShown={isBadgeShown}
        filters={filters}
        sort={sort}
        isCarousel
        limit={LIMIT}
        maxLimit={maxLimit}
        perView={perView || 8}
        withLoadMore
        buttonText={t('all')}
        buttonHref={
          group === 'Live'
            ? `/${lng}/live-casino/games/all`
            : `/${lng}/${gamePageType}/games/${String(group)}`
        }
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(perView || 8)}
      />
    </div>
  );
};

export default GameCarousel;
