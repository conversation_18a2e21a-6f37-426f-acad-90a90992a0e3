'use client';
import { useSystemData } from '@/app/wrappers/systemDataProvider';
import { Tag } from '@/atomic-design-components';
import { GENRES } from '@/constants';
import { LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const GenreButton = ({
  searchFilters,
  lng,
  isInputChildren,
}: {
  searchFilters: any;
  lng: LanguagesType;
  isInputChildren?: boolean;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const [currGenre, setCurrGenre] = useState('');
  const [currProv, setCurrProv] = useState('');

  const [{ providers = [] }] = useSystemData();

  useEffect(() => {
    const currGenreItem = GENRES.find(
      (g) => g.id === searchFilters.genres?.[0],
    );

    if (currGenreItem) {
      setCurrGenre(getAvailableTranslation(currGenreItem.label, lng) || '');
    } else {
      setCurrGenre('');
    }

    const currProvItem = providers?.find(
      (p) => p.id == searchFilters.provider_id?.[0],
    );

    if (currProvItem) {
      setCurrProv(currProvItem.name || '');
    } else {
      setCurrProv('');
    }
  }, [searchFilters]);

  const onRemove = (filterKey: string) => {
    const params = { ...searchFilters };
    delete params[filterKey];

    let url = pathname;
    if (params && Object.keys(params)?.length) {
      let filtersArray: string[] = [];
      Object.keys(params).forEach((key) => {
        filtersArray.push(`${key}=${params[key].join('|')}`);
      });
      url = `${url}?filters=${filtersArray.join(',')}`;
    }
    router.push(url);
  };

  if (!currGenre && !currProv) return null;

  if (isInputChildren)
    return (
      <div className='flex w-full gap-1'>
        {currGenre && (
          <Tag
            text={currGenre}
            withCrossIcon
            type='lightGray'
            size='small'
            onClick={(e: any) => {
              e.stopPropagation();
              onRemove('genres');
            }}
            color={undefined}
          />
        )}
        {currProv && (
          <Tag
            text={currProv}
            withCrossIcon
            type='lightGray'
            size='small'
            color={undefined}
            onClick={(e: any) => {
              e.stopPropagation();
              onRemove('provider_id');
            }}
          />
        )}
      </div>
    );

  return (
    <div className='flex gap-2'>
      {currGenre && (
        <Tag
          text={currGenre}
          onClick={() => onRemove('genres')}
          withCrossIcon
          type='primary'
          size='medium'
          color={undefined}
        />
      )}
      {currProv && (
        <Tag
          text={currProv}
          onClick={() => onRemove('provider_id')}
          withCrossIcon
          type='primary'
          size='medium'
          color={undefined}
        />
      )}
    </div>
  );
};

export default GenreButton;
