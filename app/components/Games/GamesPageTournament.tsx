'use client';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { IGamesResponse, getGamesTournaments } from '@/app/api/getGames.ts';
import { ITournament } from '@/app/api/getTournaments';
import GamesList from '@/app/components/Games/GamesList.tsx';
import GamesListRecommended from '@/app/components/Games/GamesListRecommended';
import PanelWithIconAndText from '@/app/components/PanelWithIconAndText';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Select, Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { isObjectEmpty } from '@/utils/object';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';
import { useGamesInitialSelector } from '@/app/store/selectors.ts';

interface CategoryPageProps {
  params: {
    lng: LanguagesType;
    tournaments: ITournament[];
    gamePageType: GamePageType;
  };
}

const LIMIT = 32;

const GamesPageTournament = ({
  params: { lng, tournaments, gamePageType },
}: CategoryPageProps) => {
  const searchParams = useSearchParams();
  const tournamentParam = searchParams.get('tournament');
  const { t } = useTranslation(lng);
  const router = useRouter();
  const [games, setGames] = useState<IGamesResponse>({ items: [], total: 0 });
  const [tournamentSlug, setTournamentSlug] = useState<string | null>(() =>
    tournamentParam &&
    tournaments.some((tournament) => tournament?.slug === tournamentParam)
      ? tournamentParam
      : tournaments?.[0]?.slug || null,
  );
  const [isLoaded, setIsLoaded] = useState(false);

  const { user } = useUser();
  const hasActiveBonus = user?.active_bonus_id;

  const filters = {
    is_live: [(gamePageType === 'live-casino').toString()],
  };

  // initial data for page switch - with first active tournament from list
  const {
    isInitiallyLoaded,
    saveGames: saveGamesInitialFirstTournament,
    total: totalInitialFirstTournament,
    items: itemsInitialFirstTournament,
    initialPageFilterId,
  } = useGamesInitialSelector('tournament', gamePageType);

  const fetchGames = async (tournamentId?: number) => {
    if (!tournaments?.length) {
      return;
    }

    let id = tournamentId;

    if (!id && tournamentSlug) {
      const tournament = tournaments?.find(
        (tournament) => tournament.slug === tournamentSlug,
      );
      id = tournament?.id;
    }

    if (!id) {
      id = tournaments?.[0]?.id;
    }

    if (
      !id ||
      !tournaments.some((tournament) => tournament?.slug === tournamentParam)
    ) {
      const url = `/${lng}/casino/games/tournament?tournament=${tournaments?.[0]?.slug}`;
      router.replace(url);
    }

    if (
      isInitiallyLoaded &&
      id === tournaments?.[0]?.id &&
      initialPageFilterId === tournaments?.[0]?.id
    ) {
      setGames({
        items: itemsInitialFirstTournament,
        total: totalInitialFirstTournament,
      });
      return;
    }

    const initGames = await getGamesTournaments(0, LIMIT, {
      tournament_id: [id],
    });

    setGames(initGames);
    if (
      (!isInitiallyLoaded && id === tournaments?.[0]?.id) ||
      (initialPageFilterId && initialPageFilterId !== tournaments?.[0]?.id)
    ) {
      saveGamesInitialFirstTournament({
        gamePageType,
        category: 'tournament',
        gamesResult: { items: initGames.items, total: initGames.total },
        initialPageFilterId: id,
      });
    }
  };

  useEffect(() => {
    fetchGames().finally(() => setIsLoaded(true));
  }, []);

  const tournamentOptions = tournaments?.map((tournament: ITournament) => ({
    id: tournament.id,
    label: isObjectEmpty(tournament.title)
      ? tournament.name
      : getAvailableTranslation(tournament.title, lng),
    slug: tournament.slug,
  }));

  const onFilterChange = (value: any) => {
    fetchGames(value.id);
    setTournamentSlug(value.slug);
    let url = `/${lng}/casino/games/tournament?tournament=${value.slug}`;
    router.push(url);
  };

  const getEmptyText = () => {
    const noActiveTournaments = !tournaments?.length;

    if (noActiveTournaments) {
      return (
        <>
          <PanelWithIconAndText
            icon='disappointedFace'
            title={t('noActiveTournaments')}
            text={t('tryGames')}
          />
          <GamesListRecommended lng={lng} />
        </>
      );
    } else {
      return (
        <PanelWithIconAndText
          icon='disappointedFace'
          title={t('nothingFound')}
        />
      );
    }
  };

  if (!isLoaded) {
    return (
      <>
        <div className='flex items-center max-md:justify-start md:justify-between'>
          <div className='mb-4 flex items-center gap-4 max-md:hidden'>
            <Typography text={t('tournamentss')} type='h1' iconName='cup' />
          </div>
        </div>
        <LoadingGameCardsGrid />
      </>
    );
  }

  return (
    <>
      <div className='flex items-center max-md:justify-start md:justify-between'>
        <div className='mb-4 flex items-center gap-4 max-md:hidden'>
          <Typography text={t('tournamentss')} type='h1' iconName='cup' />
        </div>
        {!!tournamentOptions?.length && (
          <Select
            options={tournamentOptions}
            valueKey='slug'
            placeholder={t('chooseTournament')}
            className='mb-4 min-w-60 max-md:w-full'
            isSearchable={false}
            onChange={onFilterChange}
            value={tournamentOptions.find(
              (tournament) => tournament.slug === tournamentSlug,
            )}
          />
        )}
      </div>
      {!games.total ? (
        getEmptyText()
      ) : (
        <>
          {hasActiveBonus && (
            <Typography
              text={t('youHaveActiveBonusTournamentsHint')}
              type='body2'
              className='!mb-3 justify-center text-center'
            />
          )}
          <GamesList
            initialData={games.items}
            totalItems={games.total}
            limit={LIMIT}
            filtersMain={filters}
            isTournamentsPage={true}
            isInitiallyLoaded
            tournamentId={
              tournaments?.find(
                (tournament) => tournament.slug === tournamentSlug,
              )?.id
            }
          />
        </>
      )}
    </>
  );
};

export default GamesPageTournament;
