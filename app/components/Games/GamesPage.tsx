'use client';
import GamesList from '@/app/components/Games/GamesList.tsx';
import { ENTITIES } from '@/app/config/navMenuEntities.ts';
import { GamePageType, LanguagesType } from '@/types/global';
import { GameCategoryType } from '@/app/store/slices/gamesSlice.ts';
import { useEffect } from 'react';
import { getGames, IGame } from '@/app/api/getGames.ts';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useGamesInitialSelector } from '@/app/store/selectors.ts';

interface CategoryPageProps {
  params: {
    category: GameCategoryType;
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
  searchParams: { [key: string]: string | undefined };
}

const LIMIT = 32;

const GamesPage = ({
  params: { category, gamePageType },
  searchParams,
}: CategoryPageProps) => {
  const { isBonusFilterActive } = useUser();

  const currentEntity = Object.values(ENTITIES.all)
    .flat()
    .find((entity) => entity.route.includes(category));

  const mainFilter = currentEntity?.filter ?? {};
  const filter = {
    ...mainFilter,
    is_live: [(gamePageType === 'live-casino').toString()],
  };

  const { isInitiallyLoaded, saveGames, total, items } =
    useGamesInitialSelector(category, gamePageType);

  useEffect(() => {
    if (!isInitiallyLoaded && !searchParams.filters) {
      getGames(0, 32, filter, currentEntity?.sort).then((result) => {
        if (saveGames) {
          saveGames({
            gamePageType,
            category,
            gamesResult: { items: result.items, total: result.total },
          });
        }
      });
    }
  }, [isInitiallyLoaded, searchParams.filters]);

  if (!isInitiallyLoaded && !searchParams.filters) {
    return <LoadingGameCardsGrid />;
  }

  const itemsInitial =
    isBonusFilterActive && gamePageType === 'casino'
      ? items.filter(
          (item: IGame) => item?.wager_percentage && item.wager_percentage >= 1,
        )
      : items;

  return (
    <GamesList
      initialData={itemsInitial}
      totalItems={total}
      limit={LIMIT}
      maxLimit={currentEntity?.maxLimit}
      filtersMain={filter}
      category={category}
      sort={currentEntity?.sort}
      isBadgeShown={false}
      isInitiallyLoaded={isInitiallyLoaded}
    />
  );
};

export default GamesPage;
