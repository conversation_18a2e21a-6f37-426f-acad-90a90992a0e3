'use client';
import { useEffect, useRef, useState } from 'react';

import { getGameByIdOrSlug, IGame } from '@/app/api/getGames';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import ModalDialog from '../ModalDialog';
import GameCardForMobileModal from './GameCardForMobileModal';

const GameModal = () => {
  const ref = useRef<HTMLDialogElement>(null);
  const { isDialogOpen } = useIsDialogOpen(ref);
  const [item, setItem] = useState<IGame | null>(null);

  useEffect(() => {
    if (isDialogOpen) {
      const slug = ref?.current?.getAttribute('data-active-game-slug');
      if (slug) {
        fetch(slug);
      }
    } else {
      ref?.current?.setAttribute('data-active-game-slug', '');
      setItem(null);
    }
  }, [isDialogOpen]);

  const fetch = async (slug: string) => {
    const item = await getGameByIdOrSlug(slug);
    setItem(item);
  };

  return (
    <ModalDialog id='gameModal' ref={ref}>
      {item ? <GameCardForMobileModal item={item} /> : <></>}
    </ModalDialog>
  );
};

GameModal.displayName = 'GameModal';
export default GameModal;
