.gameCard {
    border-radius: 8px;
    align-items: center;
    position: relative;
    background-color: #1e293b;
    overflow: hidden;
    min-height: 20px;

    &:not(.tournamentImageCard, .promotionCard, .promoLargeCard,
    .promotionCardSmall, .getBonusPromoCard, .providerCard, .partnerCard, .postCard, .getBonusCard) {
        aspect-ratio: 67 / 100;
    }

    &.rtpCard {
        aspect-ratio: 165 / 256;

        .hoverImageDiv {
            margin-bottom: 26px;

            img {
                border-bottom-left-radius: 0;
                border-bottom-right-radius: 0;
            }
        }

        .rtpHot {
            background: linear-gradient(
                    90deg,
                    rgba(37, 58, 94, 0.5) 0%,
                    rgba(187, 0, 0, 0.5) 100%
            ),
            #232c3b;
        }

        .rtpCold {
            background: linear-gradient(
                    90deg,
                    rgba(37, 58, 94, 0.5) 0%,
                    rgba(0, 96, 187, 0.5) 100%
            ),
            #232c3b;
        }

        .gameCard__overlay {
            height: calc(100% - 26px);
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 0;
        }
    }

    &.gameCardSmall {
        /*padding: 19% 0;*/
        /*width: 50px;*/
        min-height: 76px;
    }

    &.gameCardGameOverlay {
        padding: 0;
        aspect-ratio: 67 / 100;
        @media screen and (max-width: 620px) {
            padding: 0;
        }
    }

    &.modalImg {
        width: 188px;
        height: 280px;
        padding: 0;
        transition: visibility 0.3s ease-in-out,
        opacity 0.3s ease-in-out;

        &.opened {
            animation: scale-up-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
        }

        @media screen and (max-height: 500px) {
            visibility: hidden;
            opacity: 0;
        }
    }

    &.gameCardWinnings {
        height: 128px;
        width: 86px;
        padding: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;

        img {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .gameCard__overlay {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }
    }

    &.promoLargeCard {
        max-width: 500px;
        max-height: 320px;
        padding: 20% 0;
    }

    &.providerCard {
        padding: 0 10%;

        &.hovered img {
            filter: none;
        }

        img {
            filter: grayscale(100%);
            width: 140px;
            height: 70px;
        }

        &.placeholder,
        &.loading {
            width: 100%;
            height: 70px;

            img {
                padding: 0;
                height: 70px !important;
            }
        }
    }

    &.postCard {
        padding: 35% 5%;
        background-color: transparent;

        &.placeholder,
        &.loading {
            img {
                padding: 2% 4%;
            }
        }
    }

    &.partnerCard {
        padding: 8% 5%;
        background-color: transparent;
    }

    &.promotionCard {
        padding: 20% 0;
        min-height: 200px;

        img {
            &.hovered {
                transform: scale(1.05);
            }

            transition: transform 0.5s ease;
        }

        @media only screen and (min-width: 960px) {
            min-height: 228px;
        }
    }

    &.promotionCardSmall {
        padding: 10.5% 0;
        min-height: 200px;
        @media screen and (max-width: 1050px) {
            padding: 16% 0;
        }
        @media screen and (max-width: 520px) {
            padding: 33% 0;
        }
    }

    &.getBonusPromoCard {
        padding: 12% 0;
        height: 200px;
        width: 500px;
    }

    &.tournamentImageCard {
        padding: 15% 0;
        width: calc(100% - 40px);
        position: absolute;
        left: 8px;
        min-height: 200px;
        @media screen and (max-width: 960px) {
            width: calc(100% - 16px);
        }

        &.finishedCard {
            filter: grayscale(100%);
        }
    }

    &.loading {
        img:not(.placeholder) {
            visibility: hidden;
        }
    }

    img {
        border-radius: 8px;
    }

    &.placeholder,
    &.loading {
        img {
            padding: 0 20%;
        }
    }

    .hoverImageDiv {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;

        &.hovered {
            transform: scale(1.05);
        }

        transition: transform 0.5s ease;
    }
}

@media screen and (min-width: 960px) {
    .gameCard__title:not(.isMobile) {
        &:hover {
            color: #0083ff;
        }
    }
}

.gameCard__provider {
    opacity: 0;
    padding-top: 7px;

    &:hover {
        color: #0083ff;
    }

    &.hovered {
        opacity: 1;
    }
}

.gameCard__badge {
    border-radius: 0 45px 45px 0;
    width: max-content;
}

.gameCard__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 10px;
    align-items: center;
    justify-content: center;
    background-color: #0f172ab3;
    border-radius: 8px;

    .favoriteIcon {
        position: absolute;
        top: 8px;
        right: 8px;

        &:hover {
            opacity: 0.7;
        }
    }

    .playIcon {
        position: absolute;
        top: 0;
        bottom: 0;
        display: flex;
        margin-top: -10px;
    }

    &.topWinsCard {
        .playIcon {
            margin-top: -26px;
        }
    }
}
