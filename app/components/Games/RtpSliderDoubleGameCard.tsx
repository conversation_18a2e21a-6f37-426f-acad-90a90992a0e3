'use client';
import GameCard from '@/app/components/Games/GameCard.tsx';

const RtpSliderDoubleGameCard = ({
  item,
  index,
}: {
  item: any;
  index: number;
}) => {
  return (
    <div className='flex flex-col gap-y-[10px]'>
      <GameCard item={item.hot} category='rtp_hot' index={index} />
      <GameCard item={item.cold} category='rtp_cold' index={index} />
    </div>
  );
};

export default RtpSliderDoubleGameCard;
