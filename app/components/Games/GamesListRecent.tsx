'use client';
import PanelWithIconAndText from '@/app/components/PanelWithIconAndText';
import GamesList from '@/app/components/Games/GamesList.tsx';
import GamesListRecommended from '@/app/components/Games/GamesListRecommended.tsx';
import { useTranslation } from '@/app/i18n/client';
import { LanguagesType } from '@/types/global';
import { useEffect } from 'react';
import { useGamesInitialSelector } from '@/app/store/selectors.ts';
import { getRecentGamesClient } from '@/app/api/user/games/getRecentGamesClient.ts';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';

const GamesListRecent = ({ lng }: { lng: LanguagesType }) => {
  const { t } = useTranslation(lng);

  const { isInitiallyLoaded, saveGames, total, items } =
    useGamesInitialSelector('recent');

  useEffect(() => {
    if (!isInitiallyLoaded) {
      getRecentGamesClient(0, 20).then((result) => {
        if (saveGames) {
          saveGames({
            category: 'recent',
            gamesResult: { items: result.items, total: result.total },
          });
        }
      });
    }
  }, [isInitiallyLoaded]);

  if (!isInitiallyLoaded) {
    return <LoadingGameCardsGrid itemsCount={20} />;
  }

  return (
    <>
      {!items.length && (
        <PanelWithIconAndText
          icon='slotMachine'
          title={t('recentGamesTitle')}
        />
      )}
      {items.length > 0 && (
        <GamesList
          initialData={items}
          totalItems={total}
          limit={20}
          withLoadMore={false}
          category='recent'
          isInitiallyLoaded
        />
      )}
      <GamesListRecommended lng={lng} gamesToFilterOut={items} />
    </>
  );
};

export default GamesListRecent;
