import styled from 'styled-components';
import clsx from 'clsx';
import Link from 'next/link';
import { openModal } from '@/utils/openModal.ts';
import { Button } from '@/atomic-design-components';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { closeModal } from '@/utils/closeModal.ts';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';
import useParseRichText from '@/hooks/useParseRichText.tsx';

const StyledLink = styled.a`
  #promotionModal & {
    text-decoration: none;
  }
`;

const TargetUrlButton = ({
  target_url,
  buttonText,
  isPromoButton,
  isTouchDevice,
}: {
  target_url: string;
  buttonText: string;
  isPromoButton?: boolean;
  isTouchDevice?: boolean;
}) => {
  const { openModalByUrl } = useRegisterModalToOpenByUrl();
  const { getPagesUrlComparedValues } = useParseRichText();

  const { isSamePage, withOpenModalParam, isPathRestricted, formattedLink } =
    getPagesUrlComparedValues(target_url);

  const { setNextPathAfterLogin } = useUser();
  const { setActiveAuthTab } = useNavigation();

  const isInnerTargetUrl = target_url?.includes('likecasino.com');
  const buttonUrl = formattedLink;

  return (
    <StyledLink
      as={isPathRestricted || isSamePage ? 'div' : isInnerTargetUrl ? Link : 'a'}
      href={isPathRestricted || isSamePage ? undefined : buttonUrl}
      className={isPromoButton ? 'w-full' : 'w-fit'}
      target={isInnerTargetUrl ? undefined : '_blank'}
      rel={isInnerTargetUrl ? undefined : 'noopener noreferrer'}
    >
      <Button
        variant='primary'
        text={buttonText}
        className={
          isPromoButton
            ? clsx(!isTouchDevice && 'sm:ml-auto sm:w-max', 'w-full min-w-[100px]')
            : undefined
        }
        onClick={
          isPathRestricted
            ? () => {
                setNextPathAfterLogin(buttonUrl);
                if (isPromoButton) {
                  closeModal('promotionModal');
                }
                setActiveAuthTab('signUp');
                openModal('authModal');
              }
            : () => {
                if (isInnerTargetUrl && isPromoButton) {
                  closeModal('promotionModal');
                }

                if (isSamePage && withOpenModalParam) {
                  openModalByUrl(target_url.split('modal=')[1].split('&')[0]);
                }
              }
        }
      />
    </StyledLink>
  );
};

export default TargetUrlButton;
