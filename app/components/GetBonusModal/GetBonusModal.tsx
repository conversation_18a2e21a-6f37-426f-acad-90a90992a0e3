'use client';
import parse from 'html-react-parser';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import clsx from 'clsx';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography, Image } from '@/atomic-design-components';
import { IBanner } from '@/app/api/getBanners';
import ModalDialog from '@/app/components/ModalDialog';
import useClickOutside from '@/hooks/useClickOutside';
import { closeModal } from '@/utils/closeModal';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { theme } from '@/theme';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import useWindowSize from '@/hooks/useWindowSize';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';

const GetBonusModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isTouchDeviceOrSm = isTouchDevice || (width && width < theme.breakpoints?.sm);
  const [banners, setBanner] = useState<{
    desktop?: IBanner;
    mobile?: IBanner;
  }>({});
  const [bannersFetched, setBannersFetched] = useState(false);
  const ref = useRef(null);
  const banner = isTouchDeviceOrSm ? banners.mobile : banners.desktop;
  useClickOutside(ref);

  const [{ banners: bannersAll }] = useSystemData();

  const fetchBanner = async () => {
    const bannersSignupBonus =
      bannersAll?.filter((banner) => banner.banner_type === 'sign_up_bonus') || [];
    setBanner({
      mobile: bannersSignupBonus?.find((item) => item.display_mode === 'mobile'),
      desktop: bannersSignupBonus?.find((item) => item.display_mode === 'desktop'),
    });
    setBannersFetched(true);
  };

  useEffect(() => {
    if (!bannersFetched) fetchBanner();
  }, []);

  return (
    <ModalDialog
      id='getBonusModal'
      closeButtonPosition={`right-4 top-4 sm:right-6 sm:top-6 ${isTouchDevice ? 'mobileView' : ''}`}
      onClose={() => {
        closeModal('getBonusModal', false);
      }}
    >
      <div className='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'>
        <div className='mb-4 flex justify-between gap-4'>
          <Typography
            text={parse(t('forgotBonus'))}
            type='h1'
            fontSize='28px'
            displayCssProp='block'
          />
        </div>
        <div className={clsx('max-h-[calc(100vh-105px)]')}>
          <div className=''>
            <div
              className={clsx(
                'gameCard getBonusCard align-center flex cursor-pointer flex-col justify-center p-[min(100px,22dvh)_0]',
              )}
            >
              <div
                className={clsx(
                  'h-full max-h-[200px] transition-opacity duration-300',
                  banner ? 'opacity-100' : 'opacity-0',
                )}
              >
                {banner && (
                  <>
                    <Image
                      alt={banner?.target_type || `Get Bonus Background`}
                      src={banner?.photo_background_url ?? ''}
                      fill
                      sizes='500px'
                      className={clsx('h-full w-full rounded-lg object-cover object-center')}
                    />
                    <Image
                      alt={banner?.target_type || `Get Bonus Image`}
                      src={banner?.photo_main_url ?? ''}
                      sizes='500px'
                      fill
                      className={clsx('h-full w-full rounded-lg object-contain object-right')}
                    />
                  </>
                )}
                <div
                  className={clsx(
                    'absolute left-0 top-0 h-full p-8 pr-0',
                    isTouchDeviceOrSm ? 'w-[60%]' : 'w-1/2',
                  )}
                >
                  <Typography
                    text={getAvailableTranslation(banner?.title || '', lng)}
                    type={isTouchDeviceOrSm ? 'h3' : 'h2'}
                    ellipsis
                  />
                </div>
              </div>
            </div>
          </div>
          <div className='buttonContainer mt-6 flex justify-end gap-4 max-sm:flex-col sm:mt-5'>
            <Button
              variant='primary'
              type='submit'
              className='submitButton max-sm:w-full sm:order-2 sm:min-w-[120px]'
              text={t('take')}
              onClick={(e: any) => {
                e.preventDefault();
                closeModal('getBonusModal', false);
              }}
            />
            <Button
              variant='secondary'
              type='cancel'
              className='cancelButton max-sm:w-full sm:order-1 sm:min-w-[120px]'
              text={t('cancel')}
              onClick={(e: any) => {
                e.preventDefault();
                closeModal('getBonusModal');
                setTimeout(() => {
                  closeModal('authModal');
                }, 0);
              }}
            />
          </div>
        </div>
      </div>
    </ModalDialog>
  );
};

GetBonusModal.displayName = 'GetBonusModal';
export default GetBonusModal;
