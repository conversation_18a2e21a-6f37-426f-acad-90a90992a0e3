'use client';
import PasswordChecklist from 'react-password-checklist';
import styled from 'styled-components';

export const StyledPasswordChecklist = styled(PasswordChecklist)<any>`
  margin-top: 8px !important;
  &.passwordCheckList {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;

    li {
      font-size: 12px;
      line-height: 18px;
      font-weight: ${({ theme: { font } }) => font.weight.medium};
      padding: 2px 5px;
      color: ${({ theme: { color } }) => color.general.darker};
      background-color: ${({ theme: { color } }) => `${color.general.light}94`};
      border: 1px solid ${({ theme: { color } }) => color.general.dark};
      width: fit-content;
      border-radius: 6px;

      span {
        opacity: 1 !important;
        padding: 0;
      }

      &.valid {
        color: ${({ theme: { color } }) => color.general.white};
        background-color: ${({ theme: { color } }) =>
          `${color.status.success}9e`};
        border: 1px solid
          ${({ theme: { color } }) => `${color.status.success}9e`};
      }

      svg {
        display: none;
      }
    }
  }
  &.passError li {
    color: ${({ theme: { color } }) => color.general.white};
    background-color: ${({ theme: { color } }) => `${color.status.error}9e`};
    border: 1px solid ${({ theme: { color } }) => `${color.status.error}9e`};
  }
`;
