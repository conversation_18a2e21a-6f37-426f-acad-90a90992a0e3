'use client';
import { useTranslation } from '@/app/i18n/client';
import clsx from 'clsx';
import { StyledPasswordChecklist } from './styled';
import { useParams } from 'next/navigation';

interface CustomPasswordChecklistProps {
  password: string;
  setIsPasswordValid: Function;
  className: string;
}

const CustomPasswordChecklist = ({
  password,
  setIsPasswordValid,
  className,
}: CustomPasswordChecklistProps) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng, 'validation');

  return (
    <StyledPasswordChecklist
      rules={['specialChar', 'letter', 'number']}
      specialCharsRegex={/^.{6,20}$/}
      minLength={6}
      maxLength={20}
      value={password}
      messages={{
        specialChar: t('passwordLength'),
        letter: t('letter'),
        number: t('number'),
      }}
      onChange={(isValid: boolean) => {
        setIsPasswordValid(isValid);
      }}
      className={clsx(className, 'passwordCheckList')}
    />
  );
};

export default CustomPasswordChecklist;
