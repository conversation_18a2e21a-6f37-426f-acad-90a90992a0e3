import Script from 'next/script';

const YandexMetrikaScript = () => {
  return (
    <>
      <Script id='metrika-counter' strategy='afterInteractive'>
        {`(function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
   m[i].l=1*new Date();
   for (var j = 0; j < document.scripts.length; j++) {if (document.scripts[j].src === r) { return; }}
   k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
   (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");

   ym(${process.env.NEXT_PUBLIC_YM_ID}, "init", {
        defer: true,
        clickmap:true,
        trackLinks:true,
        accurateTrackBounce:true
   });`}
      </Script>
      <noscript>
        <div>
          <img
            src={`https://mc.yandex.ru/watch/${process.env.NEXT_PUBLIC_YM_ID}`}
            style={{ position: 'absolute', left: '-9999px' }}
            alt='YM'
          />
        </div>
      </noscript>
    </>
  );
};

export default YandexMetrikaScript;
