'use client';
import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { isClientReady } from '@/utils';

export default function YandexMetrika() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const url = `${pathname}${searchParams.toString() ? '?' : ''}${searchParams}`;
    if (!isClientReady()) {
      return;
    }
    window.ym(process.env.NEXT_PUBLIC_YM_ID, 'hit', url);
  }, [pathname, searchParams]);

  return null;
}
