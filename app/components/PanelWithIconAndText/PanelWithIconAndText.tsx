import { Button, Icon, Typography } from '@/atomic-design-components';
import { isObjectEmpty } from '@/utils/object.ts';
import clsx from 'clsx';

const PanelWithIconAndText = ({
  icon,
  iconProps = {},
  title,
  text,
  buttonText,
  buttonChildren,
  className,
}: {
  icon?: string;
  iconProps?: object;
  title?: string;
  text?: string;
  buttonText?: string;
  buttonChildren?: any;
  className?: string;
}) => (
  <div
    className={clsx(
      'flex min-h-[230px] flex-col items-center justify-center gap-y-6 rounded-lg bg-[#1E293B]',
      className,
    )}
  >
    {(icon || !isObjectEmpty(iconProps)) && <Icon name={icon} {...iconProps} />}
    {title && <Typography type='h2' text={title} />}
    {text && <Typography type='body1' text={text} />}
    {buttonText ||
      (buttonChildren && (
        <Button variant='secondary' text={buttonText}>
          {buttonChildren}
        </Button>
      ))}
  </div>
);

export default PanelWithIconAndText;
