'use client';
import { theme } from '@/theme.ts';
import { ReactNode } from 'react';

const ProgressBar = ({
  children,
  color,
  currentProgress,
  total,
}: {
  children?: ReactNode;
  color?: string;
  currentProgress: number;
  total: number;
}) => {
  const currentProgressChecked =
    currentProgress > total ? total : currentProgress;
  const progressBgColor = color || theme.color?.secondary.main;
  const progress = (currentProgressChecked / total) * 100;

  return (
    <>
      {/*<div className='flex items-center justify-between'>*/}
      {/*{text && <Typography text={text} type='label2' fontSize='12px' />}*/}
      {/*{currency && (*/}
      {/*  <Typography*/}
      {/*    displayCssProp='block'*/}
      {/*    type='label2'*/}
      {/*    fontSize='12px'*/}
      {/*    lineHeight='12px'*/}
      {/*    className='currency ml-auto mr-[3px] h-[16px] min-w-[17px] rounded-[2px] bg-[#475F8B] p-[3px]'*/}
      {/*    color={theme.color?.general.white}*/}
      {/*    margin='-2px 3px 0 auto'*/}
      {/*  >*/}
      {/*    {currency}*/}
      {/*  </Typography>*/}
      {/*)}*/}
      {/*<Typography*/}
      {/*  text={currentProgress}*/}
      {/*  type='label2'*/}
      {/*  fontSize='12px'*/}
      {/*  color={theme.color?.general.white}*/}
      {/*  margin={currency ? undefined : '0 0 0 auto'}*/}
      {/*/>*/}
      {/*<Typography text={`/${total}`} type='label2' fontSize='12px' />*/}
      {/*</div>*/}
      <div className='relative flex h-[4px]'>
        <div
          className='absolute left-0 top-0 z-[2] h-full rounded-full'
          style={{ width: `${progress}%`, backgroundColor: progressBgColor }}
        />
        <div className='absolute left-0 top-0 z-[1] h-full w-full overflow-hidden rounded-full bg-general-dark' />
      </div>
      {children}
    </>
  );
};

export default ProgressBar;
