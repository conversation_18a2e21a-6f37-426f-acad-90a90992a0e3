'use client';
import clsx from 'clsx';
import { useCookies } from 'react-cookie';
import ct from 'countries-and-timezones';
import { useParams, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState, Dispatch, SetStateAction } from 'react';

import { useTranslation } from '@/app/i18n/client';
import {
  Button,
  Checkbox,
  FlexRow,
  Input,
  Loader,
  Select,
  Tabs,
  Typography,
  Image,
} from '@/atomic-design-components';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { toCamelCase } from '@/utils/toCamelCase';
import { SignUpData, signUp } from '@/app/api/auth/signUp.ts';
import { getIp } from '@/app/api/getIP';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { useUser } from '@/app/wrappers/userProvider';
import PhoneInput from '@/atomic-design-components/molecules/PhoneInput/PhoneInput';
import { CURRENCY, DEFAULT_CURRENCY } from '@/constants';
import useClickOutside from '@/hooks/useClickOutside';
import { useIsPasswordShown } from '@/hooks/useIsPasswordShown';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal';
import validate, { rule } from '@/utils/formValidationRules';
import { getGamePageType } from '@/utils/getGamePageType';
import { isObjectEmpty } from '@/utils/object';
import { openModal } from '@/utils/openModal';
import { ReCaptcha } from '@/app/components/ReCaptcha/ReCaptcha';
import { useReCaptcha } from '@/app/wrappers/ReCaptchaProvider';
import useCountries from '@/hooks/useCountries';
import { parsePhoneNumber } from 'react-phone-number-input';
import CustomPasswordChecklist from '@/app/components/Form/CustomPasswordChecklist.tsx';
import { clearSignupLocalStorage, getFromLocalStorage } from '@/utils/localStorage';
import { IBanner } from '@/app/api/getBannersServer.ts';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import setUserCurrencyCookies from '@/app/api/server-actions/setUserCookies.ts';

interface SignUpFormProps {
  banner?: IBanner;
  isInProgress: Dispatch<SetStateAction<boolean>>;
}

const SignUpForm = ({ banner, isInProgress }: SignUpFormProps) => {
  const { isTouchDevice } = useIsTouchMobileView();
  const { lng, gamePageType }: { lng: LanguagesType; gamePageType: GamePageType } = useParams();
  const gamePageTypeChecked = getGamePageType(gamePageType);

  const searchParams = useSearchParams();

  const { t } = useTranslation(lng, 'validation');
  const { showAlert, showError } = useAlert();
  const ref = useRef(null);
  useClickOutside(ref);
  const { setUser, setIsRegistrationJustDone } = useUser();
  const { checkRecaptchaV3, checkRecaptchaV2 } = useReCaptcha();
  const { countries } = useCountries();

  const [password, setPassword] = useState('');
  const [isPasswordValid, setIsPasswordValid] = useState(undefined);

  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [currency, setCurrency] = useState('');
  const [promoCode, setPromoCode] = useState<string>();
  const [email, setEmail] = useState<string>('');
  const [country, setCountry] = useState<string>(
    ct.getCountryForTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)?.id || '',
  );
  const [dialCode, setDialCode] = useState<string>(
    countries.find((c: any) => c.code === country)?.dial_code || '',
  );
  const [phone, setPhone] = useState<string>('');
  const [errors, setErrors] = useState<any>({});
  const [terms, setTerms] = useState(true);
  const [hasPromocode, setHasPromocode] = useState(false);
  const [inProgress, setInProgress] = useState(false);
  const [activeField, setActiveField] = useState(0);
  const [recaptchaRequired, setRecaptchaRequired] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [cookies] = useCookies(['userCurrency']);

  const validationRulesInitial = {
    email: activeField === 0 ? ['required', 'email'] : [],
    phone: activeField === 1 ? ['required', 'phone'] : [],
    password: ['required', 'password'],
    currency: ['required'],
    terms: ['required'],
  };

  const initialValuesChangedBeforeCheck = {
    email,
    phone: phone ? dialCode + phone : '',
    password,
    currency,
    terms,
  };

  const submitForm = async () => {
    const errors = validate(validationRulesInitial)(initialValuesChangedBeforeCheck, {
      values: initialValuesChangedBeforeCheck,
    });

    setErrors(errors);
    if (isObjectEmpty(errors)) {
      setInProgress(true);
      // check if need to show reCaptcha checkbox
      try {
        const checkRecaptcha = await checkRecaptchaV3('signUp');
        if (!checkRecaptcha) {
          setRecaptchaRequired(true);
          openModal('reCaptchaModal', false, false, false);
          return;
        }
        completeFormSubmit();
      } catch (error) {
        showError(t('recaptchaBlocked'));
        setInProgress(false);
      }
    }
  };

  const onRecaptchaSubmit = async () => {
    const checkRecaptcha = await checkRecaptchaV2(recaptchaToken!);
    closeModal('reCaptchaModal', false);
    setRecaptchaToken(null);
    setRecaptchaRequired(false);
    if (!checkRecaptcha) {
      showError(t('recaptchaFailed'));
      setInProgress(false);
      return;
    }
    completeFormSubmit();
  };

  const completeFormSubmit = async () => {
    let requestBody: SignUpData = {
      clickid: getFromLocalStorage('clickid') || '',
      click_time: getFromLocalStorage('click_time') || '',
      country: country,
      currency,
      email: email && activeField === 0 ? email : '',
      ip_address: await getIp(),
      landing: getFromLocalStorage('landing') || '',
      language: lng,
      password,
      phone:
        phone && activeField === 1
          ? parsePhoneNumber(dialCode + phone)?.number || dialCode + phone
          : '',
      promo_code: promoCode ? promoCode : '',
      subid: getFromLocalStorage('subid') || '',
      sub1: getFromLocalStorage('sub1') || '',
      sub2: getFromLocalStorage('sub2') || '',
      sub3: getFromLocalStorage('sub3') || '',
      sub4: getFromLocalStorage('sub4') || '',
      sub5: getFromLocalStorage('sub5') || '',
      tracker: getFromLocalStorage('tracker') || '',
    };
    // remove empty keys
    Object.keys(requestBody).forEach((key: string) => {
      if (requestBody[key as keyof SignUpData] === '') {
        delete requestBody[key as keyof SignUpData];
      }
    });
    const signUpUserData = await signUp(requestBody);

    if (!signUpUserData || signUpUserData.error) {
      setInProgress(false);
      showError(t(toCamelCase(signUpUserData.error)));
      return;
    }

    if (signUpUserData.id) {
      clearSignupLocalStorage();
      showAlert({
        content: t('signUpSuccess'),
        id: Date.now().toString(),
        type: 'success',
        timeout: 3000,
      });

      if ((window as any).Intercom) {
        (window as any).Intercom('shutdown');
        delete (window as any).Intercom;
        delete (window as any).IntercomSettings;
      }

      setIsRegistrationJustDone(true);
      setUser(signUpUserData);
      closeModal('authModal');
      setInProgress(false);
    }
  };

  useEffect(() => {
    if (isSubmitPressed) {
      submitForm();
    }
  }, [isSubmitPressed]);

  // pass inProgress state to parent component
  useEffect(() => {
    isInProgress(inProgress);
  }, [inProgress]);

  useEffect(() => {
    if (isObjectEmpty(errors)) {
      setIsSubmitPressed(false);
    }
  }, [errors]);

  useEffect(() => {
    if (recaptchaToken) {
      onRecaptchaSubmit();
    }
  }, [recaptchaToken]);

  useEffect(() => {
    const URLParams = new URLSearchParams(searchParams.toString());
    const promo_code = URLParams.get('promo_code');
    const currencyFromUrl = URLParams.get('currency');
    if (promo_code) {
      setPromoCode(promo_code);
      setHasPromocode(true);
    }
    const currencyFromCookies = cookies?.userCurrency?.toUpperCase();
    const currencyToSet = currencyFromUrl?.toUpperCase() || currencyFromCookies || DEFAULT_CURRENCY;
    setCurrency(currencyToSet);
  }, []);

  const onChangeField = (name: string, value: string, setState: Function) => {
    const error = rule(name, validationRulesInitial)(value, initialValuesChangedBeforeCheck);
    let newErrors = { ...errors };
    const errorName = error[name] === 'email' ? 'emailFormat' : error[name];
    if (error[name]) {
      setErrors({ ...newErrors, [name]: errorName });
    } else {
      delete newErrors[name];
      setErrors({ ...newErrors });
    }

    setState(value);
  };

  const getTabTitle = (tab: any) => (
    <Typography
      text={t(tab.title)}
      justifyContent='center'
      type='sub3'
      iconName={tab.iconName}
      iconProps={{ wrapperWidth: 24, wrapperHeight: 24 }}
    />
  );

  const tabsTitles = [
    { id: 'email', title: t('byEmail'), iconName: 'letter' },
    { id: 'phone', title: t('byPhone'), iconName: 'smartphone' },
  ];

  const tabsContents = [
    <Input
      customT='validation'
      name='email'
      labelTop={t('common:email')}
      key='email'
      onChange={(e: any) => {
        const { value } = e.target;
        onChangeField('email', value, setEmail);
      }}
      value={email}
      error={isSubmitPressed ? errors.email : ''}
      disabled={inProgress}
    />,
    <PhoneInput
      name='phone'
      labelTop={t('common:phone')}
      key='phone'
      onChange={(data) => {
        setCountry(data.country);
        setDialCode(data.dialCode);
        setPhone(data.inputValue);
        onChangeField('phone', data.dialCode + data.inputValue, () => {});
      }}
      initialValue={phone}
      initialCode={dialCode}
      initialCountry={country}
      fullWidth
      error={isSubmitPressed ? errors.phone : ''}
      t={t}
      disabled={inProgress}
    />,
  ];

  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter') {
      setIsSubmitPressed(true);
      submitForm();
    }
  };

  return (
    <div
      className={`formWrapper flex flex-col items-center ${isTouchDevice ? 'mobileView' : ''}`}
      onKeyDown={handleKeyDown}
      role='presentation'
    >
      {banner && (
        <div
          className={clsx(
            'relative mx-2 mt-4 w-full transition-opacity duration-300',
            banner ? 'opacity-100' : 'opacity-0',
            isTouchDevice ? 'sm:hidden' : 'md:hidden',
          )}
        >
          <Image
            alt='Login Banner'
            src={banner?.photo_main_url || ''}
            width={688}
            height={144}
            sizes={`${theme.breakpoints?.md}px`}
            className='absolute h-full w-full rounded-lg object-cover'
          />
          <Typography
            className='relative min-h-[72px] max-w-[80%] px-3 py-2.5'
            text={getAvailableTranslation(banner?.title || '', lng)}
            type='h3'
          />
        </div>
      )}
      <form
        method='dialog'
        className={clsx(
          'form z-0 h-full w-full overflow-y-auto max-md:h-[calc(100%-140px)]',
          isTouchDevice ? 'mobileView' : '',
        )}
      >
        <Tabs
          isDisabled={inProgress}
          tabsTitles={tabsTitles}
          tabsContents={tabsContents}
          onTabChange={() => setErrors({})}
          withAddAction={false}
          setActiveField={setActiveField}
          getTabTitle={getTabTitle}
          type='buttons'
          tabPadding='16px 0'
        />
        <div>
          <Input
            name='password'
            labelTop={t('password')}
            {...useIsPasswordShown()}
            onChange={(e: any) => {
              const { value } = e.target;
              onChangeField('password', value, setPassword);
            }}
            error={isSubmitPressed && !!errors.password}
            disabled={inProgress}
            // labelBottom={t('lettersAndDigits')}
          />
          <CustomPasswordChecklist
            password={password}
            setIsPasswordValid={setIsPasswordValid}
            className={isSubmitPressed && !isPasswordValid ? 'passError' : ''}
          />
        </div>

        <Select
          options={CURRENCY}
          isSearchable={false}
          customGetOptionLabel={(option: any) => {
            return (
              <FlexRow gap='8px' alignItems='baseline'>
                <div className='flex h-6 w-6 items-center justify-center rounded !bg-[#334155]'>
                  {option.symbol}
                </div>
                <span>{option.label}</span>
              </FlexRow>
            );
          }}
          controlShouldRenderValue
          value={CURRENCY.find((c) => c.code === currency)}
          valueKey='id'
          label={t('currency')}
          onChange={(currency: any) => {
            onChangeField('currency', currency.code, setCurrency);
            setUserCurrencyCookies(currency.code);
          }}
          error={isSubmitPressed && errors.currency}
          isDisabled={inProgress}
          withBorder
          menuPortalTarget={typeof document === 'undefined' ? undefined : document.body}
        />

        <Typography
          type='sub1'
          iconName='discountTicket'
          text={t('havePromoCode')}
          color={theme.color?.primary.main}
          onClick={() => {
            setHasPromocode((prev) => !prev);
          }}
          iconProps={{ margin: '0 6px 0 0', fill: theme.color?.primary.main }}
          className={clsx('w-fit cursor-pointer', inProgress ? 'pointer-events-none' : '')}
        />
        {hasPromocode && (
          <Input
            name='promoCode'
            labelTop={t('promoCode')}
            onChange={(e: any) => {
              const { value } = e.target;
              onChangeField('promoCode', value, setPromoCode);
            }}
            error={isSubmitPressed && errors.promoCode}
            disabled={inProgress}
            value={promoCode}
          />
        )}

        <div className='flex gap-2'>
          <Checkbox
            name='terms'
            type='checkbox'
            checked={terms}
            handleChange={(checked: any) => {
              onChangeField('terms', checked, setTerms);
            }}
            error={isSubmitPressed && errors.terms}
            disabled={inProgress}
            disableLabelClick
            className='w-max'
            label={t('termsAgree')}
            labelType='body1'
            labelProps={{ textDecoration: 'underline' }}
            link={`/${lng}/${gamePageTypeChecked}/info/terms-conditions`}
            linkTarget='_blank'
          />
        </div>

        <ReCaptcha
          showReCaptcha={recaptchaRequired}
          onTokenChange={setRecaptchaToken}
          onClose={() => {
            setRecaptchaRequired(false);
            setInProgress(false);
          }}
        />
      </form>
      <div className='mt-auto w-full pt-3'>
        <Button
          variant='primary'
          text={t('signup')}
          size='medium'
          fullWidth
          onClick={(e: any) => {
            e.stopPropagation();
            setIsSubmitPressed(true);
          }}
          disabled={inProgress}
        />
      </div>
      <Loader
        active={inProgress && !recaptchaRequired}
        size='80px'
        variant='circular'
        withOverlay={true}
      />
    </div>
  );
};

export default SignUpForm;
