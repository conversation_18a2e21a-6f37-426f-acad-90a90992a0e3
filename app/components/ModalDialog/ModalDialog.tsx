'use client';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { ForwardedRef, forwardRef, ReactNode } from 'react';
import { StyledModalDialog } from '@/app/components/ModalDialog/styled.ts';
import { useTranslation } from '@/app/i18n/client';
import { Button, CloseIcon, FlexRow, Typography } from '@/atomic-design-components';
import { closeModal } from '@/utils/closeModal.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

type ModalDialogProps = {
  className?: string;
  contentChildren?: ReactNode;
  id: string;
  children?: ReactNode;
  firstButtonText?: string;
  inProgress?: boolean;
  secondButtonText?: string;
  title?: string | ReactNode;
  text?: string;
  onClose?: Function;
  onFirstButtonClick?: Function;
  onSecondButtonClick?: Function;
  withButtons?: boolean;
  withOneButton?: boolean;
  closeButtonPosition?: string;
  contentPaddings?: string;
  withCloseIcon?: boolean;
};

const ModalDialog = forwardRef(function ModalDialog(
  {
    id,
    className,
    children,
    contentChildren,
    firstButtonText,
    inProgress,
    secondButtonText,
    title,
    text,
    onClose,
    onFirstButtonClick,
    onSecondButtonClick,
    withButtons,
    withOneButton,
    closeButtonPosition,
    contentPaddings,
    withCloseIcon = true,
  }: ModalDialogProps,
  ref: ForwardedRef<HTMLDialogElement>,
) {
  const { isTouchDevice } = useIsTouchMobileView();
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const onCloseModal = () => {
    if (onClose) {
      onClose();
    } else {
      closeModal(id);
    }
  };

  return (
    <StyledModalDialog
      id={id}
      ref={ref}
      className={clsx(className, 'modalDialog', { mobileView: isTouchDevice })}
    >
      {withCloseIcon && (
        <CloseIcon
          className={clsx(
            closeButtonPosition || 'right-2 top-2 md:right-6 md:top-6',
            'closeButtonModal absolute z-10',
          )}
          onClick={(e: any) => {
            e.preventDefault();
            onCloseModal();
          }}
          size='large'
        />
      )}
      {(contentChildren || title || text || withButtons || withOneButton) && (
        <div className={clsx(contentPaddings, 'content')}>
          {title && (
            <Typography
              type='h1'
              margin='0 40px 0 0'
              className='max-md:!text-[28px] max-md:!leading-[36px]'
            >
              {title}
            </Typography>
          )}
          {text && <Typography type='body2'>{text}</Typography>}
          {contentChildren}
          {(withButtons || withOneButton) && (
            <FlexRow
              gap='10px'
              className='buttonContainer'
              alignItems='center'
              justifyContent='end'
            >
              <Button
                className='screenButton'
                textTransform='uppercase'
                type='label2'
                text={firstButtonText || t(withOneButton ? 'OK' : 'no')}
                onClick={(e: any) => {
                  e.preventDefault();
                  onCloseModal();
                  if (onFirstButtonClick) onFirstButtonClick();
                }}
                disabled={inProgress}
                variant={withOneButton ? 'primary' : 'secondary'}
              />
              {!withOneButton && (
                <Button
                  className='screenButton'
                  textTransform='uppercase'
                  type='label2'
                  text={secondButtonText || t('yes')}
                  disabled={inProgress}
                  onClick={onSecondButtonClick}
                />
              )}
            </FlexRow>
          )}
        </div>
      )}
      {children}
    </StyledModalDialog>
  );
});

export default ModalDialog;
