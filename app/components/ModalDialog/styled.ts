import styled from 'styled-components';

export const StyledModalDialog = styled.dialog<any>`
  overflow: hidden;
  background-color: ${({ theme }) => theme.color?.general.darkest};
  border-radius: ${({ theme }) => theme.size.border.radius.bigger};
  z-index: 200;
  inset: 0;
  border: 1px solid ${({ theme }) => theme.color?.general.dark};
  position: fixed;

  @media (max-height: 600px) {
    &#bonusMoreInfoCashierModal,
    &#bonusMoreInfoModal {
      overflow: auto;
      max-height: 100dvh;
    }
  }

  .screenButton {
    min-width: 120px;
  }

  &.mobileView {
    .screenButton {
      width: 100%;

      &:first-child {
        order: 1;
      }

      &:nth-child(2) {
        order: 0;
      }
    }

    .buttonContainer {
      flex-direction: column;

      a {
        width: 100%;
      }
    }
  }

  @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    &:not(.mobileView) {
      .screenButton {
        width: 100%;

        &:first-child {
          order: 1;
        }

        &:nth-child(2) {
          order: 0;
        }
      }

      .buttonContainer {
        flex-direction: column;
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .imageWrapper {
      padding: 12px;
      border-radius: 50%;
      border: 3px solid ${({ theme }) => theme.color?.general.light};

      svg {
        background: #255896;
        box-shadow: 0px 0px 60px 50px #0083ffa8;
        border-radius: 50%;
      }
    }
  }

  &:focus-visible {
    outline: none;
  }

  .title {
    align-self: start;
  }

  &#authModal {
    width: 100%;
    max-width: 460px;
    height: 100%;
    max-height: 720px;

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      max-width: 920px;
    }

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      width: 100dvw;
      height: 100dvh;
      max-height: 100dvh;
      max-width: 100dvw;
    }

    &.mobileView {
      width: 100dvw;
      height: 100dvh;
      max-width: 100dvw;
      max-height: 100dvh;
    }
  }

  &#authModal,
  &#resetPasswordModal,
  &#emailVerificationModal,
  &#phoneVerificationModal {
    &.mobileView {
      border-radius: 0;
      border: 0;
    }

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      border-radius: 0;
      border: 0;
    }
  }

  &#resetPasswordModal {
    width: 100dvw;
    height: 100dvh;
    @media (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      height: max-content;
      max-width: 550px;
    }

    &.mobileView {
      @media (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        width: 100%;
        height: 100%;
        max-width: 100dvw;
        max-height: 100dvh;
        .resetPasswordContainer {
          height: 100%;
        }

        .buttonContainer {
          margin-top: auto;
          flex-direction: row-reverse;
          justify-content: normal;

          button {
            flex-grow: 1;
          }
        }
      }
    }
  }

  &#emailVerificationModal,
  &#phoneVerificationModal {
    width: 550px;
    height: max-content;

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      width: 100dvw;
      height: 100dvh;
    }

    &.mobileView {
      @media (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        width: 100%;
        height: 100%;
        max-width: 100dvw;
        max-height: 100dvh;
        .verificationContainer {
          align-items: center;
          height: 100%;
        }

        .buttonContainer {
          margin-top: auto;
          justify-content: normal;

          button {
            flex-grow: 1;
          }
        }
      }
    }
  }

  &#emailVerificationModal {
    &.confirm {
      .closeIcon {
        display: none;
      }
    }
  }

  &#getBonusModal,
  &#signOutModal,
  &#bonusMoreInfoCashierModal,
  &#bonusMoreInfoModal,
  &#newBonusWhileActive,
  &#freespinsActivatedModal,
  &#cancelBonusModal,
  &#activeFreespinsInGame,
  &#unfinishedGameModal,
  &#continueGameWithZeroWager,
  &#promotionModal,
  &#cancelWithdrawalModal,
  &#cancelWithdrawalModalProfile,
  &#cancelBonusToTakeCashbackModal,
  &#minBalanceForCashbackModal,
  &#depositHostToHostCloseModal {
    &.mobileView {
      inset: unset;
      bottom: 0;
      left: 0;
      max-width: 100dvw;
      width: 100dvw;
      background-color: ${({ theme }) => theme.color?.general.darker};
      border: none;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      &:not(.mobileView) {
        inset: unset;
        bottom: 0;
        left: 0;
        max-width: 100dvw;
        width: 100dvw;
        background-color: ${({ theme }) => theme.color?.general.darker};
        border: none;
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }

  &#activeFreespinsInGame,
  &#unfinishedGameModal,
  &#cancelBonusModal {
    max-width: 550px;
  }

  &#signOutModal {
    height: max-content;
    width: 100%;
    max-width: 550px;

    &.mobileView {
      width: 100dvw;
      @media (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        .buttonContainer {
          flex-direction: row-reverse;
          justify-content: normal;

          button {
            flex-grow: 1;
          }
        }
      }
    }
  }

  &#getBonusModal {
    width: 100%;
    max-width: 550px;
    border-radius: 16px;
    max-height: 100dvh;

    &.mobileView {
      @media (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        .buttonContainer {
          flex-direction: row-reverse;

          button {
            flex-grow: 1;
          }
        }
      }
    }
  }

  &#promotionModal,
  &#depositHostToHostCloseModal {
    width: 550px;

    &.mobileView {
      min-width: 100vw;
      inset: unset;
      bottom: 0;
      width: 100vw;
      max-height: 100dvh;
      min-height: 100px;
      background-color: ${({ theme }) => theme.color?.general.darker};
      border: none;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      min-width: 100vw;
      inset: unset;
      bottom: 0;
      width: 100vw;
      max-height: 100dvh;
      min-height: 100px;
      background-color: ${({ theme }) => theme.color?.general.darker};
      border: none;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  &#searchModal {
    background-color: ${({ theme }) => theme.color?.general.darkest};

    &.mobileView {
      inset: initial;
      width: 100dvw;
      height: 100dvh;
      margin: 0;
      max-height: 100dvh;
      max-width: 100dvw;
      left: 0;
      top: 0;
      border-radius: 0;
      border: none;
    }
  }

  &#continueGameWithZeroWager {
    max-width: 748px;
    overflow: auto;
    background-color: ${({ theme }) => theme.color?.general.darkest} !important;

    .content {
      height: 100%;
    }

    .buttonContainer {
      flex-grow: 1;
      padding-bottom: 24px;
    }

    &.mobileView {
      height: 100dvh;
      max-width: 100dvw;
      border-radius: 0;
    }

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      height: 100dvh;
      max-width: 100dvw;
      border-radius: 0;

      .buttonContainer {
        padding-bottom: 12px;
      }
    }
  }

  &#notificationsModal {
    max-width: 100dvw;
    width: 100dvw;
    height: 100dvh;
    max-height: 100dvh;
    border-radius: 0;
    border: none;

    &.mobileView {
      max-width: 100dvw;
      width: 100dvw;
      height: 100dvh;
      max-height: 100dvh;
      border-radius: 0;
      border: none;
    }

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      max-width: 550px;
      width: 100%;
      height: 100%;
      max-height: 75vh;
      padding: 0;
      border-radius: 16px;
      border: 1px solid ${({ theme }) => theme.color?.general.dark};

      &.mobileView {
        max-width: 100dvw;
        width: 100dvw;
        height: 100dvh;
        max-height: 100dvh;
        border-radius: 0;
        border: none;
      }
    }
  }

  &#cancelWithdrawalModal,
  &#cancelWithdrawalModalProfile {
    @media (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
      inset: unset;
      bottom: 0;
      right: 0;
      left: 0;
      max-width: 100dvw;
      width: 100dvw;
      background-color: ${({ theme }) => theme.color?.general.darker};
      border: none;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;

      .buttonContainer {
        width: 100%;
        flex-direction: column;

        .screenButton {
          width: 100%;

          &:first-child {
            order: 1;
          }

          &:not(:first-child) {
            order: 0;
          }
        }
      }
    }
  }

  &#gameModal {
    .closeIcon {
      display: none;
    }

    &.mobileView {
      inset: unset;
      top: 0;
      bottom: 0;
      max-width: 100dvw;
      width: 100dvw;
      height: 100dvh;
      background-color: transparent;
      border: none;
      border-radius: 0;
      padding: 0;
      margin: 0;
      max-height: 100dvh;

      .gameModalCard {
        background-color: ${({ theme }) => theme.color?.general.darker};
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        padding: 24px 16px 16px;
      }

      > .crossIcon:not(.gameCrossIcon) {
        display: none;
      }
    }

    @media (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
      &:not(.mobileView) {
        inset: unset;
        top: 0;
        bottom: 0;
        max-width: 100dvw;
        width: 100dvw;
        height: 100dvh;
        background-color: transparent;
        border: none;
        border-radius: 0;
        padding: 0;
        margin: 0;
        max-height: 100dvh;

        .gameModalCard {
          background-color: ${({ theme }) => theme.color?.general.darker};
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          border-top-left-radius: 16px;
          border-top-right-radius: 16px;
          padding: 24px 16px 16px;
        }

        > .crossIcon:not(.gameCrossIcon) {
          display: none;
        }
      }
    }
  }

  &#gamePageOverlayModal,
  &#gameLaunchModal {
    width: 100dvw;
    height: 100dvh;
    margin: 0;
    max-height: 100dvh;
    max-width: 100dvw;
    inset: unset;
    top: 0;
    left: 0;
    border-radius: 0;
    border: none;
    padding: 0;
    background-color: #0f172acc;
  }

  &#gameLaunchModal {
    .crossIcon {
      display: none;
    }
  }

  &.opened {
    animation: scale-up-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }

  &.close {
    animation: scale-down-center 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  }

  &.pulse-animated {
    animation: heartbeat 0.5s ease-in-out 2 both;
  }

  &::backdrop {
    display: none;
  }

  .form {
    display: flex;
    flex-direction: column;
    gap: 14px;
    max-width: 430px;
  }

  &#reCaptchaModal {
    pointer-events: auto;
    overflow: visible;
    inset: unset;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 100dvw;
    width: 100dvw;
    background-color: ${({ theme }) => theme.color?.general.darker};
    border: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
`;
