'use client';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { closeModal } from '@/utils/closeModal.ts';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

const ActiveFreespinsInGameModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const router = useRouter();
  const pathname = usePathname();
  const { previousRoute } = useNavigation();
  const { user } = useUser();

  if (!user?.active_freespins) {
    return null;
  }

  const gameUrlWhenFreespinsBonusActive = `/${lng}/casino/${user.active_freespins.game_provider_slug}/game/${user.active_freespins.game_slug}`;

  return (
    <ModalDialog
      id='activeFreespinsInGame'
      title={
        <span>
          {t('youHaveActiveFreespinsInGame')}
          {/*<Link*/}
          {/*  className='w-full pl-2 underline'*/}
          {/*  href={gameUrlWhenFreespinsBonusActive}*/}
          {/*  onClick={() => {*/}
          {/*    closeModal('activeFreespinsInGame');*/}
          {/*  }}*/}
          {/*>*/}
          &nbsp;
          {user.active_freespins.game_name}
          {/*</Link>*/}
        </span>
      }
      text={t('completeYourFreespinsFirst')}
      withButtons
      firstButtonText={t('cancel')}
      secondButtonText={t('play')}
      onSecondButtonClick={() => {
        if (previousRoute || !pathname.includes('/game/')) {
          router.push(gameUrlWhenFreespinsBonusActive);
        }
        closeModal('activeFreespinsInGame');
        closeModal('gamePageOverlayModal');
        closeModal('searchModal');
        closeModal('continueGameWithZeroWager');
      }}
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

ActiveFreespinsInGameModal.displayName = 'ActiveFreespinsInGameModal';
export default ActiveFreespinsInGameModal;
