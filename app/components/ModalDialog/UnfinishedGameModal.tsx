'use client';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { closeModal } from '@/utils/closeModal.ts';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useUnfinishedGame } from '@/app/wrappers/UnfinishedGameProvider.tsx';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

const UnfinishedGameModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const router = useRouter();
  const pathname = usePathname();
  const { previousRoute } = useNavigation();

  const { unfinishedGameName, unfinishedGameUrl } = useUnfinishedGame();

  // if (typeof document === 'undefined') {
  //   return null;
  // }

  return (
    <ModalDialog
      id='unfinishedGameModal'
      title={
        <span>
          {t(
            pathname.includes('my-bonuses')
              ? 'youCantCancelBonusDueToUnfinishedGame'
              : 'youHaveUnfinishedGame',
          )}
          {/*<Link*/}
          {/*  className='w-full pl-2 underline'*/}
          {/*  href={unfinishedGameUrl || ''}*/}
          {/*  onClick={() => {*/}
          {/*    closeModal('unfinishedGameModal');*/}
          {/*  }}*/}
          {/*>*/}
          &nbsp;
          {unfinishedGameName}
          {/*</Link>*/}
        </span>
      }
      text={t(
        pathname.includes('my-bonuses')
          ? 'returnToGameBeforeCancel'
          : 'returnToGameToComplete',
      )}
      withButtons
      firstButtonText={t('cancel')}
      secondButtonText={t('play')}
      onSecondButtonClick={() => {
        if (previousRoute || !pathname.includes('/game/')) {
          router.push(unfinishedGameUrl);
        }
        closeModal('unfinishedGameModal');
        closeModal('gamePageOverlayModal');
        closeModal('searchModal');
        closeModal('continueGameWithZeroWager');
      }}
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

UnfinishedGameModal.displayName = 'UnfinishedGameModal';
export default UnfinishedGameModal;
