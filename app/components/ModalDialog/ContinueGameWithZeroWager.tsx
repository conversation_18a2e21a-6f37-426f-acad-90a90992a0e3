'use client';
import { useParams, usePathname, useRouter } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import ModalDialog from '@/app/components/ModalDialog';
import RecommendedGamesCarousel from '@/app/components/RecommendedGamesCarousel';
import { useEffect, useRef } from 'react';
import useIsDialogOpen from '@/hooks/useIsDialogOpen.ts';
import { closeModal } from '@/utils/closeModal.ts';
import { usePrevious } from '@/hooks/useReact';
import { openModal } from '@/utils/openModal.ts';
import { isClientReady } from '@/utils';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

const ContinueGameWithZeroWager = () => {
  const router = useRouter();
  const pathname = usePathname();
  const prevPathname = usePrevious(pathname);
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const ref = useRef<HTMLDialogElement>(null);
  const { isDialogOpen } = useIsDialogOpen(ref);

  const { isTouchDevice } = useIsTouchMobileView();

  const gameUrlClicked = ref?.current?.dataset?.gameUrlClicked || '';
  const isGameOverlayModalOpened =
    isClientReady() &&
    document
      .getElementById('gamePageOverlayModal')
      ?.classList.contains('opened');
  const isGameModalOpened =
    isClientReady() &&
    document.getElementById('gameModal')?.classList.contains('opened');
  const isSearchOpened =
    isClientReady() &&
    document.getElementById('searchModal')?.classList.contains('opened');

  useEffect(() => {
    if (!isDialogOpen) {
      ref?.current?.setAttribute('data-game-url-clicked', '');
      return;
    }
    if (pathname !== prevPathname) {
      closeModal('continueGameWithZeroWager');
    }
  }, [isDialogOpen, pathname, prevPathname]);

  return (
    <ModalDialog
      id='continueGameWithZeroWager'
      ref={ref}
      contentChildren={
        <RecommendedGamesCarousel
          lng={lng}
          filters={{ wager_percentage__gte: [1] }}
          isDialogOpen={isDialogOpen}
        />
      }
      title={t('bonusWageringNotAvailableInGame')}
      text={t('yourBetsWillNotCount')}
      withOneButton
      firstButtonText={t('playWithoutWagering')}
      onClose={
        isGameOverlayModalOpened && isGameModalOpened
          ? () => {
              closeModal('continueGameWithZeroWager', true, true);
            }
          : undefined
      }
      onFirstButtonClick={() => {
        if (pathname.includes('/game/') && gameUrlClicked === pathname) {
          closeModal('gamePageOverlayModal');
          closeModal('searchModal');
          return;
        }
        if (isTouchDevice) {
          document
            .getElementById('gameModal')
            ?.setAttribute(
              'data-active-game-slug',
              gameUrlClicked.split('/')[5],
            );
          openModal('gameModal', true, true, true, isSearchOpened ? '212' : '');
        } else {
          router.push(gameUrlClicked);
        }
      }}
      contentPaddings='max-sm:px-4 max-sm:pt-6 sm:px-6 sm:pt-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

ContinueGameWithZeroWager.displayName = 'ContinueGameWithZeroWager';
export default ContinueGameWithZeroWager;
