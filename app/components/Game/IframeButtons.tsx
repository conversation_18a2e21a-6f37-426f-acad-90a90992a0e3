import { IGame } from '@/app/api/getGames.ts';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Icon } from '@/atomic-design-components';
import useIsGameFavourite from '@/hooks/useIsGameFavourite.ts';
import { theme } from '@/theme.ts';
import { LanguagesType } from '@/types/global';
import { closeFullscreen, enterFullScreen } from '@/utils/fullScreen.ts';
import { openModal } from '@/utils/openModal.ts';
import { useState } from 'react';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import Link from 'next/link';

const IframeButtons = ({
  item,
  lng,
  onIframeReload,
}: {
  item: IGame;
  lng: LanguagesType;
  onIframeReload: () => void;
}) => {
  const { setActiveAuthTab } = useNavigation();

  const { user } = useUser();
  const [{ favourites = [] }, setSystemData] = useSystemData();

  const [isFullScreenState, setIsFullScreenState] = useState(false);

  const { isGameFavourite, onToggleFavourite } = useIsGameFavourite(
    item,
    favourites,
    setSystemData,
  );

  return (
    <div className='buttons flex justify-end gap-2 pb-2 max-md:hidden'>
      <Button
        variant='secondary'
        borderRadius='4px'
        size='small'
        onClick={async (e: Event) => {
          if (user?.id) {
            await onToggleFavourite(e);
          } else {
            setActiveAuthTab('login');
            openModal('authModal');
          }
        }}
      >
        <Icon
          name='heartShape'
          stroke={
            isGameFavourite
              ? theme.color?.primary.main
              : theme.color?.general.lightest
          }
          fill={isGameFavourite ? theme.color?.primary.main : 'none'}
        />
      </Button>
      <Button
        variant='secondary'
        borderRadius='4px'
        size='small'
        onClick={() => {
          onIframeReload();
        }}
      >
        <Icon name='reloadArrows' />
      </Button>
      <Button
        variant='secondary'
        borderRadius='4px'
        size='small'
        onClick={() => {
          if (isFullScreenState) {
            closeFullscreen();
            setIsFullScreenState(false);
          } else {
            enterFullScreen(document.getElementById('iframeZone'));
            setIsFullScreenState(true);
          }
        }}
      >
        <Icon name={isFullScreenState ? 'fullscreenExit' : 'fullscreen'} />
      </Button>
      <Button
        as={Link}
        style={{ width: '32px', height: '30px' }}
        href={`/${lng}`}
        variant='secondary'
        borderRadius='4px'
        size='small'
        onClick={() => {
          if (isFullScreenState) {
            closeFullscreen();
            setIsFullScreenState(false);
          }
        }}
      >
        <Icon name='cross' />
      </Button>
    </div>
  );
};

export default IframeButtons;
