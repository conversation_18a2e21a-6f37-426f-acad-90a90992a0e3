import styled from 'styled-components';

export const StyledGameIframe = styled.div`
  width: 100%;
  background-color: #0f172a;
  padding: 0;
  margin-bottom: -60px;

  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    &.isMobileView {
      margin-left: -24px;
      width: calc(100% + 24px);
    }
  }

  .iframeZone {
    display: flex;
    flex-direction: column;
    justify-content: end;
    width: 100%;
    position: relative;
    padding: 0 24px 24px;
    overflow: hidden;

    &:fullscreen {
      padding: 0;
      iframe {
        height: 100% !important;
        border-radius: 8px;
      }
      .buttons {
        position: absolute;
        top: 12px;
        right: 12px;
        z-index: 3;
      }

      .demoBar {
        display: none;
      }
    }

    .buttons {
      button {
        width: 30px;
        height: 30px;
      }
    }
    @media screen and (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
      padding: 0;
      height: 100dvh;
      //height: calc(100dvh - env(safe-area-inset-bottom));
      .buttons {
        display: none;
      }
    }
    &.isMobileView {
      padding: 0;
      height: 100dvh;
      //height: calc(100dvh - env(safe-area-inset-bottom));
      .buttons {
        display: none;
      }
    }
  }

  /*.noDemoBlock {*/
  /*  z-index: 1;*/
  /*  position: absolute;*/
  /*  width: 100%;*/
  /*  height: 100%;*/
  /*  top: 0;*/
  /*  overflow: hidden;*/
  /*  opacity: 0;*/
  /*}*/

  /*.noDemoBlock.show {*/
  /*  opacity: 1;*/
  /*}*/

  iframe {
    position: relative;
    z-index: 2;
    width: 100%;
    height: calc(100dvh - 118px);
    opacity: 1;
    border-radius: 8px;

    &.transparent {
      opacity: 0;
      position: absolute;
      top: 62px;
      height: calc(100dvh - 180px);
    }
    @media screen and (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
      &.transparent {
        height: inherit;
      }
      height: inherit;
      &.demo {
        height: calc(100dvh - 58px);
      }
    }
    &.isMobileView {
      &.transparent {
        height: inherit;
      }
      height: inherit;
      &.demo {
        height: calc(100dvh - 58px);
      }
    }
  }
`;
