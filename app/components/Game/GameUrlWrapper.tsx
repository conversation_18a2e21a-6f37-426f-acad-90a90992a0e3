'use client';
import { memo, useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { getGameUrl } from '@/utils/getGameUrl.ts';
import { LanguagesType } from '@/types/global';
import { IGame } from '@/app/api/getGames.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import GameIframe from '@/app/components/Game/GameIframe.tsx';
import { useTranslation } from '@/app/i18n/client';
import { usePrevious } from '@/hooks/useReact';
import { useGameCheckBeforeOpen } from '@/hooks/useGameCheckBeforeOpen.ts';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

interface GameUrlWrapperProps {
  lng: LanguagesType;
  game: IGame;
  isDemo: boolean;
  gameTokenResult: { token: string };
  currency: string;
}

const GameUrlWrapper = ({
  lng,
  game,
  isDemo,
  gameTokenResult,
  currency,
}: GameUrlWrapperProps) => {
  const { t } = useTranslation(lng);
  const pathname = usePathname();
  const { previousRoute } = useNavigation();

  const { user, setUser } = useUser();

  const prevToken = usePrevious(gameTokenResult?.token);
  const prevIsDemo = usePrevious(isDemo);

  // const [isLoading, setIsLoading] = useState(true);
  const [gameUrl, setGameUrl] = useState<string>('');

  const [error, setError] = useState(
    !game.external_id
      ? 'No game id'
      : !user?.id
        ? 'pleaseSignupToPlayTheGame'
        : '',
  );

  const getUrl = () => {
    const gameUrl = isDemo
      ? getGameUrl({ gameId: game.external_id, lng, userId: 'demo', currency })
      : gameTokenResult?.token &&
        getGameUrl({
          gameId: game.external_id,
          lng,
          authToken: gameTokenResult.token,
          currency,
          userId: user?.id,
        });

    setGameUrl(gameUrl);
  };

  useEffect(() => {
    if (!game.is_demo && isDemo) {
      setError('noDemoForThisGame');
      return;
    } else if (error === 'noDemoForThisGame') {
      setError('');
    }

    // console.log(gameTokenResult, currency);
    getUrl();

    // if (
    //   user?.id &&
    //   ((!isDemo && !gameTokenResult?.token) || !gameUrl || !currency)
    // ) {
    // signOut();
    // setUser(null);
    // }
  }, []);

  useEffect(() => {
    if (
      (prevIsDemo !== null && isDemo !== prevIsDemo) ||
      (prevToken !== null && prevToken !== gameTokenResult?.token)
    ) {
      // console.log(
      //   'getUrl',
      //   isDemo,
      //   prevIsDemo,
      //   prevToken,
      //   gameTokenResult?.token,
      // );
      getUrl();
    }
  }, [isDemo, prevIsDemo, prevToken, gameTokenResult?.token]);

  const { onGameClick, canGameBeOpenedByLink } = useGameCheckBeforeOpen(
    pathname,
    user,
    game.wager_percentage,
    isDemo,
  );

  useEffect(() => {
    if ((!previousRoute || (prevIsDemo && !isDemo)) && !canGameBeOpenedByLink) {
      setTimeout(() => {
        onGameClick();
      }, 100);
    }
  }, [canGameBeOpenedByLink, isDemo, prevIsDemo, previousRoute]);

  useEffect(() => {
    if (!user?.id) {
      setError('pleaseSignupToPlayTheGame');
      return;
    } else if (error === 'pleaseSignupToPlayTheGame') {
      setError('');
    }
  }, [error, user?.id]);

  if (
    error === 'pleaseSignupToPlayTheGame' ||
    error === 'noDemoForThisGame' ||
    error === 'No game id'
  ) {
    return (
      <div className='loadingIframe bg-[#0F172A] text-[40px] text-[white]'>
        {t(error)}
      </div>
    );
  }

  // if (!gameUrl) {
  //   return (
  //     <div className='loadingScreen bg-[#0F172A]'>
  //       <Image
  //         alt='logo'
  //         src={LOGO_PLACEHOLDER}
  //         height={560}
  //         width={375}
  //         style={{ height: 84 }}
  //       />
  //     </div>
  //   );
  // }

  return (
    <GameIframe
      lng={lng}
      game={game}
      isDemo={isDemo}
      gameUrl={gameUrl}
      user={user}
      setUser={setUser}
      error={error}
      setError={setError}
      // isLoading={isLoading}
      // setIsLoading={setIsLoading}
    />
  );
};

export default memo(GameUrlWrapper);
