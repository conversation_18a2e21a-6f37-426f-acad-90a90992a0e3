'use client';
import clsx from 'clsx';
import { Image } from '@/atomic-design-components';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { Suspense, useEffect, useRef, useState } from 'react';
import { IGame } from '@/app/api/getGames.ts';
import IframeButtons from '@/app/components/Game/IframeButtons.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useUnfinishedGame } from '@/app/wrappers/UnfinishedGameProvider.tsx';
import { UserType } from '@/app/wrappers/userProvider.tsx';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { useUnmount } from '@/hooks/useReact';
import { LanguagesType } from '@/types/global';
import { isValidJSON } from '@/utils/json.ts';
import GamePageIsDemoSwitch from '../Header/components/GamePageIsDemoSwitch';
import { useInterval } from '@/hooks/useInterval.ts';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { theme } from '@/theme.ts';
import { StyledGameIframe } from './styled';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { getCurrentUserClient } from '@/app/api/user/getCurrentUserClient.ts';

const DraggableButton = dynamic(() => import('../GamePageOverlay/DraggableButton'), { ssr: false });

interface GameProps {
  lng: LanguagesType;
  game: IGame;
  isDemo: boolean;
  className?: string;
  gameUrl: string;
  user: UserType | null;
  setUser: Function;
  error: string;
  setError: Function;
  // isLoading: boolean;
  // setIsLoading: Function;
}

const GameIframe = ({
  lng,
  game,
  isDemo,
  className,
  gameUrl,
  user,
  setUser,
  error,
  setError,
  // isLoading,
  // setIsLoading,
}: GameProps) => {
  const { t } = useTranslation(lng);
  const router = useRouter();

  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;
  const isMobileView = isMobile || isTouchDevice;
  // const isHorizontal = height && height <= 550;

  const iframeRef = useRef<HTMLIFrameElement>(null);

  const [isMenuButtonOpened, setIsMenuButtonOpened] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  // const [isLoadedCount, setIsLoadedCount] = useState(0);

  // const [isLoadedRightAfterError, setIsLoadedRightAfterError] = useState(false);
  const [iframeKeyToReload, setIframeKeyToReload] = useState(0);
  // const [isLoaded, setIsLoaded] = useState(false);

  const { getUnfinishedGame } = useUnfinishedGame();

  useInterval(
    isDemo || isLoading || isMobileView || !user?.id
      ? null
      : () => {
          getCurrentUserClient().then((user) => {
            setUser(user);
          });
        },
    isDemo || isLoading || isMobileView || !user?.id ? null : 60000,
  );

  useUnmount(() => {
    getCurrentUserClient().then((user) => {
      setUser(user);
    });
    getUnfinishedGame();
  });

  useEffect(() => {
    const iframe = iframeRef.current;

    const handleLoad = () => {
      // console.log('Iframe loaded successfully.', gameUrl);
      // setIsLoadedCount((prev) => prev + 1);
      setIsLoading(false);

      if (!isMobileView) {
        setTimeout(() => {
          iframe?.focus();
        }, 10000);
      }

      //expand menu button
      setIsMenuButtonOpened(true);
      setTimeout(() => {
        setIsMenuButtonOpened(false);
      }, 4000);
    };

    if (iframe) {
      iframe.addEventListener('load', handleLoad);
    }

    const handleError = (event: MessageEvent) => {
      if (event.data.type === 'Window.Redirect') {
        router.push(`/${lng}`);
      }

      if (!gameUrl || event.origin !== new URL(gameUrl).origin) {
        return;
      }

      const parsedMessage = isValidJSON(event.data) && JSON.parse(event.data);
      if (parsedMessage.type !== 'iframe-error') {
        return;
      }

      setError(parsedMessage.message || 'error');
      // setIsLoading(false);
      // console.log('Message from iframe received', event, gameUrl);
    };

    // Attach event listeners
    if (typeof window !== 'undefined') {
      window.addEventListener('message', handleError);
    }

    // Cleanup event listeners on component unmount
    return () => {
      if (iframe) {
        iframe.removeEventListener('load', handleLoad);
        window.removeEventListener('message', handleError);
      }
    };
  }, [gameUrl]);

  const onIframeReload = () => {
    // setIsLoadedCount(0);
    if (error) {
      window.location.reload();
    } else {
      setIframeKeyToReload((prev) => prev + 1);
    }
  };

  return (
    <StyledGameIframe
      className={clsx(className, 'gameIframeContent', isMobileView && 'isMobileView')}
    >
      <Suspense>
        <DraggableButton
          slug={game.slug}
          isOpen={isMenuButtonOpened}
          lng={lng}
          isMobileView={isMobileView}
        />
      </Suspense>
      <div className='loadingScreen bg-[#0F172A]'>
        <Image
          alt='logo'
          src={LOGO_PLACEHOLDER}
          height={560}
          width={375}
          style={{ height: 84 }}
          unoptimized
        />
      </div>
      {!!error && (
        <div className='loadingIframe bg-[#0F172A] text-[40px] text-[white]'>{t(error)}</div>
      )}
      <div
        id='iframeZone'
        className={clsx('iframeZone', isMobileView && 'isMobileView')}
        // onClick={
        //   isTouchDevice && isHorizontal
        //     ? (e) => {
        //         // e.preventDefault();
        //         // e.stopPropagation();
        //         // enterFullScreen(document.getElementById('iframeZone'));
        //       }
        //     : undefined
        // }
      >
        <IframeButtons item={game} lng={lng} onIframeReload={onIframeReload} />

        <iframe
          id='gameIframe'
          key={iframeKeyToReload}
          className={clsx(
            (!!error || isLoading) && 'transparent',
            isDemo && 'demo',
            isMobileView && 'isMobileView',
          )}
          ref={iframeRef}
          // TODO: check with Sergey if we need to reload iframe on login if it is demo
          src={user?.id ? gameUrl : ''}
          title={game.name}
          allow='fullscreen*; autoplay*'
          allowFullScreen
          // allow={
          //   ['iPad', 'iPhone', 'Macintosh'].includes(agentDeviceModel)
          //     ? ''
          //     : 'fullscreen*; autoplay*'
          // }
          // allowFullScreen={
          //   !['iPad', 'iPhone', 'Macintosh'].includes(agentDeviceModel)
          // }
        />
        <div className='demoBar flex w-full justify-center md:hidden'>
          <GamePageIsDemoSwitch t={t} />
        </div>
      </div>
    </StyledGameIframe>
  );
};

export default GameIframe;
