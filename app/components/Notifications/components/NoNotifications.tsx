import { Icon, Typography } from '@/atomic-design-components';

const NoNotifications = ({ t }: { t: Function }) => {
  return (
    <div className='flex flex-col items-center mx-auto mt-[20vh]'>
      <Icon name='bellSlashed' width={90} height={90} className='mb-4' />
      <Typography
        type='h3'
        text={t('noNotificationsTitle')}
        className='mb-2 justify-self-center text-center'
      />
      <Typography
        type='body1'
        text={t('noNotificationsText')}
        className='justify-self-center text-center'
      />
    </div>
  );
};

export default NoNotifications;
