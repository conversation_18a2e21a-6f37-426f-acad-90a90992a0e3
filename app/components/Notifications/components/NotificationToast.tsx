'use client';
import React, { memo } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import parse from 'html-react-parser';
import { useTranslation } from '@/app/i18n/client';
import { Button, CloseIcon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { INotification } from '@/app/api/server-actions/notifications.ts';
import { useTimeout } from '@/hooks/useTimeout.ts';
import { closeModal, queryAllDialogs } from '@/utils/closeModal';

const NotificationToast = memo(function NotificationToast({
  newNotification,
  hideNotification,
}: {
  newNotification: INotification;
  hideNotification: Function;
}) {
  const { lng } = useParams();
  const { t } = useTranslation(lng, 'notifications');

  useTimeout(
    newNotification.buttonText
      ? null
      : () => {
          hideNotification(newNotification.id);
        },
    newNotification.buttonText ? null : 7000,
  );

  return (
    <div className='relative mb-3 flex h-fit w-[90dvw] max-w-[344px] flex-col rounded-lg border border-[#334155] bg-[#1E293B] p-4'>
      {newNotification.buttonText && (
        <CloseIcon
          size='small'
          className='absolute right-4 top-4 z-10'
          onClick={() => {
            hideNotification(newNotification.id);
          }}
        />
      )}
      <Typography
        type='h3'
        color={theme.color?.secondary.main}
        text={t(newNotification.notification_type + '_title')}
      />
      <Typography
        displayCssProp='inline'
        type='body2'
        text={parse(
          t(newNotification.notification_type, {
            ...newNotification.translationsData,
            templateName:
              newNotification.translationsData?.templateName || t('wager'),
          }),
        )}
      />
      {newNotification.buttonText && (
        <Link href={newNotification.buttonLink || ''}>
          <Button
            text={t(newNotification.buttonText)}
            size='small'
            fullWidth
            className='mt-2'
            onClick={() => {
              queryAllDialogs().forEach((dialog) => {
                closeModal(dialog.id);
              });
              hideNotification(newNotification.id);
            }}
          />
        </Link>
      )}
    </div>
  );
});

export default NotificationToast;
