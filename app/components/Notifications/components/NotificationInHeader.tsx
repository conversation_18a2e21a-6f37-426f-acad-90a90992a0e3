'use client';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import useParseRichText from '@/hooks/useParseRichText.tsx';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';

import { CloseIcon, Typography } from '@/atomic-design-components';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { useGlobalNotificationState } from '@/app/wrappers/globalNotificationProvider';
import { getFromLocalStorage, saveToLocalStorage } from '@/utils/localStorage.ts';

const StyledGlobalNotification = styled.div`
  .general {
    background-color: ${({ theme }) => theme.color?.general.darker};
  }

  .warning {
    background-color: ${({ theme }) => theme.color?.secondary.dark};
  }

  .success {
    background-color: ${({ theme }) => theme.color?.status.success};
  }

  a {
    text-decoration: underline;
  }

  .notificationLeftAligned {
    transform: translateX(0);
  }
`;

const GLOBAL_NOTIFICATION_TYPES = ['general', 'warning', 'success'];

const NotificationInHeader = () => {
  const { lng } = useParams();
  const [{ constants }] = useSystemData();
  const { parse } = useParseRichText();

  const containerRef = useRef<HTMLDivElement | null>(null);
  const contentRef = useRef<HTMLDivElement | null>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const [isPaused, setIsPaused] = useState(false); // pause on hover etc.

  const onMouseEnter = () => setIsPaused(true);
  const onMouseLeave = () => setIsPaused(false);

  const globalNotificationIsActive = constants?.find(
    (constant) => constant.key === 'global_notification_is_active',
  )?.value;
  const globalNotificationType =
    constants?.find((constant) => constant.key === 'global_notification_type')?.value || '';
  const globalNotificationTexts = constants?.find(
    (constant) => constant.key === 'global_notification_texts',
  )?.value;

  const closedGlobalNotification = getFromLocalStorage('closedGlobalNotification');
  const [isShown, setIsShown] = useState(false);
  const { setIsGlobalNotificationVisible } = useGlobalNotificationState();

  const text = globalNotificationTexts && JSON.parse(globalNotificationTexts);
  const translation = getAvailableTranslation(text, lng);

  useEffect(() => {
    if (
      globalNotificationIsActive === 'true' &&
      GLOBAL_NOTIFICATION_TYPES.includes(globalNotificationType) &&
      globalNotificationTexts &&
      closedGlobalNotification !== globalNotificationTexts &&
      translation
    ) {
      setIsShown(true);
    } else {
      setIsShown(false);
    }
  }, [
    globalNotificationIsActive,
    globalNotificationType,
    globalNotificationTexts,
    closedGlobalNotification,
    translation,
  ]);

  useEffect(() => {
    setIsGlobalNotificationVisible(isShown);
  }, [isShown, setIsGlobalNotificationVisible]);

  useEffect(() => {
    const recalc = () => {
      const c = containerRef.current;
      const t = contentRef.current;
      if (!c || !t) return;
      // A small fudge for sub-pixel rounding: treat "almost equal" as not overflowing
      const overflow = t.scrollWidth - 1 > c.clientWidth;
      setIsOverflowing(overflow);
      // If it no longer overflows, ensure it's not paused forever
      if (!overflow) setIsPaused(false);
    };

    // Run once after paint
    const raf = requestAnimationFrame(recalc);

    // React to element size changes
    const ro = new ResizeObserver(() => recalc());
    if (containerRef.current) ro.observe(containerRef.current);
    if (contentRef.current) ro.observe(contentRef.current);

    // Window resize as a fallback
    window.addEventListener('resize', recalc);

    // Fonts can change metrics after load
    // @ts-ignore
    if (typeof document !== 'undefined' && (document as any).fonts?.ready) {
      // @ts-ignore
      (document as any).fonts.ready.then(() => recalc()).catch(() => {});
    }

    return () => {
      cancelAnimationFrame(raf);
      ro.disconnect();
      window.removeEventListener('resize', recalc);
    };
  }, [translation, isShown]);

  if (!isShown) return null;

  return (
    <StyledGlobalNotification className='w-full md:mx-auto md:my-2 md:rounded-lg md:px-6 xxl:w-[1680px]'>
      <div
        className={clsx(
          'globalNotification relative flex justify-between px-2 py-4 pr-10 md:rounded-lg md:p-4 md:pr-10',
          globalNotificationType,
        )}
      >
        <div
          ref={containerRef}
          className='w-full overflow-hidden whitespace-nowrap'
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          role='presentation'
        >
          <div className={clsx('marquee', isPaused && 'marquee--paused')}>
            <div className={isOverflowing ? 'marquee__track' : ''}>
              <div className='marquee__group' ref={contentRef}>
                <Typography type='sub2' text={parse(translation)} displayCssProp='block' />
              </div>
              {/* Duplicate for seamless wrap */}
              {isOverflowing && (
                <div className='marquee__group' aria-hidden='true'>
                  <Typography type='sub2' text={parse(translation)} displayCssProp='block' />
                </div>
              )}
            </div>
          </div>
        </div>

        <CloseIcon
          size='small'
          className='globalNotificationCloseButton absolute right-2 top-4 z-10 md:right-4'
          onClick={() => {
            setIsShown(false);
            saveToLocalStorage('closedGlobalNotification', globalNotificationTexts);
          }}
          wrapperWidth={24}
          wrapperHeight={24}
        />
      </div>
    </StyledGlobalNotification>
  );
};

export default NotificationInHeader;
