'use client';
import React from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import parse from 'html-react-parser';
import { INotification, readNotification } from '@/app/api/server-actions/notifications.ts';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';
import { useTransformDateFromNow } from '@/utils/dates';
import { closeModal } from '@/utils/closeModal.ts';

const NotificationsListItem = ({
  notification,
  setData,
}: {
  notification: INotification;
  setData: Function;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng, 'notifications');

  const { setNewNotificationsCount } = useNewNotification();
  const time = useTransformDateFromNow(notification.created_at, lng);

  return (
    <div
      role='button'
      tabIndex={0}
      key={notification.id}
      className='flex w-full cursor-pointer flex-col gap-2 rounded-lg bg-[#1E293B] p-3'
      onClick={() => {
        if (notification.status === 'read') return;

        setData((prev: INotification[]) =>
          prev.map((notif) =>
            notification.id === notif.id
              ? {
                  ...notif,
                  status: 'read',
                }
              : notif,
          ),
        );
        setNewNotificationsCount((prev: number) => (prev > 0 ? prev - 1 : 0));

        readNotification(notification.id).then((res) => {
          if (res.detail) {
            setNewNotificationsCount((prev: number) => prev + 1);
            setData((prev: INotification[]) =>
              prev.map((notif) =>
                notification.id === notif.id
                  ? {
                      ...notif,
                      status: 'new',
                    }
                  : notif,
              ),
            );
          }
        });
      }}
    >
      <div className='flex items-start gap-2'>
        {(notification.status === 'new' || !notification.status) && (
          <div className='mt-2 h-2 w-2 shrink-0 rounded-full bg-[#0083FF]' />
        )}
        <Typography type='body2' className='!inline'>
          {parse(
            t(notification.notification_type, {
              ...notification.translationsData,
              templateName: notification.translationsData?.templateName || t('wager'),
            }),
          )}
          {'  '}
          <Typography
            text={time}
            type='body2'
            color={theme.color?.general.light}
            displayCssProp='block'
          />
        </Typography>
      </div>
      {notification.buttonText && (
        <Link href={notification.buttonLink || ''}>
          <Button
            size='small'
            text={t(notification.buttonText)}
            fullWidth
            onClick={() => {
              closeModal('notificationsModal');
            }}
          />
        </Link>
      )}
    </div>
  );
};

export default NotificationsListItem;
