'use client';
import styled from 'styled-components';
import ModalDialog from '../ModalDialog';

export const StyledNotificationsModal: any = styled(ModalDialog)<any>`
  .tabsTitles,
  .activeTab[role='tabpanel'] {
    padding: 0 8px;

    @media only screen and (min-width: ${({ theme }) =>
        theme.breakpoints?.md}px) {
      padding: 0 24px;
    }
  }
`;

export const StyledNotificationsWrapper: any = styled.div`
  z-index: 300; //109

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    right: 50%;
    transform: translateX(50%);
  }

  //&.isNew {
  //  z-index: 300;
  //}
`;
