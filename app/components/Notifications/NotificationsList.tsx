'use client';
import { useParams } from 'next/navigation';
import React from 'react';

import {
  INotification,
  readNotificationsAll,
} from '@/app/api/server-actions/notifications.ts';
import NotificationsListItem from '@/app/components/Notifications/components/NotificationsListItem.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';
import { Button, Loader } from '@/atomic-design-components';
import { StyledLine } from '../Footer/styled';
import NoNotifications from '@/app/components/Notifications/components/NoNotifications.tsx';

const NotificationsList = ({
  // limit,
  initialData,
  setData,
  inProgress,
  type,
}: {
  // limit: number;
  setData: Function;
  initialData: INotification[] | null;
  inProgress: boolean;
  type: 'offer' | 'system';
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  // const loadMoreRef = React.useRef<HTMLDivElement>(null);
  const tableData = initialData || [];

  // const [skip, setSkip] = React.useState(limit);

  const { setNewNotificationsCount } = useNewNotification();

  // const loadNextPage = async () => {
  //   if (totalItems <= tableData?.length) return;
  //   // setInProgress(true);
  //   const { items } = await getPromotions(skip, limit);
  //   setTableData([...tableData, ...items]);
  //   setSkip((prev) => prev + limit);
  //   // setInProgress(false);
  // };

  // useLoadMoreObserver({ loadNextPage, loadMoreRef, withLoadMore: true });

  return (
    <>
      {inProgress && (
        <Loader
          position='relative'
          className='mx-[calc(50%-40px)] md:mx-[calc(50%-56px)] mt-[60px]'
          size='60px'
        />
      )}
      {!inProgress &&
        (tableData?.length ? (
          <div className='my-2 h-[calc(100%-80px)] w-full overflow-y-scroll'>
            <div className='flex w-full flex-col gap-2'>
              {tableData.map((notification: any) => (
                <NotificationsListItem
                  key={`${type}-${notification.id}`}
                  notification={notification}
                  setData={setData}
                />
              ))}
            </div>
          </div>
        ) : (
          <NoNotifications t={t} />
        ))}
      {!!initialData?.length && (
        <>
          <StyledLine className='!ml-[-24px] !mt-auto !w-[calc(100%+48px)]' />
          <div className='py-3 md:py-4'>
            <Button
              size='medium'
              text={t('markAllRead')}
              variant='secondary'
              className='w-full md:w-min md:ml-auto'
              onClick={() => {
                const prevTableData = initialData;
                setData((prev: INotification[]) =>
                  prev.map((notif) => ({
                    ...notif,
                    status: 'read',
                  })),
                );
                setNewNotificationsCount(0);

                readNotificationsAll().then((res) => {
                  if (res.detail) {
                    setData(prevTableData);
                    setNewNotificationsCount(
                      prevTableData?.filter((item) => item.status !== 'read')
                        .length || 0,
                    );
                  }
                });
              }}
            />
          </div>
        </>
      )}
      {/* <div ref={loadMoreRef} className='absolute bottom-[30vh]' /> */}
    </>
  );
};

export default NotificationsList;
