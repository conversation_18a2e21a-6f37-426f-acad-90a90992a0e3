'use client';
import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { StyledNotificationsWrapper } from '@/app/components/Notifications/styled.ts';
import NotificationToast from './components/NotificationToast';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';
import { INotification } from '@/app/api/server-actions/notifications.ts';
import { usePrevious } from '@/hooks/useReact';
import { useUser } from '@/app/wrappers/userProvider.tsx';

const NotificationToastWrapper = () => {
  const pathname = usePathname();
  const prevPathname = usePrevious(pathname);

  const { newNotification } = useNewNotification();
  const { isRegistrationJustDone } = useUser();

  const [notifications, setNotifications] = useState<INotification[]>([]);

  const hideNotification = (id: number) => {
    setNotifications((prev: INotification[]) =>
      prev.filter((notification) => notification.id !== id),
    );
  };

  const hideAllNotifications = () => {
    setNotifications([]);
  };

  useEffect(() => {
    if (prevPathname && prevPathname !== pathname) {
      hideAllNotifications();
    }
  }, [pathname, prevPathname]);

  useEffect(() => {
    if (newNotification?.notification_type) {
      const hasSameId = notifications.some(
        (notification) => notification.id === newNotification.id,
      );

      if (hasSameId) {
        return;
      }

      if (
        isRegistrationJustDone &&
        ['new_bonus', 'new_bonuses'].includes(newNotification.notification_type)
      ) {
        // setIsRegistrationJustDone(false);
        return;
      }

      setNotifications((prev: INotification[]) => [newNotification, ...prev]);
      // if (notifications?.length === 3) {
      //   setNotifications((prev: INotification[]) => [
      //     ...prev.slice(1),
      //     newNotification,
      //   ]);
      // } else {
      // }
    }
  }, [isRegistrationJustDone, newNotification]);

  return (
    <StyledNotificationsWrapper className='fixed top-4 flex flex-col justify-end max-xs:w-full max-xs:items-center xs:right-2 md:right-6 md:top-[72px] xxl:right-[unset] xxl:mr-6'>
      {notifications.map((notification) => (
        <NotificationToast
          key={`toast-${notification.id}`}
          newNotification={notification}
          hideNotification={hideNotification}
        />
      ))}
    </StyledNotificationsWrapper>
  );
};

export default NotificationToastWrapper;
