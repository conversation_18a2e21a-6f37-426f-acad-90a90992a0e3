'use client';

import { useTranslation } from '@/app/i18n/client';
import { Typography, Image } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { useParams } from 'next/navigation';
import parse from 'html-react-parser';

const NotFoundComponent = () => {
  const { lng }: { lng: LanguagesType } = useParams();
  const { t } = useTranslation(lng);

  return (
    <div className='mx-auto flex max-w-[900px] flex-col items-center justify-center px-8 max-md:h-full md:mt-[10%]'>
      <div className='relative h-[140px] w-[100%] md:h-[370px]'>
        <Image
          src='/404.png'
          alt='404'
          fill={true}
          sizes='350px'
          style={{
            objectFit: 'contain',
          }}
        />
      </div>
      <Typography
        fontSize='44px'
        lineHeight='58px'
        fontWeight='600'
        text={t('404Header')}
        textAlign='center'
        className='max-md:!hidden'
      />
      <Typography
        fontSize='20px'
        lineHeight='28px'
        fontWeight='600'
        text={t('404Header')}
        textAlign='center'
        className='md:!hidden'
      />
      <Typography
        type='body2'
        text={parse(t('404Text'))}
        textAlign='center'
        className='max-md:!hidden'
        displayCssProp='block'
      />
      <Typography
        text={parse(t('404Text'))}
        textAlign='center'
        className='md:!hidden'
        displayCssProp='block'
      />
    </div>
  );
};

export default NotFoundComponent;
