'use client';
import { Button, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';

const LiveSupportButton = ({
  className = '',
  fullWidth,
  t,
  withBorder,
  withSpecialStyles,
}: {
  className?: string;
  fullWidth?: boolean;
  t: Function;
  withSpecialStyles?: boolean;
  withBorder?: boolean;
}) => {
  return (
    <Button
      className={'live_support_button ' + className}
      fullWidth={fullWidth}
      variant='secondary'
      size='small'
      backgroundColor={
        withSpecialStyles ? theme.color?.general.dark : undefined
      }
      text={<Typography text={t('liveSupport')} type='body2' />}
      iconLeftProps={{
        name: 'headphones',
        fill: theme.color?.general.lightest,
      }}
      borderRadius={
        withSpecialStyles ? theme.size.border.radius.main : undefined
      }
      withBorder={withBorder}
    />
  );
};

export default LiveSupportButton;
