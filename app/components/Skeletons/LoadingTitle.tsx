'use client';
import { useTranslation } from '@/app/i18n/client';
import { Typography } from '@/atomic-design-components';
import { useParams } from 'next/navigation';

const LoadingTitle = ({
  iconName,
  titleKey,
  iconProps,
  secondTitleKey,
}: {
  iconName: string;
  titleKey: string;
  iconProps: object;
  secondTitleKey?: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  return (
    <div className='mb-4 flex items-center gap-4 max-md:hidden'>
      <Typography
        type='h1'
        text={`${t(titleKey)} ${t(secondTitleKey || '')}`}
        iconName={iconName}
        iconProps={iconProps}
      />
    </div>
  );
};

export default LoadingTitle;
