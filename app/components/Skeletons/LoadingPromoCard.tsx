'use client';
const LoadingPromoCard = () => {
  return (
    <div className='gameCard promotionCard flex animate-pulse flex-col justify-end gap-2 rounded-lg bg-[#1E293B] p-4 md:gap-4'>
      {/* Promo Info */}
      <div className='absolute bottom-0 left-0 flex h-[100px] w-full flex-col gap-1 p-4'>
        <div className='absolute top-[-12px] h-6 w-[120px] rounded bg-gray-600'></div>
        <div className='my-2 h-5 w-1/2 rounded bg-gray-600'></div>
        <div className='h-3 w-full rounded bg-gray-600'></div>
        <div className='h-3 w-3/4 rounded bg-gray-600'></div>
      </div>
    </div>
  );
};

export default LoadingPromoCard;
