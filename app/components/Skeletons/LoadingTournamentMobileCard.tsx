'use client';

const LoadingTournamentMobileCard = ({
  isLanding,
}: {
  isLanding?: boolean;
}) => {
  const width = isLanding ? ' w-[320px]' : ' w-full';
  return (
    <div
      className={
        'mb-2 flex h-[275px] flex-col rounded-lg bg-[#242c3b] pt-4' + width
      }
    >
      {/* Active Tag and Countdown Timer */}
      <div className='flex grow justify-between'>
        <div className='h-[22px] w-[71px] rounded-e-[48px] bg-gray-600'></div>
        {/* Active Tag */}
        <div className='h-[22px] w-[100px] rounded-s-[48px] bg-gray-600'></div>
        {/* Countdown Timer */}
      </div>

      <div className='flex w-full max-w-md animate-pulse flex-col gap-2 rounded-lg rounded-t-none bg-[#1E293B] px-4 pb-3 pt-2'>
        {/* Tournament Title */}
        <div className='mt-2 h-3 w-1/2 rounded bg-gray-600'></div>

        {/* Prize Amount */}
        <div className='h-5 w-1/3 rounded bg-gray-600'></div>
      </div>
    </div>
  );
};

export default LoadingTournamentMobileCard;
