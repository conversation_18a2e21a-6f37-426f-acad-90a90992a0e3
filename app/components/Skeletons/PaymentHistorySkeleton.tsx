import clsx from 'clsx';

export default function PaymentHistorySkeleton({
  desktopClassName,
  mobileClassName,
}: {
  desktopClassName?: string;
  mobileClassName?: string;
}) {
  return (
    <>
      <div className={clsx('rounded-lg max-md:hidden', desktopClassName)}>
        {/* Table Header */}
        <div className='flex justify-between rounded-[4px] bg-[#1e293b] py-2'>
          <div className='w-[100px] min-w-[100px] grow-0'>
            <div className='ml-2 h-4 w-8 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[23%] grow'>
            <div className='h-4 w-14 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[17%] grow'>
            <div className='h-4 w-14 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[20%] grow'>
            <div className='h-4 w-32 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[20%] grow'>
            <div className='h-4 w-20 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[20%] grow'>
            <div className='h-4 w-20 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[150px] grow'>
            <div className='h-4 w-20 animate-pulse rounded'></div>
          </div>
        </div>

        {/* Table Rows */}
        {Array.from({ length: 6 }).map((_, index) => {
          const clsnm = index < 5 ? 'border-b' : '';
          return (
            <div
              key={index}
              className={clsx(
                'flex items-center justify-between border-gray-700 py-4',
                clsnm,
              )}
            >
              {/* ID */}
              <div className='w-[100px] min-w-[100px] grow-0'>
                <div className='ml-2 h-4 w-[70px] animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Date */}
              <div className='w-[23%] grow'>
                <div className='h-4 w-32 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Type */}
              <div className='w-[17%] grow'>
                <div className='h-4 w-20 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Payment Method */}
              <div className='w-[20%] grow'>
                <div className='h-4 w-28 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Amount */}
              <div className='w-[20%] grow'>
                <div className='h-4 w-20 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Status */}
              <div className='w-[20%] grow'>
                <div className='h-4 w-24 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Button */}
              <div className='w-[150px] grow'>
                <div className='h-8 w-20 animate-pulse rounded-full bg-gray-700'></div>
              </div>
            </div>
          );
        })}
      </div>
      <div className={clsx('pt-0 md:hidden', mobileClassName)}>
        {/* Dropdown */}
        <div className='mb-2 h-10 rounded border-[#334155] bg-[#1e293b]'></div>

        {/* Log Entries */}
        {Array.from({ length: 14 }).map((_, index) => (
          <div
            key={index}
            className='mb-2 flex items-start justify-between gap-4 rounded-[4px] bg-[#1e293b] px-2 py-4'
          >
            {/* Left Section */}
            <div className='flex w-1/2 flex-col gap-3'>
              <div className='h-5 w-10 animate-pulse rounded bg-gray-600'></div>
              <div className='flex'>
                <div className='mr-10 h-4 w-[72px] animate-pulse rounded bg-gray-600'></div>
              </div>
            </div>

            {/* Right Section */}
            <div className='flex flex-col items-end gap-3'>
              <div className='h-5 w-[80px] animate-pulse rounded bg-gray-600'></div>
              <div className='h-4 w-[120px] animate-pulse rounded bg-gray-600'></div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
}
