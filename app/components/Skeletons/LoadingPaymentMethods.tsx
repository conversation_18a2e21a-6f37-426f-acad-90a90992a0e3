import { TilesGrid } from '@/atomic-design-components';
import { PAYMENT_CARDS_BREAKPOINTS } from '@/app/components/Cashier/components/PaymentMethodsAllList.tsx';
import { StyledLine } from '@/app/components/NavMenu/styled.ts';
import { theme } from '@/theme.ts';

const LoadingPaymentMethods = ({
  withHeaderText,
}: {
  withHeaderText?: boolean;
}) => {
  return (
    <>
      {withHeaderText && (
        <div className='flex h-10 w-full flex-col items-center justify-center rounded-lg bg-[#334155] px-2 py-[18px]'></div>
      )}
      <div className='h-2 py-1 md:h-4' />
      <TilesGrid
        tiles={Array(8).fill(
          <div className='h-[78px] animate-pulse rounded-lg bg-[#1E293B] md:h-[106px]'></div>,
        )}
        breakpoints={PAYMENT_CARDS_BREAKPOINTS}
      />

      <StyledLine color={theme.color?.general.darker} className='heavy' />

      <TilesGrid
        tiles={Array(8).fill(
          <div className='h-[78px] animate-pulse rounded-lg bg-[#1E293B] md:h-[106px]'></div>,
        )}
        breakpoints={PAYMENT_CARDS_BREAKPOINTS}
      />
    </>
  );
};

export default LoadingPaymentMethods;
