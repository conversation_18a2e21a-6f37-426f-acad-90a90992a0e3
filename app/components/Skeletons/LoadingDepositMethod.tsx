'use client';
import clsx from 'clsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

const LoadingDepositMethod = () => {
  const { isTouchDevice } = useIsTouchMobileView();
  return (
    <div className={clsx('md:flex', isTouchDevice && 'md:block')}>
      {/* Sidebar Menu */}
      <div
        className={clsx(
          'relative w-[188px] space-y-4 bg-[#1E293B] max-md:hidden',
          isTouchDevice && 'md:hidden',
        )}
      >
        <div className='my-[33px] ml-5 h-4 w-3/4 animate-pulse rounded bg-gray-600'></div>
        <div className='h-20 w-full animate-pulse bg-gray-600'></div>
        {Array.from({ length: 2 }).map((_, index) => (
          <div
            key={index}
            className='h-[86px] w-full animate-pulse border-b border-[#334155]'
          ></div>
        ))}
      </div>

      {/* Deposit Details Section */}
      <div className='grow gap-4 md:p-6'>
        <div className={clsx('mb-4 flex', !isTouchDevice && 'md:hidden')}>
          <div className='h-[51px] w-full animate-pulse rounded-s-lg bg-gray-600'></div>
          <div className='h-[51px] w-full animate-pulse border-l border-[#334155] bg-gray-600'></div>
          <div className='h-[51px] w-full animate-pulse rounded-e-lg border-l border-[#334155] bg-gray-600'></div>
        </div>
        {/* Title */}
        <div
          className={clsx(
            'mb-4 h-[36px] w-1/4 animate-pulse rounded bg-gray-600 max-md:hidden',
            isTouchDevice && 'md:hidden',
          )}
        ></div>
        {/* Deposit Title */}
        {/* Choose Top-Up Amount Section */}
        <div className='mb-2 h-5 w-1/2 animate-pulse rounded bg-gray-600'></div>
        {/* Section Title */}
        <div className='flex space-x-2'>
          <div className='w-3/5'>
            <div className='mb-2 mt-1 h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>
            <div className='h-10 animate-pulse rounded bg-gray-600'></div>
          </div>
          {/* Amount Input */}
          <div className='w-2/5'>
            <div className='mb-2 mt-1 h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>

            <div className='h-10 animate-pulse rounded bg-gray-600'></div>
          </div>
          {/* Bonus Input */}
        </div>
        <div className='mb-4 mt-1 h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>
        {/* Amount Range Text */}
        {/* Amount Selection Buttons */}
        <div className='mb-4 flex space-x-2'>
          {Array.from({ length: 6 }).map((_, index) => (
            <div
              key={index}
              className='h-8 w-[56px] animate-pulse rounded-full bg-gray-600'
            ></div>
          ))}
        </div>
        <div className='mb-2 mt-2 h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>
        <div className='mb-4 h-4 w-2/3 animate-pulse rounded bg-gray-600'></div>
        {/* Commission and Processing Time Text */}
        {/* Bonus Section */}
        <div className='mb-2 flex items-center justify-between'>
          <div className='h-5 w-1/4 animate-pulse rounded bg-gray-600'></div>
          {/* Bonus Label */}
          <div className='flex items-center gap-2'>
            <div className='h-4 w-[120px] animate-pulse rounded bg-gray-600'></div>
            <div className='h-6 w-12 animate-pulse rounded-full bg-gray-600'></div>
          </div>
          {/* Bonus Toggle */}
        </div>
        {/* Bonus Options */}
        <div className='space-y-2'>
          {Array.from({ length: 12 }).map((_, index) => (
            <div
              key={index}
              className='h-12 w-full animate-pulse rounded-lg bg-gray-600'
            ></div>
          ))}
        </div>

        {/* To Payment Button */}
        <div
          className={clsx(
            'absolute bottom-0 left-2 right-2 bg-[#0F172A] py-3 md:right-4 md:pb-6 md:pt-5',
            !isTouchDevice && 'md:left-[212px]',
            isTouchDevice && 'md:left-4',
          )}
        >
          <div className='h-10 animate-pulse rounded-full bg-gray-600'></div>
        </div>
      </div>
    </div>
  );
};

export default LoadingDepositMethod;
