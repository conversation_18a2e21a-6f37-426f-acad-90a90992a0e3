import { TilesGrid } from '@/atomic-design-components';
import { GAMES_LIST_BREAKPOINTS } from '@/app/components/Games/GamesList.tsx';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';

const LoadingGameCardsGrid = ({
  itemsCount,
  breakpoints,
}: {
  itemsCount?: number;
  breakpoints?: object;
}) => {
  return (
    <TilesGrid
      breakpoints={breakpoints || GAMES_LIST_BREAKPOINTS}
      tiles={Array(itemsCount || 30).fill(<LoadingGameCard />)}
    />
  );
};

export default LoadingGameCardsGrid;
