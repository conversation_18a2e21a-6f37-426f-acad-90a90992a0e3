import clsx from 'clsx';

export default function HistoryMobileSkeleton({
  mobileClassName,
}: {
  mobileClassName?: string;
}) {
  return (
    <div className={clsx('pt-2 md:hidden', mobileClassName)}>
      {/* Dropdown */}
      <div className='mb-2 h-10 rounded border-[#334155] bg-[#1e293b]'></div>

      {/* Log Entries */}
      {Array.from({ length: 14 }).map((_, index) => (
        <div
          key={index}
          className='mb-2 flex items-start justify-between gap-4 rounded-[4px] bg-[#1e293b] p-2'
        >
          {/* Left Section */}
          <div className='flex w-1/2 flex-col gap-2'>
            <div className='h-5 w-10 animate-pulse rounded bg-gray-600'></div>
            <div className='flex'>
              <div className='mr-10 h-4 w-[72px] animate-pulse rounded bg-gray-600'></div>
              <div className='h-4 w-12 animate-pulse rounded bg-gray-600'></div>
            </div>
          </div>

          {/* Right Section */}
          <div className='flex flex-col items-end gap-2'>
            <div className='h-4 w-[80px] animate-pulse rounded bg-gray-600'></div>
            <div className='h-4 w-[120px] animate-pulse rounded bg-gray-600'></div>
          </div>
        </div>
      ))}
    </div>
  );
}
