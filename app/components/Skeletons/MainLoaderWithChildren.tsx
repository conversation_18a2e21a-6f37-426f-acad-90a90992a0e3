'use client';
import { Image } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';

const MainLoaderWithChildren = ({ children }: any) => {
  const { inProgressCodeCheck } = useUser();
  if (inProgressCodeCheck) {
    return (
      <div
        className={`mt-[-14px] flex w-full max-w-[1680px] grow items-center justify-center xxl:mx-auto`}
      >
        <Image
          alt='logo'
          src={LOGO_PLACEHOLDER}
          width={356}
          height={167}
          style={{ height: 84, width: 'auto' }}
          unoptimized
        />
      </div>
    );
  }

  return children;
};

export default MainLoaderWithChildren;
