import { StyledPageMenu } from '@/app/components/PageMenu/styled.ts';

const PageMenuLoader = () => {
  return (
    <StyledPageMenu className='pageMenu w-full overflow-hidden bg-[#0F172A] pl-2'>
      {/* Placeholder for each game category */}
      {Array.from({ length: 12 }).map((_, index) => (
        <div key={index} className='mr-2 flex animate-pulse items-center gap-2'>
          <div className='h-6 w-6 rounded-full bg-gray-600'></div>
          {/* Icon */}
          <div className='h-4 w-[70px] rounded bg-gray-600 sm:w-[100px]'></div>
          {/* Text */}
        </div>
      ))}
    </StyledPageMenu>
  );
};

export default PageMenuLoader;
