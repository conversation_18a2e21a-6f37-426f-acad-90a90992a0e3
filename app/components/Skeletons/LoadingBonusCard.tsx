import { StyledBonusCard } from '@/app/components/Bonuses/styled.ts';

const LoadingBonusCard = () => {
  return (
    <StyledBonusCard>
      <div className='flex flex-col gap-6 rounded-lg bg-[#1E293B] p-6'>
        {/* Bonus Percentage & Image */}
        <div className='flex items-center justify-between'>
          <div className='h-12 w-32 animate-pulse rounded bg-gray-600'></div>
          <div className='h-16 w-16 animate-pulse rounded-full bg-gray-600'></div>
        </div>

        {/* Bonus Title & Deposit Info */}
        <div className='flex flex-col gap-2'>
          <div className='h-4 w-2/3 animate-pulse rounded bg-gray-600'></div>
          <div className='h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>
        </div>

        {/* Timer Section */}
        {/*<div className='flex items-center gap-2'>*/}
        <div className='h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>
        {/*</div>*/}

        {/* Action Buttons */}
        <div className='flex gap-4'>
          <div className='h-10 w-1/2 animate-pulse rounded bg-gray-600'></div>
          <div className='h-10 w-1/2 animate-pulse rounded bg-gray-600'></div>
        </div>
      </div>
    </StyledBonusCard>
  );
};

export default LoadingBonusCard;
