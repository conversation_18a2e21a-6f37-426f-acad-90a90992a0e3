'use client';
const LoadingTopWinsCard = () => {
  return (
    <div className='flex animate-pulse gap-4 rounded-lg bg-[#1E293B]'>
      {/* Game Image */}
      <div className='h-[128px] w-[92px] rounded bg-gray-600'></div>

      {/* Win Info */}
      <div className='flex grow flex-col justify-center gap-2'>
        <div className='h-4 w-1/2 rounded bg-gray-600'></div>
        <div className='h-6 w-1/3 rounded bg-gray-600'></div>
        <div className='h-4 w-2/3 rounded bg-gray-600'></div>
      </div>
    </div>
  );
};

export default LoadingTopWinsCard;
