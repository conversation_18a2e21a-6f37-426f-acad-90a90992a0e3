'use client';

export const LoadingTournamentCardGames = () => {
  return (
    <div className='mt-3 flex gap-2'>
      {Array.from({ length: 4 }).map((_, index) => (
        <div
          key={index}
          className='h-[76px] w-[50px] rounded bg-gray-600'
        ></div>
      ))}
      <div className='mr-2 flex h-[76px] grow items-center justify-center rounded bg-gray-600'>
        {/* "More games" Placeholder */}
      </div>
    </div>
  );
};

export const LoadingTournamentCardPrizes = ({
  isLanding,
}: {
  isLanding?: boolean;
}) => {
  return Array.from({ length: isLanding ? 3 : 5 }).map((_, index) => {
    if (isLanding) {
      return (
        <div key={index} className='flex items-center gap-2 py-2'>
          <div className='h-6 w-6 rounded-full bg-gray-600'></div>
          <div className='mr-4 h-4 w-[70px] rounded bg-gray-600'></div>
        </div>
      );
    }

    return (
      <div
        key={index}
        className={`flex items-center gap-2 ${index === 0 ? '' : 'py-2'}`}
      >
        {index > 0 ? (
          <div className='h-6 w-6 rounded-full bg-gray-600'></div>
        ) : (
          <div className='h-6 w-6'></div>
        )}
        {/* Place */}
        {index > 0 ? (
          <div className='mr-4 h-4 w-[70px] rounded bg-gray-600'></div>
        ) : (
          <div className='ml-[-32px] mr-[58px] h-4 w-[60px] rounded bg-gray-600'></div>
        )}
        {/* Player Name */}
        {index > 0 ? (
          <div className='mr-5 h-4 w-[100px] rounded bg-gray-600' />
        ) : (
          <div className='mr-[60px] h-4 w-[60px] rounded bg-gray-600'></div>
        )}
        {/* Prize */}
        {index > 0 ? (
          <div className='h-4 w-[70px] rounded bg-gray-600'></div>
        ) : (
          <div className='h-4 w-[60px] rounded bg-gray-600' />
        )}
        {/* Points */}
      </div>
    );
  });
};

const LoadingTournamentCard = ({ isLanding }: { isLanding?: boolean }) => {
  const width = isLanding ? ' w-[482px]' : ' w-[599px]';
  const height = isLanding ? ' h-[312px]' : ' h-[358px]';
  return (
    <div className={'mb-4 w-full rounded-lg bg-[#242c3b] p-6'}>
      <div
        className={
          'flex animate-pulse gap-4 rounded-lg bg-[#1E293B]' + width + height
        }
      >
        {/* Active Tag and Title */}
        <div className='flex w-[230px] flex-col items-start gap-2 rounded-lg p-6 pb-4'>
          <div className='ml-[-24px] mt-[6px] h-[22px] w-16 rounded-e-[48px] bg-gray-600'></div>
          {/* Active Tag */}
          <div className='h-8 w-full rounded bg-gray-600'></div>
          <div className='h-8 w-1/2 rounded bg-gray-600'></div>
          {/* Tournament Title */}

          {/* Prize Amount */}
          <div className='mb-8 mt-4 h-10 w-4/5 rounded bg-gray-600'></div>

          {/* Countdown Timer */}
          <div className='mb-4 flex gap-4'>
            <div className='h-10 w-10 rounded bg-gray-600'></div>
            <div className='h-10 w-10 rounded bg-gray-600'></div>
            <div className='h-10 w-10 rounded bg-gray-600'></div>
          </div>

          {/* Tournament Page Button */}
          {!isLanding && (
            <div className='h-10 w-[165px] rounded-full bg-gray-600'></div>
          )}
        </div>
        {isLanding ? (
          <div className='mt-[62px] flex h-full flex-col p-6 pt-0'>
            <div className='mb-2 h-[32px] w-[100px] rounded bg-gray-600'></div>
            <LoadingTournamentCardPrizes isLanding={isLanding} />
            <div className='mt-4 h-10 w-[165px] rounded-full bg-gray-600'></div>
          </div>
        ) : (
          <div className='mt-[62px] flex h-[42px] flex-col p-6 pt-0'>
            <LoadingTournamentCardPrizes isLanding={isLanding} />
            <LoadingTournamentCardGames />
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingTournamentCard;
