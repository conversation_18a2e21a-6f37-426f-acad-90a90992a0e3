'use client';
import clsx from 'clsx';

const LoadingGameCard = ({ className }: { className?: string }) => {
  return (
    <>
      <div
        className={clsx(
          'gameCard align-center flex cursor-pointer flex-col justify-center',
          'placeholder',
          'loading',
          className,
        )}
      />
      <div className='mx-auto mb-[30px] mt-[5px] h-4 w-1/2 rounded bg-gray-600 max-sm:mb-0' />
    </>
  );
};

export default LoadingGameCard;
