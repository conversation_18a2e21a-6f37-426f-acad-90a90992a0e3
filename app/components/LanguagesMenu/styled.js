'use client';
import { Select } from '@/atomic-design-components';
import styled from 'styled-components';

export const StyledLanguagesMenu = styled(Select)`
  .flag {
    font-size: 20px;
    width: 30px;
    display: flex;
    align-items: center;
  }
  .select {
    min-width: 70px;
  }
  header & {
    display: none;
  }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    header & {
      display: block;
    }
  }
`;
