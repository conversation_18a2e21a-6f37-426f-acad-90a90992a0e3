'use client';
import {
  LNG_COOKIE_NAME,
  LANGUAGES,
  LANGUAGES_FULL_NAME,
} from '@/app/i18n/settings';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { countryCodeToFlagEmoji } from '@/utils/countryCodeToFlagEmoji.ts';
import clsx from 'clsx';
import { StyledLanguagesMenu } from './styled';
import { useRouter } from 'next/navigation';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useState } from 'react';
import { changeUserSettings } from '@/app/api/server-actions/changeUserSettings.ts';
import { useCookies } from 'react-cookie';
import { useTranslation } from '@/app/i18n/client';

const getLanguagesList = (withFlag: boolean, isFullNamed?: boolean) => {
  return LANGUAGES?.map((lang: LanguagesType) => ({
    id: lang,
    padding: '0px',
    customElement: (
      <div className='flex w-full'>
        {withFlag && (
          <div className='flag listFlag'>{countryCodeToFlagEmoji(lang)}</div>
        )}
        <Typography
          text={isFullNamed ? LANGUAGES_FULL_NAME[lang] : lang}
          textTransform={isFullNamed ? '' : 'uppercase'}
        />
      </div>
    ),
  }));
};

const LanguagesMenu = ({
  className,
  lng,
  openToTop = false,
  isFullNamed,
  width,
  withFlag = false,
}: {
  className?: string;
  lng: LanguagesType;
  openToTop?: boolean;
  isFullNamed?: boolean;
  width?: string;
  withFlag?: boolean;
}) => {
  const router = useRouter();
  const { i18n } = useTranslation();
  const [, setCookie] = useCookies([LNG_COOKIE_NAME]);
  const langList = getLanguagesList(withFlag, isFullNamed);
  const { user } = useUser();

  const [selectedLng, setSelectedLng] = useState(lng);

  const handleLanguageChange = (lang: { id: LanguagesType }) => {
    if (lang.id !== selectedLng) {
      setSelectedLng(lang);
      // Set cookie
      setCookie(LNG_COOKIE_NAME, lang.id, { path: '/' });
      // Set path
      const segments = window.location.pathname.split('/');
      if (LANGUAGES.includes(segments[1])) {
        segments[1] = lang.id;
      } else {
        segments.splice(1, 0, lang.id);
      }
      const newPath = segments.join('/') + window.location.search;

      setTimeout(() => {
        i18n.changeLanguage(lang.id);
      }, 1000);
      router.push(newPath);

      if ((window as any).Intercom) {
        (window as any).Intercom('shutdown');
        delete (window as any).Intercom;
        delete (window as any).IntercomSettings;
      }
    }
  };

  return (
    <StyledLanguagesMenu
      className={clsx('languagesMenu', className)}
      onChange={async (lang: { id: LanguagesType }) => {
        handleLanguageChange(lang);
        if (user?.id) {
          setTimeout(() => {
            changeUserSettings({ lng: lang.id });
          }, 1000);
        }
      }}
      menuPlacement={openToTop ? 'top' : 'bottom'}
      value={langList.find((lang) => lang.id === selectedLng)}
      // customGetOptionLabel={(option: any) => option.customElement}
      options={langList}
      valueKey='id'
      labelKey='customElement'
      isSearchable={false}
      width={width}
      maxHeight='none'
      controlShouldRenderValue={true}
      // getSelectedOption={(props: any) => {
      //   const value = props.getValue()[0];
      //
      //   return (
      //     <div className='flex w-full' key={value.id}>
      //       {withFlag && getLngIcon(value.id)}
      //       <Typography
      //         text={isFullNamed ? LANGUAGES_FULL_NAME[value.id] : value.id}
      //         textTransform={isFullNamed ? '' : 'uppercase'}
      //       />
      //     </div>
      //   );
      // }}
    />
  );
};

export default LanguagesMenu;
