'use client';
import { HEADER_HEIGHT, NAV_MENU_WIDTH } from '@/constants.ts';
import styled from 'styled-components';

export const StyledNavMenu: any = styled.nav`
  width: ${NAV_MENU_WIDTH};
  display: none;
  flex-shrink: 0;
  flex-direction: column;

  .detailsWrapper .menuItem {
    margin-bottom: 10px;
  }

  .active .panelHeader {
    background-color: ${({ theme }) => theme.color?.general.gray3};
    border-radius: 6px;

    .typography {
      color: ${({ theme }) => theme.color?.general.white};
    }
  }

  .userLink {
    margin-top: 10px;
  }

  .topWrapper {
    position: sticky;
    top: ${HEADER_HEIGHT};
    overflow-y: auto;
    overflow-x: hidden;
    padding: 8px 24px 24px;
    height: calc(100dvh - ${HEADER_HEIGHT} - 60px);

    > .expansionPanel {
      margin-left: 10px;
      margin-right: 10px;
    }
  }

  .userLink,
  .signOutLink {
    display: flex;
    margin-bottom: 20px;
    padding: ${({ padding }: { padding: string }) => padding || '0 10px'};
    padding-left: 0;
    margin-left: 10px;
    margin-right: 10px;
  }

  .liveSupport {
    position: fixed;
    bottom: 0;
    width: 224px;
    padding: 24px 0;
    background-color: ${({ theme }) => theme.color?.general.darkest};

    button {
      font-size: 14px;
      text-transform: none;
    }
  }

  @media only screen and (min-width: ${({ theme }) =>
      theme.breakpoints?.md}px) {
    display: flex;
  }
`;

export const StyledMenuItems: any = styled.div`
  display: flex;
  margin: ${({ margin }: { margin: string }) => margin};

  &.vertical {
    flex-direction: column;
    gap: 4px;

    a {
      width: 100%;
      color: ${({ theme }) => theme.color?.general.dark};
    }
  }

  &.horizontal {
    gap: 4px;
  }
`;

export const StyledMenuItem: any = styled.div`
  display: flex;
  align-items: center;
  white-space: nowrap;
  position: relative;

  .typography {
    color: ${({ theme }) => theme.color?.general.lighter};
  }

  &.active {
    .typography {
      color: ${({ theme }) => theme.color?.primary.main};
    }
  }

  &:hover {
    .typography {
      color: ${({ theme }) => theme.color?.general.light};
    }

    svg path {
      fill: ${({ theme }) => theme.color?.general.light};
    }
  }

  .icon {
    margin: 0;
  }

  &.horizontal {
    border-radius: 50px;
    width: max-content;
    color: ${({ theme }) => theme.color?.general.dark};
    padding: 8px 12px;

    .typography {
      margin-left: 8px;
    }

    &:hover,
    &.active {
      .typography {
        color: ${({ theme }) => theme.color?.general.lightest};
      }

      background-color: ${({ theme }) => theme.color?.general.darker};

      svg path {
        fill: ${({ theme }) => theme.color?.general.lightest};
      }
    }
  }
`;

export const StyledLine: any = styled.hr`
  background-color: ${({ theme, color }) =>
    color || `${theme.color?.general.white}1a`};
  border-width: 0;
  height: 1px;
  margin: ${({ margin }: { margin: string }) => margin || '8px 0'};

  &.heavy {
    height: 2px;
  }

  &.extraHeavy {
    height: 4px;
  }
`;
export const StyledVerticalLine: any = styled.div`
  border-left: ${({ theme, color }) =>
    color ? `1px solid ${color}` : `1px solid ${theme.color?.general.dark}`};
`;

export const StyledUserName = styled.span`
  width: 200px;
  margin-left: 10px;
`;

export const StyledMobileMenu: any = styled.nav`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;

  .expansionPanel {
    .flag {
      font-size: 20px;
      width: 30px;
      height: 40px;
      display: flex;
      align-items: center;

      &.listFlag {
        width: 36px;
        justify-content: center;
      }
    }
  }
`;

export const StyledMobilePageTypeMenu: any = styled.div`
  padding: 8px;
  border-radius: 8px;
  background-color: #1e293b;
  gap: 8px;

  .menuItem {
    background-color: transparent;
    gap: 4px;
    padding: 4px 0;
    justify-content: center;
    height: auto;
    width: 50%;
    border-radius: 8px !important;
    margin-right: 0 !important;
    border: none;

    &:hover,
    &.selected {
      border: none;
    }

    .icon {
      margin: 0;
      display: flex;
      align-items: center;
    }

    &.selected {
      background-color: ${({ theme }) => theme.color?.general.dark};

      .menuItem.selected {
        border: none;
      }
    }
  }

  .menuItem {
    .menuItemIcon {
      width: 24px;
      height: 24px;
    }
  }
`;

export const StyledLinksMenu: any = styled.div`
  background-color: #1e293b;
  padding: 4px 8px;
  border-radius: 8px;

  .menuItem {
    padding: 0;

    .icon {
      padding: 0px;
      margin-right: 0;
      margin-top: -1px;
      display: flex;
    }

    &:not(:last-child) {
      border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
    }
  }
`;

export const StyledLiveSupport: any = styled.div<{
  isOpen: boolean;
  right?: string;
  bottom?: string;
}>`
  z-index: 100;
  position: fixed;
  display: flex;
  bottom: ${({ bottom }) =>
    `calc(${bottom || '72px'} + env(safe-area-inset-bottom))`};
  right: ${({ right }) => right || '16px'};
  height: 48px;
  width: 48px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.color?.primary.main};
  align-items: center;
  justify-content: center;
  opacity: ${({ isOpen }) => (isOpen ? 1 : 0)};
  visibility: ${({ isOpen }) => (isOpen ? 'visible' : 'hidden')};
  transition:
    opacity 0.3s ease-in-out,
    visibility 0.3s ease-in-out;
`;
