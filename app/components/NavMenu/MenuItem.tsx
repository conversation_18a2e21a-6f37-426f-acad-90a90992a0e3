'use client';
import { useTranslation } from '@/app/i18n/client';
import clsx from 'clsx';
import Link from 'next/link';
import { useParams, useSearchParams } from 'next/navigation';

import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType } from '@/types/global';
import { openModal } from '@/utils/openModal';
import { StyledMenuItem } from './styled';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

interface IMenuItemProps {
  iconName: string;
  text: string;
  secondLabel?: string;
  route: string;
  isActive: boolean;
  isActiveParent?: boolean;
  className?: string;
  isUserAuthorized?: boolean;
  withAuthorization?: boolean;
  pagesType?: GamePageType;
  iconProps?: any;
}

const MenuItem = ({
  iconName,
  text,
  secondLabel,
  route,
  isActive,
  isActiveParent,
  className,
  isUserAuthorized = false,
  withAuthorization = false,
  iconProps,
}: IMenuItemProps) => {
  const { lng, gamePageType = 'casino' } = useParams();
  const { t } = useTranslation(lng);
  const { setActiveAuthTab } = useNavigation();

  const searchParams = useSearchParams();
  const params = searchParams.get('filters');

  const pageType = ['casino', 'live-casino'].includes(gamePageType as string)
    ? gamePageType
    : 'casino';

  const { setNextPathAfterLogin } = useUser();

  const iconFill =
    isActive || isActiveParent ? theme.color?.primary.main : theme.color?.general.lightest;

  return (
    <Link
      className={clsx(className, 'menuItem')}
      href={
        (withAuthorization && isUserAuthorized) || !withAuthorization
          ? '/' +
            lng +
            '/' +
            pageType +
            route +
            (params && pageType !== 'live-casino' ? `?filters=${params}` : '')
          : ''
      }
      onClick={
        withAuthorization && !isUserAuthorized
          ? () => {
              setNextPathAfterLogin('/' + lng + '/' + pageType + route);
              setActiveAuthTab(['/recent', '/favourites'].includes(route) ? 'login' : 'signUp');
              openModal('authModal');
            }
          : undefined
      }
    >
      <StyledMenuItem className={clsx(isActive && 'active')}>
        <Icon
          name={iconName}
          width={16}
          height={16}
          wrapperWidth={40}
          wrapperHeight={40}
          className={clsx(iconName, 'menuItemIcon')}
          fill={iconFill}
          strokeWidth={1.4}
          stroke={iconFill}
          {...iconProps}
        />
        <Typography type='body2' text={t(text)} className='flex md:hidden' displayCssProp='' />
        <Typography
          type='body2'
          text={`${t(text)} ${secondLabel ? t(secondLabel) : ''}`}
          className='flex max-md:hidden'
          displayCssProp=''
        />
      </StyledMenuItem>
    </Link>
  );
};

export default MenuItem;
