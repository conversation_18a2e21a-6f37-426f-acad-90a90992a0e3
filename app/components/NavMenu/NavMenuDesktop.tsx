'use client';
import { useParams, usePathname } from 'next/navigation';

import { ENTITIES } from '@/app/config/navMenuEntities.ts';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global';
import { openModal } from '@/utils/openModal';
import MenuGroup from './MenuGroup';
import { StyledLine, StyledNavMenu } from './styled';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import LiveSupportButton from '@/app/components/LiveSupportButton';

const NavMenuDesktop = () => {
  const { isTouchDevice } = useIsTouchMobileView();
  const pathname = usePathname();
  const {
    lng,
    gamePageType = 'casino',
  }: { lng: LanguagesType; gamePageType?: GamePageType } = useParams();

  const { user } = useUser();
  const { t } = useTranslation(lng);

  const isUserAuthorized = !!user?.id;

  const isGamePage = pathname.includes('/game/');

  if (isGamePage || isTouchDevice) return null;

  const pageEntities = ENTITIES.all[gamePageType] || ENTITIES.all.casino;

  return (
    <StyledNavMenu className='vertical max-md:hidden'>
      <div className='topWrapper'>
        <div
          role='button'
          tabIndex={0}
          onClick={(e: any) => {
            e.preventDefault();
            openModal('searchModal');
          }}
          className='mb-2 flex w-full cursor-pointer gap-2 rounded-lg border border-[#334155] bg-[#1E293B] px-3 py-[7px] hover:border-[#475569]'
        >
          <Icon
            name='search'
            width={16}
            height={16}
            stroke={theme.color?.general.lighter}
          />
          <Typography
            text={t('searchGame')}
            type='body2'
            color={theme.color?.general.light}
          />
        </div>

        <MenuGroup
          entitiesArr={ENTITIES.authorized}
          isUserAuthorized={isUserAuthorized}
        />
        <StyledLine />
        <MenuGroup entitiesArr={ENTITIES.all.common} />
        <MenuGroup entitiesArr={pageEntities} />
        <StyledLine />
        <MenuGroup entitiesArr={ENTITIES.providers} />
        <StyledLine />
        <MenuGroup entitiesArr={ENTITIES.general} />
        <div className={`fixed bottom-0 h-[60px] w-[248px] bg-general-darkest`}>
          <LiveSupportButton t={t} withSpecialStyles className='mb-5' />
        </div>
      </div>
    </StyledNavMenu>
  );
};

export default NavMenuDesktop;
