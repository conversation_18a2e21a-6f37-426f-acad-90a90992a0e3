'use client';

import { GamePageType } from '@/types/global';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';
import MenuItem from './MenuItem';
import { StyledMenuItems } from './styled';

interface IMenuGroupProps {
  entitiesArr: any[];
  isUserAuthorized?: boolean;
  children?: ReactNode;
  isHorizontal?: boolean;
  pagesType?: GamePageType;
}

const MenuGroup = ({
  entitiesArr,
  isUserAuthorized,
  children,
  isHorizontal = false,
}: IMenuGroupProps) => {
  const pathname = usePathname();

  const isActiveKey = (route: string) => {
    return pathname.includes(route);
  };

  return (
    <>
      <StyledMenuItems className={isHorizontal ? 'horizontal' : 'vertical'}>
        {entitiesArr.map((entity) => {
          const {
            key,
            route,
            iconName,
            label,
            secondLabel,
            icon,
            withAuthorization,
            className,
            iconProps,
          } = entity;

          return (
            <MenuItem
              iconName={iconName || icon}
              key={key}
              text={label}
              secondLabel={secondLabel}
              route={route}
              isActive={isActiveKey(route)}
              isUserAuthorized={isUserAuthorized}
              withAuthorization={withAuthorization}
              className={clsx(isHorizontal && 'horizontal', className)}
              iconProps={iconProps}
            />
          );
        })}
      </StyledMenuItems>
      {children}
    </>
  );
};

export default MenuGroup;
