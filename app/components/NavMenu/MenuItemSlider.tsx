'use client';
import { useTranslation } from '@/app/i18n/client';
import clsx from 'clsx';
import Link from 'next/link';
import { useParams, usePathname, useSearchParams } from 'next/navigation';

import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType } from '@/types/global';
import { openModal } from '@/utils/openModal';
import { StyledMenuItem } from './styled';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

interface IMenuItemProps {
  iconName: string;
  iconProps?: any;
  label: string;
  secondLabel?: string;
  route: string;
  className?: string;
  isUserAuthorized?: boolean;
  withAuthorization?: boolean;
  pagesType?: GamePageType;
}

const MenuItemSlider = ({
  item,
  loaded,
  index,
}: {
  item: IMenuItemProps;
  loaded?: boolean;
  index?: number;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const params = searchParams.get('filters');
  const { setActiveAuthTab } = useNavigation();

  const { gamePageType = 'casino' }: { gamePageType?: GamePageType } = useParams();

  const isActiveKey = (route: string) => {
    return pathname.includes(route);
  };

  const isActive = isActiveKey(item.route);
  const iconFill = isActive ? theme.color?.primary.main : theme.color?.general.lightest;

  return (
    <Link
      className={clsx('menuItem relative', loaded && 'loaded')}
      href={
        (item.withAuthorization && item.isUserAuthorized) || !item.withAuthorization
          ? '/' + lng + '/' + gamePageType + item.route + (params ? `?filters=${params}` : '')
          : ''
      }
      onClick={
        item.withAuthorization && !item.isUserAuthorized
          ? () => {
              setActiveAuthTab('signUp');
              openModal('authModal');
            }
          : undefined
      }
    >
      <StyledMenuItem className={clsx(item.className, isActive && 'active', 'itemContent')}>
        <Icon
          name={item.iconName}
          width={16}
          height={16}
          className={clsx(item.iconName, 'menuItemIcon')}
          strokeWidth={1.4}
          {...item.iconProps}
          fill={iconFill}
        />
        <Typography
          type='body2'
          text={t(item.label)}
          className='flex md:hidden'
          displayCssProp=''
        />
        <Typography
          type='body2'
          text={`${t(item.label)} ${item.secondLabel ? t(item.secondLabel) : ''}`}
          className='flex max-md:hidden'
          displayCssProp=''
        />
      </StyledMenuItem>
      {!loaded && index === 0 && (
        <p className='absolute top-[18px] h-2 w-20 animate-pulse rounded-lg bg-gray-700'></p>
      )}
    </Link>
  );
};

export default MenuItemSlider;
