'use client';
import parse from 'html-react-parser';
import { usePara<PERSON>, usePathname, useRouter } from 'next/navigation';

import { HEADER_NAV_LINKS } from '@/app/config/headerNavLinks';
import { ENTITIES } from '@/app/config/navMenuEntities.ts';
import { useTranslation } from '@/app/i18n/client';
import {
  LNG_COOKIE_NAME,
  LANGUAGES,
  LANGUAGES_FULL_NAME,
} from '@/app/i18n/settings';
import { ExpansionPanel, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global';
import { countryCodeToFlagEmoji } from '@/utils/countryCodeToFlagEmoji';
import clsx from 'clsx';
import Link from 'next/link';
import { StyledLinksMenu, StyledMobileMenu } from '../styled';
import MobileMenuTabs from './components/MobileMenuTabs';
import { useState } from 'react';
import { changeUserSettings } from '@/app/api/server-actions/changeUserSettings.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useCookies } from 'react-cookie';

const MenuMobile = ({ close }: { close: () => void }) => {
  const [, setCookie] = useCookies([LNG_COOKIE_NAME]);
  const pathname = usePathname();
  const {
    lng,
    gamePageType = 'casino',
  }: { lng: LanguagesType; gamePageType?: GamePageType } = useParams();
  const { t, i18n } = useTranslation(lng);

  const { user } = useUser();

  const [selectedLng, setSelectedLng] = useState(lng);

  const [isLangPanelOpen, setIsLangPanelOpen] = useState(false);
  const router = useRouter();
  const pageType = ['casino', 'live-casino'].includes(gamePageType as string)
    ? gamePageType
    : 'casino';

  const isGamePage = pathname.includes('/game/');
  const isLiveCasinoPages =
    gamePageType === 'live-casino' || pathname.includes('/live-casino');

  if (isGamePage) return null;

  const menuItems = [
    ...HEADER_NAV_LINKS.slice(2),
    {
      title: 'providers',
      href: '/providers',
      iconName: 'cyberNetwork',
    },
  ];

  const closePanel = () => {
    close();
    setIsLangPanelOpen(false);
  };

  const handleLanguageChange = (lang: LanguagesType) => {
    if (lang !== selectedLng) {
      setSelectedLng(lang);
      // Set cookie
      setCookie(LNG_COOKIE_NAME, lang, { path: '/' });
      // Set path
      const segments = pathname.split('/');
      segments[1] = lang;
      const newPath = segments.join('/') + window.location.search;

      setTimeout(() => {
        i18n.changeLanguage(lang);
      }, 1000);
      closePanel();
      router.push(newPath);
    }
  };

  const getLanguagesList = () => {
    return LANGUAGES?.map((lang: LanguagesType) => {
      return (
        <button
          onClick={async () => {
            handleLanguageChange(lang);
            if (user?.id) {
              setTimeout(() => {
                changeUserSettings({ lng: lang });
              }, 1000);
            }
          }}
          className='langItem flex w-full items-center gap-1'
          key={lang}
        >
          <span className='flag listFlag'>{countryCodeToFlagEmoji(lang)}</span>
          <Typography
            text={LANGUAGES_FULL_NAME[lang]}
            type='body2'
            className=''
            color={lang === selectedLng ? theme.color?.primary.main : ''}
          />
        </button>
      );
    });
  };

  const langList = getLanguagesList();

  return (
    <StyledMobileMenu>
      <MobileMenuTabs
        lng={lng}
        gamePageType={pageType}
        initialTab={isLiveCasinoPages ? 1 : 0}
      />
      <StyledLinksMenu>
        {menuItems.map((link) => {
          const selected =
            (link.title === 'liveCasino' && isLiveCasinoPages) ||
            (link.title === 'slots' &&
              (pathname === `/${lng}` || pathname.includes('/casino'))) ||
            (link.title !== 'slots' &&
              link.title !== 'liveCasino' &&
              pathname.includes(link.href));

          return (
            <Typography
              key={link.href}
              component={Link}
              className={clsx('menuItem', selected && 'selected')}
              type='body2'
              href={
                ['liveCasino', 'slots'].includes(link.title)
                  ? '/' + lng + link.href
                  : '/' + lng + '/' + pageType + link.href
              }
              iconName={link.iconName}
              iconProps={{
                fill: theme.color?.general.lightest,
                wrapperWidth: 40,
                wrapperHeight: 40,
                ...link.iconProps,
              }}
              onClick={() => closePanel()}
            >
              {t(link.title)}
            </Typography>
          );
        })}
      </StyledLinksMenu>
      <StyledLinksMenu>
        {ENTITIES?.general.map((link) => {
          const selected = pathname.includes(link.route);

          return (
            <Typography
              key={link.route}
              component={Link}
              className={clsx('menuItem', selected && 'selected')}
              type='body2'
              href={'/' + lng + '/' + pageType + link.route}
              iconName={link.iconName}
              iconProps={{
                fill: theme.color?.general.lightest,
                wrapperWidth: 40,
                wrapperHeight: 40,
              }}
              onClick={() => closePanel()}
            >
              {t(link.label)}
            </Typography>
          );
        })}
      </StyledLinksMenu>
      <ExpansionPanel
        id='langs'
        initialOpened={false}
        controlledOpened={isLangPanelOpen}
        onToggle={setIsLangPanelOpen}
        panelName='langsCategoryPanel'
        header={parse(
          `<span class='flag'>${countryCodeToFlagEmoji(lng)}</span> ${LANGUAGES_FULL_NAME[selectedLng]}`,
        )}
      >
        {langList}
      </ExpansionPanel>
    </StyledMobileMenu>
  );
};

export default MenuMobile;
