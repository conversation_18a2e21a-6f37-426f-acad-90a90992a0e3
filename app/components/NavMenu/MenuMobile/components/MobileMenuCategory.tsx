'use client';
import { ENTITIES } from '@/app/config/navMenuEntities';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { ExpansionPanel } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import MenuGroup from '../../MenuGroup';

const MobileMenuCategory = ({
  lng,
  type,
}: {
  lng: LanguagesType;
  type: 'live-casino' | 'casino';
}) => {
  const { t } = useTranslation(lng);

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const entities = [
    ENTITIES.authorized[1],
    ...ENTITIES.all.common,
    ...ENTITIES.all[type],
  ];

  return (
    <ExpansionPanel
      header={t('categoriesOfGames')}
      id={type}
      initialOpened={false}
      panelName='menuCategoryPanel'
    >
      <MenuGroup entitiesArr={entities} isUserAuthorized={isUserAuthorized} />
    </ExpansionPanel>
  );
};

export default MobileMenuCategory;
