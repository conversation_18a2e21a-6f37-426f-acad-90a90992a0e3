'use client';
import { HEADER_NAV_LINKS } from '@/app/config/headerNavLinks';
import { useTranslation } from '@/app/i18n/client';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global';
import clsx from 'clsx';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { StyledNavLink } from '../../../Header/styled';
import { StyledMobilePageTypeMenu } from '../../styled';

const MobilePageTypeMenu = ({ lng }: { lng: LanguagesType }) => {
  const { gamePageType = 'casino' }: { gamePageType?: GamePageType } =
    useParams();
  const { t } = useTranslation(lng);

  const pathname = usePathname();
  const isLiveCasinoPages =
    gamePageType === 'live-casino' || pathname.includes('/live-casino');

  const getMenuTitle = (link: any) => {
    const selected =
      (link.title === 'liveCasino' && isLiveCasinoPages) ||
      (link.title === 'slots' &&
        (pathname === `/${lng}` || pathname.includes('/casino')));

    return (
      <StyledNavLink
        component={Link}
        className={clsx('menuItem', selected && 'selected')}
        type='sub3'
        textTransform='uppercase'
        href={
          ['liveCasino', 'slots'].includes(link.title)
            ? '/' + lng + link.href
            : '/' + lng + '/' + gamePageType + link.href
        }
        key={link.href}
        iconName={link.iconName}
        iconProps={{
          height: 18,
          width: 18,
          fill: theme.color?.general.lightest,
          wrapperWidth: 24,
          wrapperHeight: 24,
        }}
      >
        {t(link.title)}
      </StyledNavLink>
    );
  };

  const tabs = [HEADER_NAV_LINKS[0], HEADER_NAV_LINKS[1]];

  return (
    <StyledMobilePageTypeMenu className='mb-2 flex md:hidden'>
      {tabs.map((tab) => getMenuTitle(tab))}
    </StyledMobilePageTypeMenu>
  );
};

export default MobilePageTypeMenu;
