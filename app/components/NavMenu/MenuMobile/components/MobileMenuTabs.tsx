'use client';
import { HEADER_NAV_LINKS } from '@/app/config/headerNavLinks';
import { useTranslation } from '@/app/i18n/client';
import { Icon, Tabs, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global';
import clsx from 'clsx';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import MobileMenuCategory from './MobileMenuCategory';

const MobileMenuTabs = ({
  lng,
  gamePageType,
  initialTab = 0,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
  initialTab: number;
}) => {
  const { t } = useTranslation(lng);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    setActiveTab(initialTab);
  }, [initialTab]);

  const onTabClick = (e: Event, idx: number) => {
    setActiveTab(idx);
  };

  const getTabTitle = (link: any) => {
    const selected =
      (link.title === 'liveCasino' && activeTab === 1) ||
      (link.title === 'slots' && activeTab === 0);

    return (
      <Link
        className={clsx(
          'flex w-full flex-col items-center gap-1',
          selected && 'selected',
        )}
        href={
          ['liveCasino', 'slots'].includes(link.title)
            ? '/' + lng + link.href
            : '/' + lng + '/' + gamePageType + link.href
        }
        key={link.href}
      >
        <Icon
          name={link.iconName}
          height={18}
          width={18}
          fill={theme.color?.general.lightest}
          wrapperWidth={24}
          wrapperHeight={24}
        />
        <Typography text={t(link.title)} type='sub3' />
      </Link>
    );
  };

  const tabs = [HEADER_NAV_LINKS[0], HEADER_NAV_LINKS[1]];
  const tabsContents = [
    <MobileMenuCategory lng={lng} key={0} type='casino' />,
    <MobileMenuCategory lng={lng} key={1} type='live-casino' />,
  ];

  return (
    <>
      <Tabs
        activeTabProp={activeTab}
        onTabChange={onTabClick}
        getTabTitle={getTabTitle}
        tabsTitles={tabs}
        tabsContents={tabsContents}
        hideNavBtns
        tabPadding='0 0 8px'
        type='buttons'
        className='mobileMenuTabs'
      />
    </>
  );
};

export default MobileMenuTabs;
