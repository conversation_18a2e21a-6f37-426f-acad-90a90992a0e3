'use client';
import { usePathname } from 'next/navigation';
import { useLayoutEffect, useEffect, useRef, useState } from 'react';
import { useDrawerById } from '@/app/wrappers/drawerProvider';
import { Drawer, Icon } from '@/atomic-design-components';
import { HEADER_HEIGHT } from '@/constants';
import useClickOutside from '@/hooks/useClickOutside';
import { usePrevious } from '@/hooks/useReact';
import MenuMobile from './MenuMobile';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { StyledLiveSupport } from '@/app/components/NavMenu/styled.ts';

const NavMenuMobileDrawer = () => {
  const HEADER_HEIGHT_NUMBER = parseInt(HEADER_HEIGHT);

  const { isOpen, close } = useDrawerById('menuPanel');
  const [headerHeight, setHeaderHeight] = useState(HEADER_HEIGHT_NUMBER);

  const { width } = useWindowSize();
  const { isTouchDevice } = useIsTouchMobileView();
  const pathname = usePathname();
  const prevPathname: any = usePrevious(pathname) || '';

  const ref = useRef<HTMLElement>(null);
  useClickOutside(ref, close);

  const isGamePage = pathname.includes('/game/');

  const calculateHeaderHeight = () => {
    const headerBottom = document
      .querySelector('header')
      ?.getBoundingClientRect().bottom;
    if (headerBottom) {
      setHeaderHeight((prevHeight) => {
        if (prevHeight !== headerBottom) {
          return headerBottom;
        }
        return prevHeight;
      });
    }
  };

  useEffect(() => {
    if (isTouchDevice || !isOpen) return;
    if (width && width > theme.breakpoints?.md) {
      close();
    }
  }, [width]);

  useLayoutEffect(() => {
    if (!isOpen) return;
    calculateHeaderHeight();
  }, [isOpen]);

  useEffect(() => {
    if (pathname !== prevPathname) close();
  }, [pathname]);

  if (
    isGamePage ||
    !width ||
    (!isTouchDevice && width >= theme.breakpoints?.md)
  )
    return null;

  return (
    <>
      <Drawer
        headerHeight={`${headerHeight}px`}
        maxWidth='260px'
        isOpenedFixed={true}
        absolutePositioned={true}
        ref={ref}
        opened={isOpen}
        close={close}
        side='left'
        withCloseIcon={false}
        withBluredScreen={true}
        withTitleSection={false}
        className='scrollable mobileMenuDrawer'
      >
        <MenuMobile close={close} />
      </Drawer>
      <StyledLiveSupport isOpen={isOpen} className='live_support_button'>
        <Icon
          name='headphones'
          fill={theme.color?.general.lightest}
          width={24}
          height={24}
        />
      </StyledLiveSupport>
    </>
  );
};

export default NavMenuMobileDrawer;
