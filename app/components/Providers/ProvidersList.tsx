'use client';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { Container, TilesGrid } from '@/atomic-design-components';
import ProviderCard from './ProviderCard.tsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { theme } from '@/theme.ts';
import { MAX_CONTAINER_WIDTH } from '@/constants.ts';

export const PROVIDERS_CARDS_BREAKPOINTS = {
  '(min-width: 1601px)': {
    perView: 8,
    spacing: 16,
  },
  '(max-width: 1600px)': {
    perView: 7,
    spacing: 16,
  },
  '(max-width: 1440px)': {
    perView: 6,
    spacing: 16,
  },
  '(max-width: 1200px)': {
    perView: 5,
    spacing: 16,
  },
  '(max-width: 1050px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 960px)': {
    perView: 5,
    spacing: 8,
  },
  '(max-width: 760px)': {
    perView: 4,
    spacing: 8,
  },
  '(max-width: 620px)': {
    perView: 3,
    spacing: 8,
  },

  '(max-width: 420px)': {
    perView: 2,
    spacing: 8,
  },
};

export const PROVIDERS_CARDS_BREAKPOINTS_TOUCH = {
  '(min-width: 1441px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 1440px)': {
    perView: 3,
    spacing: 16,
  },
  '(max-width: 959px)': {
    perView: 2,
    spacing: 8,
  },
};

const ProvidersList = () => {
  const [{ providers }] = useSystemData();
  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isTouchDeviceOrMd =
    isTouchDevice || (width && width < theme.breakpoints?.md);

  return (
    <>
      <Container
        flexDirection='column'
        fullWidth
        centered
        maxWidth={MAX_CONTAINER_WIDTH}
        dataLength={providers?.length}
        noPadding
      >
        <TilesGrid
          itemsInRow={7}
          breakpoints={
            isTouchDeviceOrMd
              ? PROVIDERS_CARDS_BREAKPOINTS_TOUCH
              : PROVIDERS_CARDS_BREAKPOINTS || 2
          }
          tiles={providers?.map((provider, index) => (
            <ProviderCard
              item={provider}
              key={index}
              index={index}
              className='providersContainerCard'
            />
          ))}
        />
      </Container>
    </>
  );
};

export default ProvidersList;
