'use client';
import { useEffect, useState } from 'react';
import { IProvider } from '@/app/api/getProviders';
import Slider from '@/app/components/Slider/Slider';
import { useTranslation } from '@/app/i18n/client';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { sortItems } from '@/utils/sortItems';
import 'keen-slider/keen-slider.min.css';
import ProviderCard from './ProviderCard';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { PROVIDERS_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/providersBreakpoints.ts';

const ProvidersCarousel = ({
  lng,
  limit,
  gamePageType,
}: {
  lng: LanguagesType;
  limit: number;
  gamePageType: GamePageType;
}) => {
  const { t } = useTranslation(lng);
  const [sortedProviders, setSortedProviders] = useState<IProvider[]>([]);

  const [{ constants, providers: providersAll }] = useSystemData();

  useEffect(() => {
    const isLive = gamePageType === 'live-casino';
    const providers = providersAll?.filter((provider: IProvider) =>
      isLive ? provider.is_live : !provider.is_live,
    );
    const providersOrderResult = constants?.find(
      (constant) => constant.key === 'providers_order',
    );
    const providersOrderArr = providersOrderResult
      ? JSON.parse(providersOrderResult.value)
      : [];

    const activeProviders = providers?.filter(
      (provider: IProvider) => provider.is_featured,
    );
    let sortedProviders = sortItems({
      items: activeProviders || [],
      order: providersOrderArr,
    });

    // if less than 8 providers, double the array to remove empty space on wide screens
    if (sortedProviders.length < 8) {
      sortedProviders = [...sortedProviders, ...sortedProviders];
    }

    setSortedProviders(sortedProviders);
  }, []);

  if (!sortedProviders?.length) {
    return null;
  }

  return (
    <div className='providersCarousel flex flex-col gap-y-2 md:gap-y-4'>
      <Typography type='h1' text={t('providers')} iconName='cyberNetwork' />
      <Slider
        items={sortedProviders}
        total={sortedProviders.length}
        Slide={ProviderCard}
        limit={limit}
        perView={7}
        isCarousel
        renderMode='precision'
        loop={sortedProviders.length > 2}
        loopedSlides={Math.floor(sortedProviders.length / 2)}
        buttonText={t('all')}
        buttonHref={'/' + lng + '/' + gamePageType + '/providers'}
        breakpoints={PROVIDERS_SLIDER_BREAKPOINTS}
      />
    </div>
  );
};

export default ProvidersCarousel;
