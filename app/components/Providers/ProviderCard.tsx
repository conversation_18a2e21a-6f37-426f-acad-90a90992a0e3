'use client';
import { IProvider } from '@/app/api/getProviders';
import { LOGO_PLACEHOLDER_HORIZONTAL } from '@/constants';
import { LanguagesType } from '@/types/global';
import clsx from 'clsx';
import { Image } from '@/atomic-design-components';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import Link from 'next/link';

const ProviderCard = ({
  item,
  index,
  className,
  onClick,
  isLink = true,
  disabled,
}: {
  item: IProvider;
  index: number;
  className?: string;
  onClick?: any;
  isLink?: boolean;
  disabled?: boolean;
}) => {
  const { lng }: { lng: LanguagesType } = useParams();

  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const onMouseEnter = () => setIsHovered(true);
  const onMouseLeave = () => setIsHovered(false);

  const getCard = () => (
    <>
      {(!isLoaded || !item.photo_url_primary) && (
        <Image
          alt={item.name || `Provider ${index} placeholder`}
          placeholder={undefined}
          src={LOGO_PLACEHOLDER_HORIZONTAL}
          fill
          sizes='357px'
          style={{ objectFit: 'contain', willChange: 'transform' }}
          className='placeholder'
          id={`${item.id}`}
          unoptimized
        />
      )}
      {item.photo_url_primary && (
        <Image
          alt={item.name || `Provider ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={item.photo_url_primary}
          width={140}
          height={70}
          style={{
            objectFit: 'contain',
            willChange: 'transform',
          }}
          id={`${item.id}`}
          unoptimized
        />
      )}
    </>
  );
  if (!isLink) {
    return (
      <button
        className={clsx(
          'gameCard providerCard align-center relative flex flex-col justify-center bg-[#1E293B]',
          !item.photo_url_primary && 'placeholder',
          !isLoaded && 'loading',
          'hovered',
          disabled && 'disabled',
          className,
        )}
        onClick={onClick}
        disabled={disabled}
      >
        {getCard()}
      </button>
    );
  }
  return (
    <Link
      className={clsx(
        'gameCard providerCard align-center relative flex flex-col justify-center',
        !item.photo_url_primary && 'placeholder',
        !isLoaded && 'loading',
        isHovered && 'hovered',
        className,
      )}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      href={`/${lng}/${item.is_live ? 'live-casino' : 'casino'}/games/all?filters=provider_id=${item.id}`}
    >
      {getCard()}
    </Link>
  );
};

export default ProviderCard;
