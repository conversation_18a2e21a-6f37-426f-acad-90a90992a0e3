'use client';
import { withTheme } from 'styled-components';
import { useEffect, useState } from 'react';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import NoBonuses from '@/app/components/Bonuses/NoBonuses.tsx';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import BonusMoreInfoModal from './BonusMoreInfoModal';
import CancelBonusModal from './CancelBonusModal';
import BonusesContent from './components/BonusesContent';
import BonusesContentMobile from './components/BonusesContentMobile';
import FreespinsActivatedModal from './FreespinsActivatedModal';
import NewBonusWhileActiveModal from './NewBonusWhileActiveModal';
import { getBonuses } from '@/app/api/getBonuses.ts';
import {
  filterBonuses,
  useUserBonuses,
} from '@/app/wrappers/UserBonusesProvider.tsx';
import Loading from '@/app/[lng]/[gamePageType]/profile/my-bonuses/loading.tsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { GRID_BEFORE_MAX_WIDTH_KEY, GRID_MAX_WIDTH_KEY } from '@/constants.ts';

export const BONUSES_BREAKPOINTS = {
  [GRID_MAX_WIDTH_KEY]: {
    perView: 3,
    spacing: 16,
  },
  [GRID_BEFORE_MAX_WIDTH_KEY]: {
    perView: 2,
    spacing: 16,
  },
  '(max-width: 1370px)': {
    perView: 1,
    spacing: 8,
  },
};

export const BONUSES_BREAKPOINTS_TOUCH = {
  [GRID_MAX_WIDTH_KEY]: {
    perView: 4,
    spacing: 16,
  },
  [GRID_BEFORE_MAX_WIDTH_KEY]: {
    perView: 3,
    spacing: 16,
  },
  '(max-width: 1220px)': {
    perView: 2,
    spacing: 8,
  },
  '(max-width: 784px)': {
    perView: 1,
    spacing: 8,
  },
};

const BonusesPage = ({
  bonusesInitial,
  userUpdated,
  theme,
}: {
  bonusesInitial: IBonus[];
  userUpdated: UserType;
  theme: any;
}) => {
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;
  const { isTouchDevice } = useIsTouchMobileView();
  const isMobileView = isMobile || isTouchDevice;

  const { user, setUser } = useUser();
  const { userBonuses, setUserBonuses, setInProgressBonuses } =
    useUserBonuses();

  const activeBonusTemplateId =
    user?.active_wager?.template_id || user?.active_freespins?.template_id;

  const [bonuses, setBonuses] = useState<IBonus[]>(
    filterBonuses(bonusesInitial),
  );
  const [openedBonus, setOpenedBonus] = useState<IBonus | null>(null);

  const [activeBonusData, setActiveBonusData] = useState<IBonus | undefined>(
    undefined,
  );

  const hasActiveBonus = user?.active_bonus_id;

  const [inProgress, setInProgress] = useState(true);

  useEffect(() => {
    setInProgressBonuses(false);
    setUser(userUpdated);
  }, []);

  useEffect(() => {
    if (!userBonuses) {
      return;
    }
    //TODO: check setState in useEffect for max update exceeded
    setBonuses(
      filterBonuses(
        userBonuses || [],
        activeBonusTemplateId || 0,
        user?.active_bonus_type,
        user?.active_wager,
      ),
    );
  }, [userBonuses]);

  useEffect(() => {
    if (user?.active_wager?.template_id === 0) {
      const activeBonus: any = user?.active_wager;
      if (activeBonus) {
        setActiveBonusData(activeBonus);
        setUserBonuses(
          filterBonuses(
            bonuses,
            0,
            user?.active_bonus_type,
            user?.active_wager,
          ),
        );
      }
    } else if (activeBonusTemplateId) {
      const activeBonus: IBonus | undefined = bonuses.find(
        (bonus: IBonus) =>
          bonus.external_id === activeBonusTemplateId &&
          bonus.bonus_global_type === user?.active_bonus_type,
      );
      // console.log('active bonus', bonuses, activeBonus);
      if (activeBonus) {
        setActiveBonusData(activeBonus);
        setUserBonuses(
          filterBonuses(
            bonuses,
            activeBonusTemplateId,
            user?.active_bonus_type,
          ),
        );
      } else {
        // for cashback bonus
        getBonuses(0, 1, {
          external_id: [activeBonusTemplateId],
        }).then((res) => {
          if (res?.items && !res?.detail) {
            setActiveBonusData(res.items[0] as IBonus);
          }
        });
      }
    } else {
      // console.log('no active bonus', bonuses);
      setUserBonuses(filterBonuses(bonuses));
    }
    setInProgress(false);
  }, [user?.active_wager, user?.active_freespins]);

  if (inProgress) {
    return <Loading />;
  }

  if (!inProgress && !bonuses?.length && !hasActiveBonus) {
    return <NoBonuses />;
  }

  return (
    <>
      {!isMobileView && (
        <BonusesContent
          hasActiveBonus={hasActiveBonus}
          activeBonusData={activeBonusData}
          setBonuses={setBonuses}
          inProgress={inProgress}
          bonuses={bonuses}
          setOpenedBonus={setOpenedBonus}
          setActiveBonusData={setActiveBonusData}
          className={isMobileView ? '' : 'max-md:hidden'}
        />
      )}
      {isMobileView && (
        <BonusesContentMobile
          hasActiveBonus={hasActiveBonus}
          activeBonusData={activeBonusData}
          setBonuses={setBonuses}
          bonuses={bonuses}
          setOpenedBonus={setOpenedBonus}
          setActiveBonusData={setActiveBonusData}
          className={isMobileView ? '' : 'max-md:hidden'}
        />
      )}

      <BonusMoreInfoModal
        openedBonus={openedBonus}
        setBonuses={setBonuses}
        setActiveBonusData={setActiveBonusData}
      />
      <NewBonusWhileActiveModal activeBonusType={user?.active_bonus_type} />
      <FreespinsActivatedModal
        activeBonusData={activeBonusData}
        currency={user?.currency}
      />
      <CancelBonusModal />
    </>
  );
};

export default withTheme(BonusesPage);
