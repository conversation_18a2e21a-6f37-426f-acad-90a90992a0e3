'use client';
import styled from 'styled-components';
import { FlexRow } from '@/atomic-design-components';

export const StyledBonusCard: any = styled.div`
  position: relative;
  background-color: ${({ theme }) => theme.color?.general.darker};
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
`;

export const StyledBonusActiveCard: any = styled(FlexRow)`
  margin-bottom: 16px;
  position: relative;
  background: linear-gradient(180deg, #21284e 0%, #284171 100%);
  border: 1px solid rgba(248, 250, 252, 0.1);
  border-radius: 8px;
  overflow: hidden;

  &.isWager .content {
    > div:first-child {
      border-top: 1px solid ${({ theme }) => theme.color?.general.lightest}1a;
    }
  }

  .content {
    margin: 0 auto;
  }
  && .cancelButton:not(.disabled) {
    &:hover {
      background-color: transparent;
      color: ${({ theme }) => theme.color?.general.lighter};
    }
  }
  .dateWrapper {
    border-radius: 12px;
    background: ${({ theme }) => theme.color?.general.darker};
    padding: 14px 20px;
    position: relative;
  }
`;
