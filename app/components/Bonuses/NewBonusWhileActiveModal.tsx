'use client';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { useParams } from 'next/navigation';

const NewBonusWhileActiveModal = ({
  activeBonusType,
}: {
  activeBonusType?: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  if (!activeBonusType) return null;

  return (
    <ModalDialog
      id='newBonusWhileActive'
      title={t(
        activeBonusType.includes('freespins')
          ? 'youHaveActiveFreespins'
          : 'youHaveActiveBonus',
      )}
      text={t('completeOrCancelCurrentBonus')}
      withOneButton
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

NewBonusWhileActiveModal.displayName = 'NewBonusWhileActiveModal';
export default NewBonusWhileActiveModal;
