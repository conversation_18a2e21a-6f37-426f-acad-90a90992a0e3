'use client';
import dayjs from 'dayjs';
import { useParams } from 'next/navigation';
import { useEffect, useRef } from 'react';
import ReactTexty from 'react-texty';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import BonusCardImageBlock from '@/app/components/Bonuses/BonusCardImageBlock.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Typography } from '@/atomic-design-components';
import { useBonusTemplateState } from '@/hooks/useBonusTemplateState.tsx';
import { theme } from '@/theme';
import { useTransformDateFromNowInSeconds } from '@/utils/dates';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { openModal } from '@/utils/openModal';
import { StyledBonusCard } from './styled';
import { filterBonuses, useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const BonusCard = ({
  bonus,
  setOpenedBonus,
  setBonuses,
  setActiveBonusData,
  setInProgressActiveBonus,
  bonusIndex,
}: {
  bonus: IBonus;
  setOpenedBonus: Function;
  setBonuses: Function;
  setActiveBonusData: Function;
  setInProgressActiveBonus?: Function;
  bonusIndex?: number;
}) => {
  // const router = useRouter();
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const currencySymbol = getCurrencySymbol(user!.currency);

  const { setUserBonuses } = useUserBonuses();

  const currentDateInUTC = useRef(dayjs().utc());

  const lastDateTimeToActivate =
    bonus.time_to_activate && currentDateInUTC.current?.add(bonus.time_to_activate, 'second');

  const dateForActivationTimer = useTransformDateFromNowInSeconds(
    lastDateTimeToActivate,
    lng,
    true,
  ) as Array<string>;

  useEffect(() => {
    if (!dateForActivationTimer || !bonus.hours_to_activate) {
      return;
    }

    //TODO: check setState in useEffect for max update exceeded
    if (
      dateForActivationTimer.every((el: string) => el === '0' || el === '00') ||
      dateForActivationTimer.some((el: string) => el.includes('-'))
    ) {
      setUserBonuses((prev: IBonus[] | null) =>
        filterBonuses(prev || [], bonus.external_id || 0, bonus.bonus_global_type, undefined, true),
      );
    }
  }, [dateForActivationTimer, bonus.hours_to_activate]);

  const bonusType =
    bonus.bonus_type === 'wager_freespins_deposit' && bonus.bonus_global_type === 'freespins'
      ? 'freespins_no_deposit'
      : bonus.bonus_type || bonus.bonus_global_type;

  const isDepositBonus = ['wager_deposit', 'freespins_deposit', 'wager_freespins_deposit'].includes(
    bonusType,
  );

  const { isDepositNeeded, onButtonClick, inProgress, isVerificationNeeded } =
    useBonusTemplateState(bonus, isDepositBonus, false, setBonuses, setActiveBonusData);

  useEffect(() => {
    if (setInProgressActiveBonus) {
      setInProgressActiveBonus(inProgress);
    }
  }, [inProgress, setInProgressActiveBonus]);

  const betPerSpin =
    bonusType?.includes('freespins') &&
    (bonusType === 'freespins_deposit'
      ? (bonus.min_deposit_amount * (bonus.deposit_amount_percentage / 100)) /
        bonus.freespins_amount
      : bonus.bonus_amount / bonus.freespins_amount);

  const dateText = ['dayShort', 'hourShort', 'minShort', 'secShort'];

  return (
    <StyledBonusCard>
      <BonusCardImageBlock
        bonus={bonus}
        currencySymbol={currencySymbol}
        bonusType={bonusType}
        bonusIndex={bonusIndex}
      />
      <div className='grow px-4 pb-4 pt-2'>
        <div className='flex h-full flex-col'>
          <Typography
            as={ReactTexty}
            type='h3'
            className='bonusName'
            text={
              getAvailableTranslation(bonus.title, lng) ||
              bonus.name ||
              (bonusType === 'wager_no_deposit' && t('wager'))
            }
          />

          <div className='mb-1 flex flex-wrap gap-2'>
            {isDepositBonus && (
              <Typography type='body2' color={theme.color?.general.light}>
                {t('depositMin')}:&nbsp;
                <span className='text-general-lightest'>
                  {currencySymbol}
                  {addSpacesToLongNumbers(bonus.min_deposit_amount)}
                </span>
              </Typography>
            )}
            {!!betPerSpin && (
              <Typography type='body2' color={theme.color?.general.light}>
                {t('betAmount')}:&nbsp;
                <span className='text-general-lightest'>
                  {currencySymbol}
                  {addSpacesToLongNumbers(betPerSpin)}
                </span>
              </Typography>
            )}
          </div>

          {dateForActivationTimer && (
            <>
              <hr className='my-auto h-[1px] w-full border-0 bg-general-lightest bg-opacity-10' />
              <Typography type='sub2' iconName='clockOutlined' className='pb-1 pt-2'>
                {dateForActivationTimer.map(
                  (datePart, i) => `${datePart} ${t(dateText[i], { count: +datePart })} `,
                )}
              </Typography>{' '}
            </>
          )}

          <div className='mt-auto flex gap-2 pt-1'>
            <Button
              variant='primary'
              text={t(isDepositNeeded ? 'makeDeposit' : 'takeBonus')}
              size='small'
              disabled={inProgress}
              className='basis-1/2'
              onClick={(e: any) => {
                e.preventDefault();
                if (!isDepositNeeded && isVerificationNeeded) {
                  setOpenedBonus(bonus);
                  openModal('bonusMoreInfoModal');
                  return;
                }

                if (onButtonClick) {
                  onButtonClick();
                }
              }}
            />
            <Button
              variant='transparent'
              text={t('more')}
              size='small'
              withBorder
              className='basis-1/2'
              onClick={() => {
                setOpenedBonus(bonus);
                openModal('bonusMoreInfoModal');
              }}
            />
          </div>
        </div>
      </div>
    </StyledBonusCard>
  );
};

export default BonusCard;
