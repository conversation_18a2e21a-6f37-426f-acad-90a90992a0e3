import { activatePromoCode } from '@/app/api/server-actions/bonuses.ts';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { Button, Input, Typography } from '@/atomic-design-components';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import clsx from 'clsx';

const PromoCode = ({
  withInputOnly,
  className,
}: {
  withInputOnly?: boolean;
  className?: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const { showError, showAlert } = useAlert();
  const { getFilteredBonuses, setInProgressBonuses } = useUserBonuses();

  const [code, setCode] = useState('');
  const [inProgress, setInProgress] = useState(false);

  const inputWithButton = (
    <div className='flex gap-2 '>
      <Input
        name='promoCode'
        placeholder={t('enterCode')}
        fullWidth
        value={code}
        onChange={(e: any) => {
          const { value } = e.target;
          setCode(value);
        }}
      />
      <Button
        disabled={!code || inProgress}
        text={t('activate')}
        onClick={() => {
          setInProgress(true);
          setInProgressBonuses(true);

          activatePromoCode(code)
            .then((result) => {
              if (result?.error) {
                showError(result?.error || t('error'));
                return;
              }
              if (result?.ok) {
                // console.log('getFilteredBonuses on promo code');
                getFilteredBonuses();
                setCode('');
                showAlert({
                  content: t('bonusAdded'),
                  id: Date.now().toString(),
                  type: 'success',
                  timeout: 3000,
                });
              }
            })
            .finally(() => {
              setInProgress(false);
              setInProgressBonuses(false);
            });
        }}
      />
    </div>
  );

  if (withInputOnly) {
    return inputWithButton;
  }

  return (
    <div
      className={clsx(
        'w-full rounded-lg bg-general-darker p-4 max-md:mt-2',
        className,
      )}
    >
      <Typography type='h3' text={t('promoCode')} padding='0 0 8px 0' />
      {inputWithButton}
    </div>
  );
};

export default PromoCode;
