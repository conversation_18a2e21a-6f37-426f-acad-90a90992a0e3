'use client';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { useParams } from 'next/navigation';
import BonusCardImageBlock from '@/app/components/Bonuses/BonusCardImageBlock.tsx';
import { Button, Icon, Typography } from '@/atomic-design-components';
import { useBonusTemplateState } from '@/hooks/useBonusTemplateState.tsx';
import { theme } from '@/theme.ts';
import capitalize from '@/utils/capitalize.ts';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import Link from 'next/link';
import { closeModal } from '@/utils/closeModal.ts';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const BonusMoreInfoModal = ({
  id = 'bonusMoreInfoModal',
  openedBonus,
  onClose,
  setBonuses,
  onBonusChange,
  setActiveBonusData,
}: {
  id?: string;
  openedBonus: IBonus | null;
  onClose?: Function;
  setBonuses?: Function;
  onBonusChange?: Function;
  setActiveBonusData?: Function;
}) => {
  const { lng, gamePageType } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  // const { showError } = useAlert();
  const currencySymbol = getCurrencySymbol(
    openedBonus?.currency || user?.currency || '',
  );

  const bonusType =
    openedBonus?.bonus_type === 'wager_freespins_deposit' &&
    openedBonus?.bonus_global_type === 'freespins'
      ? 'freespins_no_deposit'
      : openedBonus?.bonus_type || openedBonus?.bonus_global_type;

  const isWager = bonusType?.startsWith('wager');
  const isFreespins = bonusType?.includes('freespins');
  const isDepositBonus = [
    'wager_deposit',
    'freespins_deposit',
    'wager_freespins_deposit',
  ].includes(bonusType || '');

  const { isDepositNeeded, onButtonClick, inProgress, isVerificationNeeded } =
    useBonusTemplateState(
      openedBonus!,
      isDepositBonus,
      true,
      setBonuses,
      setActiveBonusData,
    );

  const betPerSpin =
    (openedBonus &&
      bonusType &&
      bonusType?.includes('freespins') &&
      (bonusType === 'freespins_deposit'
        ? (openedBonus.min_deposit_amount *
            (openedBonus.deposit_amount_percentage / 100)) /
          openedBonus.freespins_amount
        : openedBonus.bonus_amount / openedBonus.freespins_amount)) ||
    0;

  return (
    <ModalDialog
      id={id}
      className='bonusMoreInfo-modal w-[400px] min-w-[300px]'
      onClose={onClose}
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    >
      <div className='flex flex-col p-6 max-sm:p-4 max-sm:pb-3'>
        <Typography
          type='h1'
          margin='0 40px 16px 0'
          displayCssProp='block'
          className='overflow-hidden text-ellipsis whitespace-nowrap'
        >
          {getAvailableTranslation(openedBonus?.title || {}, lng) ||
            openedBonus?.name ||
            (bonusType === 'wager_no_deposit' && t('wager'))}
        </Typography>

        <BonusCardImageBlock
          bonus={openedBonus}
          className='rounded-[8px]'
          currencySymbol={currencySymbol}
          bonusType={bonusType}
        />

        {id === 'bonusMoreInfoModal' && isVerificationNeeded && (
          <div className='mb-2 mt-2 flex items-start gap-2 rounded-lg bg-general-dark px-2 py-2'>
            <Icon name='infoIcon' fill={theme.color?.secondary.dark} />
            <div>
              <Typography text={t('toGetBonusYouNeedTo') + ':'} type='body1' />
              {openedBonus?.errors?.includes(54) && (
                <Typography
                  text={'- ' + t('passKycVerification')}
                  type='body1'
                />
              )}
              {openedBonus?.errors?.includes(55) && (
                <Typography text={'- ' + t('verifyYourMail')} type='body1' />
              )}
              {openedBonus?.errors?.includes(63) && (
                <Typography text={'- ' + t('verifyPhone')} type='body1' />
              )}
            </div>
          </div>
        )}

        {bonusType?.startsWith('freespins') && openedBonus?.game_name && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
            <Typography
              type='body2'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('game')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {openedBonus?.game_name}
            </Typography>
          </div>
        )}

        {bonusType?.startsWith('freespins') &&
          openedBonus?.game_provider_slug && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
              <Typography
                type='body2'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('provider')}:
              </Typography>
              <Typography
                type='body2'
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              >
                {capitalize(openedBonus.game_provider_slug.replace(/_/g, ' '))}
              </Typography>
            </div>
          )}

        {isWager &&
          isDepositBonus &&
          !!openedBonus?.deposit_amount_percentage && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
              <Typography
                type='body2'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('percentage')}:
              </Typography>
              <Typography
                type='body2'
                text={openedBonus?.deposit_amount_percentage + '%'}
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              />
            </div>
          )}

        {isDepositBonus && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
            <Typography
              type='body2'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('depositMin')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {currencySymbol}
              {addSpacesToLongNumbers(openedBonus?.min_deposit_amount || 0)}
            </Typography>
          </div>
        )}

        {bonusType === 'wager_freespins_deposit' &&
          openedBonus?.deposit_amount_percentage && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
              <Typography
                type='body2'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('maxBonusAmount')}:
              </Typography>
              <Typography
                type='body2'
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              >
                {currencySymbol}
                {addSpacesToLongNumbers(openedBonus?.max_deposit_amount *
                  (openedBonus?.deposit_amount_percentage / 100))}
              </Typography>
            </div>
          )}

        {bonusType === 'wager_freespins_deposit' && openedBonus?.game_name && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
            <Typography
              type='body2'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('game')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {openedBonus?.game_name}
            </Typography>
          </div>
        )}

        {bonusType === 'wager_no_deposit' &&
          !!(openedBonus?.bonus_amount || openedBonus?.wager_amount) && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
              <Typography
                type='body2'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('bonusAmount')}:
              </Typography>
              <Typography
                type='body2'
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              >
                {currencySymbol}
                {addSpacesToLongNumbers(openedBonus?.bonus_amount || openedBonus?.wager_amount || 0)}
              </Typography>
            </div>
          )}

        {isFreespins && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
            <Typography
              type='body2'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('quantity')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {openedBonus?.freespins_amount}FS
            </Typography>
          </div>
        )}

        {!!betPerSpin && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
            <Typography
              type='body2'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('betAmount')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              {currencySymbol}
              {addSpacesToLongNumbers(betPerSpin)}
            </Typography>
          </div>
        )}

        {!!(openedBonus?.wager_multiplier || openedBonus?.multiplier) && (
          <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
            <Typography
              type='body2'
              color={theme.color?.general.lighter}
              className='basis-1/2'
            >
              {t('wager')}:
            </Typography>
            <Typography
              type='body2'
              color={theme.color?.general.lightest}
              fontWeight={theme.font.weight.bold}
              className='basis-1/2'
            >
              x{openedBonus?.wager_multiplier || openedBonus?.multiplier}
            </Typography>
          </div>
        )}

        {bonusType === 'wager_deposit' &&
          openedBonus?.deposit_amount_percentage && (
            <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2 last-of-type:border-0'>
              <Typography
                type='body2'
                color={theme.color?.general.lighter}
                className='basis-1/2'
              >
                {t('maxBonusAmount')}:
              </Typography>
              <Typography
                type='body2'
                color={theme.color?.general.lightest}
                fontWeight={theme.font.weight.bold}
                className='basis-1/2'
              >
                {currencySymbol}
                {addSpacesToLongNumbers(openedBonus?.max_deposit_amount *
                  (openedBonus?.deposit_amount_percentage / 100))}
              </Typography>
            </div>
          )}
        {isDepositBonus && (
          <div className='mt-3 flex items-start gap-2 rounded-lg bg-general-dark px-2 py-2'>
            <Icon name='infoIcon' fill={theme.color?.secondary.dark} />
            <Typography text={t('bonusActivationBefore')} type='sub1' />
          </div>
        )}
        {id === 'bonusMoreInfoCashierModal' && (
          <Button
            className='ml-auto mt-4 max-md:mt-3 max-sm:w-full sm:min-w-[94px]'
            textTransform='uppercase'
            type='label2'
            text={t(onBonusChange ? 'selectBonus' : 'OK')}
            onClick={(e: any) => {
              e.preventDefault();
              if (onBonusChange) {
                onBonusChange(openedBonus?.external_id);
              }
              if (onClose) onClose();
            }}
            variant='primary'
          />
        )}
        {id === 'bonusMoreInfoModal' && (
          <Button
            padding='0'
            className='ml-auto mt-4 max-md:mt-3 max-sm:w-full sm:min-w-[94px]'
            textTransform='uppercase'
            type='label2'
            disabled={inProgress}
            onClick={(e: any) => {
              e.preventDefault();
              if (!isDepositNeeded && isVerificationNeeded) {
                closeModal('bonusMoreInfoModal');
                return;
              }

              if (onButtonClick) {
                onButtonClick();
                closeModal('bonusMoreInfoModal');
              }
              if (onClose) onClose();
            }}
            variant='primary'
          >
            <Link
              className='w-full px-4 py-3'
              href={
                !isDepositNeeded && isVerificationNeeded
                  ? `/${lng}/${gamePageType || 'casino'}/profile/personal-data`
                  : ''
              }
            >
              {t(
                isDepositNeeded
                  ? 'makeDeposit'
                  : isVerificationNeeded
                    ? 'verify'
                    : 'takeBonus',
              )}
            </Link>
          </Button>
        )}
      </div>
    </ModalDialog>
  );
};

BonusMoreInfoModal.displayName = 'BonusMoreInfoModal';
export default BonusMoreInfoModal;
