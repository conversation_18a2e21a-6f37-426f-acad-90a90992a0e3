'use client';
import styled from 'styled-components';
import clsx from 'clsx';
import { BonusCardBgImage } from '@/app/components/Bonuses/BonusCardBgImage.tsx';
import { Typography, Image } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const BONUS_IMAGES_DEFAULT = {
  freespins_deposit: '/bonusFS.png',
  freespins_no_deposit: '/bonusFS.png',
  wager_deposit: '/bonusWager.png',
  wager_no_deposit: '/bonusWager.png',
  wager_freespins_deposit: '/bonusWagerFS.png',
};

const StyledImageBlock = styled.div`
  background: linear-gradient(270deg, #284171 0%, #21284e 100%);
`;

const BonusCardImageBlock = ({
  bonus,
  bonusType,
  className,
  currencySymbol,
  defaultImageTypeForBonus,
  bonusIndex,
}: {
  bonus: IBonus | null;
  bonusType?: string;
  className?: string;
  currencySymbol: string;
  defaultImageTypeForBonus?: string;
  bonusIndex?: number;
}) => {
  const isFreespinsFromWagerFreespinsBonus =
    bonus?.bonus_global_type === 'freespins' && bonus?.bonus_type === 'wager_freespins_deposit';

  const isMixedBonusInfoShown =
    bonus?.deposit_amount_percentage &&
    bonus.freespins_amount &&
    bonus.bonus_type !== 'freespins_deposit' &&
    !isFreespinsFromWagerFreespinsBonus;

  const imageSrc =
    bonus?.photo_main_url ||
    BONUS_IMAGES_DEFAULT[
      (bonusType ||
        bonus?.bonus_type ||
        defaultImageTypeForBonus) as keyof typeof BONUS_IMAGES_DEFAULT
    ];

  return (
    <StyledImageBlock
      className={clsx(
        'relative mb-2 flex h-[129px] w-full items-center justify-between gap-2 py-[10px] pl-6 pr-4',
        className,
      )}
    >
      <BonusCardBgImage className={className} />
      <Typography
        displayCssProp='block'
        fontSize='56px'
        lineHeight='58px'
        color={theme.color?.general.lightest}
        fontWeight={theme.font.weight.bold}
        className='z-10'
      >
        {!isFreespinsFromWagerFreespinsBonus &&
          bonus?.bonus_type !== 'freespins_deposit' &&
          !!bonus?.deposit_amount_percentage &&
          `${bonus.deposit_amount_percentage}%`}

        {(isFreespinsFromWagerFreespinsBonus ||
          isMixedBonusInfoShown ||
          ['freespins_deposit', 'freespins_no_deposit'].includes(bonus?.bonus_type || '')) && (
          <Typography
            className='whitespace-nowrap'
            type='h3'
            fontSize={isMixedBonusInfoShown ? undefined : '56px'}
            lineHeight={isMixedBonusInfoShown ? undefined : '58px'}
          >{`${isMixedBonusInfoShown ? '+' : ''}${bonus?.freespins_amount || 0}FS`}</Typography>
        )}

        {bonusType === 'wager_no_deposit' &&
          bonus &&
          !!(bonus.bonus_amount || bonus.wager_amount) &&
          `${currencySymbol}${addSpacesToLongNumbers(bonus.bonus_amount || bonus.wager_amount || 0)}`}
      </Typography>
      <Image
        alt='Bonus image'
        src={imageSrc}
        priority={!!(bonusIndex && bonusIndex < 3)}
        width={110}
        height={110}
        style={bonus?.photo_main_url ? { padding: '8px' } : !bonus ? { opacity: 0 } : undefined}
        className='z-10'
      />
    </StyledImageBlock>
  );
};

export default BonusCardImageBlock;
