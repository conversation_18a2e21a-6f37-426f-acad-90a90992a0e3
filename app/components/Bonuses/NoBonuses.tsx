'use client';
import React from 'react';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import PromoCode from '@/app/components/Bonuses/PromoCode.tsx';

const NoBonuses = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  return (
    <div className='flex grow items-start'>
      <div className='mx-auto self-center text-center'>
        <div className='mb-2'>
          <Icon name='giftOpened' className='mx-auto' />
        </div>
        <Typography type='sub2' displayCssProp='block'>
          {t('noBonusesYet')}
        </Typography>
        <Typography
          type='body1'
          color={theme.color?.general.lighter}
          displayCssProp='block'
          margin='0 0 16px 0'
        >
          {t('waitForBonusesOrEnterPromoCode')}
        </Typography>
        <PromoCode withInputOnly />
      </div>
    </div>
  );
};

export default NoBonuses;
