'use client';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { useTranslation } from '@/app/i18n/client';
import { Tabs, TilesGrid, Typography } from '@/atomic-design-components';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import BonusActiveCard from '../BonusActiveCard';
import BonusCard from '../BonusCard';
import { BONUSES_BREAKPOINTS_TOUCH } from '../BonusesPage';
import PromoCode from '../PromoCode';
import clsx from 'clsx';

const BonusesContentMobile = ({
  hasActiveBonus,
  activeBonusData,
  setBonuses,
  bonuses,
  setOpenedBonus,
  setActiveBonusData,
  className,
}: {
  hasActiveBonus: number | undefined;
  activeBonusData: IBonus | undefined;
  setBonuses: Function;
  bonuses: IBonus[];
  setOpenedBonus: Function;
  setActiveBonusData: Function;
  className?: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const [activeTab, setActiveTab] = useState(0);

  const tabs = hasActiveBonus
    ? ['active', 'allBonuses', 'promoCode']
    : ['allBonuses', 'promoCode'];
  const onTabClick = (e: Event, idx: number) => {
    setActiveTab(idx);
  };

  const getTabTitle = (tab: any) => (
    <Typography
      text={t(tab || 'dashboard')}
      alignItems='center'
      justifyContent='center'
      type='body2'
    />
  );

  const tabsContents = hasActiveBonus
    ? [
        <BonusActiveCard
          bonusTemplateData={activeBonusData}
          key={0}
          isMobile
          className='mt-2'
        />,
        <TilesGrid
          alignItems='stretch'
          className='pt-2'
          tiles={bonuses.map((bonus: IBonus, i: number) => {
            return (
              <BonusCard
                bonus={bonus}
                key={bonus.external_id}
                setOpenedBonus={setOpenedBonus}
                setBonuses={setBonuses}
                setActiveBonusData={setActiveBonusData}
                bonusIndex={i}
              />
            );
          })}
          breakpoints={BONUSES_BREAKPOINTS_TOUCH}
          key={1}
        />,
        <PromoCode key={2} className='mt-2' />,
      ]
    : [
        <TilesGrid
          alignItems='stretch'
          className='pt-2'
          tiles={bonuses.map((bonus: IBonus) => {
            return (
              <BonusCard
                bonus={bonus}
                key={bonus.external_id}
                setOpenedBonus={setOpenedBonus}
                setBonuses={setBonuses}
                setActiveBonusData={setActiveBonusData}
              />
            );
          })}
          breakpoints={BONUSES_BREAKPOINTS_TOUCH}
          key={0}
        />,
        <PromoCode key={1} className='mt-2' />,
      ];

  return (
    <div
      className={clsx('flex flex-col gap-2 px-2 md:pl-0 md:pr-6', className)}
    >
      <Tabs
        activeTabProp={activeTab}
        onTabChange={onTabClick}
        tabsTitles={tabs}
        hideNavBtns
        tabsContents={tabsContents}
        getTabTitle={getTabTitle}
        // className={clsx(className, 'mx-2 md:ml-0 md:mr-6')}
      />
    </div>
  );
};

export default BonusesContentMobile;
