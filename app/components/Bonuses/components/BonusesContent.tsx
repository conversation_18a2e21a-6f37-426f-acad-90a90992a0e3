'use client';
import BonusActiveCard from '@/app/components/Bonuses/BonusActiveCard.tsx';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import BonusCard from '@/app/components/Bonuses/BonusCard.tsx';
import NoActiveBonusCard from '@/app/components/Bonuses/NoActiveBonusCard.tsx';
import PromoCode from '@/app/components/Bonuses/PromoCode.tsx';
import { TilesGrid } from '@/atomic-design-components';
import { BONUSES_BREAKPOINTS } from '../BonusesPage';
import NoBonuses from '@/app/components/Bonuses/NoBonuses.tsx';
import { useState } from 'react';
import { LoadingActiveBonusCard } from '@/app/[lng]/[gamePageType]/profile/my-bonuses/loading.tsx';
import clsx from 'clsx';

const BonusesContent = ({
  hasActiveBonus,
  activeBonusData,
  setBonuses,
  inProgress,
  bonuses,
  setOpenedBonus,
  setActiveBonusData,
  className,
}: {
  hasActiveBonus: number | undefined;
  activeBonusData: IBonus | undefined;
  setBonuses: Function;
  inProgress: boolean;
  bonuses: IBonus[];
  setOpenedBonus: Function;
  setActiveBonusData: Function;
  className?: string;
}) => {
  const [inProgressActiveBonus, setInProgressActiveBonus] = useState(false);

  return (
    <div className={clsx('flex grow items-start gap-4 pt-4', className)}>
      <div className='min-w-[280px] max-w-[360px]' style={{ width: '25%' }}>
        {inProgressActiveBonus ? (
          <LoadingActiveBonusCard />
        ) : hasActiveBonus ? (
          <BonusActiveCard bonusTemplateData={activeBonusData} />
        ) : (
          <NoActiveBonusCard />
        )}
        {!!bonuses?.length && <PromoCode />}
      </div>

      {!bonuses?.length && <NoBonuses />}

      {!inProgress && !!bonuses?.length && (
        <TilesGrid
          alignItems='stretch'
          className='pr-6'
          tiles={bonuses.map((bonus: IBonus) => {
            return (
              <BonusCard
                bonus={bonus}
                key={bonus.external_id}
                setOpenedBonus={setOpenedBonus}
                setBonuses={setBonuses}
                setActiveBonusData={setActiveBonusData}
                setInProgressActiveBonus={setInProgressActiveBonus}
              />
            );
          })}
          breakpoints={BONUSES_BREAKPOINTS}
        />
      )}
    </div>
  );
};

export default BonusesContent;
