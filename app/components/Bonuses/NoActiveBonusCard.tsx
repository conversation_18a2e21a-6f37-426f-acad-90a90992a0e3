import React from 'react';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';

const NoActiveBonusCard = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  return (
    <div className='mx-auto mb-4 rounded-[8px] bg-general-darker p-6 text-center'>
      <div className='mb-2'>
        <Icon name='giftOpened' className='mx-auto' />
      </div>
      <Typography type='sub2' displayCssProp='block'>
        {t('youDontHaveActiveBonus')}
      </Typography>
      <Typography
        type='body1'
        color={theme.color?.general.lighter}
        displayCssProp='block'
      >
        {t('chooseBonusOrEnterPromoCode')}
      </Typography>
    </div>
  );
};

export default NoActiveBonusCard;
