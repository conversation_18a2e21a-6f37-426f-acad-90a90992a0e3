export const ActiveCardBgImage = () => (
  <svg
    width='220'
    height='210'
    viewBox='0 0 220 210'
    fill='none'
    className='absolute left-0 top-0 rounded'
  >
    <g filter='url(#filter0_f_5444_87591)'>
      <circle cx='70' cy='60' r='100' fill='#0083FF' fillOpacity='0.3' />
    </g>
    <defs>
      <filter
        id='filter0_f_5444_87591'
        x='-80'
        y='-90'
        width='300'
        height='300'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='BackgroundImageFix'
          result='shape'
        />
        <feGaussianBlur
          stdDeviation='25'
          result='effect1_foregroundBlur_5444_87591'
        />
      </filter>
    </defs>
  </svg>
);
