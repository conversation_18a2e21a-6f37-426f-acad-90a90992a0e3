import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import BonusCardImageBlock from '@/app/components/Bonuses/BonusCardImageBlock.tsx';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import capitalize from '@/utils/capitalize.ts';
import { closeModal } from '@/utils/closeModal.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const FreespinsActivatedModal = ({
  activeBonusData,
  currency,
}: {
  activeBonusData?: IBonus;
  currency?: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const currencySymbol = getCurrencySymbol(currency || '');

  const bonusType =
    activeBonusData?.bonus_type === 'wager_freespins_deposit' &&
    activeBonusData.bonus_global_type === 'freespins'
      ? 'freespins_no_deposit'
      : activeBonusData?.bonus_type;

  const betPerSpin =
    (activeBonusData &&
      bonusType?.includes('freespins') &&
      (bonusType === 'freespins_deposit'
        ? (activeBonusData.min_deposit_amount *
            (activeBonusData.deposit_amount_percentage / 100)) /
          activeBonusData.freespins_amount
        : activeBonusData.bonus_amount / activeBonusData.freespins_amount)) ||
    0;

  return (
    <ModalDialog
      id='freespinsActivatedModal'
      title={t('bonusActivated')}
      className='min-w-[352px] max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    >
      <BonusCardImageBlock
        bonus={activeBonusData || null}
        className='mt-4 rounded-[8px]'
        currencySymbol={currencySymbol}
        defaultImageTypeForBonus='freespins_no_deposit'
        bonusType={bonusType}
      />

      <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 pb-2 pt-5'>
        <Typography
          type='body2'
          color={theme.color?.general.lighter}
          className='basis-1/2'
        >
          {t('game')}:
        </Typography>
        <Typography
          type='body2'
          color={theme.color?.general.lightest}
          fontWeight={theme.font.weight.bold}
          className='basis-1/2'
        >
          {activeBonusData?.game_name}
        </Typography>
      </div>

      <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
        <Typography
          type='body2'
          color={theme.color?.general.lighter}
          className='basis-1/2'
        >
          {t('provider')}:
        </Typography>
        <Typography
          type='body2'
          color={theme.color?.general.lightest}
          fontWeight={theme.font.weight.bold}
          className='basis-1/2'
        >
          {capitalize(
            activeBonusData?.game_provider_slug?.replace(/_/g, ' ') || '',
          )}
        </Typography>
      </div>

      <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
        <Typography
          type='body2'
          color={theme.color?.general.lighter}
          className='basis-1/2'
        >
          {t('quantity')}:
        </Typography>
        <Typography
          type='body2'
          color={theme.color?.general.lightest}
          fontWeight={theme.font.weight.bold}
          className='basis-1/2'
        >
          {activeBonusData?.freespins_amount}FS
        </Typography>
      </div>

      <div className='flex w-full border-b-[1px] border-b-general-lightest border-opacity-10 py-2'>
        <Typography
          type='body2'
          color={theme.color?.general.lighter}
          className='basis-1/2'
        >
          {t('betAmount')}:
        </Typography>
        <Typography
          type='body2'
          color={theme.color?.general.lightest}
          fontWeight={theme.font.weight.bold}
          className='basis-1/2'
        >
          {currencySymbol}
          {addSpacesToLongNumbers(betPerSpin)}
        </Typography>
      </div>

      <div className='flex w-full py-2'>
        <Typography
          type='body2'
          color={theme.color?.general.lighter}
          className='basis-1/2'
        >
          {t('wager')}:
        </Typography>
        <Typography
          type='body2'
          color={theme.color?.general.lightest}
          fontWeight={theme.font.weight.bold}
          className='basis-1/2'
        >
          x{activeBonusData?.wager_multiplier}
        </Typography>
      </div>

      <div className='buttonContainer mt-[28px] flex items-center justify-end gap-2 max-sm:flex-col'>
        <Button
          className='screenButton max-sm:order-2 max-sm:w-full'
          textTransform='uppercase'
          type='label2'
          text={t('playLater')}
          onClick={(e: any) => {
            e.preventDefault();
            closeModal('freespinsActivatedModal');
          }}
          variant='secondary'
        />
        <Link
          href={`/${lng}/casino/${activeBonusData?.game_provider_slug || ''}/game/${activeBonusData?.game_slug || ''}`}
          className='max-sm:w-full'
        >
          <Button
            className='screenButton max-sm:w-full'
            textTransform='uppercase'
            type='label2'
            text={t('play')}
            onClick={() => {
              closeModal('freespinsActivatedModal');
            }}
          />
        </Link>
      </div>
    </ModalDialog>
  );
};

export default FreespinsActivatedModal;
