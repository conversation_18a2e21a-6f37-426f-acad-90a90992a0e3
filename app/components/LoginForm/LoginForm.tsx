'use client';
import clsx from 'clsx';
import { useTranslation } from '@/app/i18n/client';
import ct from 'countries-and-timezones';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useCookies } from 'react-cookie';
import { parsePhoneNumber } from 'react-phone-number-input';
import { useEffect, useRef, useState, Dispatch, SetStateAction } from 'react';

import { Button, Input, Loader, Tabs, Typography, Image } from '@/atomic-design-components';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { toCamelCase } from '@/utils/toCamelCase';
import { LoginData, login } from '@/app/api/auth/login.ts';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import PhoneInput from '@/atomic-design-components/molecules/PhoneInput/PhoneInput.tsx';
import useClickOutside from '@/hooks/useClickOutside';
import { useIsPasswordShown } from '@/hooks/useIsPasswordShown.ts';
import { theme } from '@/theme';
import { closeModal } from '@/utils/closeModal';
import validate, { rule } from '@/utils/formValidationRules';
import { isObjectEmpty } from '@/utils/object';
import { openModal } from '@/utils/openModal';
import { ReCaptcha } from '@/app/components/ReCaptcha/ReCaptcha';
import { useReCaptcha } from '@/app/wrappers/ReCaptchaProvider';
import useCountries from '@/hooks/useCountries';
import { IBanner } from '@/app/api/getBannersServer.ts';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { changeUserSettings } from '@/app/api/server-actions/changeUserSettings.ts';
import { LNG_COOKIE_NAME } from '@/app/i18n/settings.ts';

interface LoginFormProps {
  banner?: IBanner;
  isInProgress: Dispatch<SetStateAction<boolean>>;
  // isDialogOpen: boolean;
}

const LoginForm = ({ banner, isInProgress }: LoginFormProps) => {
  const { isTouchDevice } = useIsTouchMobileView();
  const router = useRouter();
  const { lng } = useParams();
  const { t, i18n } = useTranslation(lng, 'validation');
  const [cookies] = useCookies([LNG_COOKIE_NAME]);

  const ref = useRef(null);
  useClickOutside(ref);
  const { showError } = useAlert();
  const { countries } = useCountries();
  const { checkRecaptchaV3, checkRecaptchaV2 } = useReCaptcha();

  const { setUser, nextPathAfterLogin, setNextPathAfterLogin } = useUser();

  const [errors, setErrors] = useState<any>({});
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState<string>('');
  const [country, setCountry] = useState<string>(
    ct.getCountryForTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)?.id || '',
  );
  const [dialCode, setDialCode] = useState<string>(
    countries.find((c: any) => c.code === country)?.dial_code || '',
  );
  const [phone, setPhone] = useState<string>('');
  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [inProgress, setInProgress] = useState(false);
  const [activeField, setActiveField] = useState(0);
  const [recaptchaRequired, setRecaptchaRequired] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  // useEffect(() => {
  //   if (!isDialogOpen) {
  //     setErrors({});
  //     setIsSubmitPressed(false);
  //     setInProgress(false);
  //     setRecaptchaRequired(false);
  //     setRecaptchaToken(null);
  //   }
  // }, [isDialogOpen]);

  const getTabTitle = (tab: any) => (
    <Typography
      text={t(tab.title)}
      justifyContent='center'
      type='sub3'
      iconName={tab.iconName}
      iconProps={{ wrapperWidth: 24, wrapperHeight: 24 }}
    />
  );

  const tabsTitles = [
    { id: 'login', title: t('byEmail'), iconName: 'letter' },
    { id: 'loginPhone', title: t('byPhone'), iconName: 'smartphone' },
  ];

  const validationRulesInitial = {
    email: activeField === 0 ? ['required', 'email'] : [],
    phone: activeField === 1 ? ['required', 'phone'] : [],
    password: ['required'],
  };

  const initialValuesChangedBeforeCheck = {
    email,
    phone: phone ? dialCode + phone : '',
    password,
  };

  const onChangeField = (name: string, value: string, setState: Function) => {
    const error = rule(name, validationRulesInitial)(value, initialValuesChangedBeforeCheck);

    let newErrors = { ...errors };
    const errorName = error[name] === 'email' ? 'emailFormat' : error[name];
    if (error[name]) {
      setErrors({ ...newErrors, [name]: errorName });
    } else {
      delete newErrors[name];
      setErrors({ ...newErrors });
    }

    setState(value);
  };

  const onFormSubmit = async () => {
    const errors = validate(validationRulesInitial)(initialValuesChangedBeforeCheck, {
      values: initialValuesChangedBeforeCheck,
    });
    setErrors(errors);
    if (isObjectEmpty(errors)) {
      setInProgress(true);

      // check if need to show reCaptcha checkbox
      try {
        const checkRecaptcha = await checkRecaptchaV3('login');

        if (!checkRecaptcha) {
          setRecaptchaRequired(true);
          openModal('reCaptchaModal', false, false, false);
          return;
        }
        completeFormSubmit().finally(() => {
          setInProgress(false);
        });
      } catch (error) {
        showError(t('recaptchaBlocked'));
        setInProgress(false);
      }
    }
  };

  const onRecaptchaSubmit = async () => {
    const checkRecaptcha = await checkRecaptchaV2(recaptchaToken!);

    closeModal('reCaptchaModal', false);
    setRecaptchaToken(null);
    setRecaptchaRequired(false);
    if (!checkRecaptcha) {
      showError(t('recaptchaFailed'));
      setInProgress(false);
      return;
    }
    completeFormSubmit().finally(() => {
      setInProgress(false);
    });
  };

  const completeFormSubmit = async () => {
    const requestData: LoginData = { password };

    if (email && activeField === 0) {
      requestData.email = email;
      delete requestData.phone;
    }
    if (phone && activeField === 1) {
      requestData.phone = parsePhoneNumber(dialCode + phone)?.number;
      delete requestData.email;
    }
    const result = await login(requestData);

    if (!result || result.error) {
      setInProgress(false);
      const field = activeField === 0 ? 'Email' : 'Phone';
      const error = toCamelCase(result.error) + field;
      const errorText = i18n.exists(error) ? t(error) : result.error;
      showError(errorText);
      return;
    }

    if ((window as any).Intercom) {
      (window as any).Intercom('shutdown');
      delete (window as any).Intercom;
      delete (window as any).IntercomSettings;
    }

    if (nextPathAfterLogin) {
      router.push(nextPathAfterLogin);
      setNextPathAfterLogin('');
    }
    setUser(result);
    setInProgress(false);
    closeModal('authModal');

    const lang = cookies?.[LNG_COOKIE_NAME];
    if (lang) {
      await changeUserSettings({ lng: lang });
    }
  };

  useEffect(() => {
    if (isSubmitPressed) {
      onFormSubmit();
    }
  }, [isSubmitPressed]);

  // pass inProgress state to parent component
  useEffect(() => {
    isInProgress(inProgress);
  }, [inProgress]);

  useEffect(() => {
    if (isObjectEmpty(errors)) {
      setIsSubmitPressed(false);
    }
  }, [errors]);

  useEffect(() => {
    if (recaptchaToken) {
      onRecaptchaSubmit();
    }
  }, [recaptchaToken]);

  const tabsContents = [
    <Input
      customT='validation'
      name='email'
      labelTop={t('common:email')}
      key='login'
      onChange={(e: any) => {
        const { value } = e.target;
        onChangeField('email', value, setEmail);
      }}
      value={email}
      error={isSubmitPressed ? errors.email : ''}
      disabled={inProgress}
    />,
    <PhoneInput
      name='phone'
      labelTop={t('common:phone')}
      key='phone'
      onChange={(data) => {
        setCountry(data.country);
        setDialCode(data.dialCode);
        setPhone(data.inputValue);
        onChangeField('phone', data.dialCode + data.inputValue, () => {});
      }}
      initialValue={phone}
      initialCode={dialCode}
      initialCountry={country}
      fullWidth
      error={isSubmitPressed ? errors.phone : ''}
      t={t}
      disabled={inProgress}
    />,
  ];

  const handleKeyDown = (e: any) => {
    if (e.key === 'Enter') {
      setIsSubmitPressed(true);
      onFormSubmit();
    }
  };

  return (
    <div
      className={`formWrapper flex flex-col items-center ${isTouchDevice ? 'mobileView' : ''}`}
      onKeyDown={handleKeyDown}
      role='presentation'
    >
      {banner && (
        <div
          className={clsx(
            'relative mx-2 mt-4 w-full transition-opacity duration-300',
            banner ? 'opacity-100' : 'opacity-0',
            isTouchDevice ? 'sm:hidden' : 'md:hidden',
          )}
        >
          <Image
            alt='Login Banner'
            src={banner?.photo_main_url || ''}
            width={688}
            height={144}
            sizes={`${theme.breakpoints?.md}px`}
            className='absolute h-full w-full rounded-lg object-cover'
          />
          <Typography
            className='relative min-h-[72px] max-w-[80%] px-3 py-2.5'
            text={getAvailableTranslation(banner?.title || '', lng)}
            type='h3'
          />
        </div>
      )}
      <form
        method='dialog'
        className={clsx('form z-0 mb-4 w-full overflow-y-auto', isTouchDevice ? 'mobileView' : '')}
      >
        <Tabs
          isDisabled={inProgress}
          setActiveField={setActiveField}
          tabsTitles={tabsTitles}
          getTabTitle={getTabTitle}
          onTabChange={() => setErrors({})}
          type='buttons'
          tabPadding='16px 0'
          tabsContents={tabsContents}
          withAddAction={false}
        />
        <Input
          name='password'
          labelTop={t('password')}
          {...useIsPasswordShown()}
          error={errors.password}
          disabled={inProgress}
          onChange={(e: any) => {
            const { value } = e.target;
            onChangeField('password', value, setPassword);
          }}
        />
        <Typography
          type='body1'
          color={theme.color?.primary.main}
          className={clsx('w-fit', inProgress ? 'pointer-events-none' : 'cursor-pointer')}
          text={t('forgotPassword')}
          onClick={(e: any) => {
            e.preventDefault();
            closeModal('authModal', false);
            document
              .getElementById('resetPasswordModal')
              ?.setAttribute('data-active-screen-verify', activeField === 0 ? 'email' : 'phone');
            document
              .getElementById('resetPasswordModal')
              ?.setAttribute(
                'data-reset-pass-initial-value',
                activeField === 0 ? email : dialCode + phone,
              );
            setTimeout(() => {
              openModal('resetPasswordModal', false);
            }, 0);
          }}
        />
        <ReCaptcha
          showReCaptcha={recaptchaRequired}
          onTokenChange={setRecaptchaToken}
          onClose={() => {
            setRecaptchaRequired(false);
            setInProgress(false);
          }}
        />
      </form>
      <Button
        className='submitButton mt-auto'
        text={t('login')}
        size='medium'
        onClick={(e: any) => {
          e.stopPropagation();
          setIsSubmitPressed(true);
        }}
        fullWidth
        disabled={inProgress}
      />
      <Loader
        active={inProgress && !recaptchaRequired}
        size='80px'
        variant='circular'
        withOverlay={true}
      />
    </div>
  );
};

export default LoginForm;
