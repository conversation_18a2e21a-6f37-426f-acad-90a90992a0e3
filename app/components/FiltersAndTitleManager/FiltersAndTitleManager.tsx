'use client';
import {
  useParams,
  usePathname,
  useRouter,
  useSearchParams,
} from 'next/navigation';
import { useCookies } from 'react-cookie';
import { useTranslation } from '@/app/i18n/client';
import { useDrawerById } from '@/app/wrappers/drawerProvider';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Icon, Switch, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global';
import { convertFiltersToObject } from '@/utils/convertFiltersToObject';
import { getGamePageType } from '@/utils/getGamePageType';
import setCookieIsBonusActive from '@/utils/setCookieIsBonusActive.ts';
import GenreButton from '../Games/GenreButton';
import { StyledFilterInput } from './styled';
import { isObjectEmpty } from '@/utils/object.ts';

const FiltersAndTitleManager = ({
  currentEntity,
  lng,
}: {
  currentEntity: any;
  lng: LanguagesType;
}) => {
  const { t } = useTranslation(lng);
  const [, setCookie] = useCookies(['isBonusActive']);

  const { gamePageType = 'casino' }: { gamePageType?: GamePageType } =
    useParams();
  const searchParams = useSearchParams();
  const params = searchParams.get('filters');
  const pathname = usePathname();
  const router = useRouter();

  const { isBonusFilterActive, setIsBonusFilterActive, user } = useUser();

  const { open } = useDrawerById('filtersPanel');

  const gamePageTypeChecked = getGamePageType(gamePageType);
  const isFilterToShow =
    pathname.includes('/casino/games/all') ||
    pathname.includes('/live-casino/games');

  const isBonusToggleShown =
    !!user?.active_wager &&
    pathname.includes('/casino/games') &&
    !['tournament', 'rtp', 'live'].some((pathPart) =>
      pathname.includes(pathPart),
    );

  const searchFilters = params ? convertFiltersToObject(params) : {};

  const onFilterClick = (e: any) => {
    e.stopPropagation();
    if (
      !pathname.includes('/games/') &&
      !pathname.includes('/favourites') &&
      !pathname.includes('/recent')
    ) {
      let url = `/${lng}/${gamePageTypeChecked}/games/all`;
      if (params) url = `${url}?filters=${params}`;
      router.push(url);
    }
    setTimeout(() => {
      open();
    }, 0);
  };

  return (
    <>
      <div className='mb-4 flex items-center gap-4'>
        <div className='flex items-center gap-4 max-md:hidden'>
          <Typography
            text={t(currentEntity?.label || currentEntity?.key || '')}
            type='h1'
            iconName={currentEntity?.iconNameOnPage || currentEntity?.iconName}
          />
          {!isObjectEmpty(searchFilters) && (
            <GenreButton searchFilters={searchFilters} lng={lng} />
          )}
        </div>
        <div className='flex justify-start gap-2 max-md:w-full sm:ml-auto sm:max-w-[450px] sm:justify-end'>
          {isBonusToggleShown && (
            <div className='flex whitespace-nowrap'>
              <Switch
                checked={isBonusFilterActive}
                id='isBonus'
                leftLabel={t('activeBonus')}
                labelProps={{
                  textTransform: 'none',
                  margin: '0 auto 0 0',
                  className: 'text-nowrap',
                }}
                name='isBonus'
                onChange={(state: any) => {
                  setIsBonusFilterActive(state);
                  setCookieIsBonusActive(state, setCookie);
                }}
              />
            </div>
          )}
          {isFilterToShow && (
            <div
              onClick={onFilterClick}
              role='button'
              tabIndex={0}
              className='relative z-0 block w-full overflow-hidden'
            >
              <StyledFilterInput className='flex w-full cursor-pointer items-center gap-2 sm:min-w-[180px]'>
                <Icon name='filters' width={24} height={24} />

                <div className='md:hidden'>
                  <GenreButton
                    searchFilters={searchFilters}
                    lng={lng}
                    isInputChildren={true}
                  />
                </div>

                <Typography
                  text={t('selectFilter')}
                  color={theme.color?.general.light}
                  className='!block overflow-hidden overflow-ellipsis text-nowrap pt-[2px]'
                />
              </StyledFilterInput>
            </div>
          )}
        </div>
      </div>
      {!isFilterToShow && !isBonusToggleShown && (
        <div className='mb-[-16px] md:hidden' />
      )}
    </>
  );
};

export default FiltersAndTitleManager;
