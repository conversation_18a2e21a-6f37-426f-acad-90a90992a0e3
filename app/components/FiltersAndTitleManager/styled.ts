'use client';
import styled from 'styled-components';

export const StyledFilterInput: any = styled.div`
  border: 1px solid ${({ theme }) => theme.color?.general.dark};
  color: ${({ theme }) => theme.color?.general.lightest};
  background-color: ${({ theme }) => theme.color?.general.darker};
  line-height: ${({ theme }) => theme.font.size.body2.lineHeight};
  font-size: ${({ theme }) => theme.font.size.body2.value};
  font-weight: ${({ theme }) => theme.font.weight.regular};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: 7px 12px;
  overflow: hidden;
`;
