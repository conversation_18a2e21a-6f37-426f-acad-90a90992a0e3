'use client';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { Button, Input, Typography } from '@/atomic-design-components';
import { useIsPasswordShown } from '@/hooks/useIsPasswordShown.ts';
import { closeModal } from '@/utils/closeModal';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';
import CustomPasswordChecklist from '@/app/components/Form/CustomPasswordChecklist.tsx';
import validate, { rule } from '@/utils/formValidationRules.ts';
import { isObjectEmpty } from '@/utils/object.ts';
import { updatePassword } from '@/app/api/server-actions/updatePassword.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { openModal } from '@/utils/openModal.ts';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { setUserDataAfterAuth } from '@/app/api/auth/setUserDataAfterAuth.ts';

const PasswordScreen = ({
  tokenDataAfterCodeVerified,
}: {
  tokenDataAfterCodeVerified: any;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { setActiveAuthTab } = useNavigation();

  const router = useRouter();
  // const searchParams = useSearchParams();

  const { setUser } = useUser();

  const [inProgress, setInProgress] = useState(false);
  const [password, setPassword] = useState('');
  const [isPasswordValid, setIsPasswordValid] = useState(undefined);
  const [isSubmitPressed, setIsSubmitPressed] = useState(false);
  const [errors, setErrors] = useState<any>({});

  const { showError, showAlert } = useAlert();

  const validationRulesInitial = {
    password: ['required', 'password'],
  };

  const initialValuesChangedBeforeCheck = {
    password,
  };
  const onReset = async () => {
    // if (!code) {
    //   showError('Code is required');
    //   return;
    // }
    setIsSubmitPressed(true);
    const errors = validate(validationRulesInitial)(
      initialValuesChangedBeforeCheck,
      {
        values: initialValuesChangedBeforeCheck,
      },
    );
    setErrors(errors);

    if (isObjectEmpty(errors)) {
      setInProgress(true);
      const requestData = {
        token: tokenDataAfterCodeVerified.access_token,
        new_password: password,
      };

      const result = await updatePassword(requestData);

      if (result.error) {
        setInProgress(false);
        showError(result.error);
        return;
      }

      showAlert({
        content: t('passwordUpdated'),
        id: Date.now().toString(),
        type: 'success',
        timeout: 5000,
      });

      const userData = await setUserDataAfterAuth(tokenDataAfterCodeVerified);

      if (!userData || userData.detail || userData.error) {
        setActiveAuthTab('login');
        closeModal('resetPasswordModal', false);
        setPassword('');
        setInProgress(false);
        router.push(`/${lng}`);
        setTimeout(() => {
          openModal('authModal', false);
        }, 200);
        return;
      }

      setUser(userData);
      closeModal('resetPasswordModal');
      setPassword('');
      setInProgress(false);
      router.push(`/${lng}`);
    }
  };

  const onChangeField = (name: string, value: string, setState: Function) => {
    const error = rule(name, validationRulesInitial)(
      value,
      initialValuesChangedBeforeCheck,
    );
    let newErrors = { ...errors };
    if (error[name]) {
      setErrors({ ...newErrors, [name]: error[name] });
    } else {
      delete newErrors[name];
      setErrors({ ...newErrors });
    }

    setState(value);
  };

  return (
    <div className='resetPasswordContainer flex flex-col max-sm:h-full max-sm:px-2 max-sm:pb-3 max-sm:pt-3 sm:p-6'>
      <div className='flex h-full flex-col'>
        <Typography
          type='h1'
          text={t('createNewPass')}
          className='max-sm:!mb-5 sm:!mb-6'
        />
        <div>
          <Input
            name='password'
            labelTop={t('password')}
            {...useIsPasswordShown()}
            onChange={(e: any) => {
              const { value } = e.target;
              onChangeField('password', value, setPassword);
            }}
            error={isSubmitPressed && !!errors.password}
            disabled={inProgress}
            // labelBottom={t('lettersAndDigits')}
          />
          <CustomPasswordChecklist
            password={password}
            setIsPasswordValid={setIsPasswordValid}
            className={isSubmitPressed && !isPasswordValid ? 'passError' : ''}
          />
        </div>
        <div className='buttonContainer flex justify-end gap-2 max-sm:mt-auto max-sm:flex-col sm:mt-6 sm:gap-4'>
          <Button
            className='max-sm:w-full sm:order-2 sm:min-w-[120px]'
            text={t('confirm')}
            onClick={(e: any) => {
              e.preventDefault();
              onReset();
            }}
            disabled={!password || inProgress}
          />
          <Button
            variant='secondary'
            text={t('cancel')}
            className='max-sm:w-full sm:order-1 sm:min-w-[120px]'
            onClick={(e: any) => {
              e.preventDefault();
              closeModal('resetPasswordModal');
            }}
            disabled={inProgress}
          />
        </div>
      </div>
    </div>
  );
};

export default PasswordScreen;
