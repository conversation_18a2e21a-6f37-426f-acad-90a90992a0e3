'use client';
import {
  RequestResetData,
  requestResetPassword,
  setRequestResetPassNumber,
} from '@/app/api/server-actions/forgotPassword';
import ModalDialog from '@/app/components/ModalDialog';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { Button, Input, Typography } from '@/atomic-design-components';
import PhoneInput from '@/atomic-design-components/molecules/PhoneInput/PhoneInput';
import useClickOutside from '@/hooks/useClickOutside';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { closeModal } from '@/utils/closeModal';
import { openModal } from '@/utils/openModal';
import ct from 'countries-and-timezones';
import { useParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import PasswordScreen from './components/PasswordScreen';
import { ReCaptcha } from '@/app/components/ReCaptcha/ReCaptcha';
import { useReCaptcha } from '@/app/wrappers/ReCaptchaProvider';
import clsx from 'clsx';
import useCountries from '@/hooks/useCountries';
import { parsePhoneNumber } from 'react-phone-number-input';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';
import {
  getFromLocalStorage,
  removeFromLocalStorage,
  saveToLocalStorage,
} from '@/utils/localStorage.ts';
import { FIRST_PHONE_CODE_TIMER_SEC } from '@/constants.ts';
import { getCookie } from '@/app/api/server-actions/getCookies.ts';

const ResetPasswordModal = ({
  tokenDataAfterCodeVerified,
}: {
  tokenDataAfterCodeVerified: any;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const ref = useRef<HTMLDialogElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  useClickOutside(ref);

  const { register } = useRegisterModalToOpenByUrl();
  useEffect(() => {
    if (ref.current) {
      register('resetPasswordModal');
    }
  }, [ref?.current]);

  const { setActiveAuthTab } = useNavigation();

  const { countries } = useCountries();
  const { checkRecaptchaV3, checkRecaptchaV2 } = useReCaptcha();

  const { isTouchDevice } = useIsTouchMobileView();

  const { showError } = useAlert();

  // const [codeDefault, setCodeDefault] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [country, setCountry] = useState<string>(
    ct.getCountryForTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone)?.id || '',
  );
  const [dialCode, setDialCode] = useState<string>(
    countries.find((c: any) => c.code === country)?.dial_code || '',
  );
  const [phone, setPhone] = useState<string>('');
  const [screen, setScreen] = useState<'email' | 'phone' | 'password' | ''>('');
  const [inProgress, setInProgress] = useState(false);
  const [recaptchaRequired, setRecaptchaRequired] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  const { isDialogOpen } = useIsDialogOpen(ref);

  useEffect(() => {
    if (isDialogOpen) {
      const screenAttr = ref?.current?.getAttribute('data-active-screen-verify');
      const initialValAttr = ref?.current?.getAttribute('data-reset-pass-initial-value');
      if (screenAttr === 'email' && initialValAttr) {
        setEmail(initialValAttr);
      }

      if (screenAttr) setScreen(screenAttr as 'email' | 'phone' | 'password');
      // const code = ref?.current?.getAttribute('verify-code');
      // if (code) setCodeDefault(code);
    } else {
      ref?.current?.setAttribute('data-active-screen-verify', 'email');
    }
  }, [isDialogOpen]);

  useEffect(() => {
    recaptchaToken && onRecaptchaSubmit();
  }, [recaptchaToken]);

  const onReset = async () => {
    setInProgress(true);
    // check if need to show reCaptcha checkbox
    const checkRecaptcha = await checkRecaptchaV3('resetPassword');
    if (!checkRecaptcha) {
      setRecaptchaRequired(true);
      return;
    }
    await completeFormSubmit();
  };

  const onRecaptchaSubmit = async () => {
    const checkRecaptcha = await checkRecaptchaV2(recaptchaToken!);
    if (!checkRecaptcha) {
      showError('reCAPTCHA validation failed, please try again.');
      setRecaptchaToken(null);
      setRecaptchaRequired(false);
      setInProgress(false);
      return;
    }
    await completeFormSubmit();
  };

  const completeRequestResetByEmail = async () => {
    const requestData: RequestResetData = { email };
    const result = await requestResetPassword(requestData);
    if (!result || result.error) {
      setInProgress(false);
      showError(result.error || t('error'));
      return;
    }
    if (result.result === 'ok') {
      closeModal('resetPasswordModal', false);
      setTimeout(() => {
        openModal('emailVerificationModal', false);
        document
          .getElementById('emailVerificationModal')
          ?.setAttribute('data-active-screen-verify', 'confirm');
      }, 0);
    }
    setInProgress(false);
  };

  const openPhoneVerificationModal = () => {
    closeModal('resetPasswordModal', false);
    setTimeout(() => {
      openModal('phoneVerificationModal', false);
      document
        .getElementById('phoneVerificationModal')
        ?.setAttribute('data-active-screen-verify', 'reset_password');
      document
        .getElementById('phoneVerificationModal')
        ?.setAttribute(
          'verify-data',
          parsePhoneNumber(dialCode + phone)?.number || dialCode + phone,
        );
    }, 0);
    setInProgress(false);
  };

  const completeFormSubmit = async () => {
    if (screen === 'email') {
      await completeRequestResetByEmail();
      return;
    }

    const requestResetPassNumber = await getCookie('requestResetPassNumber');

    if (
      requestResetPassNumber !== (parsePhoneNumber(dialCode + phone)?.number || dialCode + phone)
    ) {
      removeFromLocalStorage('resetPasswordTimerExpires');
      removeFromLocalStorage('resetPasswordCodeSentCount');
    }

    await setRequestResetPassNumber(parsePhoneNumber(dialCode + phone)?.number || dialCode + phone);

    const resendCodeTimerExpiresFromLS = getFromLocalStorage('resetPasswordTimerExpires');
    const codeSentCount = getFromLocalStorage('resetPasswordCodeSentCount');

    if (!resendCodeTimerExpiresFromLS && !codeSentCount) {
      const phoneNum = parsePhoneNumber(dialCode + phone)?.number || dialCode + phone;
      const requestData: RequestResetData = {
        phone: phoneNum,
        // no phone type for now - code will be sent in Telegram and then by phone call
      };
      const result = await requestResetPassword(requestData);
      // const result = { result: 'ok' };
      if (!result || result.error) {
        setInProgress(false);
        showError(t('failedToSendCode'));
        return;
      }

      if (result.result === 'ok') {
        saveToLocalStorage(
          'resetPasswordTimerExpires',
          Date.now() + FIRST_PHONE_CODE_TIMER_SEC * 1000,
        );
        saveToLocalStorage('resetPasswordCodeSentCount', 1);
        openPhoneVerificationModal();
      }
    } else {
      openPhoneVerificationModal();
    }
  };

  return (
    <ModalDialog
      id='resetPasswordModal'
      ref={ref}
      className={clsx(screen, { mobileView: isTouchDevice })}
    >
      {screen === 'email' ? (
        <div className='resetPasswordContainer flex flex-col max-sm:h-full max-sm:px-2 max-sm:pb-3 max-sm:pt-3 sm:p-6'>
          <div className='flex h-full flex-col'>
            <Typography type='h1' text={t('resetPassword')} className='max-sm:!mb-5 sm:!mb-6' />
            <Input
              ref={inputRef}
              name='email'
              key='email'
              autoFocus={!isTouchDevice}
              fullWidth
              labelTop={t('email')}
              value={email}
              onChange={(e: any) => {
                const { value } = e.target;
                setEmail(value);
              }}
              disabled={inProgress}
            />
            <ReCaptcha
              onTokenChange={setRecaptchaToken}
              showReCaptcha={recaptchaRequired}
              className='mt-4'
              useModal={false}
            />
            <div className='buttonContainer flex justify-end gap-2 max-sm:mt-auto max-sm:flex-col sm:mt-6 sm:gap-4'>
              <Button
                className='max-sm:w-full sm:order-2 sm:min-w-[120px]'
                text={t('reset')}
                onClick={(e: any) => {
                  e.preventDefault();
                  onReset();
                }}
                disabled={!email || inProgress}
              />
              <Button
                variant='secondary'
                text={t('cancel')}
                className='max-sm:w-full sm:order-1 sm:min-w-[120px]'
                onClick={(e: any) => {
                  e.preventDefault();
                  closeModal('resetPasswordModal', false);
                  setScreen('email');
                  setTimeout(() => {
                    setActiveAuthTab('login');
                    openModal('authModal', false);
                  }, 0);
                }}
                disabled={inProgress}
              />
            </div>
          </div>
        </div>
      ) : screen === 'phone' ? (
        <div className='resetPasswordContainer flex flex-col max-sm:h-full max-sm:px-2 max-sm:pb-3 max-sm:pt-3 sm:p-6'>
          <div className='flex h-full flex-col justify-between'>
            <Typography type='h1' text={t('resetPassword')} className='max-sm:!mb-5 sm:!mb-6' />
            <PhoneInput
              autoFocus={!isTouchDevice}
              name='phone'
              labelTop={t('phone')}
              key='phone'
              onChange={(data) => {
                setCountry(data.country);
                setDialCode(data.dialCode);
                setPhone(data.inputValue);
              }}
              fullWidth
              initialCountry={country}
              initialCode={dialCode}
              initialValue={phone}
              t={t}
              disabled={inProgress}
            />
            <ReCaptcha
              onTokenChange={setRecaptchaToken}
              showReCaptcha={recaptchaRequired}
              className='mt-4'
              useModal={false}
            />
            <div className='flex justify-end gap-2 max-sm:mt-auto max-sm:flex-col sm:mt-6 sm:gap-4'>
              <Button
                className='max-sm:w-full sm:order-2 sm:min-w-[120px]'
                text={t('reset')}
                onClick={(e: any) => {
                  e.preventDefault();
                  onReset();
                }}
                disabled={!phone || inProgress}
              />
              <Button
                variant='secondary'
                text={t('cancel')}
                className='max-sm:w-full sm:order-1 sm:min-w-[120px]'
                onClick={(e: any) => {
                  e.preventDefault();
                  closeModal('resetPasswordModal', false);
                  setScreen('email');
                  setTimeout(() => {
                    setActiveAuthTab('login');
                    openModal('authModal', false);
                  }, 0);
                }}
                disabled={inProgress}
              />
            </div>
          </div>
        </div>
      ) : (
        <PasswordScreen tokenDataAfterCodeVerified={tokenDataAfterCodeVerified} />
      )}
    </ModalDialog>
  );
};

ResetPasswordModal.displayName = 'ResetPasswordModal';
export default ResetPasswordModal;
