'use client';
import styled from 'styled-components';

export const StyledPageMenu: any = styled.nav`
  display: flex;
  gap: 12px;
  flex-shrink: 0;
  margin-bottom: 16px;
  min-height: 42px;

  & > .flex {
    justify-content: space-between;
  }

  .detailsWrapper .menuItem {
    margin-bottom: 10px;
  }

  .active .panelHeader {
    background-color: ${({ theme }) => theme.color?.general.gray3};
    border-radius: 6px;

    .typography {
      color: ${({ theme }) => theme.color?.general.white};
    }
  }
  button.filtersButton {
    border-radius: ${({ theme }) => theme.size.border.radius.main};
  }
  .keen-slider {
    height: 42px;
  }
  .keen-slider__slide:not(.loaded) {
    width: 100%;
    .menuItem:not(.loaded) {
      .itemContent {
        opacity: 0;
      }
    }
  }

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    margin-bottom: 8px;
  }
`;
