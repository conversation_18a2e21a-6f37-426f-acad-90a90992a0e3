'use client';
import { ENTITIES } from '@/app/config/navMenuEntities.ts';
import { useUser } from '@/app/wrappers/userProvider';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { GamePageType } from '@/types/global';
import { useParams } from 'next/navigation';
import { useEffect, useRef } from 'react';
import MenuItemSlider from '../NavMenu/MenuItemSlider';
import Slider from '../Slider/Slider';
import { StyledPageMenu } from './styled';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';

const PageMenu = () => {
  const { gamePageType = 'casino' }: { gamePageType?: GamePageType } =
    useParams();

  const ref = useRef<HTMLElement>(null);

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isMobile = !!width && (width < theme.breakpoints?.md || isTouchDevice);

  const pageEntities = ENTITIES.all[gamePageType] || ENTITIES.all.casino;

  const handleScroll = () => {
    const header = document.querySelector('header');
    const headerBottom = header?.getBoundingClientRect().bottom;
    const bannerBottom = document
      .getElementById('landing-banner')
      ?.getBoundingClientRect().bottom;

    if (headerBottom && bannerBottom) {
      const isFixed = bannerBottom <= headerBottom;
      ref?.current?.classList.toggle('pageMenuFixed', isFixed);
      if (isFixed) header?.classList.remove('scrolled');
    }
  };

  useEffect(() => {
    if (!isMobile) {
      if (ref?.current?.classList.contains('pageMenuFixed')) {
        const header = document.querySelector('header');
        ref?.current?.classList.remove('pageMenuFixed');
        header?.classList.remove('scrolled');
      }
      return;
    }
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isMobile]);

  const pages = pageEntities.map((el) => {
    return {
      ...el,
      className: 'horizontal',
    };
  });

  const items = [
    {
      ...ENTITIES.all.common[0],
      className: 'horizontal',
    },

    {
      ...ENTITIES.all.common[1],
      className: 'horizontal',
    },
    ...pages,
  ];
  const itemsAuthorized = [
    {
      ...ENTITIES.all.common[0],
      className: 'horizontal',
    },
    {
      ...ENTITIES.authorized[0],
      className: 'horizontal',
      isUserAuthorized,
      withAuthorization: true,
    },
    {
      ...ENTITIES.all.common[1],
      className: 'horizontal',
    },
    ...pages,
    {
      ...ENTITIES.authorized[1],
      className: 'horizontal',
      isUserAuthorized,
      withAuthorization: true,
    },
  ];

  return (
    <StyledPageMenu className='pageMenu w-full bg-[#0F172A]' ref={ref}>
      {isUserAuthorized ? (
        <Slider
          items={itemsAuthorized}
          total={itemsAuthorized.length}
          Slide={MenuItemSlider}
          withAutosizeItems
          isSliding={true}
          spacing={8}
        />
      ) : (
        <Slider
          items={items}
          total={items.length}
          Slide={MenuItemSlider}
          spacing={8}
          withAutosizeItems
          isSliding={true}
        />
      )}
    </StyledPageMenu>
  );
};

export default PageMenu;
