import { useTranslation } from '@/app/i18n/client';
import { Icon, Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import clsx from 'clsx';

const EmptyTable = ({
  lng,
  className,
  iconName,
  header,
  text,
}: {
  lng: LanguagesType;
  className?: string;
  iconName: string;
  header: string;
  text?: string;
}) => {
  const { t } = useTranslation(lng);

  return (
    <div
      className={clsx(
        className,
        'flex flex-col items-center justify-center gap-2',
      )}
    >
      <Icon name={iconName} wrapperWidth={50} wrapperHeight={50} />
      <Typography text={t(header) || header} type='sub2' />
      {text && <Typography text={t(text) || text} />}
    </div>
  );
};

export default EmptyTable;
