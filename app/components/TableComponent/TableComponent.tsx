import { useTranslation } from '@/app/i18n/client';
import { Icon } from '@/atomic-design-components';
import { useLoadMoreObserver } from '@/hooks/useLoadMoreObserver.tsx';
import { usePrevious } from '@/hooks/useReact';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import {
  Pagination,
  PaginationItemType,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  getKeyValue,
} from '@nextui-org/react';
import clsx from 'clsx';
import React, { useEffect, useState } from 'react';
import PanelWithIconAndText from '../PanelWithIconAndText/PanelWithIconAndText';
import { StyledTable } from './styled';
import { useGlobalNotificationState } from '@/app/wrappers/globalNotificationProvider';

interface IProps {
  columns: Array<any>;
  initialData: Array<any>;
  lng: LanguagesType;
  emptyText?: string;
  emptyIconName?: string;
  emptyIconProps?: any;
  initSort?: { column: string; direction: string };
  isHeaderSticky?: boolean;
  withInfiniteScroll?: boolean;
  className?: string;
  getRowClass?: any;
  countInNavigation?: boolean;
  withArrows?: boolean;
  maxLimit?: number | null;
  totalItems: number;
  limit: number;
  withPagination?: boolean;
  withClientSidePagination?: boolean;
  extraItem?: any;
  loadMore?: any;
  // pageInitial?: number;
}

const TableComponent = ({
  columns,
  initialData,
  emptyIconName,
  emptyText,
  emptyIconProps,
  lng,
  isHeaderSticky,
  withInfiniteScroll,
  className,
  getRowClass,
  countInNavigation,
  withArrows = true,
  maxLimit,
  totalItems,
  limit,
  withPagination = false,
  withClientSidePagination,
  extraItem,
  loadMore,
  // pageInitial = 1,
  // initSort,
}: IProps) => {
  const { t } = useTranslation(lng);
  const { isGlobalNotificationVisible } = useGlobalNotificationState();
  const loadMoreRef = React.useRef<HTMLDivElement>(null);
  // const [sortDescriptor, setSortDescriptor] = useState(
  //   initSort || { column: '', direction: '' },
  // );
  const [page, setPage] = useState(1);
  const rowsPerPage = 10;
  const prevPage: number = usePrevious(page) || 1;

  const pages = Math.ceil(totalItems / rowsPerPage);

  const [tableData, setTableData] = useState(initialData);
  const [skip, setSkip] = useState(limit);
  const [inProgress, setInProgress] = useState(false);

  const maxItemsCount = maxLimit || totalItems;

  useEffect(() => {
    if (withClientSidePagination) {
      setTableData(initialData.slice(0, rowsPerPage));
    } else {
      setTableData(initialData);
      setSkip(limit);
    }
  }, [initialData]);

  const loadMoreWithInfiniteScroll = async () => {
    if (
      !withInfiniteScroll ||
      inProgress ||
      maxItemsCount <= tableData?.length
    ) {
      return;
    }

    if (loadMore) {
      // setInProgress(true);
      const newItems = await loadMore(skip);
      setSkip((prev) => prev + newItems.length);
      setTableData((prev) => [...prev, ...newItems]);
      // setInProgress(false);
    }
  };

  useLoadMoreObserver({
    loadNextPage: withInfiniteScroll ? loadMoreWithInfiniteScroll : undefined,
    loadMoreRef,
    withLoadMore: !!withInfiniteScroll,
  });

  const loadNextPage = async () => {
    if (!withPagination || inProgress) {
      return;
    }

    if (loadMore) {
      setInProgress(true);
      const newItems = await loadMore(skip);
      setTableData(newItems);
      setInProgress(false);
    }
  };

  useEffect(() => {
    if (page !== prevPage) {
      if (!withClientSidePagination) {
        loadNextPage();
      } else {
        const start = (page - 1) * rowsPerPage;
        const end = start + rowsPerPage;

        setTableData(initialData.slice(start, end));
      }
    }
  }, [page]);

  const renderItem = ({
    ref,
    key,
    value,
    isActive,
    onNext,
    onPrevious,
    setPage,
    className,
  }: {
    ref: any;
    key: any;
    value: any;
    isActive: any;
    onNext: any;
    onPrevious: any;
    setPage: any;
    className: any;
  }) => {
    if (value === PaginationItemType.NEXT) {
      if (!withArrows) return null;
      return (
        <button
          key={key}
          className={clsx(className, 'arrowButton')}
          onClick={onNext}
          disabled={page === pages}
        >
          <Icon
            name='chevronDown'
            className='-rotate-90'
            fill={
              page === pages
                ? theme.color?.general.lighter
                : theme.color?.general.light
            }
          />
        </button>
      );
    }

    if (value === PaginationItemType.PREV) {
      if (!withArrows) return null;
      return (
        <button
          key={key}
          className={clsx(className, 'arrowButton')}
          onClick={onPrevious}
          disabled={page === 1}
        >
          <Icon
            name='chevronDown'
            className='rotate-90'
            fill={
              page === 1
                ? theme.color?.general.lighter
                : theme.color?.general.light
            }
          />
        </button>
      );
    }

    if (value === PaginationItemType.DOTS) {
      return (
        <button key={key} className={className}>
          ...
        </button>
      );
    }

    return (
      <button
        key={key}
        ref={ref}
        className={clsx(className, isActive && 'active')}
        onClick={() => {
          setPage(value);
          setSkip(limit * value - limit);
        }}
      >
        {countInNavigation
          ? `${value * rowsPerPage - (rowsPerPage - 1)}-${value * rowsPerPage}`
          : value}
      </button>
    );
  };

  return (
    <StyledTable
      isGlobalNotificationVisible={isGlobalNotificationVisible}
      className={clsx(
        withInfiniteScroll && 'infinite',
        className,
        countInNavigation && 'withCount',
      )}
    >
      {!totalItems ? (
        <PanelWithIconAndText
          icon={emptyIconName || 'disappointedFace'}
          title={t(emptyText || 'nothingFound')}
          className='ml-8 mr-8'
          iconProps={emptyIconProps}
        />
      ) : (
        <Table
          isHeaderSticky={isHeaderSticky}
          aria-label='table'
          // sortDescriptor={sortDescriptor}
          // onSortChange={setSortDescriptor}
          bottomContent={
            withInfiniteScroll ? (
              <div ref={loadMoreRef} className='loadMoreRef' />
            ) : (
              pages > 1 && (
                <div className='flex w-full justify-center'>
                  <Pagination
                    isCompact
                    showControls
                    showShadow
                    page={page}
                    total={pages}
                    onChange={(page: number) => setPage(page)}
                    renderItem={renderItem}
                  />
                </div>
              )
            )
          }
        >
          <TableHeader>
            {columns.map((column) => {
              const value: any = Object.values(column)[0];

              return (
                <TableColumn key={value.dataKey} allowsSorting={value.sortable}>
                  {value.getTitle ? value.getTitle() : t(value.key)}
                </TableColumn>
              );
            })}
          </TableHeader>
          <TableBody items={tableData}>
            {tableData?.map((item: any, idx: number) => {
              const rowClass = getRowClass ? getRowClass(item) : '';

              return (
                <TableRow key={idx} className={rowClass}>
                  {(columnKey: any) => {
                    const column = columns.find((col) => {
                      const column: any = Object.values(col)[0];
                      return column.dataKey === columnKey;
                    });

                    return (
                      <TableCell>
                        {column && column[columnKey]?.cellRenderer
                          ? column[columnKey].cellRenderer(item)
                          : getKeyValue(item, columnKey)}
                      </TableCell>
                    );
                  }}
                </TableRow>
              );
            })}
            {extraItem && (
              <TableRow key={tableData?.length} className='userRow'>
                {(columnKey: any) => {
                  const column = columns.find((col) => {
                    const column: any = Object.values(col)[0];
                    return column.dataKey === columnKey;
                  });

                  return (
                    <TableCell>
                      {column && column[columnKey].cellRenderer
                        ? column[columnKey].cellRenderer(extraItem)
                        : getKeyValue(extraItem, columnKey)}
                    </TableCell>
                  );
                }}
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}
    </StyledTable>
  );
};

export default TableComponent;
