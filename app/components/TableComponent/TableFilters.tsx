import dayjs from 'dayjs';
import { useTranslation } from '@/app/i18n/client';
import {
  Button,
  DatePicker,
  FlexRow,
  Typography,
} from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { getEndDateTime, getStartDateTime } from '@/utils/dates';
import { StyledFilters } from './styled';
import { theme } from '@/theme';

interface IProps {
  lng: LanguagesType;
  setStartDate: Function;
  setEndDate: Function;
  setRange: Function;
}

const TableFilters = ({ lng, setStartDate, setEndDate, setRange }: IProps) => {
  const { t } = useTranslation(lng);

  return (
    <StyledFilters margin='0 0 24px' gap='8px'>
      <FlexRow gap='15px'>
        <Typography
          text={`${t('periodOfTime')}: `}
          lineHeight='10px'
          fontWeight={theme.font.weight.medium}
        />
        <DatePicker
          onChange={(date: any) =>
            setRange((prev: any) => {
              return {
                ...prev,
                start: getStartDateTime(dayjs(date)),
              };
            })
          }
        />
        <DatePicker
          onChange={(date: any) =>
            setRange((prev: any) => {
              return { ...prev, end: getEndDateTime(dayjs(date)) };
            })
          }
        />
      </FlexRow>
      <Button
        text={`24 ${'hours'}`}
        className='filterButton'
        onClick={() => {
          setStartDate(getStartDateTime(dayjs().day(6)));
          setEndDate(getEndDateTime(dayjs().day(6)));
        }}
      />
      <Button
        text={`7 ${t('days')}`}
        className='filterButton'
        onClick={() => {
          setStartDate(getStartDateTime(dayjs().day(0)));
          setEndDate(getEndDateTime(dayjs().day(6)));
        }}
      />
      <Button
        text={`14 ${t('days')}`}
        className='filterButton'
        onClick={() => {
          setStartDate(getStartDateTime(dayjs().day(-7)));
          setEndDate(getEndDateTime(dayjs().day(6)));
        }}
      />
      <Button
        text={t('month')}
        className='filterButton'
        onClick={() => {
          setStartDate(getStartDateTime(dayjs().day(6)));
          setEndDate(getEndDateTime(dayjs().day(6)));
        }}
      />
      <Button
        text={`6 ${t('months')}`}
        className='filterButton'
        onClick={() => {
          setStartDate(getStartDateTime(dayjs().day(6)));
          setEndDate(getEndDateTime(dayjs().day(6)));
        }}
      />
    </StyledFilters>
  );
};

export default TableFilters;
