import { FlexRow, getTokens } from '@/atomic-design-components';
import styled from 'styled-components';

export const StyledFilters: any = styled(FlexRow)`
  .filterButton {
    background-color: transparent;
    border-color: transparent;
    color: #94a3b8;
    line-height: 9px;
    font-size: 13px;
    font-weight: ${({ theme }) => theme.font.weight.medium};
  }
`;
export const StyledTable: any = styled.div<{
  isGlobalNotificationVisible: boolean;
}>`
  //max-width: 90%;
  margin: 0 auto;

  &.infinite {
    padding: 0 0 8px;
    > div > div {
      max-height: 485px;
      overflow-y: auto;
      thead {
        z-index: 1;
        tr {
          border-radius: 4px;
          &:first-child {
            padding: 4px 0;
          }
          th:not(:first-child) {
            padding-left: 0px;
          }
        }
      }
    }
    table {
      tbody {
        tr[tabindex] {
          border-color: ${({ theme }) => theme.color?.general.dark};
        }
      }
    }
  }
  .overflow-auto {
    gap: 20px;
  }
  table {
    min-height: 200px;
    color: black;
    thead {
      tr {
        background-color: ${({ theme }) => theme.color?.general.darker};
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        margin: 0 !important;

        th {
          ${({ theme }) => getTokens('typography-label1-black-large', theme)};
          flex-grow: 1;
          width: 20%;
          text-align: left;
          height: 24px;
          display: flex;
          align-items: center;

          &:first-child {
            padding-left: 8px;
          }
          &:last-child {
            padding-right: 8px;
          }
          &[aria-sort='ascending'] {
            svg[data-direction='ascending'] {
              transform: rotate(180deg);
            }
          }
        }
      }
    }
    tbody {
      tr[tabindex] {
        border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
        align-items: center;
        padding: 12px 0;
      }
    }
    tr {
      display: flex;
    }
    td {
      display: block;
      flex-grow: 1;
      width: 20%;
      padding: 8px 0;
      &:first-child {
        padding-left: 8px;
      }
      &:last-child {
        padding-right: 8px;
      }
    }
  }
  nav {
    ul {
      gap: 8px;
      > span {
        display: none;
      }
      button {
        ${({ theme }) => getTokens('typography-body1-black-large', theme)};
        @media (max-width: 620px) {
          ${({ theme }) => getTokens('typography-label2-black-large', theme)};
          color: ${({ theme }) => theme.color?.general.lightest};
        }
        border-radius: 48px;

        &.active {
          background-color: ${({ theme }) => theme.color?.general.dark};
        }
        &:not(.active):hover {
          background-color: ${({ theme }) => theme.color?.general.light};
          @media (max-width: 620px) {
            background-color: initial;
          }
        }
        &:not(.arrowButton) {
          padding: 10px 12px;
          @media (max-width: 620px) {
            padding: 8px 12px;
          }
        }
      }
    }
  }
  &.paymentHistoryTable {
    padding: 0;
    td,
    th {
      &:first-child {
        width: 100px;
        min-width: 100px;
        flex-grow: 0;
      }
      &:nth-child(2) {
        width: 23%;
      }
      &:nth-child(3) {
        width: 17%;
      }
      &:last-child {
        width: 130px;
      }
    }
    tr {
      &:last-child {
        border: none;
      }
    }
    td:last-child {
      padding: 0;
    }
    &.infinite > div > div {
      padding: 0;
    }
  }
  &.accountTable {
    &.infinite {
      > div > div {
        max-height: ${({ isGlobalNotificationVisible }) =>
          isGlobalNotificationVisible
            ? 'calc(100dvh - 302px)'
            : 'calc(100dvh - 228px)'};
      }
    }
    td,
    th {
      &:first-child {
        width: 25%;
      }
      &:nth-child(2) {
        width: 23%;
      }
      &:nth-child(3) {
        width: 10%;
      }
      &:nth-child(4) {
        width: 10%;
      }
      &:last-child {
        width: 150px;
      }
    }
    tbody tr[tabindex] {
      padding: 8px 0;
    }
  }

  &.scoreboardTable {
    border-collapse: separate;
    border-spacing: 0;
    min-width: 320px;

    thead {
      tr {
        background-color: transparent;
      }
      th {
        padding: 0;
        width: 27%;
        &:second-child {
          padding-right: 8px;
        }
        &:not(:first-child) {
          justify-content: end;
        }

        &:last-child {
          width: 45%;
        }
        @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
          &:last-child {
            width: 45%;
          }
        }
      }
    }
    tbody {
      tr,
      tr[tabindex] {
        background-color: transparent;
        border-radius: 0 !important;
        border-bottom: 0;
        padding: 0;
        td {
          width: 27%;
          border-top: 1px solid transparent;
          border-bottom: 1px solid transparent;
          border-bottom-color: rgba(255, 255, 255, 0.1);
          &:first-child {
            border-left: 1px solid transparent;
          }
          &:last-child {
            border-right: 1px solid transparent;
            width: 45%;
          }
          @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
            &:last-child {
              width: 45%;
            }
          }
        }
        &.userRow {
          td {
            border-top-color: ${({ theme }) => theme.color?.primary.main};
            border-bottom-color: ${({ theme }) => theme.color?.primary.main};
            background-color: #253a5e;
            &:first-child {
              border-left-color: ${({ theme }) => theme.color?.primary.main};
            }
            &:last-child {
              border-right-color: ${({ theme }) => theme.color?.primary.main};
            }
          }
        }
        &.finished:last-child {
          td {
            border-bottom: 0;
          }
        }
      }
    }
    @media (max-width: 960px) {
      & > div > div {
        padding: 0;
      }
    }
  }
`;
