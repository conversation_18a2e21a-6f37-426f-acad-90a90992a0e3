import { getGamesTopWins, IGame } from '@/app/api/getGames';
import { getServerTranslation } from '@/app/i18n';
import { DEFAULT_CURRENCY } from '@/constants';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { getGamePageType } from '@/utils/getGamePageType';
import 'keen-slider/keen-slider.min.css';
import { cookies } from 'next/headers';
import Slider from '../Slider/Slider';
import TopWinningsCard from './TopWinningsCard';
import { TOP_WINS_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/topWinsBreakpoints.ts';

const LIMIT = 36;

const TopWinningsCarousel = async ({
  lng,
  gamePageType,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
}) => {
  const { t } = await getServerTranslation(lng);
  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;
  const gamePageTypeChecked = getGamePageType(gamePageType);

  const { items, total } = await getGamesTopWins(0, LIMIT, {
    is_live: [(gamePageType === 'live-casino').toString()],
    rate_currency: [userCurrency],
    days: ['30'],
  });
  const currGames = userCurrency
    ? items?.map((game: IGame) => {
        const amount =
          game.win_amount && game.rate
            ? Math.floor(game.win_amount * game.rate)
            : game.win_amount;
        return { ...game, win_amount: amount };
      })
    : items;

  if (!total) return null;

  return (
    <div className='winningsCarousel flex flex-col gap-y-2 md:gap-y-4'>
      <Typography
        type='h1'
        text={t('topWins')}
        iconName='coins'
        iconProps={{
          width: 18,
          height: 18,
        }}
      />
      <Slider
        items={currGames}
        total={total}
        Slide={TopWinningsCard}
        slideProps={{ userCurrency }}
        isCarousel
        perView={4}
        buttonText={t('all')}
        buttonHref={`/${lng}/${gamePageTypeChecked}/top-wins`}
        breakpoints={TOP_WINS_SLIDER_BREAKPOINTS}
      />
    </div>
  );
};

export default TopWinningsCarousel;
