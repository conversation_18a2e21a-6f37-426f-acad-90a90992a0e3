'use client';
import clsx from 'clsx';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { memo, useState } from 'react';
import ReactTexty from 'react-texty';

import { IGame } from '@/app/api/getGames.ts';
import { StyledDemoTag } from '@/app/components/Games/styled';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Icon, Typography, Image } from '@/atomic-design-components';
import { CURRENCY, LOGO_PLACEHOLDER } from '@/constants.ts';
import { useGameCheckBeforeOpen } from '@/hooks/useGameCheckBeforeOpen.ts';
import { theme } from '@/theme';
import { closeModal } from '@/utils/closeModal.ts';
import { openModal } from '@/utils/openModal.ts';
import Link from 'next/link';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import useWindowSize from '@/hooks/useWindowSize';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const TopWinningsCard = ({
  item,
  index,
  userCurrency,
}: {
  item: IGame;
  index: number;
  userCurrency: string;
}) => {
  const router = useRouter();
  const { lng, gamePageType } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();

  const [isLoaded, setIsLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const onMouseEnter = () => {
    setIsHovered(true);
  };
  const onMouseLeave = () => {
    setIsHovered(false);
  };

  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isTouchDeviceOrMd = isTouchDevice || (width && width < theme.breakpoints?.md);

  const gameUrl = `/${lng}/${gamePageType || 'casino'}/${item.provider_slug}/game/${item.slug}`;

  const { onGameClick, canGameBeOpenedByLink, isUserAuthorized } = useGameCheckBeforeOpen(
    gameUrl,
    user,
    item.wager_percentage,
  );

  const currency = userCurrency
    ? CURRENCY.find((c) => c.code === userCurrency)?.symbol || '$'
    : '$';

  return (
    <div className='relative flex items-center rounded-lg bg-[#1E293B]'>
      <div
        onMouseEnter={isTouchDeviceOrMd ? undefined : () => onMouseEnter()}
        onMouseLeave={isTouchDeviceOrMd ? undefined : () => onMouseLeave()}
        role='presentation'
        onClick={(e: any) => {
          if (isTouchDeviceOrMd) {
            if (canGameBeOpenedByLink) {
              e.preventDefault();
              document
                .getElementById('gameModal')
                ?.setAttribute('data-active-game-slug', item.slug);
              openModal('gameModal', true, true, true);
              closeModal('continueGameWithZeroWager');
            } else {
              onGameClick();
            }
          }
        }}
      >
        <div
          className={clsx(
            'gameCard gameCardWinnings align-center flex cursor-pointer flex-col justify-center',
            !item.photo_url_vertical && 'placeholder',
            !isLoaded && 'loading',
          )}
          onClick={() => {
            if (isTouchDeviceOrMd) {
              return;
            }
            if (canGameBeOpenedByLink) {
              router.push(gameUrl);
            }
            onGameClick();
          }}
          role='presentation'
        >
          {(!isLoaded || !item.photo_url_vertical) && (
            <div className={clsx('hoverImageDiv')}>
              <Image
                alt={item.name || `Game ${index} placeholder`}
                placeholder={undefined}
                src={LOGO_PLACEHOLDER}
                fill
                sizes='357px'
                style={{ objectFit: 'contain' }}
                className='placeholder'
                unoptimized
              />
            </div>
          )}
          {item.photo_url_vertical && (
            <div className={clsx('hoverImageDiv')}>
              <Image
                alt={item.name || `Game ${index}`}
                placeholder={undefined}
                onLoad={() => setIsLoaded(true)}
                src={item.photo_url_vertical}
                fill
                style={{ objectFit: 'cover' }}
                sizes='180px'
              />
            </div>
          )}
          {isHovered && !isTouchDeviceOrMd && (
            <div className='gameCard__overlay topWinsCard'>
              <Link
                href={canGameBeOpenedByLink ? gameUrl : ''}
                onClick={() => {
                  onGameClick();
                }}
                className='playIcon'
              >
                <Icon name='play' />
              </Link>
              {item.is_demo ? (
                <StyledDemoTag
                  size='small'
                  variant='transparent'
                  className='!mt-auto'
                  text={t('demo')}
                  onClick={(e: any) => {
                    if (isTouchDeviceOrMd) {
                      return;
                    }
                    e.stopPropagation();
                    if (isUserAuthorized) {
                      router.push(`${gameUrl}?demo=true`);
                    } else {
                      onGameClick();
                    }
                  }}
                />
              ) : (
                <div className='mt-auto' />
              )}
            </div>
          )}
        </div>
      </div>
      <div className='flex flex-col gap-1 overflow-x-hidden max-md:p-5 md:p-6'>
        <Typography text={item.name} as={ReactTexty} />
        <Typography
          text={`${currency} ${addSpacesToLongNumbers(item.win_amount || 0)}`}
          color={theme.color?.secondary.dark}
          type='h3'
          className='text-nowrap'
        />
        <Typography
          text={item.nickname || item.email || item.phone}
          iconName='user'
          iconProps={{ width: 16, height: 16, margin: '0 4px 0 0' }}
          type='label2'
        />
      </div>
    </div>
  );
};

export default memo(TopWinningsCard);
