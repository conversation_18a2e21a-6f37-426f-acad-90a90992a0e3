'use client';
import { Button, Typography } from '@/atomic-design-components';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { getWallets, IWallet } from '@/app/api/crypto/getWallets.ts';
import {
  getPaymentDataClient,
  IFeaturedMethodsResponse,
  IMethod,
  IMethodGroup,
  IMethodsResponse,
} from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import PaymentHistory from '@/app/components/Cashier/History/PaymentHistory.tsx';
import {
  StyledCashierModal,
  StyledPaymentTabs,
} from '@/app/components/Cashier/styled.ts';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import useClickOutside from '@/hooks/useClickOutside';
import useIsDialogOpen from '@/hooks/useIsDialogOpen';
import { usePrevious } from '@/hooks/useReact';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import DepositOrWithdrawalWrapper from './components/DepositOrWithdrawalWrapper';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { closeModal } from '@/utils/closeModal.ts';
import { openModal } from '@/utils/openModal.ts';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';

const getBonusForTip = (bonuses: IBonus[]) => {
  if (!bonuses.length) return null;
  const wager_freespins_deposit = bonuses.filter(
    (bonus) => bonus.bonus_type === 'wager_freespins_deposit',
  );
  if (wager_freespins_deposit?.length) {
    return wager_freespins_deposit.sort((a, b) => {
      return b.deposit_amount_percentage - a.deposit_amount_percentage;
    })[0];
  }
  const wager_deposit = bonuses.filter(
    (bonus) => bonus.bonus_type === 'wager_deposit',
  );
  if (wager_deposit?.length) {
    return wager_deposit.sort((a, b) => {
      return b.deposit_amount_percentage - a.deposit_amount_percentage;
    })[0];
  }
  const freespins_deposit = bonuses.filter(
    (bonus) => bonus.bonus_type === 'freespins_deposit',
  );
  if (freespins_deposit?.length) {
    return freespins_deposit.sort((a, b) => {
      return b.freespins_amount - a.freespins_amount;
    })[0];
  }
  return null;
};

export const CRYPTO_TOKENS = {
  USDTTRC: {
    id: 1,
    type: 'crypto',
    name: 'Tether',
    tokenExnode: 'USDTTRC',
    token: 'USDT',
    network: 'TRC20',
    provider: 'Exnode',
    providerId: 1, // Exnode (dev)
    multiplierForMathCeil: 100,
    image: '',
  },
  USDTERC: {
    id: 2,
    type: 'crypto',
    name: 'Tether',
    tokenExnode: 'USDTERC',
    token: 'USDT',
    network: 'ERC20',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 100,
    image: '',
  },
  BTC: {
    id: 3,
    type: 'crypto',
    name: 'Bitcoin',
    tokenExnode: 'BTC',
    token: 'BTC',
    network: 'Bitcoin BTC',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 100000,
    image: '',
  },
  ETH: {
    id: 4,
    type: 'crypto',
    name: 'Ethereum',
    tokenExnode: 'ETC',
    token: 'ETH',
    network: 'ERC20',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 100000,
    image: '',
  },
  TRX: {
    id: 5,
    type: 'crypto',
    name: 'TRON',
    tokenExnode: 'TRON',
    token: 'TRX',
    network: 'TRC20',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 100,
  },
  USDCERC: {
    id: 6,
    type: 'crypto',
    name: 'USD Coin',
    tokenExnode: 'USDCERC',
    token: 'USDC',
    network: 'ERC20',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 100,
    image: '',
  },
  BNB: {
    id: 7,
    type: 'crypto',
    name: 'Binance Coin',
    tokenExnode: 'BNB',
    token: 'BNB',
    network: 'BEP20',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 1000,
    image: '',
  },
  TON: {
    id: 8,
    type: 'crypto',
    name: 'TON Crystal',
    tokenExnode: 'TON',
    token: 'TON',
    network: 'TON',
    provider: 'Exnode',
    providerId: 1,
    multiplierForMathCeil: 100,
    image: '',
  },
};

export type CashierTabType = 'deposit' | 'withdrawal' | 'history';
export type CashierTabPageType =
  | 'deposit'
  | 'depositByCrypto'
  | 'depositByCryptoNoBonuses'
  | 'depositByCard'
  | 'depositByPtoP'
  | 'depositByCryptoAddress'
  | 'withdrawal'
  | 'withdrawalByCrypto'
  | 'withdrawalByCard'
  | 'withdrawalByPtoP'
  | 'payment'
  | 'paymentResult';
type CashierTabsType = { title: CashierTabType }[];
export type CashierTabScreenType =
  | 'paymentMethodsAll'
  | 'paymentMethodsInGroup'
  | 'paymentAction';

const CASHIER_TABS: CashierTabsType = [
  { title: 'deposit' },
  { title: 'withdrawal' },
  { title: 'history' },
];

// Type guard for IMethod
export function isIMethod(
  method: IMethod | IMethodGroup | null,
): method is IMethod {
  return (method as IMethod).currency_user !== undefined;
}

// Type guard for IMethodGroup
export function isIMethodGroup(
  method: IMethod | IMethodGroup | null,
): method is IMethodGroup {
  return (method as IMethodGroup).methods !== undefined;
}

const CashierModal = () => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { width } = useWindowSize();
  const { isTouchDevice } = useIsTouchMobileView();
  const isTouchDeviceMd =
    (width && width < theme.breakpoints?.md) || isTouchDevice;

  const ref: any = useRef(null);
  useClickOutside(ref);

  const { register } = useRegisterModalToOpenByUrl();
  useLayoutEffect(() => {
    if (ref.current) {
      register('cashierModal');
    }
  }, [ref?.current]);

  const { user, isRegistrationJustDone, setIsRegistrationJustDone } = useUser();
  const [{ constants }] = useSystemData();

  const { isDialogOpen } = useIsDialogOpen(ref);
  const prevIsDialogOpen = usePrevious(isDialogOpen);

  const [activeTab, setActiveTab] = useState<CashierTabType>('deposit');
  const [activeTabPage, setActiveTabPage] = useState<CashierTabPageType>();
  const [activeScreen, setActiveScreen] = useState<CashierTabScreenType>(
    user?.total_deposits_qty ? 'paymentAction' : 'paymentMethodsAll',
  );
  const [activeCard, setActiveCard] = useState<IMethod | null>(null);

  const [wallets, setWallets] = useState(null as IWallet[] | null);
  const [inProgress, setInProgress] = useState(true);

  const [paymentMethods, setPaymentMethods] = useState({
    deposit: [],
    withdrawal: [],
  } as IMethodsResponse);

  const [featuredPaymentMethods, setFeaturedPaymentMethods] = useState({
    deposit: [],
    withdrawal: [],
  } as IMethodsResponse);

  const { userBonuses, getFilteredBonuses, setInProgressBonuses } =
    useUserBonuses();
  const [bonuses, setBonuses] = useState<IBonus[] | null>(null);
  const [bonusForTip, setBonusForTip] = useState<IBonus | null>(null);

  const getUserWallets = async () => {
    const userWallets: IWallet[] = await getWallets();
    setWallets(userWallets);
  };

  const getMethods = async () => {
    const result = await getPaymentDataClient(constants);
    const methods = result?.methods || { deposit: [], withdrawal: [] };
    const featuredMethods = result?.featuredMethods || {
      deposit: [],
      withdrawal: [],
    };
    // console.log(methods, featuredMethods);
    setPaymentMethods(methods);
    setFeaturedPaymentMethods(featuredMethods);
    // if (activeScreen)
    return featuredMethods as IFeaturedMethodsResponse;
  };

  useEffect(() => {
    if (!isDialogOpen || !userBonuses) {
      return;
    }

    const filteredBonuses = userBonuses?.filter(
      (bonus) =>
        [
          'freespins_deposit',
          'wager_deposit',
          'wager_freespins_deposit',
        ].includes(bonus.bonus_type) &&
        !(
          bonus.bonus_type === 'wager_freespins_deposit' &&
          bonus.bonus_global_type === 'freespins'
        ), // it's a freespins_no_deposit bonus now
    );

    setBonuses(filteredBonuses || []);

    const bonusForInfoTip = user?.active_bonus_id
      ? null
      : getBonusForTip(filteredBonuses || []);
    setBonusForTip(bonusForInfoTip);
  }, [userBonuses, isDialogOpen]);

  useEffect(() => {
    if (isDialogOpen && wallets === null) {
      getUserWallets();
    }
  }, [isDialogOpen]);

  useEffect(() => {
    if (isRegistrationJustDone) {
      openModal('cashierModal');
    }
  }, [isRegistrationJustDone]);

  useEffect(() => {
    if (isDialogOpen && !prevIsDialogOpen) {
      setActiveScreen(
        user?.total_deposits_qty ? 'paymentAction' : 'paymentMethodsAll',
      );

      getMethods()
        .then((featuredMethods) => {
          setInProgressBonuses(true);
          if (
            featuredMethods?.deposit?.length &&
            featuredMethods.deposit[0]?.lastPaidAmount
          ) {
            setActiveCard(featuredMethods.deposit[0] as IMethod);
            setActiveScreen('paymentAction');
          } else {
            setActiveScreen('paymentMethodsAll');
          }
          // console.log('getFilteredBonuses in cashier');
          getFilteredBonuses();
        })
        .catch((e) => console.error(e))
        .finally(() => {
          setInProgressBonuses(false);
          setInProgress(false);
        });
    } else if (prevIsDialogOpen && !isDialogOpen) {
      setActiveTab('deposit');
      setActiveScreen(
        user?.total_deposits_qty ? 'paymentAction' : 'paymentMethodsAll',
      );
      setInProgress(true);
      setActiveTabPage('deposit');
    }
  }, [isDialogOpen, prevIsDialogOpen, user?.total_deposits_qty]);

  const onTabClick = (e: Event, idx: number) => {
    const newTabTitle = CASHIER_TABS[idx].title;
    setActiveTab(newTabTitle);
  };

  useEffect(() => {
    setActiveScreen('paymentMethodsAll');
  }, [activeTab]);

  const getTabTitle = (tab: any) => (
    <Typography text={t(tab.title)} justifyContent="center" type="body2" />
  );

  const inProgressAll = inProgress || !bonuses;

  const tabsContents = [
    <DepositOrWithdrawalWrapper
      key={0}
      lng={lng}
      activeTab={activeTab}
      activeScreen={activeScreen}
      setActiveScreen={setActiveScreen}
      activeCard={activeCard}
      setActiveCard={setActiveCard}
      activeTabPage={activeTabPage}
      setActiveTabPage={setActiveTabPage}
      paymentMethods={paymentMethods.deposit}
      featuredPaymentMethods={featuredPaymentMethods.deposit as IMethod[]}
      wallets={wallets}
      setWallets={setWallets}
      bonuses={bonuses}
      bonusForTip={bonusForTip}
      inProgress={inProgressAll}
      isDialogOpen={isDialogOpen}
    />,
    <DepositOrWithdrawalWrapper
      key={1}
      lng={lng}
      activeTab={activeTab}
      activeScreen={activeScreen}
      setActiveScreen={setActiveScreen}
      activeCard={activeCard}
      setActiveCard={setActiveCard}
      setActiveTabPage={setActiveTabPage}
      paymentMethods={paymentMethods.withdrawal}
      featuredPaymentMethods={featuredPaymentMethods.withdrawal as IMethod[]}
      isDialogOpen={isDialogOpen}
    />,
    <PaymentHistory lng={lng} key={2} />,
  ];

  const isPaymentScreenForP2P =
    activeTabPage &&
    ['payment', 'paymentResult'].includes(activeTabPage) &&
    activeCard?.payment_type === 'p2p';

  return (
    <StyledCashierModal
      id="cashierModal"
      className={clsx(
        activeTab,
        activeScreen,
        activeTabPage,
        activeCard?.payment_type,
      )}
      ref={ref}
      closeButtonPosition={clsx(
        'right-2 top-3',
        isTouchDevice
          ? 'md:right-6 md:top-3 mobileView'
          : 'md:right-6 md:top-6',
      )}
      onClose={() => {
        if (isRegistrationJustDone) {
          setIsRegistrationJustDone(false);
        }
        closeModal('cashierModal');
      }}
      withCloseIcon={!isPaymentScreenForP2P}
    >
      <div className="cashierHeader">
        {!(
          activeCard?.payment_type === 'p2p' &&
          activeTabPage === 'paymentResult'
        ) && (
          <Button
            id="liveChatInCashier"
            iconLeftProps={{
              name: 'headphones',
              wrapperWidth:
                (activeScreen === 'paymentMethodsAll' ||
                  activeScreen === 'paymentMethodsInGroup') &&
                !isTouchDeviceMd
                  ? undefined
                  : 32,
              wrapperHeight:
                (activeScreen === 'paymentMethodsAll' ||
                  activeScreen === 'paymentMethodsInGroup') &&
                !isTouchDeviceMd
                  ? undefined
                  : 32,
            }}
            text={
              (activeScreen === 'paymentMethodsAll' ||
                activeScreen === 'paymentMethodsInGroup') &&
              !isTouchDeviceMd
                ? 'Live Chat'
                : ''
            }
            size="small"
            variant={
              (activeScreen === 'paymentMethodsAll' ||
                activeScreen === 'paymentMethodsInGroup') &&
              !isTouchDeviceMd
                ? 'secondary'
                : 'transparent'
            }
            padding={
              (activeScreen === 'paymentMethodsAll' ||
                activeScreen === 'paymentMethodsInGroup') &&
              !isTouchDeviceMd
                ? undefined
                : '0'
            }
            className={clsx(
              'live_support_button absolute top-3 z-10',
              isPaymentScreenForP2P
                ? 'right-[6px] md:right-[24px]'
                : 'right-[64px]',
              isPaymentScreenForP2P && !isTouchDevice && 'md:top-6',
              !isPaymentScreenForP2P &&
              (isTouchDevice
                ? 'md:right-[80px]'
                : 'md:right-[80px] md:top-6'),
            )}
          />
        )}
        <Typography
          text={
            activeScreen === 'paymentMethodsAll'
              ? t('casa')
              : activeScreen === 'paymentAction' &&
              activeTabPage &&
              ['payment', 'paymentResult'].includes(activeTabPage)
                ? ''
                : t(activeTab)
          }
          type="h2"
          className={clsx(
            'absolute left-2 top-3',
            isTouchDevice ? 'md:left-6' : 'md:!hidden',
          )}
          iconName={
            activeScreen === 'paymentMethodsAll' ||
            (activeScreen === 'paymentAction' &&
              activeTabPage &&
              ['payment', 'paymentResult'].includes(activeTabPage))
              ? ''
              : 'chevronDown'
          }
          lineHeight="32px"
          fontSize="24px"
          iconProps={{
            className: 'rotate-90',
            width: 15,
            height: 9,
            wrapperWidth: 16,
            wrapperHeight: 16,
            margin: '0 20px 0 0',
          }}
          onClick={
            activeScreen === 'paymentMethodsAll'
              ? undefined
              : () => setActiveScreen('paymentMethodsAll')
          }
        />
      </div>
      <StyledPaymentTabs
        activeTabProp={CASHIER_TABS.findIndex((el) => el.title === activeTab)}
        onTabChange={onTabClick}
        getTabTitle={getTabTitle}
        tabsTitles={CASHIER_TABS}
        tabsContents={tabsContents}
        className={clsx(activeScreen === 'paymentAction' && 'hideTabsTitles')}
        hideNavBtns
      />
    </StyledCashierModal>
  );
};

CashierModal.displayName = 'CashierModal';
export default CashierModal;
