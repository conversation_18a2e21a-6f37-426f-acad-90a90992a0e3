'use client';
import ModalDialog from '@/app/components/ModalDialog';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import { useRef, useState } from 'react';
import { cancelWithdrawal } from '@/app/api/payments/cancelWithdrawal.ts';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import { closeModal } from '@/utils/closeModal.ts';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { IPaymentTransaction } from '@/app/api/payments/getPaymentTransactionsClient.ts';

const CancelWithdrawalModal = ({
  modalId,
  setTransactions,
}: {
  modalId?: string;
  setTransactions?: Function;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { setUser } = useUser();
  const { showError } = useAlert();
  const ref = useRef<HTMLDialogElement>(null);

  const [inProgressCancel, setInProgressCancel] = useState(false);

  const onCancelWithdrawal = (id: string) => {
    setInProgressCancel(true);
    cancelWithdrawal(id)
      .then((res) => {
        if (res.error) {
          showError(res.error);
          return;
        } else {
          setUser((prev: UserType | null) =>
            prev
              ? {
                  ...prev,
                  account_balance: prev.account_balance + res.amount_user,
                }
              : null,
          );

          if (setTransactions) {
            setTransactions((prev: any) =>
              prev.map((transaction: IPaymentTransaction) => {
                if (transaction.transaction_id === res.transaction_id) {
                  return {
                    ...transaction,
                    status: 'cancelled',
                  };
                }
                return transaction;
              }),
            );
          }
        }
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => {
        setInProgressCancel(false);
        closeModal(modalId || 'cancelWithdrawalModal', !!modalId);
      });
  };

  return (
    <ModalDialog
      id={modalId || 'cancelWithdrawalModal'}
      ref={ref}
      title={t('doYouWantToCancelTransaction')}
      text={t('fundsWillBeReturned')}
      withButtons
      inProgress={inProgressCancel}
      onClose={
        !modalId
          ? () => {
              closeModal('cancelWithdrawalModal', false);
            }
          : undefined
      }
      onSecondButtonClick={() => {
        const id = document
          .getElementById(modalId || 'cancelWithdrawalModal')
          ?.getAttribute('data-cancel-withdrawal-id');

        if (id) {
          onCancelWithdrawal(id);
        } else {
          showError(t('somethingWentWrong'));
          closeModal(modalId || 'cancelWithdrawalModal', !!modalId);
        }
      }}
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
    />
  );
};

export default CancelWithdrawalModal;
