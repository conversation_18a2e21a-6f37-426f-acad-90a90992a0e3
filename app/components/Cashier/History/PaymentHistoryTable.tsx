'use client';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import clsx from 'clsx';
import utc from 'dayjs/plugin/utc';
import {
  getPaymentTransactionsClient,
  IPaymentTransaction,
} from '@/app/api/payments/getPaymentTransactionsClient.ts';
import { useTranslation } from '@/app/i18n/client';
import { Button, Select, Typography, Image } from '@/atomic-design-components';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { transformDate } from '@/utils/dates';
import { openModal } from '@/utils/openModal.ts';
import { StyledLine } from '../../Footer/styled';
import EmptyTable from '../../TableComponent/EmptyTable';
import TableComponent from '../../TableComponent/TableComponent';

dayjs.extend(utc);
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';
import PaymentHistoryTableMobile from '@/app/components/Cashier/History/PaymentHistoryTableMobile.tsx';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

interface TableProps {
  total: number;
  setTotal: Function;
  transactionsInitial: IPaymentTransaction[];
  lng: LanguagesType;
  inProgress: boolean;
  limit: number;
}

const getFilterString = (month: dayjs.Dayjs) =>
  `created_at__gte=${dayjs(month).startOf('month').utc().format('YYYY-MM-DD HH:mm:ss')},created_at__lte=${dayjs(month).endOf('month').utc().format('YYYY-MM-DD HH:mm:ss')}`;

const PaymentHistoryTable = ({
  lng,
  total,
  setTotal,
  transactionsInitial,
  // setTransactions,
  inProgress,
  limit,
}: TableProps) => {
  const { t } = useTranslation(lng);

  const [transactions, setTransactions] = useState<IPaymentTransaction[]>(transactionsInitial);

  const { width } = useWindowSize();
  const { isTouchDevice } = useIsTouchMobileView();
  const isMobile = !!width && width < theme.breakpoints?.md;
  const isTouchDeviceOrMd = isTouchDevice || isMobile;

  const { newNotification } = useNewNotification();

  const [activeMonthFilter, setActiveMonthFilter] = useState<dayjs.Dayjs | null>(null);

  useEffect(() => {
    setTransactions(transactionsInitial);
  }, [transactionsInitial]);

  const lastSixMonthsInDayJsFormat = transactionsInitial?.[0]?.created_at
    ? Array.from({ length: 6 }, (_, i) => {
        if (i === 0) {
          return dayjs(transactionsInitial[0].created_at);
        }
        return dayjs(transactionsInitial[0].created_at).subtract(i, 'month');
      })
    : [];

  const loadMore = async (skip: number) => {
    const newItems = await getPaymentTransactionsClient(
      skip,
      limit,
      activeMonthFilter ? getFilterString(activeMonthFilter) : '',
    );
    return newItems?.items;
  };

  const onFilterChange = (month: dayjs.Dayjs) => {
    const isSameMonthClicked = month && dayjs(activeMonthFilter).isSame(month, 'month');
    setActiveMonthFilter(isSameMonthClicked || !month ? null : month);
    getPaymentTransactionsClient(0, limit, !month || isSameMonthClicked ? '' : getFilterString(month))
      .then((result) => {
        setTransactions(result?.items);
        setTotal(result?.total);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  useEffect(() => {
    if (
      [
        'deposit_completed',
        'deposit_completed_tournament',
        'withdrawal_rejected',
        'withdrawal_completed',
      ].includes(newNotification?.notification_type)
    ) {
      getPaymentTransactionsClient(0, limit)
        .then((result) => {
          setTransactions(result?.items);
          setTotal(result?.total);
          setActiveMonthFilter(null);
        })
        .catch((error) => {
          console.error(error);
        });
    }
  }, [newNotification]);

  // console.log(transactionsInitial);

  const columns = [
    {
      transaction_id: {
        key: 'id',
        dataKey: 'transaction_id',
        width: 0,
        flexGrow: 0.5,
        cellRenderer: (item: IPaymentTransaction) => {
          const isWithdrawalCascade =
            item.payment_operation === 'withdrawal' && item.cascade_uuid;
          const idToShow = isWithdrawalCascade ? item.cascade_uuid : item.transaction_id;

          return <Typography type='body2' text={idToShow?.split('-')?.[0]?.toUpperCase()} />;
        },
      },
    },
    {
      created_at: {
        key: 'date',
        dataKey: 'created_at',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ created_at }: { created_at: string }) => (
          <Typography type='body2' text={transformDate(created_at, 'DD/MM/YYYY, HH:mm')} />
        ),
      },
    },
    {
      payment_operation: {
        key: 'type',
        dataKey: 'payment_operation',
        width: 0,
        flexGrow: 1,
        cellRenderer: (item: IPaymentTransaction) => {
          const operationKey =
            item.payment_operation === 'deposit' ? 'Deposit' : item.payment_operation;
          return <Typography type='body2' text={t(operationKey)} />;
        },
      },
    },
    {
      payment_setting_title: {
        key: 'paymentMethod',
        dataKey: 'payment_setting_title',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({
          payment_setting_title,
          payment_setting_title_platform,
          photo_url,
        }: {
          payment_setting_title: string;
          payment_setting_title_platform: string;
          photo_url: string;
        }) => (
          <div className='flex items-center gap-2'>
            {photo_url && (
              <Image
                src={photo_url}
                alt={payment_setting_title_platform || payment_setting_title}
                width={16}
                height={16}
                unoptimized
                className='mr-[2px] mt-[-2px] h-[16px]'
              />
            )}
            <Typography
              type='body2'
              text={t(payment_setting_title_platform || payment_setting_title)}
            />
          </div>
        ),
      },
    },
    {
      amount_user: {
        key: 'amount',
        dataKey: 'amount_user',
        width: 0,
        flexGrow: 1,
        cellRenderer: (rowData: IPaymentTransaction) => {
          const { amount_user, payment_operation, currency_user } = rowData;
          return (
            <Typography
              type='body2'
              text={`${payment_operation === 'withdrawal' ? '-' : '+'}${addSpacesToLongNumbers(amount_user || 0)} ${currency_user}`}
              color={payment_operation === 'withdrawal' ? '#EB5645' : '#489F37'}
            />
          );
        },
      },
    },
    {
      status: {
        key: 'status',
        dataKey: 'status',
        width: 0,
        flexGrow: 1,
        cellRenderer: (rowData: IPaymentTransaction) => (
          <Typography
            type='body2'
            text={t(
              rowData.status === 'in_progress' || (rowData.status === 'pending' && rowData.is_cascade_in_progress)
                ? 'inProgress'
                : rowData.status,
            )}
          />
        ),
      },
    },
    {
      action: {
        dataKey: 'action',
        flexGrow: 0,
        cellRenderer: (rowData: IPaymentTransaction) => {
          if (
            rowData.status === 'pending' &&
            !rowData.is_cascade_in_progress &&
            rowData.payment_operation === 'withdrawal'
          ) {
            return (
              <Button
                text={t('cancel')}
                size='small'
                onClick={(e: any) => {
                  e.preventDefault();
                  document
                    .getElementById('cancelWithdrawalModal')
                    ?.setAttribute('data-cancel-withdrawal-id', rowData.transaction_id);
                  openModal('cancelWithdrawalModal', true, true, true);
                }}
                // className='my-2'
              />
            );
          } else {
            return null;
          }
        },
      },
    },
  ];

  return (
    <div
      className={clsx(
        'historyContainer flex w-full flex-col',
        isTouchDevice ? 'gap-2 px-2' : 'max-md:gap-2 max-md:px-2',
      )}
    >
      {!inProgress && !transactions?.length ? (
        <EmptyTable
          lng={lng}
          className={clsx('paymentHistoryTable', isTouchDevice ? 'order-1' : 'max-md:order-1')}
          iconName='walletCross'
          header={t('noTransactionHistory')}
          text={t('noTransactionHistoryText')}
        />
      ) : (
        <>
          <TableComponent
            className={clsx('paymentHistoryTable', isTouchDevice ? 'hidden' : 'max-md:hidden')}
            columns={columns}
            initialData={transactions}
            totalItems={total}
            lng={lng}
            isHeaderSticky
            withInfiniteScroll
            limit={limit}
            loadMore={loadMore}
          />
          <PaymentHistoryTableMobile
            lng={lng}
            isTouchDevice={isTouchDevice}
            transactions={transactions}
            loadMore={loadMore}
            setTransactions={setTransactions}
            totalItems={total}
            limit={limit}
          />
        </>
      )}
      <div className={clsx(isTouchDevice ? '' : 'md:mt-4 md:h-[80px]')}>
        {!!lastSixMonthsInDayJsFormat.length && (
          <StyledLine
            className={clsx(
              'absolute bottom-[80px] left-0 w-[1018px]',
              isTouchDevice ? 'hidden' : 'max-md:hidden',
            )}
          />
        )}
        {!!lastSixMonthsInDayJsFormat?.length && (
          <Select
            className={clsx('z-10', isTouchDevice ? 'order-none' : 'max-md:order-none')}
            controlShouldRenderValue
            customGetOptionLabel={(option: any) => {
              return <Typography text={transformDate(option, 'MMMM', lng)} />;
            }}
            getSelectedOption={(props: any) => {
              const value = props.getValue()[0];
              return <Typography text={transformDate(value, 'MMMM', lng)} />;
            }}
            isSearchable={false}
            isClearable
            menuPlacement={!isTouchDeviceOrMd && 'top'}
            menuPortalTarget={isTouchDeviceOrMd ? document?.body : null}
            name='month'
            onChange={(month: any) => onFilterChange(month)}
            options={lastSixMonthsInDayJsFormat}
            placeholder={t('month')}
            width={!width || isTouchDeviceOrMd ? '100%' : '224px'}
            withShadowScreen
            value={activeMonthFilter}
          />
        )}
      </div>
    </div>
  );
};

export default PaymentHistoryTable;
