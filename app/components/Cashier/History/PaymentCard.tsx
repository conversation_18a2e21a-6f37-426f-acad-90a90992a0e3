'use client';
import { IPaymentTransaction } from '@/app/api/payments/getPaymentTransactionsClient.ts';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography, Image } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { transformDate } from '@/utils/dates';
import { openModal } from '@/utils/openModal';
import clsx from 'clsx';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const PaymentCard = ({
  lng,
  item,
  className,
  isCashier,
}: {
  className?: string;
  lng: LanguagesType;
  item: IPaymentTransaction;
  isCashier?: boolean;
}) => {
  const { t } = useTranslation(lng);
  const isWithdrawalCascade = item.payment_operation === 'withdrawal' && item.cascade_uuid;
  const idToShow = isWithdrawalCascade ? item.cascade_uuid : item.transaction_id;

  return (
    <div
      className={clsx(
        'flex w-full flex-col gap-2 rounded-lg bg-[#1E293B] px-2 py-4 sm:gap-4 md:px-4',
        className,
      )}
    >
      <div className='flex justify-between'>
        <div className='flex gap-2'>
          {item.photo_url && (
            <Image
              src={item.photo_url}
              alt={item.payment_setting_title_platform || item.payment_setting_title || ''}
              width={16}
              height={16}
              unoptimized
              className='mr-[2px] mt-[3px] h-[16px]'
            />
          )}
          <Typography
            type='body2'
            text={t(item.payment_setting_title_platform || item.payment_setting_title || '')}
            color={theme.color?.general.white}
          />
        </div>
        <Typography
          type='sub2'
          text={`${item.payment_operation === 'withdrawal' ? '-' : '+'}${addSpacesToLongNumbers(item.amount_user || 0)} ${item.currency_user}`}
          color={item.payment_operation === 'withdrawal' ? '#EB5645' : '#489F37'}
        />
      </div>
      <div className='flex justify-between'>
        <Typography
          type='label1'
          text={`ID ${idToShow?.split('-')?.[0]?.toUpperCase() || ''}, ${transformDate(item.created_at, 'DD/MM/YYYY, HH:mm')}`}
        />
        <Typography
          type='label1'
          text={t(
            item.status === 'in_progress' || (item.status === 'pending' && item.is_cascade_in_progress)
              ? 'inProgress'
              : item.status,
          )}
        />
      </div>
      {item.status === 'pending' && !item.is_cascade_in_progress && item.payment_operation === 'withdrawal' && (
        <Button
          text={t('cancel')}
          size='small'
          fullWidth
          onClick={(e: any) => {
            e.preventDefault();
            document
              .getElementById(isCashier ? 'cancelWithdrawalModal' : 'cancelWithdrawalModalProfile')
              ?.setAttribute('data-cancel-withdrawal-id', item.transaction_id || '');
            openModal(
              isCashier ? 'cancelWithdrawalModal' : 'cancelWithdrawalModalProfile',
              true,
              true,
              true,
            );
          }}
          className='mt-2'
        />
      )}
    </div>
  );
};

export default PaymentCard;
