'use client';
import { LanguagesType } from '@/types/global';
import PaymentHistoryTable from './PaymentHistoryTable.tsx';
import {
  getPaymentTransactionsClient,
  IPaymentTransaction,
} from '@/app/api/payments/getPaymentTransactionsClient.ts';
import { useEffect, useState } from 'react';
import CancelWithdrawalModal from '@/app/components/Cashier/History/CancelWithdrawalModal.tsx';
import PaymentHistorySkeleton from '@/app/components/Skeletons/PaymentHistorySkeleton.tsx';
import { StyledHistoryWrapper } from '../styled.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

const LIMIT = 10;

const PaymentHistory = ({ lng }: { lng: LanguagesType }) => {
  const [transactions, setTransactions] = useState<IPaymentTransaction[]>([]);
  const [total, setTotal] = useState(0);
  const [inProgress, setInProgress] = useState(true);
  const { isTouchDevice } = useIsTouchMobileView();

  useEffect(() => {
    getPaymentTransactionsClient(0, LIMIT)
      .then((result) => {
        setTransactions(result?.items);
        setTotal(result?.total);
      })
      .catch((error) => {
        console.error(error);
      })
      .finally(() => {
        setInProgress(false);
      });
  }, []);

  if (inProgress) {
    return <PaymentHistorySkeleton mobileClassName='p-2' />;
  }

  return (
    <StyledHistoryWrapper className={isTouchDevice ? 'mobileView' : ''}>
      <PaymentHistoryTable
        lng={lng}
        transactionsInitial={transactions}
        total={total}
        setTotal={setTotal}
        inProgress={inProgress}
        limit={LIMIT}
      />
      <CancelWithdrawalModal setTransactions={setTransactions} />
    </StyledHistoryWrapper>
  );
};

export default PaymentHistory;
