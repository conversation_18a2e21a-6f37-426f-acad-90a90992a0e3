'use client';
import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';
import { useTranslation } from '@/app/i18n/client';
import { Typography } from '@/atomic-design-components/index.ts';
import { LanguagesType } from '@/types/global';
import WithdrawalInputBlock from './WithdrawalInputBlock.tsx';

import { useEffect } from 'react';

import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import clsx from 'clsx';

const WithdrawalByCard = ({
  activeCard,
  amountError,
  setAmountError,
  lng,
  userCurrency,
  setActiveTabPage,
}: {
  activeCard: IMethod;
  amountError: string;
  setAmountError: Function;
  lng: LanguagesType;
  userCurrency: string;
  setActiveTabPage: Function;
}) => {
  const { t } = useTranslation(lng);
  useEffect(() => {
    if (setActiveTabPage) {
      setActiveTabPage('withdrawalByCard');
    }
  }, []);

  const { isTouchDevice } = useIsTouchMobileView();

  return (
    <div
      className={clsx(
        'withdrawalContentWrapper flex flex-col',
        isTouchDevice && 'mobileView',
      )}
    >
      <Typography
        text={t('withdrawal')}
        type='h1'
        className={clsx('max-md:!hidden', isTouchDevice && '!hidden')}
      />
      <WithdrawalInputBlock
        lng={lng}
        activeCard={activeCard}
        amountError={amountError}
        setAmountError={setAmountError}
        userCurrency={userCurrency}
        className='md:mt-6'
      />
    </div>
  );
};

export default WithdrawalByCard;
