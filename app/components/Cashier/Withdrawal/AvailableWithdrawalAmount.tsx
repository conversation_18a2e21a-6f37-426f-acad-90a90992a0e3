'use client';
import { countAvailableAmountWithBlocked } from '@/app/components/Cashier/Withdrawal/WithdrawalInputBlock.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import clsx from 'clsx';
import parse from 'html-react-parser';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const AvailableWithdrawalAmount = ({
  className,
  t,
}: {
  className?: string;
  t: Function;
}) => {
  const { user } = useUser();

  const amountAvailable = countAvailableAmountWithBlocked(
    user!.account_balance,
    user!.blocked_amount,
  );
  const currencySymbol = getCurrencySymbol(user!.currency);

  return (
    <div
      className={clsx(
        className,
        'availableWithdrawal flex min-h-10 items-center justify-center rounded-lg bg-general-dark text-center',
      )}
    >
      <Icon
        name='infoIcon'
        fill={theme.color?.general.lightest}
        margin='-2px 0 0 0'
      />
      <Typography
        text={parse(
          t('availableWithdrawalAmount', {
            amount: `${currencySymbol}${addSpacesToLongNumbers(amountAvailable)}`,
          }),
        )}
        type='sub1'
      />
    </div>
  );
};

export default AvailableWithdrawalAmount;
