import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import parse from 'html-react-parser';
import { Button, Icon, Typography } from '@/atomic-design-components';
import { StyledPaymentWindow } from '@/app/components/Cashier/styled.ts';
import { theme } from '@/theme.ts';
import { useCopyToClipboard } from '@/utils/useCopyToClipboard.ts';
import { LanguagesType } from '@/types/global';
import { openModal } from '@/utils/openModal.ts';
import DepositHostToHostResult from '@/app/components/Cashier/Deposit/DepositHostToHostResult.tsx';
import { CashierTabPageType } from '@/app/components/Cashier/CashierModal.tsx';
import capitalize from '@/utils/capitalize.ts';
import { useTransformDateFromNowInSeconds } from '@/utils/dates';
import DepositHostToHostCloseModal from '@/app/components/Cashier/Deposit/DepositHostToHostCloseModal.tsx';

const DepositHostToHost = ({
  activeTabPage,
  isTouchDevice,
  lng,
  t,
  userCurrency,
  p2pPaymentInfo,
  setActiveTabPage,
  setActiveScreen,
}: {
  activeTabPage: CashierTabPageType;
  isTouchDevice: boolean;
  lng: LanguagesType;
  t: Function;
  userCurrency: string;
  p2pPaymentInfo: any;
  setActiveTabPage: Function;
  setActiveScreen: Function;
}) => {
  const [resultScreenType, setResultScreenType] = useState<
    'success' | 'reject' | ''
  >('');

  const copy = useCopyToClipboard(lng);

  const amount = p2pPaymentInfo?.amount;
  let bank = p2pPaymentInfo?.bank_name || '';
  bank = bank === 'visa/mc' ? 'Uzcard' : bank;
  const cardNumber = p2pPaymentInfo?.number || p2pPaymentInfo?.card || '';
  const name = p2pPaymentInfo?.holder || p2pPaymentInfo?.card_holder_name || '';

  const deadline = p2pPaymentInfo?.deadline || '';
  const created = p2pPaymentInfo?.created_at || '';
  const diff =
    deadline && created && dayjs(deadline).diff(dayjs(created), 'minute');

  const minutesInterval = diff || p2pPaymentInfo?.lifetime_in_minutes || 0;
  const secondsInterval =
    typeof p2pPaymentInfo?.expire_at === 'number'
      ? p2pPaymentInfo?.expire_at
      : 0;

  const currentDateInUTC = useRef(dayjs().utc());

  const dateTimeOut =
    (minutesInterval || secondsInterval) &&
    currentDateInUTC.current?.add(
      minutesInterval || secondsInterval,
      minutesInterval ? 'minute' : 'second',
    );

  const timer = useTransformDateFromNowInSeconds(
    dateTimeOut,
    lng,
    true,
    false,
  ) as Array<string>;

  useEffect(() => {
    if (
      timer?.length &&
      ((timer[1] === '00' && timer[2] === '00' && timer[3] === '00') ||
        timer.some((el: string) => el.includes('-')))
    ) {
      setActiveTabPage('paymentResult');
      setResultScreenType('reject');
    }
  }, [timer]);

  if (resultScreenType && activeTabPage === 'paymentResult') {
    return (
      <DepositHostToHostResult
        copy={copy}
        type={resultScreenType}
        t={t}
        isTouchDevice={isTouchDevice}
        setActiveScreen={setActiveScreen}
        setActiveTabPage={setActiveTabPage}
      />
    );
  }

  return (
    <>
      <div
        className={clsx(
          'p2pPayment flex h-full max-h-[calc(100dvh-174px)] flex-col md:px-2',
          isTouchDevice
            ? 'mobileView md:max-h-[calc(100dvh-122px)] md:px-6'
            : 'md:mt-[70px] md:max-h-full',
        )}
      >
        <Typography
          text={t('payment')}
          type='h2'
          className={clsx(
            'absolute left-[6px] top-[11px] z-10 md:left-5 md:top-6',
            isTouchDevice ? 'md:top-[11px]' : '',
          )}
          lineHeight='32px'
          fontSize='24px'
          // onClick={() => {
          //   if (setActiveTabPage) {
          //     setActiveTabPage('depositByPtoP');
          //   }
          // }}
        />
        <StyledPaymentWindow
          flexDirection='column'
          alignItems='start'
          gap='8px'
          className={clsx(
            'paymentWindow h-full overflow-auto !pt-0 md:!basis-full',
            isTouchDevice && 'mobileView',
          )}
        >
          <Typography
            iconName='cardsConvert'
            text={t('sendToNumber')}
            type='h3'
          />

          <div className='infoCard flex h-full w-full flex-col items-start gap-2'>
            <div className='flex w-full flex-col gap-4 rounded-lg bg-[#1E293B] px-2 py-4'>
              {timer?.length && (
                <div className='flex flex-col gap-1'>
                  {/*<Typography text={`${t('appN')} 3948493`} type='label2' />*/}
                  <div className='flex gap-1'>
                    <Typography
                      text={t('remainingTime')}
                      type='sub2'
                      fontWeight={theme.font.weight.regular}
                    />
                    <Typography
                      text={`${timer[2]}:${timer[3]}`}
                      type='sub2'
                      color={theme.color?.secondary.main}
                    />
                  </div>
                </div>
              )}
              <div className='flex w-full flex-col gap-2'>
                <Typography
                  type='body2'
                  className='cursor-pointer justify-between rounded-lg bg-[#334155] px-3 py-2'
                  text={cardNumber}
                  iconName='copy'
                  iconProps={{
                    className: 'order-1 !m-0 !ml-2',
                    width: 20,
                    height: 20,
                  }}
                  onClick={() => {
                    copy(cardNumber);
                  }}
                />
                <Typography type='body2'>
                  {capitalize(bank)}
                  &nbsp; &nbsp;
                  {capitalize(name)}
                </Typography>
                <Typography
                  type='body2'
                  className='cursor-pointer justify-between rounded-lg bg-[#334155] px-3 py-2'
                  iconName='copy'
                  iconProps={{
                    className: 'order-1 !m-0 !ml-2',
                    width: 20,
                    height: 20,
                  }}
                  onClick={() => {
                    copy((amount || 0).toString());
                  }}
                >
                  {amount || 0} {userCurrency}
                </Typography>
                {/*<Typography type='label2'>{t('rateMayChange')}</Typography>*/}
              </div>
              <Button
                text={t('copyNumber')}
                fullWidth
                variant='secondary'
                withBorder
                onClick={() => {
                  copy(cardNumber);
                }}
                className={clsx(
                  isTouchDevice ? `!bg-[#1e293b]` : 'max-md:!bg-[#1e293b]',
                )}
              />
              <Typography
                text={parse(t('p2pTip'))}
                color={theme.color?.general.white}
              />
            </div>
            <div className='flex w-full items-start gap-2 rounded-lg bg-[#1E293B] p-2'>
              <Icon name='infoIcon' fill={theme.color?.general.lightest} />
              <div className='flex flex-col gap-1'>
                <Typography text={t('fundsTransferRules')} type='sub1' />
                <Typography
                  text={t('fundsTransferRulesText')}
                  color={theme.color?.general.lighter}
                />
              </div>
            </div>
          </div>
        </StyledPaymentWindow>
        <div
          className={clsx(
            'mx-0 mt-2 px-0 max-md:fixed max-md:bottom-2 max-md:w-[calc(100%-16px)] md:mt-2',
            isTouchDevice
              ? 'fixed bottom-2 w-[calc(100%-16px)] md:bottom-5 md:w-[calc(100%-48px)]'
              : 'md:mx-[17px]',
          )}
        >
          <Button
            text={t('transferred')}
            fullWidth
            onClick={() => {
              setActiveTabPage('paymentResult');
              setResultScreenType('success');
            }}
          />
          <Button
            onClick={() => {
              openModal('depositHostToHostCloseModal', true, true, true);
            }}
            text={t('cancelPayment')}
            fullWidth
            variant='secondary'
            withBorder
            className='mt-2'
          />
        </div>
      </div>
      <DepositHostToHostCloseModal
        setActiveTabPage={setActiveTabPage}
        setResultScreenType={setResultScreenType}
      />
    </>
  );
};

export default DepositHostToHost;
