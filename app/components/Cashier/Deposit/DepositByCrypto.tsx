'use client';
import { createWallet } from '@/app/api/crypto/createWallet.ts';
import { IWallet } from '@/app/api/crypto/getWalletsClient.ts';
import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { CashierTabPageType, isIMethod } from '@/app/components/Cashier/CashierModal.tsx';
import DepositByCryptoAddress from '@/app/components/Cashier/Deposit/DepositByCryptoAddress.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { StyledVerticalLine } from '../../NavMenu/styled';
import DepositInputsBlock from './DepositInputsBlock';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';

export const getAmountInitial = (minAmount: number, latestPaidAmount?: number) => {
  if (!latestPaidAmount || latestPaidAmount < minAmount) return minAmount;

  return latestPaidAmount;
};

const DepositByCrypto = ({
  lng,
  activeCard,
  amountError,
  setAmountError,
  latestPaidAmount,
  isGetWalletsInProgress,
  wallet,
  setWallets,
  userCurrency,
  bonuses,
  activeTabPage,
  setActiveTabPage,
  selectedBonus,
  setSelectedBonus,
  savedBonus,
  setSavedBonus,
  inProgressSavingBonus,
  setInProgressSavingBonus,
}: {
  lng: LanguagesType;
  activeCard: IMethod;
  amountError: string;
  setAmountError: Function;
  latestPaidAmount?: number | undefined;
  isGetWalletsInProgress: boolean;
  wallet?: IWallet;
  setWallets?: Function;
  userCurrency: string;
  bonuses?: IBonus[] | null;
  activeTabPage?: CashierTabPageType;
  setActiveTabPage?: Function;
  selectedBonus: number;
  setSelectedBonus: Function;
  savedBonus: number | null;
  setSavedBonus: Function;
  inProgressSavingBonus: boolean;
  setInProgressSavingBonus: Function;
}) => {
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const { showError } = useAlert();
  const { isTouchDevice } = useIsTouchMobileView();

  const isPaymentBlockIsShown = activeTabPage === 'payment';

  const [amount, setAmount] = useState(
    getAmountInitial(activeCard.amount_min, latestPaidAmount) || 0,
  );

  const [inProgress, setInProgress] = useState(false);
  // const bonuses = [];
  const hasBonuses = !!bonuses && bonuses.length > 0;
  // const hasBonuses = false;

  //TODO: error handling
  useEffect(() => {
    const createUserWallet = async () => {
      if (isGetWalletsInProgress || inProgress || !setWallets) return;
      // const bonusSelected =
      //   bonuses?.some((bonus: IBonus) => {
      //     return bonus.external_id === getFromLocalStorage('selectedBonusId');
      //   }) && getFromLocalStorage('selectedBonusId');
      const wallet = await createWallet(
        activeCard?.currency_platform || '',
        user?.id || 0,
        // bonusSelected || undefined,
        activeCard?.payment_provider_id || 0,
        activeCard?.id || 0,
      );
      console.log(wallet, 'wallet');
      if (!wallet?.error) {
        setWallets((prev: IWallet[]) => [...prev, wallet]);
      } else {
        showError(wallet.error || 'Error creating wallet');
      }
      setInProgress(false);
    };

    if (!wallet && setWallets) {
      setInProgress(true);
      createUserWallet();
    }
  }, [wallet, setWallets, isGetWalletsInProgress, inProgress, activeCard]);

  useEffect(() => {
    if (setActiveTabPage) {
      setActiveTabPage('depositByCrypto');
    }
  }, []);

  if (!isIMethod(activeCard)) {
    return null;
  }

  return (
    <>
      {isPaymentBlockIsShown ? (
        <div
          className={clsx(
            'mb-3 flex md:mb-6  md:px-2',
            isTouchDevice ? 'md:mx-4 md:mt-[27px]' : 'md:mt-[76px]',
          )}
        >
          {activeTabPage === 'payment' && (
            <Typography
              text={t('payment')}
              type='h2'
              className={clsx(
                'absolute left-[6px] top-[11px] z-10 cursor-pointer md:top-5',
                isTouchDevice ? 'md:top-[11px]' : '',
              )}
              lineHeight='32px'
              fontSize='24px'
              iconName='chevronDown'
              iconProps={{
                className: 'rotate-90 !items-center !justify-center !flex',
                width: 15,
                height: 9,
                wrapperWidth: 28,
                wrapperHeight: 16,
              }}
              onClick={() => {
                setActiveTabPage && setActiveTabPage('deposit');
              }}
            />
          )}
          <DepositByCryptoAddress
            activeCard={activeCard}
            address={wallet?.address || ''}
            amount={+amount}
            // hasBonuses={hasBonuses}
            lng={lng}
            t={t}
            userCurrency={userCurrency}
          />
        </div>
      ) : (
        <div
          className={clsx(
            'depositContentWrapper flex flex-col',
            isTouchDevice ? 'mobileView' : 'md:pb-4',
          )}
        >
          <Typography
            text={t('deposit')}
            type='h1'
            className={clsx('ml-6 md:pb-6', isTouchDevice ? '!hidden' : 'max-md:!hidden')}
          />
          <div
            className={clsx(
              // !hasBonuses && 'column',
              'flex h-full overflow-y-auto max-md:flex-col',
              isTouchDevice ? 'flex-col' : 'md:max-h-[min(598px,calc(100dvh-61px))]',
            )}
          >
            <DepositInputsBlock
              lng={lng}
              amountError={amountError}
              setAmountError={setAmountError}
              setAmount={setAmount}
              bonuses={bonuses}
              amount={+amount}
              activeCard={activeCard}
              hasBonuses={hasBonuses}
              userCurrency={userCurrency}
              className={clsx(
                '!pb-0 md:!overflow-visible',
                isTouchDevice ? 'md:max-h-[calc(100dvh-112px)] md:basis-full' : 'md:basis-1/2',
              )}
              selectedBonus={selectedBonus}
              setSelectedBonus={setSelectedBonus}
              savedBonus={savedBonus}
              setSavedBonus={setSavedBonus}
              inProgressSavingBonus={inProgressSavingBonus}
              setInProgressSavingBonus={setInProgressSavingBonus}
            />

            {/*hasBonuses &&*/}
            {!isTouchDevice && (
              <StyledVerticalLine
                className='h-[calc(100%-36px)] overflow-visible max-md:hidden'
                color={theme.color?.general.dark}
              />
            )}
            <DepositByCryptoAddress
              activeCard={activeCard}
              address={wallet?.address || ''}
              amount={+amount}
              className={clsx(
                'max-md:!hidden md:!overflow-visible',
                isTouchDevice ? '!hidden' : '',
              )}
              // hasBonuses={hasBonuses}
              lng={lng}
              t={t}
              userCurrency={userCurrency}
            />
          </div>
          <div
            id='paymentButton'
            className={clsx(
              'fixed bottom-2 w-[calc(100%-16px)] md:w-[calc(100%-48px)]',
              isTouchDevice ? '' : 'md:hidden',
            )}
          >
            <Button
              text={t('toPayment')}
              fullWidth
              onClick={() => {
                setActiveTabPage && setActiveTabPage('payment');
              }}
              disabled={!+amount || (amountError && amountError !== 'enterMinAmountForBonus')}
            />
          </div>
        </div>
      )}
    </>
  );
};

export default DepositByCrypto;
