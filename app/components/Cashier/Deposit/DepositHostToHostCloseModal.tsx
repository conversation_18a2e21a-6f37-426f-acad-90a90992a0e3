'use client';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import ModalDialog from '@/app/components/ModalDialog';
import { closeModal } from '@/utils/closeModal.ts';

const DepositHostToHostCloseModal = ({
  setActiveTabPage,
  setResultScreenType,
}: {
  setActiveTabPage: Function;
  setResultScreenType: Function;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  return (
    <ModalDialog
      id='depositHostToHostCloseModal'
      title={t('warning')}
      text={t('ifYouMadePaymentDoNotCancel')}
      contentPaddings='max-sm:px-4 max-sm:pb-3 max-sm:pt-6 sm:p-6'
      closeButtonPosition='right-4 top-4 sm:right-6 sm:top-6'
      withButtons
      firstButtonText={t('back')}
      secondButtonText={t('cancelAnyway')}
      onClose={() => {
        closeModal('depositHostToHostCloseModal', false);
      }}
      onSecondButtonClick={() => {
        closeModal('depositHostToHostCloseModal', false);
        setActiveTabPage('paymentResult');
        setResultScreenType('reject');
      }}
    />
  );
};

export default DepositHostToHostCloseModal;
