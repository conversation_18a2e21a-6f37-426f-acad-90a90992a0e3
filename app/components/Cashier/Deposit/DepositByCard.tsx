'use client';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { getAmountInitial } from '@/app/components/Cashier/Deposit/DepositByCrypto.tsx';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import DepositInputsBlock from './DepositInputsBlock';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';

const DepositByCard = ({
  lng,
  activeCard,
  amountError,
  setAmountError,
  userCurrency,
  latestPaidAmount,
  bonuses,
  setActiveTabPage,
  selectedBonus,
  setSelectedBonus,
  savedBonus,
  setSavedBonus,
  inProgressSavingBonus,
  setInProgressSavingBonus,
}: {
  amountError: string;
  setAmountError: Function;
  lng: LanguagesType;
  activeCard: IMethod;
  userCurrency: string;
  latestPaidAmount?: number | undefined;
  bonuses?: IBonus[] | null;
  setActiveTabPage?: Function;
  selectedBonus: number;
  setSelectedBonus: Function;
  savedBonus: number | null;
  setSavedBonus: Function;
  inProgressSavingBonus: boolean;
  setInProgressSavingBonus: Function;
}) => {
  const { t } = useTranslation(lng);
  const { isTouchDevice } = useIsTouchMobileView();
  const [amount, setAmount] = useState(
    getAmountInitial(activeCard.amount_min, latestPaidAmount) || 0,
  );

  const hasBonuses = !!bonuses && bonuses.length > 0;

  const onPaymentClick = () => {};

  useEffect(() => {
    if (setActiveTabPage) {
      setActiveTabPage('depositByCard');
    }
  }, []);

  return (
    <div
      className={clsx(
        'depositContentWrapper flex flex-col',
        isTouchDevice ? 'mobileView' : '',
      )}
    >
      <Typography
        text={t('deposit')}
        type='h1'
        className='ml-6 max-md:!hidden'
      />
      <DepositInputsBlock
        lng={lng}
        activeCard={activeCard}
        amount={amount}
        setAmount={setAmount}
        amountError={amountError}
        setAmountError={setAmountError}
        userCurrency={userCurrency}
        bonuses={bonuses}
        hasBonuses={hasBonuses}
        className='md:mt-6'
        selectedBonus={selectedBonus}
        setSelectedBonus={setSelectedBonus}
        savedBonus={savedBonus}
        setSavedBonus={setSavedBonus}
        inProgressSavingBonus={inProgressSavingBonus}
        setInProgressSavingBonus={setInProgressSavingBonus}
      />
      <div
        id='paymentButton'
        className={clsx(
          !isTouchDevice &&
            'max-md:fixed max-md:bottom-2 max-md:w-[calc(100%-16px)]',
          isTouchDevice &&
            'fixed bottom-2 w-[calc(100%-16px)] md:w-[calc(100%-48px)]',
        )}
      >
        <Button
          text={t('toPayment')}
          fullWidth
          onClick={() => onPaymentClick()}
          disabled={
            !amount || (amountError && amountError !== 'enterMinAmountForBonus')
          }
        />
      </div>
    </div>
  );
};

export default DepositByCard;
