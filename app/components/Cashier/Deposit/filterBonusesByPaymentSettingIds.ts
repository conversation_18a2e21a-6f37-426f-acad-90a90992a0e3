import { IBonus } from '@/app/api/server-actions/bonuses.ts';

export const filterBonusesByPaymentSettingIds = (
  bonuses: IBonus[],
  currentMethodId: number,
  currentCascadeMethodsIds?: number[],
) => {
  const isMethodInBonus = (bonus: IBonus) =>
    currentMethodId && bonus.payment_setting_ids.includes(currentMethodId);
  const isCascadeInBonus = (bonus: IBonus) =>
    currentCascadeMethodsIds?.every((cascadeMethodId: number) =>
      bonus.payment_setting_ids.includes(cascadeMethodId),
    );

  return (
    bonuses?.filter(
      (bonus: IBonus) =>
        (!currentMethodId && !currentCascadeMethodsIds?.length) ||
        !bonus.payment_setting_ids?.length ||
        (currentCascadeMethodsIds?.length ? isCascadeInBonus(bonus) : isMethodInBonus(bonus)),
    ) || []
  );
};
