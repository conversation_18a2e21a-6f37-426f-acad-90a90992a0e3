'use client';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { changeUserSettings } from '@/app/api/server-actions/changeUserSettings.ts';
import BonusMoreInfoModal from '@/app/components/Bonuses/BonusMoreInfoModal.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import {
  Checkbox,
  Icon,
  Switch,
  Tooltip,
  Typography,
} from '@/atomic-design-components';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import capitalize from '@/utils/capitalize';
import { closeModal } from '@/utils/closeModal';
import { openModal } from '@/utils/openModal';
import { useCallback, useEffect, useRef, useState } from 'react';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const BonusesDepositBlock = ({
  savedBonus,
  lng,
  paymentMethodId,
  selectedBonus,
  // amount,
  // setAmount,
  setSelectedBonus,
  bonuses,
  currencySymbol,
  setSavedBonus,
  inProgressSavingBonus,
  setInProgressSavingBonus,
}: {
  amount: number;
  lng: LanguagesType;
  paymentMethodId: number;
  setAmount: Function;
  selectedBonus: number;
  setSelectedBonus: Function;
  bonuses: IBonus[];
  currencySymbol: string;
  savedBonus?: number | null;
  setSavedBonus?: Function;
  inProgressSavingBonus?: boolean;
  setInProgressSavingBonus?: Function;
}) => {
  const { t } = useTranslation(lng);
  const { showError } = useAlert();
  const { user } = useUser();
  const [openedBonus, setOpenedBonus] = useState<IBonus | null>(null);
  const ref = useRef<HTMLDivElement>(null);

  const hasActiveBonus = !!user?.active_bonus_id;
  // const selectedBonusIdFromLS = getFromLocalStorage('selectedBonusId');
  const { width } = useWindowSize();
  const { isTouchDevice } = useIsTouchMobileView();
  const isTouchDeviceMd =
    isTouchDevice || (!!width && width < theme.breakpoints?.md);

  useEffect(() => {
    if (savedBonus || selectedBonus) {
      document
        .getElementById(
          `deposit-bonus-${(savedBonus || selectedBonus).toString()}`,
        )
        ?.scrollIntoView({ behavior: 'instant', block: 'end' });
    }
  }, []);

  useEffect(() => {
    if (savedBonus === null || inProgressSavingBonus) {
      //while loading or the same payment method
      return;
    }

    if (hasActiveBonus) {
      setSelectedBonus(0);
      return;
    }

    if (savedBonus === selectedBonus) {
      if (
        savedBonus &&
        !bonuses?.some((bonus) => bonus.external_id === savedBonus)
      ) {
        onBonusChange(0);
      }
      return;
    }

    const newSelectedBonus = savedBonus || 0;

    if (
      newSelectedBonus &&
      !bonuses?.some((bonus) => bonus.external_id === newSelectedBonus)
    ) {
      //TODO: in this case we sent 4 requests, needs fix
      onBonusChange(0);
      return;
    }

    onBonusChange(newSelectedBonus);

    document
      .getElementById(`deposit-bonus-${newSelectedBonus.toString()}`)
      ?.scrollIntoView({ behavior: 'instant', block: 'end' });
  }, [
    savedBonus,
    selectedBonus,
    hasActiveBonus,
    bonuses,
    inProgressSavingBonus,
    paymentMethodId,
  ]);

  // useEffect(() => {
  //   if (
  //     selectedBonusData?.min_deposit_amount &&
  //     amount < selectedBonusData.min_deposit_amount
  //   ) {
  //     setAmount(selectedBonusData.min_deposit_amount);
  //   }
  //   console.log(amount, selectedBonusData?.min_deposit_amount);
  // }, [amount, selectedBonusData]);

  useEffect(() => {
    if (!openedBonus) {
      return;
    }
    openModal('bonusMoreInfoCashierModal', true, true, true);
  }, [openedBonus]);

  const onBonusChange = useCallback(
    (bonusId: number) => {
      if (savedBonus === bonusId) {
        if (selectedBonus !== bonusId) {
          setSelectedBonus(bonusId);
        }
        return;
      }

      const prevSelectedBonus = selectedBonus;
      const prevSavedBonus = savedBonus;

      if (setInProgressSavingBonus) {
        setInProgressSavingBonus(true);
        setSelectedBonus(bonusId || 0);
        setSavedBonus && setSavedBonus(bonusId || 0);
        changeUserSettings({ bonusId: bonusId })
          .then((result: any) => {
            if (!result || result.detail) {
              showError(result.detail?.message || result.detail || t('error'));
              setInProgressSavingBonus(false);
              setSelectedBonus(prevSelectedBonus);
              setSavedBonus && setSavedBonus(prevSavedBonus);
              return;
            } else {
              setInProgressSavingBonus(false);
            }
          })
          .catch((e) => {
            console.error(e);
            setInProgressSavingBonus(false);
            setSelectedBonus(prevSelectedBonus);
            setSavedBonus && setSavedBonus(prevSavedBonus);
          })
          .finally(() => {
            setInProgressSavingBonus(false);
          });
        setInProgressSavingBonus(false);
      }
    },
    [savedBonus, selectedBonus],
  );

  const isDisabled = hasActiveBonus || inProgressSavingBonus;

  return (
    <>
      <div className='bonusesWrapper flex min-h-[236px] w-full flex-col items-start gap-2'>
        <div className='flex w-full justify-between'>
          <Typography
            iconName='giftPromo'
            iconProps={{
              width: 16,
              height: 16,
              fill: theme.color?.general.lightest,
            }}
            text={t('bonus')}
            type='h3'
          />
          {!hasActiveBonus && (
            <Switch
              checked={hasActiveBonus || selectedBonus === 0}
              disabled={isDisabled}
              inProgress={setInProgressSavingBonus}
              id='isBonus'
              leftLabel={t('dontUseBonus')}
              labelProps={{
                textTransform: 'none',
                margin: '0 auto 0 0',
                className: 'text-nowrap',
              }}
              name='useBonus'
              onChange={(selected: boolean) => {
                onBonusChange(selected ? 0 : bonuses[0].external_id);
              }}
            />
          )}
        </div>

        {hasActiveBonus && (
          <div className='flex w-full items-start gap-2 rounded-lg bg-general-dark px-2 py-2'>
            <Icon name='infoIcon' fill={theme.color?.secondary.dark} />
            <Typography
              text={t('bonusActivationNotPossibleWhileActiveBonus')}
            />
          </div>
        )}

        <div
          className='bonusesList flex h-full w-full flex-col items-start gap-2 pb-3'
          ref={ref}
        >
          {bonuses.map((bonus: IBonus, i: number) => {
            const bonusType = bonus?.bonus_type;
            // bonus?.bonus_type === 'wager_freespins_deposit' &&
            // bonus?.bonus_global_type === 'freespins'
            //   ? 'freespins_no_deposit'
            //   : bonus?.bonus_type;

            const isWager = bonusType?.startsWith('wager');
            const isFreespins = bonusType === 'freespins_deposit';

            const betPerSpin =
              (bonus &&
                bonusType &&
                bonusType.includes('freespins') &&
                (isFreespins
                  ? (bonus.min_deposit_amount *
                      (bonus.deposit_amount_percentage / 100)) /
                    bonus.freespins_amount
                  : bonus.bonus_amount / bonus.freespins_amount)) ||
              0;

            const bonusForTipText =
              bonus.bonus_type === 'wager_deposit' ||
              bonus.bonus_type === 'wager_freespins_deposit'
                ? `${t('forBonus')} + ${t('deposit').toLowerCase()}`
                : t('forBonus');
            return (
              <div
                id={`deposit-bonus-${bonus.external_id.toString()}`}
                role='button'
                tabIndex={0}
                className='checkboxContainer relative'
                style={{ cursor: isDisabled ? 'not-allowed' : 'pointer' }}
                key={bonus.external_id}
                onClick={() => {
                  if (isDisabled) {
                    return;
                  }
                  onBonusChange(bonus.external_id);
                }}
              >
                {!hasActiveBonus && (
                  <Checkbox
                    checked={selectedBonus === bonus.external_id}
                    handleChange={() => {
                      if (isDisabled) {
                        return;
                      }
                      onBonusChange(bonus.external_id);
                    }}
                    value={bonus.external_id}
                    name='bonus1'
                    type='radio'
                    variant='secondary'
                    disabled={isDisabled}
                  />
                )}
                <div className='flex w-full flex-col'>
                  {isFreespins ? (
                    <Typography
                      type='sub2'
                      color={
                        hasActiveBonus
                          ? theme.color?.general.lightest
                          : theme.color?.secondary.main
                      }
                    >
                      {bonus.freespins_amount}FS x {currencySymbol}
                      {addSpacesToLongNumbers(betPerSpin)}
                    </Typography>
                  ) : (
                    <Typography
                      type='sub2'
                      color={
                        hasActiveBonus
                          ? theme.color?.general.lightest
                          : theme.color?.secondary.main
                      }
                    >
                      {bonus.deposit_amount_percentage}% {t('upTo')}{' '}
                      {currencySymbol}
                      {addSpacesToLongNumbers((bonus.deposit_amount_percentage / 100) *
                        bonus.max_deposit_amount)}
                      {!!bonus.freespins_amount && (
                        <span className='pl-1'>
                          {' '}
                          + {bonus.freespins_amount}FS
                        </span>
                      )}
                    </Typography>
                  )}
                  <Typography color={theme.color?.general.lighter}>
                    {t('minDepositOf')} {currencySymbol}
                    {addSpacesToLongNumbers(bonus.min_deposit_amount)}
                  </Typography>
                </div>
                {!isTouchDeviceMd && (
                  <Tooltip
                    text={
                      <div>
                        <div className='w:[230px] overflow-hidden text-ellipsis whitespace-nowrap text-[16px] font-bold text-secondary-main'>
                          {getAvailableTranslation(bonus?.title || {}, lng) ||
                            bonus?.name ||
                            ''}
                        </div>
                        {bonus.game_name && (
                          <>
                            <span className='text-general-lighter'>
                              {t('game')}:{' '}
                            </span>
                            <span className='font-bold'>{bonus.game_name}</span>
                            <br />
                          </>
                        )}
                        {bonus.game_provider_slug && (
                          <>
                            <span className='text-general-lighter'>
                              {t('provider')}:{' '}
                            </span>
                            <span className='font-bold'>
                              {capitalize(
                                bonus.game_provider_slug.replace(/_/g, ' '),
                              )}
                            </span>
                            <br />
                          </>
                        )}
                        {isWager && (
                          <>
                            <span className='text-general-lighter'>
                              {t('bonusAmountInPercent')}:{' '}
                            </span>
                            <span className='font-bold'>
                              {bonus.deposit_amount_percentage}%
                            </span>
                            <br />
                          </>
                        )}
                        {!isFreespins && (
                          <>
                            <span className='text-general-lighter'>
                              {' '}
                              {t('maxBonusAmount')}:
                            </span>{' '}
                            <span className='font-bold'>
                              {currencySymbol}
                              {addSpacesToLongNumbers((bonus.deposit_amount_percentage / 100) *
                                bonus.max_deposit_amount)}
                            </span>
                            <br />{' '}
                          </>
                        )}
                        {!!bonus.freespins_amount && (
                          <>
                            <span className='text-general-lighter'>
                              {t('quantity')}:
                            </span>{' '}
                            <span className='font-bold'>
                              {bonus.freespins_amount}FS
                            </span>
                            <br />{' '}
                          </>
                        )}
                        {!!betPerSpin && (
                          <>
                            <span className='text-general-lighter'>
                              {t('betAmount')}:
                            </span>{' '}
                            <span className='font-bold'>
                              {currencySymbol}
                              {addSpacesToLongNumbers(betPerSpin)}
                            </span>
                            <br />{' '}
                          </>
                        )}
                        <span className='text-general-lighter'>
                          {t('wager')}:
                        </span>{' '}
                        <span className='font-bold'>
                          x{bonus.wager_multiplier} {bonusForTipText}
                        </span>
                      </div>
                    }
                    textVariant='body1'
                    textLineHeight='21px'
                    whiteSpace='wrap'
                    textAlign='left'
                    right='25px'
                    top={
                      i === 0
                        ? '-15px'
                        : i === bonuses.length - 1
                          ? '-40px'
                          : isWager
                            ? '-30px'
                            : '-50px'
                    }
                    padding='8px 8px 4px 16px'
                    textColor={theme.color?.general.lightest}
                  >
                    <Icon
                      name='infoIconEmpty'
                      stroke={theme.color?.general.lighter}
                      margin='5px 0 0 0'
                    />
                  </Tooltip>
                )}
                {isTouchDeviceMd && (
                  <Icon
                    name='infoIconEmpty'
                    fill={theme.color?.general.lighter}
                    onClick={(e: any) => {
                      e.stopPropagation();
                      e.preventDefault();
                      setOpenedBonus(bonus);
                    }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
      {isTouchDeviceMd && (
        <BonusMoreInfoModal
          openedBonus={openedBonus}
          onBonusChange={
            hasActiveBonus || openedBonus?.external_id === selectedBonus
              ? undefined
              : onBonusChange
          }
          id='bonusMoreInfoCashierModal'
          onClose={() => {
            closeModal('bonusMoreInfoCashierModal');
            setOpenedBonus(null);
          }}
        />
      )}
    </>
  );
};

export default BonusesDepositBlock;
