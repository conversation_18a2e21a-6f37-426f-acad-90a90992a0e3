import clsx from 'clsx';
import styled from 'styled-components';
import { Button, Typography } from '@/atomic-design-components';
import capitalize from '@/utils/capitalize.ts';
import LiveSupportButton from '@/app/components/LiveSupportButton';
import { StyledPaymentWindow } from '@/app/components/Cashier/styled.ts';

const StyledButtons = styled.div`
  button span {
    font-size: 14px;
    line-height: 16px;
    font-weight: 600;
  }
`;

const DepositHostToHostResult = ({
  // copy,
  isTouchDevice,
  t,
  type,
  setActiveScreen,
  setActiveTabPage,
}: {
  copy: Function;
  isTouchDevice: boolean;
  t: Function;
  type: 'success' | 'reject';
  setActiveScreen: Function;
  setActiveTabPage: Function;
}) => {
  const titleKey = 'p2pPayment' + capitalize(type) + 'Title';
  const textKey = 'p2pPayment' + capitalize(type) + 'Text';
  return (
    <div
      className={clsx(
        'p2pPaymentResult flex h-[calc(100dvh-174px)] flex-col  md:px-2',
        isTouchDevice
          ? 'mobileView md:h-[calc(100dvh-122px)] md:px-6'
          : 'md:mt-[70px] md:h-[370px] md:max-h-full',
      )}
    >
      <Typography
        text={t(titleKey)}
        type='h2'
        className={clsx(
          'absolute left-[6px] top-[11px] z-10 md:left-5 md:top-6',
          isTouchDevice ? 'md:top-[11px]' : '',
        )}
        lineHeight='32px'
        fontSize='24px'
        // onClick={() => {
        //   if (setActiveTabPage) {
        //     setActiveTabPage('depositByPtoP');
        //   }
        // }}
      />
      <StyledPaymentWindow
        flexDirection='column'
        alignItems='center'
        gap='8px'
        className={clsx(
          'paymentWindow h-full overflow-auto !pt-0 md:!basis-auto',
          isTouchDevice && 'mobileView',
        )}
      >
        {type === 'success' && (
          <div className='my-4 flex h-[100px] w-[100px] items-center justify-center rounded-full bg-gray-800'>
            <span className='ml-1 mt-1 text-5xl font-bold text-white'>
              &#10004;
            </span>
          </div>
        )}
        {type === 'reject' && (
          <div className='my-4 flex h-[100px] w-[100px] items-center justify-center rounded-full bg-gray-800'>
            <span className='mt-1 text-5xl font-bold text-white'>!</span>
          </div>
        )}
        {/*<Typography type='caption2' text={t('transactionNumber')} />*/}
        <Typography text={t(textKey)} type='body2' textAlign='center' />
      </StyledPaymentWindow>

      <StyledButtons
        className={clsx(
          'mx-0 mt-2 px-0 max-md:fixed max-md:bottom-2 max-md:w-[calc(100%-16px)] md:mt-auto',
          isTouchDevice
            ? 'fixed bottom-2 w-[calc(100%-16px)] md:bottom-5 md:w-[calc(100%-48px)]'
            : 'md:mx-[17px] md:mb-5',
        )}
      >
        <LiveSupportButton t={t} withBorder fullWidth />
        <Button
          onClick={() => {
            setActiveTabPage();
            setActiveScreen('paymentMethodsAll');
          }}
          text={<Typography text={t('back')} type='body2' />}
          size='medium'
          fullWidth
          className='mt-2'
        />
      </StyledButtons>
    </div>
  );
};

export default DepositHostToHostResult;
