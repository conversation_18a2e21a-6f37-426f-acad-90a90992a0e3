'use client';
import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import Commissions from '@/app/components/Cashier/components/Commissions.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Button, Input, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import clsx from 'clsx';
import { useEffect, useRef } from 'react';
import { StyledDepositInputsBlock } from '../styled';
import BonusesDepositBlock from './BonusesDepositBlock';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

export const AMOUNTS_BY_CURRENCY = {
  USD: [5, 10, 20, 30, 50, 100, 150],
  EUR: [5, 10, 20, 30, 50, 100, 150],
  USDT: [5, 10, 20, 30, 50, 100, 150],
  UZS: [50000, 100000, 200000, 300000, 500000, 1000000, 1500000],
  KZT: [2500, 5000, 10000, 15000, 25000, 50000, 75000],
  RUB: [500, 1000, 2000, 3000, 5000, 10000, 15000],
};

const DepositInputsBlock = ({
                              activeCard,
                              amountError,
                              savedBonus,
                              setAmountError,
                              amount,
                              children,
                              setAmount,
                              lng,
                              userCurrency,
                              bonuses,
                              hasBonuses,
                              className,
                              selectedBonus,
                              setSelectedBonus,
                              setSavedBonus,
                              inProgressSavingBonus,
                              setInProgressSavingBonus,
                            }: {
  activeCard: IMethod;
  amountError: string;
  children?: any;
  lng: LanguagesType;
  amount: number;
  setAmount: Function;
  setAmountError: Function;
  userCurrency?: string;
  bonuses?: IBonus[] | null;
  hasBonuses: boolean;
  className?: string;
  savedBonus?: number | null;
  selectedBonus: number;
  setSelectedBonus: Function;
  setSavedBonus?: Function;
  inProgressSavingBonus?: boolean;
  setInProgressSavingBonus?: Function;
}) => {
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const hasActiveBonus = !!user?.active_bonus_id;
  const { isTouchDevice } = useIsTouchMobileView();
  const inputRef = useRef<HTMLInputElement>(null);
  const selectedBonusData = bonuses?.find(
    (bonus) => bonus.external_id === selectedBonus,
  );

  const maxPossibleBonusAmount =
    selectedBonusData &&
    selectedBonusData?.max_deposit_amount *
    (selectedBonusData?.deposit_amount_percentage / 100);

  const bonusAmount = selectedBonusData
    ? amount * (selectedBonusData?.deposit_amount_percentage / 100)
    : 0;

  const min = activeCard!.amount_min;
  const max = activeCard!.amount_max;

  const getBonusAmountFinal = () => {
    if (!selectedBonusData || !bonusAmount || !amount || amountError) return 0;

    if (selectedBonusData.bonus_type === 'freespins_deposit') {
      return selectedBonusData.freespins_amount
        ? `${selectedBonusData.freespins_amount}FS`
        : '';
    }

    if (!maxPossibleBonusAmount)
      return `+${addSpacesToLongNumbers(Math.round(bonusAmount * 100) / 100)}`;

    if (bonusAmount > maxPossibleBonusAmount) {
      return `+${addSpacesToLongNumbers(maxPossibleBonusAmount)}`;
    }

    return `+${addSpacesToLongNumbers(Math.round(bonusAmount * 100) / 100)}`;
  };

  useEffect(() => {
    const minAmountError = amount < min && 'enterMinAmount';
    const maxAmountError = max && amount > max && 'amountExceedsDepositLimit';
    const minAmountForBonusError =
      selectedBonusData &&
      amount < selectedBonusData?.min_deposit_amount &&
      'enterMinAmountForBonus';

    const error = minAmountError || maxAmountError || minAmountForBonusError;

    if (error) {
      setAmountError(error);
    } else {
      setAmountError('');
    }
  }, [amount, selectedBonusData]);

  const currencySymbol = getCurrencySymbol(
    userCurrency as keyof typeof getCurrencySymbol,
  );

  const amountArr = (
    AMOUNTS_BY_CURRENCY[userCurrency as keyof typeof AMOUNTS_BY_CURRENCY] ||
    AMOUNTS_BY_CURRENCY.USD
  ).filter(
    (amountBtnValue) =>
      (!activeCard?.amount_min || amountBtnValue >= min) &&
      (!activeCard?.amount_max || amountBtnValue < max), // if amountBtnValue === amount_max, we use MAX button for it
  );

  return (
    <StyledDepositInputsBlock
      className={clsx(
        className,
        'horizontal hasBonuses', // hasBonuses &&,
        'flex w-full flex-col items-start gap-4 max-md:h-dvh',
        activeCard.payment_type,
        isTouchDevice ? 'mobileView' : '',
      )}
    >
      <div className="flex w-full flex-col items-start gap-2">
        <Typography iconName="coins" text={t('chooseTopAmount')} type="h3" />
        <div className="flex w-full flex-col items-start gap-2">
          <div className="relative flex w-full gap-2">
            <Input
              ref={inputRef}
              name="amount"
              key="amount"
              type="number"
              onChange={(value: number) => {
                setAmount(value);
              }}
              onFocus={() => {
                inputRef.current?.select();
              }}
              value={amount}
              labelTop={t('amount')}
              labelBottom={
                <Typography type="label2">
                  Min - {currencySymbol}
                  {addSpacesToLongNumbers(min)}
                  {!!max && (
                    <span>
                      , Max - {currencySymbol}
                      {addSpacesToLongNumbers(max)}
                    </span>
                  )}
                </Typography>
              }
              fullWidth
              className="amountInput"
              error={
                amountError &&
                t(amountError, {
                  amount:
                    currencySymbol +
                    (amountError === 'enterMinAmount'
                      ? addSpacesToLongNumbers(min)
                      : amountError === 'amountExceedsDepositLimit'
                        ? addSpacesToLongNumbers(max)
                        : amountError === 'enterMinAmountForBonus'
                          ? selectedBonusData?.min_deposit_amount
                          : ''),
                })
              }
              withBorder
              // disabled={inProgress}
            />
            {/*{hasBonuses && (*/}
            <Input
              name="bonusAmount"
              key="bonusAmount"
              value={hasActiveBonus ? 0 : getBonusAmountFinal()}
              labelTop={t('bonus')}
              readOnly={true}
              className="bonusInput basis-2/5"
              iconRightProps={{
                name: 'giftPromo',
                fill: 'white',
                width: 16,
                height: 16,
                wrapperColor: theme.color?.secondary.dark,
                wrapperWidth: 24,
                wrapperHeight: 24,
                borderRadius: '50%',
              }}
            />
            {/*)}*/}
          </div>

          <div
            className={clsx(
              'flex w-full flex-wrap gap-2',
              amountError && 'amountButtonsWithError',
              amountArr.some((amount) => amount.toString().length >= 6) &&
              'longAmounts',
            )}
          >
            {(amountArr.length >= 7 ? amountArr.slice(0, 6) : amountArr).map(
              (item: number) => {
                const stringAmount = addSpacesToLongNumbers(item);
                return (
                  <Button
                    key={item}
                    onClick={() => setAmount(item)}
                    className="amountButton"
                    variant="secondary"
                  >
                    <Typography
                      text={stringAmount}
                      type="sub3"
                      fontSize={theme.font.size.caption2.value}
                      lineHeight="14px"
                    />
                  </Button>
                );
              },
            )}
            {!!max && (
              <Button
                key="max"
                onClick={() => setAmount(max)}
                className="amountButton"
                variant="secondary"
              >
                <Typography
                  text="MAX"
                  type="sub3"
                  fontSize={theme.font.size.caption2.value}
                  lineHeight="14px"
                />
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="flex w-full flex-col">
        <Typography>
          {getAvailableTranslation(activeCard?.comment, lng)}
        </Typography>
        <Commissions
          t={t}
          activeCard={activeCard!}
          currencySymbol={currencySymbol}
        />
      </div>
      {hasBonuses && (
        <BonusesDepositBlock
          savedBonus={savedBonus}
          paymentMethodId={activeCard.id}
          lng={lng}
          selectedBonus={selectedBonus}
          setSelectedBonus={setSelectedBonus}
          bonuses={bonuses || []}
          currencySymbol={currencySymbol}
          amount={amount}
          setAmount={setAmount}
          setSavedBonus={setSavedBonus}
          inProgressSavingBonus={inProgressSavingBonus}
          setInProgressSavingBonus={setInProgressSavingBonus}
        />
      )}
      {children}
    </StyledDepositInputsBlock>
  );
};

export default DepositInputsBlock;
