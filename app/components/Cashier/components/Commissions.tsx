import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';
import { Typography } from '@/atomic-design-components';
import i18next from 'i18next';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

const Commissions = ({
  activeCard,
  currencySymbol,
  t,
}: {
  activeCard: IMethod;
  currencySymbol: string;
  t: typeof i18next.t;
}) => {
  const { fee_percent_user, fee_fixed_user } = activeCard!;

  if (!fee_percent_user && !fee_fixed_user) {
    return <Typography>{t('commission')} 0%</Typography>;
  }

  if (!fee_percent_user || !fee_fixed_user) {
    return (
      <Typography>
        {t('commission')}&nbsp;
        {fee_percent_user ? (
          <span>{fee_percent_user}%</span>
        ) : (
          <span>
            {currencySymbol} {addSpacesToLongNumbers(fee_fixed_user)}
          </span>
        )}
      </Typography>
    );
  }

  return (
    <Typography>
      {t('commission')} {fee_percent_user}% + {t('fixed')} {currencySymbol}
      {fee_fixed_user}
    </Typography>
  );
};

export default Commissions;
