'use client';
import { IWallet } from '@/app/api/crypto/getWallets.ts';
import {
  IMethod,
  IMethodGroup,
  IPaymentCard,
} from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import {
  CashierTabPageType,
  CashierTabScreenType,
  CashierTabType,
} from '@/app/components/Cashier/CashierModal.tsx';
import PaymentActionScreen from '@/app/components/Cashier/components/PaymentActionScreen.tsx';
import PaymentMethodsAllList from '@/app/components/Cashier/components/PaymentMethodsAllList.tsx';
import PaymentMethodsInGroupList from '@/app/components/Cashier/components/PaymentMethodsInGroupList.tsx';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { FlexRow } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import { StyledDepositOrWithdrawalWrapper } from '../styled';
import LoadingPaymentMethods from '@/app/components/Skeletons/LoadingPaymentMethods.tsx';
import LoadingDepositMethod from '@/app/components/Skeletons/LoadingDepositMethod.tsx';
import { useEffect, useState } from 'react';
import { getUserSettingsClient } from '@/app/api/user/getUserSettingsClient.ts';

const DepositOrWithdrawalWrapper = ({
  lng,
  activeTab,
  activeScreen,
  setActiveScreen,
  activeCard,
  activeTabPage,
  setActiveTabPage,
  featuredPaymentMethods,
  paymentMethods,
  setActiveCard,
  wallets,
  setWallets,
  bonuses,
  bonusForTip,
  inProgress,
  isDialogOpen,
}: {
  lng: LanguagesType;
  activeTab: CashierTabType;
  activeScreen: CashierTabScreenType;
  setActiveScreen: any;
  activeCard: IPaymentCard | null;
  featuredPaymentMethods: IMethod[];
  paymentMethods: IPaymentCard[];
  setActiveCard: Function;
  activeTabPage?: CashierTabPageType;
  setActiveTabPage: Function;
  wallets?: IWallet[] | null;
  setWallets?: Function;
  bonuses?: IBonus[] | null;
  bonusForTip?: IBonus | null;
  inProgress?: boolean;
  isDialogOpen: boolean;
}) => {
  const { user } = useUser();
  const inProgressPaymentMethods = inProgress && !user?.total_deposits_qty;
  const inProgressDepositMethod = inProgress && user?.total_deposits_qty;

  const [savedBonus, setSavedBonus] = useState<number | null>(null);
  const [inProgressSavingBonus, setInProgressSavingBonus] =
    useState<boolean>(true);

  useEffect(() => {
    if (isDialogOpen) {
      setInProgressSavingBonus(true);
      getUserSettingsClient()
        .then((result) => {
          setSavedBonus(result.deposit_bonus_external_id || 0);
        })
        .catch(() => {
          setSavedBonus(0);
        });
      setInProgressSavingBonus(false);
    }
  }, [isDialogOpen]);

  const wallet = wallets?.find(
    (el: IWallet) =>
      activeCard &&
      'currency_platform' in activeCard &&
      el.currency === activeCard?.currency_platform &&
      el.payment_setting_id === activeCard?.id,
  );

  return (
    <StyledDepositOrWithdrawalWrapper
      alignItems='start'
      className={activeScreen}
    >
      <FlexRow
        alignItems='stretch'
        flexDirection='column'
        className='tabsContainer'
      >
        {activeScreen === 'paymentMethodsAll' &&
          (inProgressPaymentMethods ? (
            <LoadingPaymentMethods withHeaderText={!user?.active_bonus_id} />
          ) : (
            <PaymentMethodsAllList
              bonusForTip={bonusForTip}
              cryptoMethodsData={paymentMethods.filter(
                (el) => 'payment_type' in el && el.payment_type === 'crypto',
              )}
              notCryptoMethodsData={paymentMethods.filter(
                (el) => 'payment_type' in el && el.payment_type !== 'crypto',
              )}
              setActiveScreen={setActiveScreen}
              setActiveCard={setActiveCard}
              isWithdrawal={activeTab === 'withdrawal'}
              userCurrency={user!.currency}
              withdrawalMultiplier={user!.withdrawal_multiplicator}
            />
          ))}
        {activeScreen === 'paymentMethodsInGroup' && (
          <PaymentMethodsInGroupList
            data={
              (
                paymentMethods.find(
                  (el) => 'methods' in el && el.group === activeCard?.group,
                ) as IMethodGroup
              )?.methods
            }
            setActiveScreen={setActiveScreen}
            setActiveCard={setActiveCard}
            activeTab={activeTab}
            userCurrency={user!.currency}
          />
        )}
        {activeScreen === 'paymentAction' &&
          (inProgressDepositMethod ? (
            <LoadingDepositMethod />
          ) : (
            activeCard && (
              <PaymentActionScreen
                lng={lng}
                type={activeTab}
                activeCard={activeCard as IMethod}
                wallet={wallet}
                isGetWalletsInProgress={wallets === null}
                setActiveScreen={setActiveScreen}
                setActiveCard={setActiveCard}
                setWallets={setWallets}
                featuredPaymentMethods={featuredPaymentMethods}
                bonuses={bonuses}
                activeTabPage={activeTabPage}
                setActiveTabPage={setActiveTabPage}
                savedBonus={savedBonus}
                setSavedBonus={setSavedBonus}
                inProgressSavingBonus={inProgressSavingBonus}
                setInProgressSavingBonus={setInProgressSavingBonus}
              />
            )
          ))}
      </FlexRow>
    </StyledDepositOrWithdrawalWrapper>
  );
};

export default DepositOrWithdrawalWrapper;
