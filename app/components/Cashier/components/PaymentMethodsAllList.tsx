'use client';
import { IPaymentCard } from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import AvailableWithdrawalAmount from '@/app/components/Cashier/Withdrawal/AvailableWithdrawalAmount.tsx';
import { useTranslation } from '@/app/i18n/client';
import { TilesGrid, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol.ts';
import clsx from 'clsx';
import parse from 'html-react-parser';
import { useParams } from 'next/navigation';
import { StyledLine } from '../../NavMenu/styled';
import PaymentMethodCard from './PaymentMethodCard';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

export const PAYMENT_CARDS_BREAKPOINTS = {
  '(min-width: 1060px)': {
    perView: 4,
    spacing: 8,
  },
  '(max-width: 1060px)': {
    perView: 3,
    spacing: 8,
  },
  '(max-width: 760px)': {
    perView: 2,
    spacing: 8,
  },
};

const PaymentMethodsAllList = ({
  bonusForTip,
  setActiveCard,
  cryptoMethodsData,
  notCryptoMethodsData,
  setActiveScreen,
  isWithdrawal,
  userCurrency,
  withdrawalMultiplier,
}: {
  bonusForTip?: IBonus | null;
  setActiveCard: Function;
  cryptoMethodsData: IPaymentCard[];
  notCryptoMethodsData: IPaymentCard[];
  setActiveScreen: any;
  isWithdrawal?: boolean;
  userCurrency: string;
  withdrawalMultiplier?: number;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const isMethodsDisabled = isWithdrawal && !!withdrawalMultiplier;

  const getHeaderText = () => {
    if (isWithdrawal) {
      if (withdrawalMultiplier) {
        const currencySymbol = getCurrencySymbol(userCurrency);
        return (
          <div className='mb-2 flex items-center justify-center gap-2 rounded-lg bg-general-dark px-2 py-2 text-center md:mb-4'>
            <Typography
              text={parse(
                t('depositNeedsToBeWagered', {
                  amount: `${currencySymbol}${addSpacesToLongNumbers(withdrawalMultiplier)}`,
                }),
              )}
              className='!block'
              type='sub1'
            />
          </div>
        );
      }
      return <AvailableWithdrawalAmount className='mb-2  justify-center md:mb-4' t={t} />;
    }

    if (!bonusForTip) {
      return null;
    }
    // parse("depositToActivate": "Deposit <span className='orangeText'>{{amount}}</span> to activate your bonus.",
    const depositTipKey =
      bonusForTip.bonus_type === 'wager_freespins_deposit'
        ? 'depositToActivateBoth'
        : bonusForTip?.bonus_type === 'wager_deposit'
          ? 'depositToActivateWager'
          : 'depositToActivateFreespins';

    return (
      <div className='mb-2 flex min-h-10 w-full flex-col items-center justify-center rounded-lg bg-[#334155] p-2 text-center md:mb-4'>
        <Typography
          text={t(depositTipKey, {
            depositPercent: `${bonusForTip?.deposit_amount_percentage}%`,
            maxAmount: addSpacesToLongNumbers(
              (bonusForTip.max_deposit_amount * bonusForTip.deposit_amount_percentage) / 100,
            ),
            currency: getCurrencySymbol(userCurrency),
            freespins: bonusForTip?.freespins_amount,
          })}
        />
        {/*<Typography>*/}
        {/*  {t('ifChangeYourMindCancelBonus')}*/}
        {/*  <Link href='/' className='underlineLink ml-[4px]'>*/}
        {/*    {t('here')}*/}
        {/*  </Link>*/}
        {/*</Typography>*/}
      </div>
    );
  };

  return (
    <div className='paymentMethodsAllList'>
      {getHeaderText()}
      <TilesGrid
        itemsInRow={4}
        breakpoints={PAYMENT_CARDS_BREAKPOINTS}
        rowGap='8px'
        className={clsx('', isMethodsDisabled && 'disabled', isWithdrawal && 'withdrawalMethods')}
        tiles={notCryptoMethodsData.map((method: IPaymentCard, index: number) => (
          <PaymentMethodCard
            item={method}
            key={index}
            index={index}
            setActiveCard={setActiveCard}
            setActiveScreen={setActiveScreen}
            isWithdrawal={isWithdrawal}
            userCurrency={userCurrency}
            isDisabled={isMethodsDisabled}
          />
        ))}
      />
      <StyledLine color={theme.color?.general.darker} className='heavy' />
      <TilesGrid
        itemsInRow={4}
        breakpoints={PAYMENT_CARDS_BREAKPOINTS}
        rowGap='8px'
        className={clsx(isMethodsDisabled && 'disabled', isWithdrawal && 'withdrawalMethods')}
        tiles={cryptoMethodsData.map((method: IPaymentCard, index: number) => (
          <PaymentMethodCard
            item={method}
            key={index}
            index={index}
            setActiveCard={setActiveCard}
            setActiveScreen={setActiveScreen}
            isWithdrawal={isWithdrawal}
            userCurrency={userCurrency}
            isDisabled={isMethodsDisabled}
          />
        ))}
      />
    </div>
  );
};

export default PaymentMethodsAllList;
