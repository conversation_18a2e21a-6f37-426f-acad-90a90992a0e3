import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';
import { Typography } from '@/atomic-design-components';
import clsx from 'clsx';
import i18next from 'i18next';
import getWithdrawalIntervals from '@/app/components/Cashier/components/getWithdrawalIntervals.ts';

const AverageProcessingTime = ({
  activeCard,
  className,
  t,
}: {
  activeCard: IMethod;
  className?: string;
  t: typeof i18next.t;
}) => {
  const withdrawalDuration = getWithdrawalIntervals(activeCard, t);

  return (
    <Typography className={clsx('processingTime w-full', className)}>
      <span>
        {t('averageWithdrawalProcessing')}
        {withdrawalDuration}
      </span>
    </Typography>
  );
};

export default AverageProcessingTime;
