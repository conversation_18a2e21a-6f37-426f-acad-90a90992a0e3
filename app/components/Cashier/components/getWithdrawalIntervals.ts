import { IMethod } from '@/app/api/payments/getPaymentDataClient.ts';

const getWithdrawalIntervals = (activeCard: IMethod, t: Function) => {
  const { withdrawal_interval_minutes_max, withdrawal_interval_minutes_min } =
    activeCard;
  const timeToShowMax =
    withdrawal_interval_minutes_max > 59
      ? Math.round((withdrawal_interval_minutes_max / 60) * 10) / 10
      : withdrawal_interval_minutes_max;

  const timeToShowMin =
    withdrawal_interval_minutes_min > 59
      ? Math.round((withdrawal_interval_minutes_min / 60) * 10) / 10
      : withdrawal_interval_minutes_min;

  if (!withdrawal_interval_minutes_min && !!withdrawal_interval_minutes_max) {
    return `${timeToShowMax} ${t(
      withdrawal_interval_minutes_max > 59 ? 'hour' : 'minute',
      {
        count: +timeToShowMax,
      },
    )}`;
  }

  if (!!withdrawal_interval_minutes_min && !!withdrawal_interval_minutes_max) {
    return `${timeToShowMin}-${timeToShowMax} ${t(
      withdrawal_interval_minutes_max > 59 ? 'hour' : 'minute',
      {
        count: +timeToShowMax,
      },
    )}`;
  }

  return '';
};
export default getWithdrawalIntervals;
