'use client';
import { IWallet } from '@/app/api/crypto/getWalletsClient.ts';
import { IMethod, IPaymentCard } from '@/app/api/payments/getPaymentDataClient.ts';
import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { CashierTabPageType, CashierTabType } from '@/app/components/Cashier/CashierModal.tsx';
import DepositByCard from '@/app/components/Cashier/Deposit/DepositByCard.tsx';
import DepositByCrypto from '@/app/components/Cashier/Deposit/DepositByCrypto.tsx';
import DepositByPtoP from '@/app/components/Cashier/Deposit/DepositByPtoP.tsx';
import { filterBonusesByPaymentSettingIds } from '@/app/components/Cashier/Deposit/filterBonusesByPaymentSettingIds.ts';
import { StyledVerticalTabs } from '@/app/components/Cashier/styled.ts';
import WithdrawalByCard from '@/app/components/Cashier/Withdrawal/WithdrawalByCard.tsx';
import WithdrawalByCrypto from '@/app/components/Cashier/Withdrawal/WithdrawalByCrypto.tsx';
import WithdrawalByPtoP from '@/app/components/Cashier/Withdrawal/WithdrawalByPtoP.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Typography, Image } from '@/atomic-design-components';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';

const getRecentOrFeaturedPaymentMethods = (
  featuredPaymentMethods: IMethod[],
  activeCard: IMethod,
) => {
  return featuredPaymentMethods
    .filter((method) =>
      activeCard.cascadeId
        ? activeCard.cascadeId !== method.cascadeId
        : method.id !== activeCard.id,
    )
    .slice(0, 2);
};

export const getAmountWithFeesApplied = (
  amount: number,
  feePercentUser: number = 0,
  feeFixedUser: number = 0,
  exchangeFeePercent: number = 0,
) => {
  return (amount * (1 + feePercentUser / 100) + feeFixedUser) * (1 + exchangeFeePercent / 100);
};

const PaymentActionScreen = ({
  activeCard,
  featuredPaymentMethods,
  lng,
  type,
  isGetWalletsInProgress,
  wallet,
  setWallets,
  setActiveCard,
  setActiveScreen,
  activeTabPage,
  setActiveTabPage,
  bonuses,
  savedBonus,
  setSavedBonus,
  inProgressSavingBonus,
  setInProgressSavingBonus,
}: {
  activeCard: IMethod;
  type: CashierTabType;
  featuredPaymentMethods: IMethod[];
  lng: LanguagesType;
  isGetWalletsInProgress: boolean;
  wallet: IWallet | undefined;
  setWallets?: Function;
  setActiveCard: Function;
  setActiveScreen: Function;
  activeTabPage?: CashierTabPageType;
  setActiveTabPage: Function;
  bonuses?: IBonus[] | null;
  savedBonus: number | null;
  setSavedBonus: Function;
  inProgressSavingBonus: boolean;
  setInProgressSavingBonus: Function;
}) => {
  const { t } = useTranslation(lng);

  const [recentOrFeaturedPaymentMethods] = useState<IPaymentCard[]>(() =>
    getRecentOrFeaturedPaymentMethods(featuredPaymentMethods, activeCard),
  );

  const { width } = useWindowSize();
  const { isTouchDevice } = useIsTouchMobileView();
  const isTouchDeviceMd = isTouchDevice || (!!width && width < theme.breakpoints?.md);

  const activeCardInitial = useRef(activeCard);

  const { user } = useUser();
  const userCurrency = user!.currency;

  const [amountError, setAmountError] = useState('');

  const [selectedBonus, setSelectedBonus] = useState<number>(0);

  useEffect(() => {
    document
      .getElementById(`deposit-bonus-${selectedBonus.toString()}`)
      ?.scrollIntoView({ behavior: 'instant', block: 'end' });
  }, [activeCard.id]);

  const latestPaidAmount = type === 'deposit' ? featuredPaymentMethods?.[0]?.lastPaidAmount : 0;
  const filteredBonuses = filterBonusesByPaymentSettingIds(
    bonuses || [],
    activeCard.id,
    activeCard.payment_settings_ids,
  );

  const tabs: IMethod | { id: number; title: any }[] = [
    activeCardInitial.current,
    ...recentOrFeaturedPaymentMethods,
  ];

  const [activeTab, setActiveTab] = useState(
    tabs.findIndex((el: IMethod | { id: number; title: any }) => el.id === activeCard?.id),
  );

  const onTabClick = (e: Event, idx: number) => {
    setActiveCard(tabs[idx]);
    setActiveTab(idx);
  };

  const getTabTitle = (tab: any) => {
    if (tab.photo_url) {
      return (
        <Image
          alt={`Method ${tab.id} placeholder`}
          src={tab.photo_url}
          width={isTouchDeviceMd ? 83 : 156}
          height={isTouchDeviceMd ? 35 : 50}
          style={{ objectFit: 'contain' }}
          unoptimized
        />
      );
    }
    return (
      <Typography>{tab.title_platform || tab.title || `${tab.token} ${tab.network}`}</Typography>
    );
  };

  return (
    <>
      <StyledVerticalTabs
        activeTabProp={activeTab}
        hideNavBtns
        className={clsx('verticalPaymentMethods', activeTabPage)}
        onTabChange={onTabClick}
        getTabTitle={getTabTitle}
        tabsTitles={tabs}
        childrenBeforeTabTitles={
          <Typography
            iconName='chevronDown'
            lineHeight='32px'
            text={t('backToMethods')}
            iconProps={{
              className: 'rotate-90',
              width: 12,
              height: 9,
              wrapperWidth: 16,
              wrapperHeight: 16,
            }}
            className='backTab'
            onClick={() => {
              if (!isTouchDeviceMd) {
                setActiveScreen('paymentMethodsAll');
                setActiveCard(null);
              }
            }}
          />
        }
        tabsContents={tabs.map((tab: any) => {
          if (activeCard?.payment_type === 'card') {
            if (type === 'deposit') {
              return (
                <DepositByCard
                  lng={lng}
                  key={tab.id}
                  activeCard={activeCard}
                  userCurrency={userCurrency}
                  latestPaidAmount={latestPaidAmount}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  bonuses={filteredBonuses}
                  setActiveTabPage={setActiveTabPage}
                  selectedBonus={selectedBonus}
                  setSelectedBonus={setSelectedBonus}
                  savedBonus={savedBonus}
                  setSavedBonus={setSavedBonus}
                  inProgressSavingBonus={inProgressSavingBonus}
                  setInProgressSavingBonus={setInProgressSavingBonus}
                />
              );
            }
            if (type === 'withdrawal') {
              return (
                <WithdrawalByCard
                  lng={lng}
                  key={tab.id}
                  activeCard={activeCard}
                  userCurrency={userCurrency}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  setActiveTabPage={setActiveTabPage}
                />
              );
            }
          }
          if (activeCard?.payment_type === 'crypto') {
            if (type === 'deposit') {
              return (
                <DepositByCrypto
                  lng={lng}
                  key={tab.id}
                  activeCard={activeCard}
                  isGetWalletsInProgress={isGetWalletsInProgress}
                  wallet={wallet}
                  setWallets={setWallets}
                  userCurrency={userCurrency}
                  latestPaidAmount={latestPaidAmount}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  bonuses={filteredBonuses}
                  activeTabPage={activeTabPage}
                  setActiveTabPage={setActiveTabPage}
                  selectedBonus={selectedBonus}
                  setSelectedBonus={setSelectedBonus}
                  savedBonus={savedBonus}
                  setSavedBonus={setSavedBonus}
                  inProgressSavingBonus={inProgressSavingBonus}
                  setInProgressSavingBonus={setInProgressSavingBonus}
                />
              );
            }
            if (type === 'withdrawal') {
              return (
                <WithdrawalByCrypto
                  lng={lng}
                  key={tab.id}
                  activeCard={activeCard}
                  userCurrency={userCurrency}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  setActiveTabPage={setActiveTabPage}
                />
              );
            }
          }
          if (activeCard?.payment_type === 'p2p') {
            if (type === 'deposit') {
              return (
                <DepositByPtoP
                  lng={lng}
                  key={tab.id}
                  activeCard={activeCard}
                  userCurrency={userCurrency}
                  latestPaidAmount={latestPaidAmount}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  bonuses={filteredBonuses}
                  activeTabPage={activeTabPage}
                  setActiveTabPage={setActiveTabPage}
                  setActiveScreen={setActiveScreen}
                  selectedBonus={selectedBonus}
                  setSelectedBonus={setSelectedBonus}
                  savedBonus={savedBonus}
                  setSavedBonus={setSavedBonus}
                  inProgressSavingBonus={inProgressSavingBonus}
                  setInProgressSavingBonus={setInProgressSavingBonus}
                />
              );
            }
            if (type === 'withdrawal') {
              return (
                <WithdrawalByPtoP
                  lng={lng}
                  key={tab.id}
                  activeCard={activeCard}
                  userCurrency={userCurrency}
                  amountError={amountError}
                  setAmountError={setAmountError}
                  setActiveTabPage={setActiveTabPage}
                />
              );
            }
          }
          return <></>;
        })}
        type={isTouchDeviceMd ? 'buttons' : 'inlines'}
      />
    </>
  );
};
export default PaymentActionScreen;
