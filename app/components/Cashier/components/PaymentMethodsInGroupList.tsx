'use client';
import { useParams } from 'next/navigation';

import { TilesGrid, Typography } from '@/atomic-design-components';

import { IPaymentCard } from '@/app/api/payments/getPaymentDataClient.ts';
import { CashierTabType } from '@/app/components/Cashier/CashierModal.tsx';
import { PAYMENT_CARDS_BREAKPOINTS } from '@/app/components/Cashier/components/PaymentMethodsAllList.tsx';
import { useTranslation } from '@/app/i18n/client';
import { theme } from '@/theme';
import { StyledCard } from '../styled';
import PaymentMethodCard from './PaymentMethodCard';
import clsx from 'clsx';

const PaymentMethodsInGroupList = ({
  setActiveCard,
  data,
  setActiveScreen,
  activeTab,
  userCurrency,
}: {
  setActiveCard: Function;
  data: Array<IPaymentCard>;
  setActiveScreen: any;
  activeTab: CashierTabType;
  userCurrency: string;
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const isWithdrawal = activeTab === 'withdrawal';

  return (
    <div className='flex w-full flex-col gap-2'>
      <div
        className={clsx(
          'flex min-h-10 w-full flex-col items-center justify-center rounded-lg bg-[#334155] p-2 text-center md:mb-2',
        )}
      >
        <Typography
          text={t('ifOptionDoesntWork')}
          iconName='infoIcon'
          iconProps={{
            fill: theme.color?.general.lightest,
            width: 24,
            height: 24,
          }}
        />
      </div>
      <Typography
        text={t('backToMethods')}
        lineHeight='32px'
        iconName='chevronDown'
        iconProps={{ className: 'rotate-90', width: 15, height: 9 }}
        className='whitespace-nowrap md:!hidden'
        onClick={() => setActiveScreen('paymentMethodsAll')}
      />
      <TilesGrid
        itemsInRow={4}
        breakpoints={PAYMENT_CARDS_BREAKPOINTS}
        tiles={data?.map((method, index) => (
          <PaymentMethodCard
            item={method}
            key={index}
            index={index}
            setActiveScreen={setActiveScreen}
            setActiveCard={setActiveCard}
            isWithdrawal={isWithdrawal}
            userCurrency={userCurrency}
          />
        ))}
        customChild={
          <StyledCard
            className='tilesGridItem customTilesGridItem cursor-pointer max-md:!hidden'
            onClick={() => setActiveScreen('paymentMethodsAll')}
          >
            <Typography
              className='whitespace-nowrap'
              text={t('backToMethods')}
              lineHeight='32px'
              iconName='chevronDown'
              iconProps={{ className: 'rotate-90', width: 15, height: 9 }}
            />
          </StyledCard>
        }
      />
    </div>
  );
};

export default PaymentMethodsInGroupList;
