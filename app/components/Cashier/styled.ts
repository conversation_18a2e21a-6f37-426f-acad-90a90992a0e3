'use client';
import { FlexRow, Tabs } from '@/atomic-design-components';
import { HEADER_HEIGHT } from '@/constants';
import styled from 'styled-components';
import ModalDialog from '../ModalDialog/ModalDialog';

export const StyledCashierModal: any = styled(ModalDialog)`
  height: 100dvh;
  max-height: 100dvh;
  width: 100%;
  max-width: 100dvw;
  border-radius: 0;
  border: none;
  transition: max-width 0.3s ease-in-out,
  width 0.3s ease-in-out;

  &.paymentMethodsAll.deposit, &.paymentMethodsAll.withdrawal {
    .activeTab {
      overflow: auto;
      //height: calc(100% - 47px); // 47px is the height of the tabsTitles
    }
  }

  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    border-radius: ${({ theme }) => theme.size.border.radius.bigger};
    border: 1px solid ${({ theme }) => theme.color?.general.dark};
    height: 100%;
    max-height: 660px;
    width: 100%;
    max-width: 1053px;

    :not(&.mobileView) {
      &.paymentMethodsAll.deposit, &.paymentMethodsAll.withdrawal {
        .activeTab {
          height: calc(100% - 47px); // 47px is the height of the tabsTitles
        }
      }
    }

    &.depositByCrypto,
    &.paymentMethodsAll,
    &.paymentMethodsInGroup {
      max-width: 1053px;
    }

    &.paymentAction {
      max-width: 644px;

      &.depositByCryptoNoBonuses,
      &.depositByCard,
      &.depositByPtoP,
      &.withdrawalByCard,
      &.withdrawalByPtoP,
      &.withdrawalByCrypto {
        max-width: 644px;
      }

      &.depositByCrypto {
        max-width: 1053px;
      }
    }

    &.paymentAction {
      &.payment {
        max-width: 454px;
        max-height: 696px !important;

        &.p2p {
          max-height: 728px !important;
        }
      }

      &.paymentResult {
        max-width: 454px;
        max-height: 450px !important;
      }
    }
  }

  &.mobileView {
    border-radius: 0;
    border: none;
    height: 100dvh;
    max-height: 100dvh !important;
    width: 100%;
    max-width: 100dvw !important;

    &.paymentAction {
      &.payment, &.paymentResult {
        max-width: 100dvw !important;
        max-height: 100dvh !important;
      }
    }
  }
`;

export const StyledPaymentTabs: any = styled(Tabs)`
  height: 100%;
  margin-top: 56px;
  padding: 0;

  .tabsTitles {
    margin-right: 0;
    margin-bottom: 8px;

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      min-width: 188px;
      margin-right: 208px;
      margin-bottom: 16px;
    }
  }
  .tabsContainer {
    width: 100%;
    margin-bottom: 8px;
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      margin-bottom: 0px;
    }
  }

  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    margin-top: 0px;
    padding: 24px;
    .deposit.paymentAction &,
    .withdrawal.paymentAction & {
      height: 100%;
      .tabsTitles {
        height: inherit;
      }
    }

    &.mobileView {
      padding-top: 0;
    }
  }
  &.mobileView {
    margin-top: 56px;
    .tabsTitles {
      min-width: auto;
      margin-right: 0;
      margin-bottom: 8px;
    }
    .tabsContainer {
      margin-bottom: 8px;
    }
    .deposit.paymentAction &,
    .withdrawal.paymentAction & {
      height: auto;
      .tabsTitles {
        height: auto;
      }
    }
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      .tabsTitles {
        margin-bottom: 16px;
      }
    }
  }
`;

export const StyledDepositOrWithdrawalWrapper: any = styled(FlexRow)`
  width: 100%;
  min-width: 320px;
  overflow-y: auto;
  height: 100%;
  max-height: 100dvh;
  border-radius: 0 0 10px 10px;
  padding: 0 8px;
  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    :not(.mobileView &) {
      max-height: min(660px, calc(100dvh - 114px));
      padding: 0;
    }
  }

  .tabsContainer {
    max-height: inherit;
  }

  .withdrawalMethods {
    &.disabled {
      opacity: 0.3;
    }
  }

  &.paymentAction {
    overflow-y: visible;
  }

  .paymentMethodsAllList {
    max-height: calc(100dvh - 130px);
  }

  .depositContentWrapper,
  .withdrawalContentWrapper {
    // overflow-y: auto;
    height: 100%;
    max-height: calc(100dvh - 126px);

    width: 100%;
    // padding-bottom: 16px;

    h1 {
      margin-left: 24px;
    }

    #paymentButton {
      margin: auto 0 0 0;
      padding-top: 8px;
    }

    &.mobileView,
    &.mobileView.depositContentWrapperNoBonuses {
      max-height: calc(100dvh - 172px);
    }

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      // overflow-y: visible;
      height: min(660px, 100dvh);
      max-height: min(660px, 100dvh);
      // padding-bottom: 24px;
      padding-top: 24px;
      #paymentButton {
        margin: auto 24px 24px 24px;
        padding-top: 16px;
      }

      &.depositContentWrapperNoBonuses {
        max-height: min(660px, 100dvh);
      }

      &.mobileView {
        height: 100%;
        max-height: calc(100dvh - 200px);
        padding-top: 0;

        #paymentButton {
          margin: auto 24px 16px 24px;
          padding-top: 16px;
        }
      }
    }
  }

  .withdrawalContentWrapper {
    @media (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
      max-height: calc(100dvh - 172px);
    }
  }

  &.mobileView {
    .depositContentWrapper,
    .withdrawalContentWrapper {
      @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
        max-height: calc(100dvh - 106px);
        #paymentButton {
          padding: 12px 0;
        }
      }
    }
  }
`;

export const StyledHistoryWrapper: any = styled(FlexRow)`
  border-radius: 0 0 10px 10px;

  // container around history table
  .historyContainer {
    overflow-y: auto;
    height: 100%;
    max-height: calc(100dvh - 112px);
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      overflow-y: none;
      max-height: min(577px, calc(100dvh - 83px));
    }
  }
  &.mobileView {
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      .historyContainer {
        overflow-y: auto;
        max-height: calc(100dvh - 134px);
        padding: 0;
      }
    }
  }
  // history table itself
  .paymentHistoryTable {
    width: 100%;
    overflow-y: none;
    // EmptyTable
    margin-top: calc(50dvh - 150px);

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      // EmptyTable
      min-height: min(496px, calc(100dvh - 164px));
      margin-top: 0;
      // TableComponent
      > div:first-child {
        max-height: min(496px, calc(100dvh - 164px));
      }
    }
  }
`;

export const StyledVerticalTabs: any = styled(Tabs)`
  flex-grow: 1;
  max-height: 100dvh;

  .p2p & {
    &.paymentResult, &.payment {
      .tabsTitles {
        display: none;
      }
    }
  }

  &.verticalPaymentMethods {
      // height: calc(100dvh - ${HEADER_HEIGHT});
    // .activeTab {
      //   height: calc(100% - ${HEADER_HEIGHT} + 4px);
    // }

    .rts___tab.rts___btn {
      height: 80px;
    }

    .backTab {
      display: none;
    }

    && .tabsTitles {
      padding: 0;
    }

    .rts___tabs {
      padding: 0 !important;
    }

    .rts___tabs.hide___rts___tabs___scroll {
      flex-direction: row;
      gap: 0 !important;

      .rts___tab.rts___btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 51px;
        border-radius: 0;
        margin: 0;
        border-left: 1px solid ${({ theme }) => theme.color?.general.dark};
        width: 33.33333%;

        &:nth-child(1) {
          border-color: transparent;
        }

        .typography {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: inline;
        }

        img {
          width: 100%;
          max-height: 35px;
        }
      }
    }
  }

  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    :not(&.mobileView) {
      &.verticalPaymentMethods {
        .backTab {
          display: flex;
        }

        && .tabsTitles {
          padding: 0;
        }

        &.payment {
          .tabsTitles {
            display: none;
          }
        }

        .tabsTitles {
          flex-direction: column;
          margin: 0;

          .backTab {
            white-space: nowrap;
            padding: 16px;
            height: 82px;
            cursor: pointer;
          }
        }

        .activeTab[role='tabpanel'] {
          flex-grow: 1;
        }

        > * {
          align-items: start;

          &:first-child {
            background-color: ${({ theme }) => theme.color?.general.darker};
            border-top-left-radius: ${({ theme }) =>
              theme.size.border.radius.main};
            border-bottom-left-radius: ${({ theme }) =>
              theme.size.border.radius.main};
          }
        }

        display: flex;

        .rts___tabs.hide___rts___tabs___scroll {
          width: 100%;
          flex-direction: column;
          padding: 0;
          border-bottom: none;

          img {
            max-height: 50px;
          }

          .rts___tab.rts___btn {
            height: 86px;
            border-radius: 0;
            background-color: ${({ theme }) => theme.color?.general.darker};
            border-left: 0;
            border-right: 0;
            border-top: 0;
            padding: 16px;
            border-top: 1px solid ${({ theme }) => theme.color?.general.dark};
            width: 100%;

            &:hover {
              background-color: ${({ theme }) => theme.color?.general.darkest};
            }

            &:last-child {
              border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
            }

            &:not(:first-child) {
              margin: 0;
            }

            &.rts___tab___selected {
              background-color: ${({ theme }) => theme.color?.general.dark};
              border-color: ${({ theme }) => theme.color?.general.dark};
            }
          }
        }
      }
    }
  }

  &.mobileView {
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      .rts___tabs___container {
        margin: 0 24px;
      }
    }
  }
`;

export const StyledCard: any = styled(FlexRow)`
    width: 100%;
    border-radius: ${({ theme }) => theme.size.border.radius.main};
    background-color: ${({ theme }) => theme.color?.general.darker};
    cursor: pointer;
    height: 78px;

    &:hover:not(.disabled) {
        background-color: ${({ theme }) => theme.color?.general.dark};

        hr {
            background-color: #475569;
        }
    }

    &.disabled {
        cursor: not-allowed;
    }

    img {
        position: absolute;
    }

    .methodCard {
        display: flex;
        justify-content: center;
        width: 100%;
        height: 100%;
        padding: 8px 16px;
        align-items: center;
        position: relative;
        overflow: hidden;
        min-height: 20px;

        &.loading {
            img:not(.placeholder) {
                visibility: hidden;
            }
        }

        img {
            max-height: 35px;
        }

        &.placeholder,
        &.loading {
            img {
                padding: 0 20%;
            }
        }

        &.cardMethod {
            img {
                padding: 0 10%;
            }
        }
    }

    &.customTilesGridItem {
        height: 78px;
        background: transparent;
        border: 1px solid ${({ theme }) => theme.color?.general.dark};
        justify-content: center;
        align-items: center;
    }

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
        height: 106px;

        &.customTilesGridItem {
            height: 106px;
        }

        .methodCard {
            padding: 16px;

            img {
                max-height: 50px;
            }
        }
    }

    &.mobileView {
        &.customTilesGridItem {
            height: 78px;
        }

        height: 78px;

        .methodCard {
            padding: 8px 16px;

            img {
                max-height: 35px;
            }
        }
    }
`;

export const StyledDepositInputsBlock: any = styled.div`
  width: 100%;
  overflow-y: auto;
  align-items: stretch;

  .banksSelect {
    & .react-select__menu {
      & .react-select__menu-list {
        max-height: 140px;
      }
    }
  }

  @media (max-height: 675px) {
    .banksSelect {
      & .react-select__menu {
        & .react-select__menu-list {
          max-height: 120px;
        }
      }
    }
  }

  @media (max-height: 650px) {
    .banksSelect {
      & .react-select__menu {
        & .react-select__menu-list {
          max-height: 105px;
        }
      }
    }
  }

  &.hasBonuses {
    overflow-y: auto;
    overflow-x: hidden;

    .amountInput {
      flex-basis: 60%;
    }
  }

  .bonusesWrapper {
    overflow: hidden;
  }

  .bonusesList {
    overflow: auto;
  }

  @media (max-height: 600px) {
    .bonusesWrapper {
      overflow: initial;
    }

    .bonusesList {
      overflow: initial;
    }
  }

  .availableWithdrawal {
    margin: 0;
  }

  .bonusInput {
    input {
      border: none;
      padding: 8px 12px;
    }

    .rightIcon {
      top: 8px;
    }
  }

  .amountInput {
    flex-basis: 100%;

    .label.error {
      position: absolute;
      top: 100%;
    }
  }

  .amountButtonsWithError {
    margin-top: 20px;
  }

  & button.amountButton {
    flex-grow: 1;
    padding: 9px 8px 7px;
    min-width: 44px;
    border: 1px solid transparent;
    max-width: 60px;
  }

  .longAmounts {
    & button.amountButton {
      max-width: 80px;
    }
  }

  & .withButtonInside button.maxButton {
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.24px;
    text-transform: uppercase;
    padding: 8px;
    right: 12px;
    top: 5px;

    &.withCross {
      right: 36px;
    }
  }

  .checkboxContainer {
    width: 100%;
    display: flex;
    gap: 16px;
    padding: 8px 16px;
    border-radius: 6px;
    background: ${({ theme }) => theme.color?.general.darker};
    align-items: center;
  }

  .infoCard {
    background-color: #2e4469;
    border-radius: 6px;
  }

  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    // height: auto;
    // padding-top: 21px;
    & > *:not(dialog) {
      padding: 0 24px;
    }

    .availableWithdrawal {
      margin: 0 1.5rem;
    }

    &.crypto {
      .bonusesWrapper {
        //max-height: 305px;
        //overflow-y: auto;
        margin-bottom: 25px;
      }
    }

    &.mobileView {
      height: 100dvh;
      margin-top: 0;
      padding-top: 8px;

      & > *:not(dialog) {
        padding: 0 24px;
      }

      &.crypto {
        .bonusesWrapper {
          max-height: 100%;
          margin-bottom: 0;
        }
      }

      .availableWithdrawal {
        margin: 0 1.5rem;
      }
    }
  }
`;

export const StyledPaymentWindow: any = styled(FlexRow)`
    // max-height: calc(100dvh - 182px);
    padding: 0;
    margin: 0 auto;
    flex-basis: 100%;
    height: 100%;

    &.cryptoAddress {
        height: 100%;
        max-height: calc(100dvh - 124px);
        overflow-y: auto;
    }

    .paymentResult & {
        margin: -20px auto;
        justify-content: center;
        padding: 0 16px;
    }

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
        max-height: min(542px, calc(100dvh - 141px));
        // margin-top: 21px;
        padding: 0 16px;
        flex-basis: 50%;

        .p2pPayment & {
            max-height: min(535px, calc(100dvh - 141px));
        }

        &.cryptoAddress {
            max-height: min(577px, calc(100dvh - 100px));
            width: 100%;

            .processingTime {
                justify-content: center;
            }
        }

        .paymentResult & {
            &.mobileView {
                margin: -20px auto;
                justify-content: center;
                padding: 0 30px;
            }
        }

        &.mobileView {
            max-height: calc(100dvh - 205px);
            padding: 0;
            flex-basis: 100%;
            height: 100%;

            &.cryptoAddress {
                height: 100%;
                max-height: calc(100dvh - 162px);
                overflow-y: auto;
            }
        }
    }
`;
