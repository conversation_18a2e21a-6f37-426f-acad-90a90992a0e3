'use client';
import Link from 'next/link';
import { useRef } from 'react';
import { ITournament } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { GamePageType, LanguagesType } from '@/types/global.js';
import TournamentsCard from './TournamentsCard';
import TournamentsCardMobile from './TournamentsCardMobile';
import { sortItems } from '@/utils/sortItems.ts';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import SliderArrowsSimple from '@/app/components/Levels/SliderArrowsSimple.tsx';
import { useIsLastSliderElementFullyVisible } from '@/hooks/useCheckIsLastSliderElementFullyVisible.ts';

const TOURNAMENTS_LIMIT = 6;

const TournamentsList = ({
  lng,
  gamePageType,
  isLandingCard,
  activeTournaments,
  plannedTournaments,
  finishedTournaments,
  userCurrencyFromCookies,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
  isLandingCard?: boolean;
  activeTournaments: ITournament[];
  plannedTournaments: ITournament[];
  finishedTournaments: ITournament[];
  userCurrencyFromCookies: string;
}) => {
  const { t } = useTranslation(lng);
  const [{ constants }] = useSystemData();
  const ref = useRef<HTMLDivElement>(null);

  const isLastElementFullyVisible =
    useIsLastSliderElementFullyVisible('lastTournament');

  const tournamentsOrderResult = constants?.find(
    (constant) => constant.key === 'tournaments_order',
  );

  const activeTournamentsOrderArr = tournamentsOrderResult?.value
    ? JSON.parse(tournamentsOrderResult.value)['active'] || []
    : [];
  const sortedActiveTournaments = sortItems({
    items: activeTournaments,
    order: activeTournamentsOrderArr,
  });

  const plannedTournamentsOrderArr = tournamentsOrderResult?.value
    ? JSON.parse(tournamentsOrderResult.value)['planned'] || []
    : [];
  const sortedPlannedTournaments = sortItems({
    items: plannedTournaments,
    order: plannedTournamentsOrderArr,
  });

  const sortedTournaments = [
    ...sortedActiveTournaments,
    ...sortedPlannedTournaments,
    ...finishedTournaments,
  ];

  return (
    <div className='tournamentsList mb-6 flex flex-col gap-y-2 md:gap-y-4'>
      <div className='flex w-full justify-between'>
        <Typography
          type='h1'
          className='!leading-8 md:!leading-10'
          text={t('tournaments')}
          iconName='cup'
          iconProps={{
            width: 18,
            height: 18,
            fill: theme.color?.status.success,
          }}
        />
        <div className='flex gap-2'>
          <Link
            href={`/${lng}/${gamePageType}/tournaments`}
            className='ml-auto'
          >
            <Button
              text={t('all')}
              variant='secondary'
              className='max-md:!hidden'
            />
            <Button
              text={t('all')}
              size='small'
              variant='secondary'
              className='md:!hidden'
            />
          </Link>
          <SliderArrowsSimple
            elementRef={ref}
            elementWidth={550}
            scrollElementsCount={1}
            isLastElementFullyVisible={isLastElementFullyVisible}
          />
        </div>
      </div>

      <div className='scrollBarHidden flex gap-4 overflow-y-scroll md:hidden'>
        {sortedTournaments
          .slice(0, TOURNAMENTS_LIMIT)
          .map((tournament, index) => (
            <TournamentsCardMobile
              item={tournament}
              key={index}
              index={index}
              isLandingCard={isLandingCard}
              lng={lng}
              userCurrencyFromCookies={userCurrencyFromCookies}
            />
          ))}
      </div>
      <div className='flex gap-5 overflow-y-scroll max-md:hidden' ref={ref}>
        {sortedTournaments
          .slice(0, TOURNAMENTS_LIMIT)
          .map((tournament, index) => (
            <TournamentsCard
              item={tournament}
              key={index}
              index={index}
              isLandingCard={isLandingCard}
              userCurrencyFromCookies={userCurrencyFromCookies}
              id={index === TOURNAMENTS_LIMIT - 1 ? 'lastTournament' : ''}
            />
          ))}
      </div>
    </div>
  );
};

export default TournamentsList;
