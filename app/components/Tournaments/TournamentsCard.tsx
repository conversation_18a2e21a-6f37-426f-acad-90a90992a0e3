'use client';
import { ITournament } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Button, Icon, Typography, Image } from '@/atomic-design-components';
import { DEFAULT_CURRENCY, LOGO_PLACEHOLDER_HORIZONTAL } from '@/constants';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { useTransformDateFromNowInSeconds } from '@/utils/dates';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { splitObjects } from '@/utils/splitObject';
import clsx from 'clsx';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { Fragment, useEffect, useRef, useState } from 'react';
import { StyledVerticalLine } from '../NavMenu/styled';
import PrizesCard from './components/PrizesCard';
import PrizesTableSmall from './components/PrizesTableSmall';
import RelatedGamesList from './components/RelatedGamesList';
import { getValueWithCurrency } from './components/TournamentInfoCard';
import { getPrizeString } from './components/TournamentTableCard';
import { StyledTournamentsCard } from './styled';

const TournamentsCard = ({
  item,
  index,
  isLandingCard = false,
  className,
  userCurrencyFromCookies,
  id,
}: {
  item: ITournament;
  index: number;
  isLandingCard?: boolean;
  className?: string;
  userCurrencyFromCookies: string;
  id?: string;
}) => {
  const {
    lng,
  }: {
    lng: LanguagesType;
  } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const pathname = usePathname();
  const [isLoaded, setIsLoaded] = useState(false);
  const [cardWidth, setCardWidth] = useState<any>(0);
  const [cardHeight, setCardHeight] = useState<any>(0);
  const { width } = useWindowSize();
  const ref = useRef<HTMLDivElement>(null);

  const actualDate = item.status === 'active' ? item.end_date : item.start_date;
  const date: any = useTransformDateFromNowInSeconds(actualDate, lng, true);
  const dateText = ['dayShort', 'hourShort', 'minShort', 'secShort'];
  const currency =
    user?.currency || userCurrencyFromCookies || DEFAULT_CURRENCY;

  let currDates = date || [];
  let currDateText = dateText;
  if (date[0] === '0') {
    currDates = date.slice(1);
    currDateText = dateText.slice(1);
  }

  const prizes = splitObjects(item.prizes, 3);
  const photoUrl = isLandingCard ? item.photo_small_url : item.photo_large_url;

  useEffect(() => {
    if (ref?.current) {
      setCardWidth(ref.current.clientWidth);
      setCardHeight(ref.current.clientHeight);
    }
  }, [ref, width]);

  return (
    <StyledTournamentsCard
      className={clsx(
        className,
        isLandingCard ? 'landingCard' : 'tournamentsPageCard',
      )}
      width={cardWidth - 15}
      height={cardHeight}
      id={id}
    >
      <div
        className={clsx(
          'imageCard',
          item.status === 'finished' ? 'finishedCard' : '',
        )}
        ref={ref}
      >
        {(!isLoaded || !photoUrl) && (
          <Image
            alt={item.name || `Tournament ${index} placeholder`}
            placeholder={undefined}
            src={LOGO_PLACEHOLDER_HORIZONTAL}
            fill
            style={{ objectFit: 'contain' }}
            className='placeholder'
            unoptimized
          />
        )}
        {photoUrl && (
          <Image
            alt={item.name || `Tournament ${index}`}
            placeholder={undefined}
            onLoad={() => setIsLoaded(true)}
            src={photoUrl}
            fill
            style={{ objectFit: 'cover' }}
          />
        )}
      </div>
      <div className='infoCard'>
        <Typography
          className='badge'
          text={t(item.status)}
          fontSize='12px'
          fontWeight={theme.font.weight.bold}
          textAlign='center'
          textTransform='uppercase'
          padding='4px 24px'
          lineHeight='14px'
          backgroundColor={
            item.status === 'active'
              ? theme.color?.status.success
              : theme.color?.general.dark
          }
        />
        <div className='infoBlock flex flex-col'>
          <div className='mb-auto flex h-[136px] flex-col'>
            <Typography
              text={getAvailableTranslation(item.title, lng) || item.name}
              type='h2'
              className='!mb-2 overflow-hidden'
              alignItems='start'
            />
            <Typography
              text={getValueWithCurrency(
                item.total_prize,
                item.rates,
                currency,
              )}
              type='h2'
              className='bonusTag !mb-6'
              backgroundColor={
                item.status === 'finished'
                  ? theme.color?.general.darker
                  : theme.color?.secondary.dark
              }
            />
          </div>
          {item.status !== 'finished' && (
            <>
              <Typography
                text={
                  item.status === 'active'
                    ? `${t('endIn')}:`
                    : `${t('startIn')}:`
                }
              />
              <div className='mt-1 flex w-max items-start justify-between'>
                {currDates.map((el: string, idx: number) => (
                  <Fragment key={idx}>
                    {idx !== 0 && (
                      <Typography text=':' type='h3' className='px-2 pt-1' />
                    )}
                    <div className='flex flex-col items-center gap-1'>
                      <div className='relative h-8 w-8'>
                        <Typography
                          text={el}
                          type='h3'
                          className='date absolute z-10'
                          suppressHydrationWarning
                        />
                        <div className='bg'></div>
                        <div className='bg'></div>
                      </div>
                      <Typography
                        text={t(currDateText[idx])}
                        className='dateText'
                      />
                    </div>
                  </Fragment>
                ))}
              </div>
            </>
          )}
          {!isLandingCard && (
            <Link href={`${pathname}/${item.id}`}>
              <Button
                text={t('tournamentPage')}
                variant={item.status === 'finished' ? 'secondary' : 'primary'}
                className='mt-4'
              />
            </Link>
          )}
        </div>
        <div className='prizesBlock flex'>
          <StyledVerticalLine
            className='mr-6 h-[initial]'
            color={theme.color?.general.dark}
          />
          <div className='flex flex-col'>
            {isLandingCard ? (
              <>
                <Typography text={t('prizes')} type='h2' />
                <div className='mb-auto mt-2 flex flex-col'>
                  {Object.keys(prizes).map((place, idx) => (
                    <div key={idx} className='flex'>
                      <Icon
                        name='placeStar'
                        level={place}
                        stroke={['#D3B517', '#C0C0C0', '#CD7F32'][idx]}
                        className='my-2 mr-2'
                      />
                      <Typography
                        text={`${t(`${place}Place`)} -`}
                        fontWeight={theme.font.weight.medium}
                        className='text-nowrap'
                      />
                      <Typography
                        text={getPrizeString(
                          prizes[place],
                          item.rates,
                          currency,
                        )}
                        className='text-nowrap'
                      />
                    </div>
                  ))}
                </div>
              </>
            ) : item.status === 'planned' ? (
              <PrizesCard item={item} />
            ) : (
              <PrizesTableSmall item={item} />
            )}
            {!isLandingCard && item.status !== 'finished' && (
              <RelatedGamesList
                tournamentId={item.id}
                tournamentSlug={item.slug}
                isActive={item.status === 'active'}
              />
            )}
            {isLandingCard && (
              <Link href={`${lng}/casino/tournaments/${item.slug}`}>
                <Button
                  text={t('tournamentPage')}
                  variant='primary'
                  fullWidth
                />
              </Link>
            )}
          </div>
        </div>
      </div>
    </StyledTournamentsCard>
  );
};

export default TournamentsCard;
