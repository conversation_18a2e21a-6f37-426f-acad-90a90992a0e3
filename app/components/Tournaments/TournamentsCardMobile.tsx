'use client';
import { ITournament } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Typography, Image } from '@/atomic-design-components';
import { DEFAULT_CURRENCY, LOGO_PLACEHOLDER_HORIZONTAL } from '@/constants';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { useTransformDateFromNowInSeconds } from '@/utils/dates';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import clsx from 'clsx';
import { usePathname, useRouter } from 'next/navigation';
import { useState } from 'react';
import ReactTexty from 'react-texty';
import { getValueWithCurrency } from './components/TournamentInfoCard';
import { StyledTournamentsCard } from './styled';

const TournamentsCardMobile = ({
  item,
  index,
  lng,
  className,
  isLandingCard = true,
  isPageCard = false,
  userCurrencyFromCookies,
}: {
  item: ITournament;
  index: number;
  isLandingCard?: boolean;
  lng: LanguagesType;
  className?: string;
  isPageCard?: boolean;
  userCurrencyFromCookies: string;
}) => {
  const { t } = useTranslation(lng);
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useUser();

  const [isLoaded, setIsLoaded] = useState(false);

  const currency =
    user?.currency || userCurrencyFromCookies || DEFAULT_CURRENCY;

  const link = isLandingCard
    ? `${lng}/casino/tournaments/${item.slug}`
    : `${pathname}/${item.slug}`;

  const actualDate = item.status === 'active' ? item.end_date : item.start_date;
  const date: any = useTransformDateFromNowInSeconds(actualDate, lng, true);

  let dateToShow = '';
  if (date[0] === '0') {
    dateToShow = `${date.slice(1).join(':')}`;
  } else {
    const dayText = date[0] === '1' ? t('day_one') : t('day_other');
    dateToShow = `${date[0]} ${dayText} ${date[1]}:${date[2]}:${date[3]}`;
  }

  return (
    <StyledTournamentsCard
      className={clsx(
        className,
        isPageCard ? 'pageCard' : '',
        isLandingCard ? 'landingCard' : '',
        'mobileCard',
      )}
      onClick={() => router.push(link)}
    >
      <div
        className={clsx(
          'imageCard',
          item.status === 'finished' ? 'finishedCard' : '',
        )}
      >
        {(!isLoaded || !item.photo_small_url) && (
          <Image
            alt={item.name || `Tournament ${index} placeholder`}
            placeholder={undefined}
            src={LOGO_PLACEHOLDER_HORIZONTAL}
            fill
            style={{ objectFit: 'contain' }}
            className='placeholder'
            unoptimized
          />
        )}
        {item.photo_small_url && (
          <Image
            alt={item.name || `Tournament ${index}`}
            placeholder={undefined}
            onLoad={() => setIsLoaded(true)}
            src={item.photo_small_url}
            fill
            style={{ objectFit: 'cover' }}
          />
        )}
      </div>
      <Typography
        className='badge'
        text={t(item.status)}
        fontSize='12px'
        fontWeight={theme.font.weight.bold}
        textAlign='center'
        textTransform='uppercase'
        padding='4px 16px'
        lineHeight='14px'
        backgroundColor={
          item.status === 'active'
            ? theme.color?.status.success
            : theme.color?.general.dark
        }
      />
      {item.status !== 'finished' && (
        <div className='badge date flex gap-1'>
          <Typography
            text={dateToShow}
            fontSize='12px'
            fontWeight={theme.font.weight.bold}
            textAlign='center'
            textTransform='uppercase'
            lineHeight='14px'
            suppressHydrationWarning
            className='min-w-[114px]'
          />
        </div>
      )}
      {!isPageCard && (
        <div className='infoBlock flex flex-col gap-1'>
          <Typography
            text={getAvailableTranslation(item.title, lng) || item.name}
            as={ReactTexty}
          />
          <Typography
            text={getValueWithCurrency(item.total_prize, item.rates, currency)}
            type='h3'
          />
        </div>
      )}
    </StyledTournamentsCard>
  );
};

export default TournamentsCardMobile;
