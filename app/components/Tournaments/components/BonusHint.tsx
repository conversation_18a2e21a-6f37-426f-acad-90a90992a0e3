'use client';
import { useParams } from 'next/navigation';

import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import clsx from 'clsx';

const BonusHint = ({ className }: { className: string }) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user } = useUser();
  const hasActiveBonus = user?.active_bonus_id;

  if (!hasActiveBonus) return;

  return (
    <div className={clsx(className, 'w-full')}>
      <Typography
        text={t('youHaveActiveBonusTournamentsHint')}
        iconName='infoIcon'
        iconProps={{
          fill: theme.color?.secondary.main,
          width: 24,
          height: 24,
        }}
        className='rounded-lg bg-[#334155] p-2'
      />
    </div>
  );
};

export default BonusHint;
