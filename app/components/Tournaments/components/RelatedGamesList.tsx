'use client';
import { IGame } from '@/app/api/getGames';
import { useTranslation } from '@/app/i18n/client';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import Slider from '../../Slider/Slider';
import GameCardSmall from './GameCardSmall';
import TournamentSliderDoubleGameCard from './TournamentSliderDoubleGameCard';
import Link from 'next/link';
import { GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';
import { LoadingTournamentCardGames } from '@/app/components/Skeletons/LoadingTournamentCard.tsx';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';
import { useCookies } from 'react-cookie';
import { getGamesTournaments } from '@/app/api/getGamesTournaments.ts';

const getDoubledItems = (items: any) => {
  const result: any = [];
  for (let i = 0; i < items.length; i += 2) {
    result.push([items[i], items[i + 1]]);
  }
  return result;
};

const RelatedGamesList = ({
  tournamentId,
  tournamentSlug,
  isPageList,
  isActive,
}: {
  tournamentId: number;
  tournamentSlug: string;
  isPageList?: boolean;
  isActive: boolean;
}) => {
  const LIMIT = isPageList ? 24 : isActive ? 4 : 5;
  const [cookies] = useCookies(['userCurrency']);

  const {
    lng,
    gamePageType = 'casino',
  }: {
    lng: LanguagesType;
    gamePageType?: GamePageType;
  } = useParams();
  const [games, setGames] = useState<IGame[]>([]);
  const [total, setTotal] = useState<number>(0);
  const filters: any = {
    is_live: [(gamePageType === 'live-casino').toString()],
    tournament_id: [tournamentId],
  };

  const [isLoading, setIsLoading] = useState(true);

  const loadMore = async (skip: number) => {
    const { items: newItemsData } = await getGamesTournaments(
      skip,
      LIMIT,
      cookies?.userCurrency,
      filters,
    );

    return getDoubledItems(newItemsData);
  };

  const fetchGames = useCallback(async () => {
    const { items, total } = await getGamesTournaments(0, LIMIT, cookies?.userCurrency, filters);
    const result = isPageList ? getDoubledItems(items) : items;
    setGames(result);
    setTotal(total);
  }, []);
  const { t } = useTranslation(lng);
  const url = `/${lng}/casino/games/tournament?tournament=${tournamentSlug}`;

  useEffect(() => {
    fetchGames().finally(() => setIsLoading(false));
  }, []);

  if (!isPageList && isLoading) {
    return <LoadingTournamentCardGames />;
  }

  return (
    <>
      {isPageList ? (
        <div className='gameCarousel relatedCarousel flex flex-col gap-y-2 md:gap-y-4'>
          <Typography
            type='h1'
            text={`${t(String('tournamentss'))} ${t('games')}`}
            iconName='cup'
            iconProps={{ width: 20, height: 20 }}
          />
          <Slider
            items={
              isLoading
                ? getDoubledItems(
                    Array(16).fill(
                      <div className='flex flex-col gap-y-2'>
                        <LoadingGameCard className='bg-gray-600' />
                        <LoadingGameCard className='bg-gray-600' />
                      </div>,
                    ),
                  )
                : games
            }
            total={isLoading ? 16 : total}
            Slide={TournamentSliderDoubleGameCard}
            isCarousel
            perView={6}
            customLoadMore={loadMore}
            withLoadMore
            breakpoints={GAMES_SLIDER_BREAKPOINTS()}
            buttonText={t('all')}
            buttonHref={
              isActive ? `/${lng}/casino/games/tournament?tournament=${tournamentSlug}` : undefined
            }
          />
        </div>
      ) : (
        <div className='mt-4 mt-auto flex gap-2 '>
          {games.map((game: IGame, index: number) => (
            <GameCardSmall key={index} item={game} index={index} />
          ))}
          {isActive && (
            <Link href={url}>
              <Typography
                text={t('moreGames')}
                padding='18px 11px'
                className='w-[80px] cursor-pointer rounded-lg bg-[#1E293B]'
                textAlign='center'
              />
            </Link>
          )}
        </div>
      )}
    </>
  );
};

export default RelatedGamesList;
