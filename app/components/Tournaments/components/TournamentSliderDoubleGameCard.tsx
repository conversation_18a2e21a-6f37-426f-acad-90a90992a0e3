'use client';
import GameCard from '@/app/components/Games/GameCard.tsx';

const TournamentSliderDoubleGameCard = ({
  item,
  index,
}: {
  item: any;
  index: number;
}) => {
  return (
    <div className='flex flex-col gap-y-2'>
      <GameCard
        className='bg-[#0F172A]'
        item={item[0]}
        category='tournament'
        index={index}
      />
      <GameCard
        className='bg-[#0F172A]'
        item={item[1]}
        category='tournament'
        index={index}
      />
    </div>
  );
};

export default TournamentSliderDoubleGameCard;
