'use client';
import { getScoreBoard } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import TableComponent from '../../TableComponent/TableComponent';
import { getPrizeString } from './TournamentTableCard';
import { DEFAULT_CURRENCY } from '@/constants.ts';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { LoadingPrizesTable } from '@/app/[lng]/[gamePageType]/tournaments/components/loadingOneTournament.tsx';
import { useCookies } from 'react-cookie';
const limit = 10;
const maxLimit = 30;

const PrizesTable = ({
  id,
  setPrizesName,
  prizes,
  rates,
}: {
  id: number;
  setPrizesName: any;
  prizes: any;
  rates: any;
}) => {
  const { showError } = useAlert();
  const {
    lng,
  }: {
    lng: LanguagesType;
  } = useParams();

  const { t } = useTranslation(lng);

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const [cookies] = useCookies(['userCurrency']);
  const userCurrency = user?.currency || cookies.userCurrency || DEFAULT_CURRENCY;

  const [scoreboard, setScoreboard] = useState<any[]>([]);
  const [inProgress, setInProgress] = useState(true);
  const [userItem, setUserItem] = useState<object | null>(null);
  const [totalItems, setTotalItems] = useState(0);
  const [userPlace, setUserPlace] = useState(0);
  const prizesLength = Object.keys(prizes).length || 0;
  const tableLength = prizesLength > maxLimit ? maxLimit : prizesLength;

  const fetchScoreboard = useCallback(async () => {
    const result = await getScoreBoard(
      0,
      10,
      id,
      isUserAuthorized,
      userCurrency,
    );

    if (!result || result?.detail) {
      showError(result.detail || t('error'));
      return;
    }

    const { board, total, user_place } = result;
    const totalItems = maxLimit < total ? maxLimit : total;
    setScoreboard(board);
    setTotalItems(totalItems);
    setUserPlace(user_place || 0);

    if (user_place === 0 || typeof user_place === 'undefined') {
      setUserItem({ place: '-', prize: '-', points: '-', nick: t('you') });
    }
    if (user_place > 10) {
      const { board: usersItem } = await getScoreBoard(
        user_place - 1,
        1,
        id,
        isUserAuthorized,
        userCurrency,
      );
      setUserItem(usersItem?.[0] || null);
    }
    setPrizesName({
      first: {
        name: board[0]?.nick || board[0]?.email || board[0]?.phone,
      },
      second: {
        name: board[1]?.nick || board[1]?.email || board[1]?.phone,
      },
      third: {
        name: board[2]?.nick || board[2]?.email || board[2]?.phone,
      },
    });
    if (tableLength > totalItems) {
      const emptyItemsKeys = Object.keys(prizes).slice(totalItems);
      const emptyItems = emptyItemsKeys.map((key) => {
        return {
          place: key,
          prize: prizes[key].prize,
          freespins: prizes[key].freespins,
          points: '-',
          nick: '-',
          rate: board[0]?.rate || rates,
        };
      });
      setScoreboard((prev: any) => [...prev, ...emptyItems]);
    }
  }, []);

  useEffect(() => {
    fetchScoreboard().finally(() => setInProgress(false));
  }, []);

  if (inProgress) {
    return <LoadingPrizesTable />;
  }

  const loadMore = async (skip: number) => {
    const { board: items, user_place } = await getScoreBoard(
      skip,
      limit,
      id,
      isUserAuthorized,
      userCurrency,
    );

    const lastItemsToAddCountIsMaxLimit =
      maxLimit && maxLimit - scoreboard.length;
    const newItems = maxLimit
      ? items.slice(0, lastItemsToAddCountIsMaxLimit)
      : items;

    if (user_place !== 0) {
      if (user_place >= skip + limit) {
        const { board: usersItem } = await getScoreBoard(
          user_place - 1,
          1,
          id,
          isUserAuthorized,
          userCurrency,
        );
        setUserItem(usersItem?.[0] || null);
      } else {
        setUserItem(null);
      }
    } else {
      setUserItem({ place: '-', prize: '-', points: '-', nick: t('you') });
    }

    return newItems;
  };

  const columns = [
    {
      place: {
        key: 'place',
        dataKey: 'place',
        width: 0,
        flexGrow: 1,
        cellRenderer: (item: any) => {
          return (
            <div className='flex gap-4'>
              {item.place < 4 ? (
                <Icon
                  name='placeStar'
                  level={item.place}
                  stroke={['#D3B517', '#C0C0C0', '#CD7F32'][item.place - 1]}
                />
              ) : (
                <Typography
                  text={item.place}
                  type='label2'
                  color={theme.color?.general.lightest}
                  className='h-6 w-6 justify-center rounded-md bg-[#334155] py-1'
                />
              )}
              <Typography
                text={
                  item.place === userPlace
                    ? t('you')
                    : item.nick || item.email || item.phone
                }
                color={theme.color?.general.white}
                type='body2'
                className='!block overflow-hidden text-ellipsis text-nowrap'
              />
            </div>
          );
        },
      },
    },
    {
      prize: {
        key: 'prizes',
        dataKey: 'prize',
        width: 0,
        flexGrow: 1,
        cellRenderer: (item: any) => {
          const text = getPrizeString(item, item.rate, userCurrency, true);
          return (
            <Typography
              text={text}
              type={item.place > 3 ? 'body2' : 'sub2'}
              justifyContent='end'
              className='text-nowrap'
            />
          );
        },
        getTitle: () => {
          const currency = getCurrencySymbol(
            userCurrency,
          );
          return `${t('prize')}, ${currency}`;
        },
      },
    },
    {
      points: {
        key: 'points',
        dataKey: 'points',
        width: 0,
        flexGrow: 1,
        cellRenderer: ({ points }: any) => (
          <Typography text={points} type='body2' justifyContent='end' />
        ),
      },
    },
  ];

  const getRowClass = (item: any) =>
    item?.place === userPlace ? 'userRow' : '';

  return (
    <TableComponent
      className='scoreboardTable'
      columns={columns}
      initialData={scoreboard}
      lng={lng}
      emptyIconName='cup'
      emptyIconProps={{
        width: 80,
        height: 80,
        fill: theme.color?.general.light,
      }}
      isHeaderSticky
      withInfiniteScroll={false}
      getRowClass={getRowClass}
      countInNavigation={true}
      withArrows={false}
      totalItems={totalItems > tableLength ? totalItems : tableLength}
      limit={limit}
      maxLimit={maxLimit}
      withPagination={tableLength <= totalItems}
      withClientSidePagination={tableLength > totalItems}
      extraItem={userItem}
      loadMore={
        id && typeof isUserAuthorized !== 'undefined' ? loadMore : undefined
      }
    />
  );
};

export default PrizesTable;
