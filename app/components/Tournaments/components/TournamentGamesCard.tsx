'use client';

import { ITournament } from '@/app/api/getTournaments';
import RelatedGamesList from './RelatedGamesList';

const TournamentGamesCard = ({ item }: { item: ITournament }) => {
  return (
    <div className='relative rounded-lg bg-[#1E293B] px-2 py-4 md:p-6'>
      <RelatedGamesList
        tournamentId={item.id}
        tournamentSlug={item.slug}
        isPageList
        isActive={item.status === 'active'}
      />
    </div>
  );
};

export default TournamentGamesCard;
