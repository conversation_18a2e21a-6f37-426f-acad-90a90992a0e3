'use client';
import { Image } from '@/atomic-design-components';
import { useState } from 'react';

import { LOGO_PLACEHOLDER_HORIZONTAL } from '@/constants.ts';
import clsx from 'clsx';

const TournamentCardImage = ({
  name,
  url,
  index,
  className,
}: {
  name: string;
  url: string;
  index: number;
  className?: string;
}) => {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div className={clsx(className, 'gameCard tournamentImageCard')}>
      {(!isLoaded || !url) && (
        <Image
          alt={name || `Tournament ${index} placeholder`}
          placeholder={undefined}
          src={LOGO_PLACEHOLDER_HORIZONTAL}
          fill
          sizes='357px'
          style={{ objectFit: 'contain' }}
          className='placeholder'
          unoptimized
        />
      )}
      {url && (
        <Image
          alt={name || `Tournament ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={url}
          fill
          sizes='50vw'
          style={{ objectFit: 'cover' }}
        />
      )}
    </div>
  );
};

export default TournamentCardImage;
