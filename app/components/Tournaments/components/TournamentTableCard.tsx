'use client';

import { ITournament } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Icon, Typography } from '@/atomic-design-components';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { useState } from 'react';
import PrizesTable from './PrizesTable';
import { getValueWithCurrency } from './TournamentInfoCard';
import { useCookies } from 'react-cookie';
import { DEFAULT_CURRENCY } from '@/constants';

export const getPrizeString = (
  item: any,
  rates: any,
  currency?: string,
  skipCurrencySymbol?: boolean,
) => {
  if (!item) return '';
  let str = '';
  if (item.prize) {
    if (item.prize === '-') {
      str = '-';
    } else {
      str += `${getValueWithCurrency(item.prize, rates, currency, skipCurrencySymbol)}`;
    }
  }
  if (item.freespins) {
    str += item.prize ? `+ ${item.freespins}FS` : `${item.freespins}FS`;
  }
  return str;
};

const TournamentTableCard = ({
  item,
  lng,
}: {
  item: ITournament;
  lng: LanguagesType;
}) => {
  const { t } = useTranslation(lng);

  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.sm;

  const { user } = useUser();
  const [cookies] = useCookies(['userCurrency']);
  const userCurrency = user?.currency || cookies.userCurrency || DEFAULT_CURRENCY;

  const [prizesName, setPrizesName] = useState<any>({});

  if (!item.prizes) return null;

  return (
    <div className='relative rounded-lg bg-[#1E293B] px-2 py-4 md:p-6'>
      <div className='relative mb-4 flex items-start max-sm:flex-col sm:pb-[160px]'>
        <Typography text={t('prizes')} type='h1' />
        {isMobile ? (
          <Icon
            name='prizePlatformMobile'
            className='top-0 max-sm:m-auto max-sm:!h-[126px] max-sm:!w-[328px] sm:absolute sm:left-[50%] sm:translate-x-[-50%]'
            firstPrize={getPrizeString(
              item.prizes[1],
              item.rates,
              userCurrency,
            )}
            secondPrize={getPrizeString(
              item.prizes[2],
              item.rates,
              userCurrency,
            )}
            thirdPrize={getPrizeString(
              item.prizes[3],
              item.rates,
              userCurrency,
            )}
          />
        ) : (
          <Icon
            name='prizePlatform'
            className='top-0 max-sm:m-auto max-sm:!h-[126px] max-sm:!w-[328px] sm:absolute sm:left-[50%] sm:translate-x-[-50%]'
            firstName={prizesName.first?.name}
            firstPrize={getPrizeString(
              item.prizes[1],
              item.rates,
              userCurrency,
            )}
            secondName={prizesName.second?.name}
            secondPrize={getPrizeString(
              item.prizes[2],
              item.rates,
              userCurrency,
            )}
            thirdName={prizesName.third?.name}
            thirdPrize={getPrizeString(
              item.prizes[3],
              item.rates,
              userCurrency,
            )}
          />
        )}
      </div>
      <PrizesTable
        id={item.external_id}
        setPrizesName={setPrizesName}
        prizes={item.prizes}
        rates={item.rates}
      />
    </div>
  );
};

export default TournamentTableCard;
