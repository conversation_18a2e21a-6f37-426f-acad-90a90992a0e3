'use client';
import { theme } from '@/theme';
import clsx from 'clsx';
import { Image } from '@/atomic-design-components';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

import { IGame } from '@/app/api/getGames.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { useGameCheckBeforeOpen } from '@/hooks/useGameCheckBeforeOpen.ts';
import { openModal } from '@/utils/openModal.ts';
import { closeModal } from '@/utils/closeModal.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import useWindowSize from '@/hooks/useWindowSize';

const GameCardSmall = ({ item, index }: { item: IGame; index: number }) => {
  const router = useRouter();
  const { lng, gamePageType } = useParams();

  const { user } = useUser();

  const [isLoaded, setIsLoaded] = useState(false);

  const { isTouchDevice } = useIsTouchMobileView();
  const { width } = useWindowSize();
  const isTouchDeviceOrMd = isTouchDevice || (width && width < theme.breakpoints?.md);

  const gameUrl = `/${lng}/${gamePageType || 'casino'}/${item.provider_slug}/game/${item.slug}`;

  const { onGameClick, canGameBeOpenedByLink } = useGameCheckBeforeOpen(
    gameUrl,
    user,
    item.wager_percentage,
  );

  return (
    <div role='presentation'>
      <div
        className={clsx(
          'gameCard gameCardSmall align-center flex cursor-pointer flex-col justify-center',
          !item.photo_url_vertical && 'placeholder',
          !isLoaded && 'loading',
        )}
        onClick={(e: any) => {
          if (!canGameBeOpenedByLink) {
            onGameClick();
            return;
          }
          if (isTouchDeviceOrMd) {
            e.preventDefault();
            document.getElementById('gameModal')?.setAttribute('data-active-game-slug', item.slug);
            openModal('gameModal', true, true, true);
            closeModal('continueGameWithZeroWager');
          } else {
            router.push(gameUrl);
          }
        }}
        role='presentation'
      >
        {(!isLoaded || !item.photo_url_vertical) && (
          <div className={clsx('hoverImageDiv')}>
            <Image
              alt={item.name || `Game ${index} placeholder`}
              placeholder={undefined}
              src={LOGO_PLACEHOLDER}
              style={{ objectFit: 'contain' }}
              className='placeholder'
              fill
              sizes='357px'
              unoptimized
            />
          </div>
        )}
        {item.photo_url_vertical && (
          <div className={clsx('hoverImageDiv')}>
            <Image
              alt={item.name || `Game ${index}`}
              placeholder={undefined}
              onLoad={() => setIsLoaded(true)}
              src={item.photo_url_vertical}
              fill
              sizes='357px'
              style={{ objectFit: 'cover' }}
              unoptimized
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default GameCardSmall;
