'use client';
import { ITournament, getScoreBoard } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { useParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';
import { StyledPrizesTable } from '../styled';
import { getPrizeString } from './TournamentTableCard';
import { LoadingTournamentCardPrizes } from '@/app/components/Skeletons/LoadingTournamentCard.tsx';

const PrizesTableSmall = ({ item }: { item: ITournament }) => {
  const {
    lng,
  }: {
    lng: LanguagesType;
  } = useParams();

  const { t } = useTranslation(lng);

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const [scoreboard, setScoreboard] = useState([]);
  const [userPlace, setUserPlace] = useState(0);
  const [userItem, setUserItem] = useState<any>(null);

  const [isLoading, setIsLoading] = useState(true);

  const fetchScoreboard = useCallback(async () => {
    const { board, user_place } = await getScoreBoard(
      0,
      4,
      item.external_id,
      isUserAuthorized,
      user?.currency,
    );
    const sortedBoard =
      board?.sort((a: any, b: any) => a.place - b.place) || [];

    let withUser = false;
    if (user_place > 3) {
      const { board: usersItem } = await getScoreBoard(
        user_place - 1,
        1,
        item.external_id,
        isUserAuthorized,
        user?.currency,
      );
      setUserItem(usersItem?.[0] || null);
      withUser = true;
    }

    if (
      (withUser && sortedBoard.length < 3) ||
      (!withUser && sortedBoard.length < 4)
    ) {
      const emptyItems = Array(4 - sortedBoard.length).fill({
        prize: '-',
        points: '-',
        nick: '-',
      });
      sortedBoard.push(...emptyItems);
    }

    const finalBoard =
      withUser && sortedBoard.length > 3
        ? sortedBoard.slice(0, 3)
        : sortedBoard;

    setScoreboard(finalBoard);
    setUserPlace(user_place || 0);
  }, []);

  useEffect(() => {
    fetchScoreboard().finally(() => {
      setIsLoading(false);
    });
  }, []);

  if (isLoading) {
    return <LoadingTournamentCardPrizes />;
  }

  return (
    <div className='flex flex-col gap-4'>
      {item.status === 'active' || item.status === 'finished' ? (
        <StyledPrizesTable>
          <thead>
            <tr>
              <td>
                <Typography
                  text={t('place')}
                  type='label1'
                  color={theme.color?.general.lighter}
                />
              </td>
              <td>
                <Typography
                  text={t('prize')}
                  type='label1'
                  color={theme.color?.general.lighter}
                />
              </td>
              <td>
                <Typography
                  text={t('points')}
                  type='label1'
                  color={theme.color?.general.lighter}
                />
              </td>
            </tr>
          </thead>
          <tbody>
            {scoreboard.map((row: any, idx) => (
              <tr
                key={idx}
                className={
                  row.place === userPlace && item.status === 'active'
                    ? 'userRow'
                    : ''
                }
              >
                <td className='flex gap-4'>
                  {row.place < 4 || idx + 1 < 4 ? (
                    <Icon
                      name='placeStar'
                      level={row.place || idx + 1}
                      stroke={['#D3B517', '#C0C0C0', '#CD7F32'][idx]}
                    />
                  ) : (
                    <Typography
                      text={row.place || '-'}
                      fontSize='10px'
                      fontWeight={theme.font.weight.bold}
                      color={theme.color?.general.white}
                      className='h-6 w-6 justify-center rounded-lg bg-[#253A5E] py-1'
                    />
                  )}

                  <Typography
                    text={
                      row.place === userPlace
                        ? t('you')
                        : row.nick || row.email || row.phone
                    }
                    type='label1'
                    className='text-nowrap'
                  />
                </td>
                <td>
                  <Typography
                    text={getPrizeString(row, row.rate, user?.currency)}
                    type='sub1'
                  />
                </td>
                <td>
                  <Typography text={row.points} type='label1' />
                </td>
              </tr>
            ))}
            {userItem && (
              <tr
                key={userPlace}
                className={item.status === 'active' ? 'userRow' : ''}
              >
                <td className='flex gap-4'>
                  <Typography
                    text={userItem.place}
                    fontSize='10px'
                    fontWeight={theme.font.weight.bold}
                    color={theme.color?.general.white}
                    className='h-6 w-6 justify-center rounded-lg bg-[#253A5E] py-1'
                  />

                  <Typography
                    text={t('you')}
                    color={theme.color?.general.white}
                    type='label1'
                    className='text-nowrap'
                  />
                </td>
                <td>
                  <Typography
                    text={getPrizeString(userItem, item.rates, user?.currency)}
                    type='sub1'
                  />
                </td>
                <td>
                  <Typography text={userItem.points} type='label1' />
                </td>
              </tr>
            )}
          </tbody>
        </StyledPrizesTable>
      ) : (
        Object.keys(scoreboard).map((place, idx) => (
          <div key={idx}>
            <Icon
              name='placeStar'
              level={place}
              stroke={['#D3B517', '#C0C0C0', '#CD7F32'][idx]}
            />
            <Typography
              text={getPrizeString(
                item.prizes[place],
                item.rates,
                user?.currency,
              )}
              type='sub1'
            />
          </div>
        ))
      )}
    </div>
  );
};

export default PrizesTableSmall;
