'use client';
import clsx from 'clsx';
import { Fragment } from 'react';
import { ITournament } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Typography } from '@/atomic-design-components';
import useWindowSize from '@/hooks/useWindowSize';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { transformDate, useTransformDateFromNowInSeconds } from '@/utils/dates';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { getCurrencySymbol } from '@/utils/getCurrencySymbol';
import { StyledLine } from '../../Footer/styled';
import { StyledVerticalLine } from '../../NavMenu/styled';
import BonusHint from './BonusHint';
import { useCookies } from 'react-cookie';
import { DEFAULT_CURRENCY } from '@/constants';
import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

export const getValueWithCurrency = (
  value: number | '-',
  rates: any,
  currency?: string,
  skipCurrencySymbol?: boolean,
) => {
  if (value === '-') return '-';
  if (!currency || !rates) {
    return `${skipCurrencySymbol ? '' : '$'}${value}`;
  }
  if (typeof rates === 'number') {
    return `${skipCurrencySymbol ? '' : getCurrencySymbol(currency)}${addSpacesToLongNumbers(Math.floor(value * rates))}`;
  } else {
    return `${skipCurrencySymbol ? '' : getCurrencySymbol(currency)}${addSpacesToLongNumbers(Math.floor(value * rates[currency]))}`;
  }
};

const TournamentInfoCard = ({
  item,
  lng,
  className,
}: {
  item: ITournament;
  lng: LanguagesType;
  className?: string;
}) => {
  const { t } = useTranslation(lng);
  const [cookies] = useCookies(['userCurrency']);
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;

  const { user } = useUser();
  const userCurrency = user?.currency || cookies.userCurrency || DEFAULT_CURRENCY;

  const actualDate = item.status === 'active' ? item.end_date : item.start_date;
  const date: any = useTransformDateFromNowInSeconds(actualDate, lng, true);
  const dateText = ['dayShort', 'hourShort', 'minShort', 'secShort'];
  let currDates = date || [];
  let currDateText = dateText;
  if (date[0] === '0') {
    currDates = date.slice(1);
    currDateText = dateText.slice(1);
  }

  return (
    <div
      className={clsx(
        className,
        'relative rounded-b-lg bg-[#1E293B] px-2 py-4 max-sm:rounded-se-none max-sm:rounded-ss-none md:rounded-lg md:p-6',
      )}
    >
      <div className='badgeContainer mb-1 ms-4 flex gap-4 sm:absolute md:ms-0'>
        <Typography
          className='badge !hidden md:!block'
          text={t(item.status)}
          fontSize='12px'
          fontWeight={theme.font.weight.bold}
          textAlign='center'
          textTransform='uppercase'
          padding='4px 16px'
          lineHeight='14px'
          backgroundColor={
            item.status === 'active' ? theme.color?.status.success : theme.color?.general.dark
          }
        />
        <Typography
          className='md:leading-4'
          type={isMobile ? 'label1' : 'body1'}
          color={theme.color?.general.lighter}
        >
          {`${transformDate(item.start_date, 'DD MMMM HH:mm', lng)} -
          ${transformDate(item.end_date, 'DD MMMM HH:mm', lng)}`}
        </Typography>
      </div>
      <div className='flex gap-1 max-md:flex-col sm:mt-[40px]'>
        <div className='flex grow flex-col gap-1 md:gap-4'>
          <Typography
            text={getAvailableTranslation(item.title, lng) || item.name}
            type='h2'
            className='break-all'
          />
          <div className='flex gap-1'>
            <Typography
              text={`${t('type')}:`}
              color={theme.color?.general.lighter}
              type={isMobile ? 'label2' : 'body1'}
            />
            <Typography
              text={t(item.calculation_type)}
              type={isMobile ? 'label2' : 'body1'}
              color={theme.color?.general.lightest}
            />
          </div>
        </div>
        <StyledVerticalLine className='h-[72px] max-md:hidden' />
        <StyledLine className='md:hidden' margin='4px 0 8px' />
        {item.status !== 'finished' && (
          <div className='hidden flex-grow justify-center md:flex'>
            <div>
              <Typography
                text={item.status === 'active' ? `${t('endIn')}:` : `${t('startIn')}:`}
                color={theme.color?.general.lighter}
              />
              <div className='mt-2 flex w-full items-start justify-between gap-[2px]'>
                {(currDates || []).map((el: string, idx: number) => {
                  return (
                    <Fragment key={idx}>
                      {idx !== 0 && <Typography text=':' className='lg:px-1 xl:px-2' type='h3' />}
                      <div className='flex flex-col items-center'>
                        <div className='flex justify-center'>
                          <Typography
                            text={el}
                            type='h3'
                            className='date z-10'
                            suppressHydrationWarning
                          />
                        </div>
                        <Typography
                          text={t(currDateText[idx])}
                          className='dateText'
                          type='label2'
                          color={theme.color?.general.lighter}
                        />
                      </div>
                    </Fragment>
                  );
                })}
              </div>
            </div>
          </div>
        )}
        {item.status !== 'finished' && <StyledVerticalLine className='h-[72px] max-md:hidden' />}
        <div className='flex grow gap-2'>
          <div className='flex grow justify-start md:justify-center'>
            <div className='flex flex-col gap-1 md:gap-2'>
              <Typography
                className='w-max'
                text={`${t('prizeFund')}:`}
                type={isMobile ? 'label1' : 'body1'}
                color={theme.color?.general.lighter}
              />
              <Typography
                className='break-all'
                text={getValueWithCurrency(item.total_prize, item.rates, userCurrency)}
                type='h3'
                color={theme.color?.secondary.dark}
              />
            </div>
          </div>
          <StyledVerticalLine className='h-[72px] max-md:hidden' />
          <div className='flex grow justify-center'>
            <div className='flex flex-col gap-1 md:gap-2'>
              <Typography
                text={`${t('bet')}:`}
                type={isMobile ? 'label1' : 'body1'}
                color={theme.color?.general.lighter}
              />
              <Typography
                className='break-all'
                text={`${getValueWithCurrency(item.bet_min, item.rates, userCurrency)}-${getValueWithCurrency(item.bet_max, item.rates, userCurrency)}`}
                type='h3'
              />
            </div>
          </div>
          <StyledVerticalLine className='h-[72px] max-md:hidden' />
          <div className='flex grow justify-end md:justify-center'>
            <div className='flex flex-col gap-1 md:gap-2'>
              <Typography
                className='w-max'
                text={`${t('prizePlaces')}:`}
                type={isMobile ? 'label1' : 'body1'}
                color={theme.color?.general.lighter}
              />
              <Typography text={item?.prizes ? Object.keys(item?.prizes)?.length : '0'} type='h3' />
            </div>
          </div>
        </div>
      </div>
      {/*<div className='mt-4 flex gap-1 max-md:hidden'>*/}
      {/*  <Typography*/}
      {/*    text={`${t('type')}:`}*/}
      {/*    color={theme.color?.general.lighter}*/}
      {/*    type={isMobile ? 'label2' : 'body1'}*/}
      {/*  />*/}
      {/*  <Typography*/}
      {/*    text={t(item.calculation_type)}*/}
      {/*    type={isMobile ? 'label2' : 'body1'}*/}
      {/*    color={theme.color?.general.lightest}*/}
      {/*  />*/}
      {/*</div>*/}
      <BonusHint className='mt-4 max-sm:hidden' />
    </div>
  );
};

export default TournamentInfoCard;
