'use client';
import { ITournament } from '@/app/api/getTournaments';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { splitObjects } from '@/utils/splitObject';
import { useParams } from 'next/navigation';
import { getPrizeString } from './TournamentTableCard';

const PrizesCard = ({ item }: { item: ITournament }) => {
  const {
    lng,
  }: {
    lng: LanguagesType;
  } = useParams();

  const { t } = useTranslation(lng);
  const { user } = useUser();

  const prizes = splitObjects(item.prizes, 8);

  return (
    <>
      <Typography
        text={t('prizes')}
        fontSize='28px'
        fontWeight={theme.font.weight.medium}
        color={theme.color?.general.white}
        margin='0 0 10px 0'
      />
      <div className='mb-4 mt-[10px] flex max-h-[156px] min-w-[320px] flex-col flex-wrap gap-[15px]'>
        {Object.keys(prizes).map((place: any, idx: any) => (
          <div key={idx} className='flex'>
            {idx < 3 ? (
              <Icon
                name='placeStar'
                level={place}
                stroke={['#D3B517', '#C0C0C0', '#CD7F32'][idx]}
              />
            ) : (
              <Typography
                text={place}
                fontSize='10px'
                fontWeight={theme.font.weight.bold}
                color={theme.color?.general.white}
                className='h-[24px] w-[24px] justify-center rounded-[6px] bg-[#253A5E]'
              />
            )}
            <Typography
              text='-'
              fontWeight={theme.font.weight.medium}
              color={theme.color?.general.white}
              className='ml-[10px] mr-[5px]'
            />
            <Typography
              text={getPrizeString(prizes[place], item.rates, user?.currency)}
              fontWeight={theme.font.weight.medium}
              color={theme.color?.general.white}
            />
          </div>
        ))}
      </div>
    </>
  );
};

export default PrizesCard;
