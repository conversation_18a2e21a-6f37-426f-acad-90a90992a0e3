'use client';
import styled from 'styled-components';

export const StyledTournamentsCard: any = styled.div`
  position: relative;
  &.landingCard {
    .imageCard {
      height: auto;
      height: 360px;
      min-width: 530px;
      img {
        height: 360px;
        min-width: 530px;
      }
      &.finishedCard {
        filter: grayscale(100%);
      }
    }
    .infoBlock,
    .prizesBlock {
      padding: 60px 24px 20px;
      max-width: 260px;
      min-width: 230px;
    }
    .prizesBlock {
      padding-left: 0;
    }
    .infoCard {
      right: 24px;
      .badge {
        top: 24px;
      }
    }
  }

  .imageCard {
    border-radius: ${({ theme }: any) => theme.size.border.radius.main};
    position: relative;
    height: 406px;
    background-color: rgb(35, 44, 59);
    overflow: hidden;
    &.finishedCard {
      filter: grayscale(100%);
    }
  }
  .infoCard {
    position: absolute;
    top: 24px;
    bottom: 24px;
    left: 24px;
    border-radius: ${({ theme }: any) => theme.size.border.radius.main};
    background: ${({ theme }) => `${theme.color?.general.darkest}e6`};
    display: flex;
    @media screen and (min-width: ${({ theme }) =>
        theme.breakpoints?.sm}px) and (max-width: 768px) {
      width: 96%;
      left: 12px;
      .infoBlock,
      .prizesBlock {
        padding: 62px 12px 24px;
      }
    }
    .badge {
      border-radius: 0 48px 48px 0;
      width: max-content;
      position: absolute;
      top: 30px;
      left: 0;
    }
    .date {
      left: 50%;
      transform: translate(-50%, -45%);
      top: 50%;
    }
    .bg {
      background-color: ${({ theme }) => theme.color?.general.darker};
      width: 100%;
      height: 19px;
      position: absolute;
      top: 0;
      border-radius: ${({ theme }: any) =>
        `${theme.size.border.radius.main}  ${theme.size.border.radius.main} 0 0`};
      &:nth-child(2) {
        top: auto;
        bottom: 0;
        border-radius: ${({ theme }: any) =>
          `0 0 ${theme.size.border.radius.main}  ${theme.size.border.radius.main}`};
      }
    }
    .bonusTag {
      border-radius: ${({ theme }: any) => theme.size.border.radius.main};
      padding: 4px 10px;
      width: max-content;
      margin-bottom: auto;
    }
    .infoBlock,
    .prizesBlock {
      padding: 62px 24px 24px;
    }
    .infoBlock {
      max-width: 230px;
    }
    .prizesBlock {
      padding-left: 0;
    }
  }

  &.mobileCard {
    .imageCard {
      height: 275px;
      min-height: 275px;
      width: 320px;
      min-width: 320px;
      width: 100%;
      height: 100%;
      img {
        height: 275px;
        min-width: 320px;
        width: 320px;
      }
      &.finishedCard {
        filter: grayscale(100%);
      }
    }
    &.pageCard {
      .imageCard {
        border-bottom-left-radius: 0;
        border-bottom-right-radius: 0;
        &.finishedCard {
          filter: grayscale(100%);
        }
      }
    }
    &.landingCard {
      .imageCard {
        &.finishedCard {
          filter: grayscale(100%);
        }
      }
    }
    .badge {
      position: absolute;
      top: 16px;
      border-radius: 0 48px 48px 0;
      left: 0;
      &.date {
        border-radius: 48px 0 0 48px;
        right: 0;
        left: auto;
        background-color: ${({ theme }) => theme.color?.general.dark};
        padding: 4px 0px 4px 16px;
      }
    }
    .infoBlock {
      position: absolute;
      bottom: 0;
      background-color: #1e293b;
      min-width: auto;
      max-width: none;
      width: 100%;
      padding: 8px 16px;
      border-bottom-left-radius: ${({ theme }: any) =>
        theme.size.border.radius.main};
      border-bottom-right-radius: ${({ theme }: any) =>
        theme.size.border.radius.main};
    }
  }
`;

export const StyledPrizesTable: any = styled.table`
  border-collapse: separate;
  border-spacing: 0;
  min-width: 320px;

  thead {
    td {
      padding: 0 10px 5px;
    }
  }
  tbody {
    tr {
      td {
        padding: 8px 10px;
        border-top: 1px solid transparent;
        border-bottom: 1px solid transparent;
        border-bottom-color: rgba(255, 255, 255, 0.1);
        &:first-child {
          border-left: 1px solid transparent;
        }
        &:last-child {
          border-right: 1px solid transparent;
        }
      }
      &.userRow {
        td {
          border-top-color: #0083ff;
          border-bottom-color: #0083ff;
          background-color: #253a5e;
          &:first-child {
            border-left-color: #0083ff;
          }
          &:last-child {
            border-right-color: #0083ff;
          }
        }
      }
      &.finished:last-child {
        td {
          border-bottom: 0;
        }
      }
    }
  }
`;

export const StyledTournamentPage: any = styled.div`
  .badgeContainer {
    top: 24px;
    left: 0;
    .badge {
      border-radius: 0 48px 48px 0;
      width: max-content;
    }
  }
  .typography.description {
    ul {
      list-style: disc;
      padding: 0 16px;
    }
    ol {
      list-style: decimal;
      padding: 0 16px;
    }
    strong {
      color: ${({ theme }) => theme.color?.secondary.dark};
    }
  }
  .relatedCarousel {
    .button.carousel,
    .arrow.carousel {
      border: 1px solid ${({ theme }) => theme.color?.general.dark};
    }
  }
`;
