import { getTournaments } from '@/app/api/getTournaments';
import { LanguagesType } from '@/types/global.js';
import TournamentsList from './TournamentsList';
import { cookies } from 'next/headers';

const LIMIT = 50;

const TournamentsComponent = async ({ lng }: { lng: LanguagesType }) => {
  const { items: tournaments } = await getTournaments(0, LIMIT);

  const userCurrencyFromCookies = cookies().get('userCurrency')?.value || '';

  const activeTournaments = tournaments.filter(
    (tournament) => tournament.status === 'active',
  );
  const plannedTournaments = tournaments.filter(
    (tournament) => tournament.status === 'planned',
  );
  const finishedTournaments = tournaments.filter(
    (tournament) => tournament.status === 'finished',
  );

  return (
    <TournamentsList
      lng={lng}
      gamePageType='casino'
      isLandingCard={true}
      activeTournaments={activeTournaments}
      plannedTournaments={plannedTournaments}
      finishedTournaments={finishedTournaments}
      userCurrencyFromCookies={userCurrencyFromCookies}
    />
  );
};

export default TournamentsComponent;
