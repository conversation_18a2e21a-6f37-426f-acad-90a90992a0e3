'use client';
import { IPost, ITopic } from '@/app/api/getPosts.ts';
import { LanguagesType } from '@/types/global';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import FaqItem from '@/app/components/HelpCenter/FaqItem.tsx';
import { sortItems } from '@/utils/sortItems.ts';

const FaqList = ({
  faqs,
  lng,
  topic,
  isMobile,
  faqItemClassName,
}: {
  faqs: IPost[];
  topic: ITopic;
  lng: LanguagesType;
  isMobile: boolean;
  faqItemClassName?: string;
}) => {
  const topicId = topic.id || '';

  const [{ constants }] = useSystemData();

  if (!faqs?.length) {
    return null;
  }

  const postsConstant = constants?.find(
    (constant) => constant.key === 'posts_order',
  );
  const postsOrderArr = postsConstant
    ? JSON.parse(postsConstant.value)[topicId.toString()] ||
      JSON.parse(postsConstant.value)[topicId] ||
      []
    : [];

  const faqSorted = sortItems({
    items: faqs,
    order: postsOrderArr,
  });

  return (
    <>
      {faqSorted.map((post: IPost) => {
        return (
          <FaqItem
            key={post.id}
            faq={post}
            lng={lng}
            isMobile={isMobile}
            faqItemClassName={faqItemClassName}
          />
        );
      })}
    </>
  );
};

export default FaqList;
