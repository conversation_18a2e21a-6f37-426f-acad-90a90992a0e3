'use client';
import Link from 'next/link';

import { IPost, ITopic } from '@/app/api/getPosts';
import { Icon, Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { sortItems } from '@/utils/sortItems.ts';
import { StyledHeaderWrapper } from '@/atomic-design-components/organisms/ExpansionPanel/styled';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { theme } from '@/theme.ts';

const PostsList = ({
  posts,
  lng,
  topic,
  gamePageType,
}: {
  posts: IPost[];
  topic: ITopic;
  lng: LanguagesType;
  gamePageType: GamePageType;
}) => {
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;

  const topicId = topic.id || '';

  const [{ constants }] = useSystemData();

  const postsConstant = constants?.find(
    (constant) => constant.key === 'posts_order',
  );
  const postsOrderArr = postsConstant
    ? JSON.parse(postsConstant.value)[topicId.toString()] ||
      JSON.parse(postsConstant.value)[topicId] ||
      []
    : [];

  if (!posts?.length) {
    return null;
  }

  const postsSorted = sortItems({
    items: posts,
    order: postsOrderArr,
  });

  return (
    <div className='flex w-full flex-wrap items-start gap-2'>
      <Typography
        className='w-full'
        type='h3'
        text={getAvailableTranslation(topic.translations, lng)}
      />
      {postsSorted.map((post: IPost) => {
        return (
          <StyledHeaderWrapper
            as={Link}
            href={`/${lng}/${gamePageType}/help-center/post/${post.slug}`}
            key={post.id}
            className='w-full md:w-[calc(50%-4px)]'
            headerPadding={isMobile ? '12px 8px' : '12px 16px'}
          >
            <div className='flex w-full justify-between'>
              <Typography
                text={getAvailableTranslation(post.translations, lng)}
                type='body2'
              />
              <Icon
                name='chevronDown'
                width={16}
                height={16}
                wrapperWidth={24}
                wrapperHeight={24}
                style={{ transform: 'rotate(-90deg)' }}
              />
            </div>
          </StyledHeaderWrapper>
        );
      })}
    </div>
  );
};

export default PostsList;
