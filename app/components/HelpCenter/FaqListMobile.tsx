'use client';

import { Tabs, Typography } from '@/atomic-design-components';
import React, { useState } from 'react';
import clsx from 'clsx';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import FaqList from '@/app/components/HelpCenter/FaqList.tsx';
import { IPost, ITopic } from '@/app/api/getPostsServer.ts';
import { LanguagesType } from '@/types/global';

const FaqListMobile = ({
  lng,
  faqs,
  topics,
}: {
  faqs: IPost[];
  topics: ITopic[];
  lng: LanguagesType;
}) => {
  const [activeTab, setActiveTab] = useState(0);

  const topicsFiltered = topics.filter((topic) => {
    const faqsFiltered = faqs.filter((faq: IPost) => faq.topic_id === topic.id);
    return faqsFiltered.length > 0;
  });

  const onTabClick = (e: Event, idx: number) => {
    setActiveTab(idx);
  };

  const getTabTitle = (topic: any, i: number) => {
    const selected = i === activeTab;

    return (
      <div id={topic.id} className={clsx('menuItem', selected && 'selected')}>
        <Typography
          text={getAvailableTranslation(topic.translations, lng)}
          type='sub3'
        />
      </div>
    );
  };

  const tabs = topicsFiltered;
  const tabsContents = topicsFiltered.map((topic) => {
    return (
      <FaqList
        faqs={faqs.filter((faq: IPost) => faq.topic_id === topic.id)}
        topic={topic}
        lng={lng}
        key={topic.id}
        isMobile
        faqItemClassName='mb-2'
      />
    );
  });

  return (
    <Tabs
      activeTabProp={activeTab}
      onTabChange={onTabClick}
      getTabTitle={getTabTitle}
      tabsTitles={tabs}
      tabsContents={tabsContents}
      hideNavBtns
      tabPadding='0 0 8px;'
      type='buttons'
      className='w-full'
    />
  );
};

export default FaqListMobile;
