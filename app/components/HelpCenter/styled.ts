'use client';
import { FlexRow } from '@/atomic-design-components';
import styled from 'styled-components';

export const StyledListContainer: any = styled(FlexRow)`
  background-color: #212531;
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: 16px 26px;
  max-height: 584px;
`;

export const StyledList: any = styled(FlexRow)`
  .listItemContent {
    cursor: pointer;
    &::before {
      content: '•';
      font-size: 38px;
      line-height: 14px;
      margin-right: 8px;
      color: ${({ theme }) => theme.color?.general.light};
    }
  }
`;

export const StyledPostItem: any = styled(FlexRow)`
  background-color: #212531;
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  border: 1px solid #292d3a;
  padding: 12px 40px 12px 12px;
  position: relative;
  &::after {
    content: '';
    border-radius: 4px;
    position: absolute;
    top: 5px;
    right: 5px;
    border-top: 0px solid #292d3a;
    border-right: 20px solid #292d3a;
    border-bottom: 24px solid transparent;
  }
  &:hover {
    border-color: ${({ theme }) => theme.color?.general.light};
    .typography {
      color: ${({ theme }) => theme.color?.general.light};
    }
  }
`;
