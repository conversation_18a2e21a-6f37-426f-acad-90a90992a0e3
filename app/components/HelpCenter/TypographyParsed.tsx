'use client';
import useParseRichText from '@/hooks/useParseRichText.tsx';
import { Typography } from '@/atomic-design-components';

const TypographyParsed = ({
  textToParse,
  type,
  className,
  color,
}: {
  textToParse: string;
  type: string;
  className?: string;
  color?: string;
}) => {
  const { parse } = useParseRichText();

  return (
    <Typography
      text={parse(textToParse)}
      displayCssProp='block'
      type={type}
      className={className}
      color={color}
    />
  );
};

export default TypographyParsed;
