'use client';
import { LOGO_PLACEHOLDER } from '@/constants';
import clsx from 'clsx';
import { Image } from '@/atomic-design-components';
import { useState } from 'react';

const PostCard = ({ item, index }: { item: any; index: number }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div
      className={clsx(
        'gameCard postCard align-center flex flex-col justify-center',
        !item.url && 'placeholder',
        !isLoaded && 'loading',
      )}
    >
      {(!isLoaded || !item.url) && (
        <Image
          alt={item.meta.file_name_original || `Post ${index} placeholder`}
          placeholder={undefined}
          src={LOGO_PLACEHOLDER}
          fill
          sizes='357px'
          style={{ objectFit: 'contain' }}
          className='placeholder'
          unoptimized
        />
      )}
      {item.url && (
        <Image
          alt={item.meta.file_name_original || `Post ${index}`}
          placeholder={undefined}
          onLoad={() => setIsLoaded(true)}
          src={item.url}
          fill
          sizes='100vw'
          style={{ objectFit: 'cover' }}
        />
      )}
    </div>
  );
};

export default PostCard;
