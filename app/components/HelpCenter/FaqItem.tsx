'use client';
import useParseRichText from '@/hooks/useParseRichText.tsx';

import { IPost } from '@/app/api/getPostsServer.ts';
import { ExpansionPanel, Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation';
import clsx from 'clsx';

const FaqItem = ({
  faq,
  lng,
  isMobile,
  faqItemClassName,
}: {
  faq: IPost;
  lng: LanguagesType;
  isMobile: boolean;
  faqItemClassName?: string;
}) => {
  const { parse } = useParseRichText();
  const text = getAvailableTranslation(faq.body, lng);
  const targetIndex = text.indexOf('</p>', 800);
  const textToShow = targetIndex > 0 ? text.slice(0, targetIndex + 4) : text;

  return (
    <ExpansionPanel
      key={faq.id}
      className={clsx('basis-full md:basis-[calc(50%-4px)]', faqItemClassName)}
      align='left'
      header={getAvailableTranslation(faq.translations, lng)}
      id={faq.name}
      panelName={faq.name}
      headerIconRightProps={{
        width: 16,
        height: 16,
        wrapperWidth: 24,
        wrapperHeight: 24,
      }}
      headerPadding={isMobile ? '12px 8px' : '12px 16px'}
      detailsPadding={isMobile ? '0 8px 12px' : '0 16px 16px'}
    >
      <Typography
        text={parse(textToShow)}
        type='body1'
        className='postContent'
        color={theme.color?.general.lighter}
        displayCssProp='block'
      />
      {/*<Link href={`/${lng}/${gamePageType}/help-center/post/${post.slug}`}>*/}
      {/*  <Button text={t('readMore')} size='small' className='mt-4' />*/}
      {/*</Link>*/}
    </ExpansionPanel>
  );
};

export default FaqItem;
