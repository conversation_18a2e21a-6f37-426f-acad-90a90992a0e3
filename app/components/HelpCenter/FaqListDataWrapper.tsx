'use client';
import React from 'react';
import { IPost, ITopic } from '@/app/api/getPostsServer.ts';
import { LanguagesType } from '@/types/global';
import { Typography } from '@/atomic-design-components';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import FaqList from '@/app/components/HelpCenter/FaqList.tsx';
import useWindowSize from '@/hooks/useWindowSize.ts';
import { theme } from '@/theme.ts';

const FaqListDataWrapper = ({
  lng,
  topics,
  faqs,
}: {
  topics: ITopic[];
  lng: LanguagesType;
  faqs: IPost[];
}) => {
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;

  return topics.map((topic: ITopic) => {
    const faqsFiltered = faqs.filter((faq: IPost) => faq.topic_id === topic.id);

    if (!faqsFiltered?.length) {
      return null;
    }

    return (
      <div className='flex w-full flex-wrap items-start gap-2' key={topic.id}>
        <Typography
          className='w-full'
          type='h3'
          text={getAvailableTranslation(topic.translations, lng)}
        />
        <FaqList
          key={topic.id}
          faqs={faqsFiltered}
          topic={topic}
          lng={lng}
          isMobile={isMobile}
        />
      </div>
    );
  });
};

export default FaqListDataWrapper;
