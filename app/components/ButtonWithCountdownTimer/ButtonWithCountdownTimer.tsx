import { useState, useEffect, useRef } from 'react';
import { Button } from '@/atomic-design-components';
import parse from 'html-react-parser';

const ButtonWithCountdownTimer = ({
  secondsInitial = 60,
  buttonProps = {},
  buttonTranslationKey,
  t,
}: {
  secondsInitial: number;
  buttonProps?: any;
  buttonTranslationKey: string;
  t: Function;
}) => {
  const [secondsLeft, setSecondsLeft] = useState(secondsInitial);
  const endTimeRef = useRef(Date.now() + secondsInitial * 1000);
  const intervalRef: any = useRef(null);

  // Start/restart timer when secondsInitial changes
  useEffect(() => {
    endTimeRef.current = Date.now() + secondsInitial * 1000;
    setSecondsLeft(secondsInitial);

    if (intervalRef.current) clearInterval(intervalRef.current);

    intervalRef.current = setInterval(() => {
      const seconds = Math.max(0, Math.round((endTimeRef.current - Date.now()) / 1000));
      setSecondsLeft(seconds);

      if (seconds <= 0) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }, 1000);

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [secondsInitial]);

  // derive minutes and seconds
  const mins = Math.floor(secondsLeft / 60);
  const secs = secondsLeft % 60;

  // format as “m:ss” with leading zero for seconds
  const formatted = `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;

  const onButtonClick = (e: any) => {
    e.preventDefault();
    if (secondsLeft > 0) return;
    if (buttonProps.onClick) {
      buttonProps.onClick(e);
    }
    // setSecondsLeft(minutes * 60);
    // startInterval();
  };

  // const timerWidth = 'w-[44px]'; //secondsLeft >= 600 ? 'w-[43px]' : 'w-[37px]';
  const translationKey =
    secondsLeft <= 0 ? buttonTranslationKey : `${buttonTranslationKey}WithTimer`;
  const text = parse(t(translationKey, { timer: formatted }));

  return (
    <>
      <Button
        {...buttonProps}
        disabled={secondsLeft > 0 || buttonProps.disabled}
        onClick={onButtonClick}
      >
        {text}
      </Button>
    </>
  );
};

export default ButtonWithCountdownTimer;
