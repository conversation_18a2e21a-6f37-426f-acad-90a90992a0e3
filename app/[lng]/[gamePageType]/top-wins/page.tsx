import React, { Suspense } from 'react';
import clsx from 'clsx';
import { cookies } from 'next/headers';
import Link from 'next/link';

import { IGame } from '@/app/api/getGames';
import GamesList from '@/app/components/Games/GamesList';
import TopWinningsCard from '@/app/components/TopWinnigs/TopWinningsCard';
import { TilesGrid, Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { TOP_WINS_GRID_BREAKPOINTS } from '@/app/config/breakpoints/topWinsBreakpoints.ts';
import LoadingTopWinsCard from '@/app/components/Skeletons/LoadingTopWinsCard.tsx';
import { DEFAULT_CURRENCY } from '@/constants.ts';
import { getServerTranslation } from '@/app/i18n';
import { getGamesTopWinsServer } from '@/app/api/getGamesTopWinsServer.ts';

interface IProps {
  params: {
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
  searchParams: { days?: string; currency?: string };
}

const LIMIT = 100;

const transformItems = (items: IGame[], currency: string | undefined) => {
  const itemsTransformed = currency
    ? items?.map((game: IGame) => {
        const amount =
          game.win_amount && game.rate ? Math.floor(game.win_amount * game.rate) : game.win_amount;
        return { ...game, win_amount: amount };
      })
    : items;
  return itemsTransformed;
};

function DaysTabs({ activeDays, t }: { activeDays: 1 | 7 | 30; t: Function }) {
  const tabs = [
    { label: t('Day'), value: 1 as const },
    { label: t('week'), value: 7 as const },
    { label: t('month'), value: 30 as const },
  ];

  return (
    <div className='mb-4 flex gap-2'>
      {tabs.map(({ label, value }) => (
        <Link key={value} href={`?days=${value}`} prefetch>
          <Typography
            text={label}
            padding='8px 12px'
            type='body2'
            className={clsx(
              'cursor-pointer rounded-[48px] hover:bg-[#334155]',
              activeDays === value && 'bg-[#1E293B]',
            )}
          />
        </Link>
      ))}
    </div>
  );
}

async function TopWinsData({
  gamePageType,
  days,
  currency,
}: {
  gamePageType: GamePageType;
  days: 1 | 7 | 30;
  currency: string;
}) {
  const { items, total } = await getGamesTopWinsServer(0, LIMIT, {
    is_live: [(gamePageType === 'live-casino').toString()],
    rate_currency: [currency],
    days: [days.toString()],
  });

  const games = transformItems(items, currency);

  return (
    <GamesList
      initialData={games}
      totalItems={total}
      limit={LIMIT}
      withLoadMore={false}
      cardComponent={TopWinningsCard}
      itemsInRow={5}
      breakpoints={TOP_WINS_GRID_BREAKPOINTS}
      cardProps={{ userCurrency: currency }}
      isInitiallyLoaded
      isTopWinsPage
    />
  );
}

export default async function TopWinsPage({ params, searchParams }: IProps) {
  const { lng, gamePageType } = params;

  const { t } = await getServerTranslation(lng);

  const currency = cookies()?.get('userCurrency')?.value || DEFAULT_CURRENCY;

  const daysParam = searchParams.days;
  const days: 1 | 7 | 30 = daysParam === '1' ? 1 : daysParam === '7' ? 7 : 30;

  return (
    <div className='topWinsPage'>
      <div className='align-center relative mb-2'>
        <Typography text={t('topWins')} type='h1' iconName='coins' />
      </div>

      <DaysTabs activeDays={days} t={t} />

      <Suspense
        key={days}
        fallback={
          <TilesGrid
            tiles={Array(40).fill(<LoadingTopWinsCard />)}
            breakpoints={TOP_WINS_GRID_BREAKPOINTS}
          />
        }
      >
        <TopWinsData gamePageType={gamePageType} days={days} currency={currency} />
      </Suspense>
    </div>
  );
}
