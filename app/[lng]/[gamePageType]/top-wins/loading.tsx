import { TilesGrid } from '@/atomic-design-components';
import LoadingTopWinsCard from '@/app/components/Skeletons/LoadingTopWinsCard.tsx';
import { TOP_WINS_GRID_BREAKPOINTS } from '@/app/config/breakpoints/topWinsBreakpoints.ts';

export default function Loading() {
  return (
    <div className='topWinsPage'>
      {/* Title */}
      <div className='mb-4 h-6 w-40 animate-pulse rounded bg-gray-600'></div>

      {/* Time Filters (Day, Week, Month) */}
      <div className='mb-6 flex gap-4'>
        <div className='h-8 w-14 animate-pulse rounded-full bg-gray-600'></div>
        <div className='h-8 w-14 animate-pulse rounded-full bg-gray-600'></div>
        <div className='h-8 w-14 animate-pulse rounded-full bg-gray-600'></div>
      </div>

      <TilesGrid
        tiles={Array(40).fill(<LoadingTopWinsCard />)}
        breakpoints={TOP_WINS_GRID_BREAKPOINTS}
      />
    </div>
  );
}
