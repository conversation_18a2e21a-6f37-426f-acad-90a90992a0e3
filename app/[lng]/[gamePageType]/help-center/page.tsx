import React from 'react';

import { getPosts, getTopics, IPost, ITopic } from '@/app/api/getPosts';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import PostsList from '@/app/components/HelpCenter/PostsList.tsx';

interface IProps {
  params: {
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
}

const HelpCenterPage: React.FC<IProps> = async ({
  params: { lng, gamePageType },
}) => {
  const { t } = await getServerTranslation(lng);
  const { items: topics } = await getTopics();
  const { items: posts } = await getPosts(0, 200, {
    post_type: ['post'],
  });

  return (
    <div className='helpCenterPage'>
      <Typography text={t('helpCenter')} type='h1' className='!mb-4' />
      <div className='flex flex-col items-start gap-2'>
        {topics.map((topic: ITopic) => (
          <PostsList
            posts={posts.filter((faq: IPost) => faq.topic_id === topic.id)}
            topic={topic}
            lng={lng}
            key={topic.id?.toString()}
            gamePageType={gamePageType}
          />
        ))}
      </div>
    </div>
  );
};

export default HelpCenterPage;
