export default function Loading() {
  // Create arrays for topics and FAQ items
  const topics = Array.from({ length: 4 }); // 4 topic sections
  const faqItemsPerTopic = Array.from({ length: 4 }); // 4 FAQ items per topic

  return (
    <div className='helpCenterPage'>
      {/* Page Title */}
      <div className='mb-4 h-6 w-40 animate-pulse rounded bg-gray-600 sm:h-8'></div>

      {/* Topics and FAQs Container */}
      <div className='flex flex-col items-start gap-3'>
        {topics.map((_, topicIndex) => (
          <div
            key={topicIndex}
            className='flex w-full flex-wrap items-start gap-2'
          >
            {/* Topic Title */}
            <div className='mb-1 h-[23px] w-1/4 animate-pulse rounded bg-gray-600'></div>

            {/* FAQ Items */}
            <div className='flex w-full flex-wrap gap-2'>
              {faqItemsPerTopic.map((_, faqIndex) => (
                <div
                  key={faqIndex}
                  className='basis-full animate-pulse md:basis-[calc(50%-4px)]'
                >
                  {/* FAQ Header */}
                  <div className='flex h-12 items-center justify-between rounded-lg bg-[#1E293B] px-4'>
                    <div className='h-4 w-2/3 rounded bg-gray-600'></div>
                    <div className='h-4 w-6 rounded-full bg-gray-600'></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
