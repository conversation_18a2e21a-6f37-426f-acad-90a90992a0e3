import Link from 'next/link';
import { notFound } from 'next/navigation';

import { getPostServer } from '@/app/api/getPostsServer.ts';
import PostCard from '@/app/components/HelpCenter/PostCard.tsx';
import Slider from '@/app/components/Slider/Slider.tsx';
import { getServerTranslation } from '@/app/i18n';
import { Icon, Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { GamePageType, LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import TypographyParsed from '@/app/components/HelpCenter/TypographyParsed.tsx';

const BlogPostPage = async ({
  params: { id, slug, lng, gamePageType },
}: {
  params: {
    id: number;
    slug: string;
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
}) => {
  const { t } = await getServerTranslation(lng);
  const post = await getPostServer(id || slug);
  const photos = post?.photos?.general?.active || [];

  if (post.post_type !== 'post') {
    notFound();
  }

  return (
    <div className='postPage max-w-[750px]'>
      <Link href={`/${lng}/${gamePageType}/help-center`} className='!text-general-lighter'>
        <div className='flex items-center gap-1'>
          <Icon
            name='chevronDown'
            width={10}
            height={10}
            wrapperWidth={16}
            wrapperHeight={16}
            style={{ transform: 'rotate(90deg)' }}
          />
          <Typography text={t('backToHelpCenter')} lineHeight='28px' />
        </div>
      </Link>
      <Typography text={post.translations[lng]} type='h2' className='!my-2' />

      <TypographyParsed
        textToParse={getAvailableTranslation(post.body, lng)}
        type='body2'
        className='postContent !mb-4'
        color={theme.color?.general.lighter}
      />

      <Slider
        items={photos}
        total={photos.length}
        Slide={PostCard}
        perView={2}
        renderMode='performance'
        loop={photos.length > 2}
        withArrows={photos.length > 3}
        loopedSlides={Math.floor(photos.length / 2)}
        breakpoints={{
          '(max-width: 1050px)': {
            slides: {
              perView: 2,
              spacing: 16,
              dragSpeed: 0,
              defaultAnimation: { duration: 4000 },
            },
          },
          '(max-width: 520px)': {
            slides: {
              perView: 1,
              spacing: 8,
              dragSpeed: 0,
              defaultAnimation: { duration: 4000 },
            },
          },
        }}
      />
    </div>
  );
};

export default BlogPostPage;
