import { cookies } from 'next/headers';
import { getGameByIdOrSlug } from '@/app/api/getGames.ts';
import { getGameToken } from '@/app/api/getGameToken.ts';
import { LanguagesType } from '@/types/global';
import './styles.css';
import GameUrlWrapper from '@/app/components/Game/GameUrlWrapper.tsx';

interface GamePageProps {
  params: {
    gameSlug: string;
    lng: LanguagesType;
  };
  searchParams: { [key: string]: string | undefined };
}

const GamePage = async ({
  params: { gameSlug, lng },
  searchParams,
}: GamePageProps) => {
  const currency = cookies().get('userCurrency')?.value || '';
  const userId = cookies().get('userExternalId')?.value || '';

  const isDemo = searchParams?.demo === 'true';

  const result = isDemo || !userId ? '' : await getGameToken(currency);
  // console.log(userId, result);
  const game = await getGameByIdOrSlug(gameSlug);

  return (
    <GameUrlWrapper
      lng={lng}
      game={game || {}}
      isDemo={isDemo}
      gameTokenResult={result}
      currency={currency}
    />
  );
};

export default GamePage;
