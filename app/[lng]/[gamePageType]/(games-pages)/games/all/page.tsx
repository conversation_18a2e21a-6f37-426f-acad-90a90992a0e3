import GamesPage from '@/app/components/Games/GamesPage';
import { GamePageType, LanguagesType } from '@/types/global';
import FiltersAndTitleManager from '@/app/components/FiltersAndTitleManager/FiltersAndTitleManager.tsx';
import { ENTITIES } from '@/app/config/navMenuEntities.ts';

interface CategoryPageProps {
  params: { lng: LanguagesType; gamePageType: GamePageType };
  searchParams: { [key: string]: string | undefined };
}

const CategoryGamesPage = async ({
  params: { lng, gamePageType },
  searchParams,
}: CategoryPageProps) => {
  const currentEntity = Object.values(ENTITIES.all)
    .flat()
    .find((entity) => entity.route.includes('all'));

  return (
    <>
      <FiltersAndTitleManager currentEntity={currentEntity} lng={lng} />
      {/*<Suspense fallback={<LoadingGameCardsGrid />}>*/}
      <GamesPage
        params={{ category: 'all', lng, gamePageType }}
        searchParams={searchParams}
      />
      {/*</Suspense>*/}
    </>
  );
};

export default CategoryGamesPage;
