// import { Suspense } from 'react';
// import { getTournaments } from '@/app/api/getTournaments';
// import GamesPageTournament from '@/app/components/Games/GamesPageTournament';
// import { getServerTranslation } from '@/app/i18n';
// import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';
// import { Typography } from '@/atomic-design-components';
import GamesPage from '@/app/components/Games/GamesPage';
import GamesPageRtp from '@/app/components/Games/GamesPageRtp.tsx';
import { GamePageType, LanguagesType } from '@/types/global';
import FiltersAndTitleManager from '@/app/components/FiltersAndTitleManager/FiltersAndTitleManager.tsx';
import { ENTITIES } from '@/app/config/navMenuEntities.ts';
import { GameCategoryType } from '@/app/store/slices/gamesSlice.ts';

interface CategoryPageProps {
  params: {
    category: GameCategoryType;
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
  searchParams: { [key: string]: string | undefined };
}

// const GamePageTournamentLoading = async ({
//   params: { lng, gamePageType },
// }: {
//   params: {
//     lng: LanguagesType;
//     gamePageType: GamePageType;
//   };
// }) => {
//   const { items: tournaments } = await getTournaments(0, 50, {
//     status: ['active'],
//   });
//
//   return (
//     <GamesPageTournament
//       params={{
//         lng,
//         tournaments,
//         gamePageType,
//       }}
//     />
//   );
// };

const CategoryGamesPage = async ({
  params: { category, lng, gamePageType },
  searchParams,
}: CategoryPageProps) => {
  // const { t } = await getServerTranslation(lng);

  const currentEntity = Object.values(ENTITIES.all)
    .flat()
    .find((entity) => entity.route.includes(category));

  // if (category === 'tournament')
  //   return (
  //     <Suspense
  //       fallback={
  //         <>
  //           <div className='flex items-center max-md:justify-start md:justify-between'>
  //             <div className='mb-4 flex items-center gap-4 max-md:hidden'>
  //               <Typography text={t('tournamentss')} type='h1' iconName='cup' />
  //             </div>
  //           </div>
  //           <LoadingGameCardsGrid />
  //         </>
  //       }
  //     >
  //       <GamePageTournamentLoading
  //         params={{
  //           lng,
  //           gamePageType,
  //         }}
  //       />
  //     </Suspense>
  //   );

  if (category === 'rtp')
    return (
      <GamesPageRtp
        params={{
          lng,
        }}
      />
    );

  return (
    <>
      <FiltersAndTitleManager currentEntity={currentEntity} lng={lng} />
      {/*<Suspense fallback={<LoadingGameCardsGrid />}>*/}
      <GamesPage
        params={{ category, lng, gamePageType }}
        searchParams={searchParams}
      />
      {/*</Suspense>*/}
    </>
  );
};

export default CategoryGamesPage;
