'use client';
import GamesList from '@/app/components/Games/GamesList.tsx';
import GamesListRecommended from '@/app/components/Games/GamesListRecommended.tsx';
import { LanguagesType } from '@/types/global';
import PanelWithIconAndText from '@/app/components/PanelWithIconAndText';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { useTranslation } from '@/app/i18n/client';

const FavouriteGamesWithRecommended = ({ lng }: { lng: LanguagesType }) => {
  const { t } = useTranslation(lng);
  const [{ favourites }] = useSystemData();
  return (
    <>
      {!favourites?.length && (
        <PanelWithIconAndText
          icon='disappointedFace'
          title={t('noGamesYet')}
          text={t('clickHeartToAddGameToLiked')}
        />
      )}
      {!!favourites?.length && (
        <GamesList
          initialData={favourites}
          totalItems={favourites.length}
          limit={50}
          passSetTableDataToCard
          withLoadMore={false}
          category='favourites'
          isInitiallyLoaded
        />
      )}
      <GamesListRecommended lng={lng} gamesToFilterOut={favourites || []} />
    </>
  );
};

export default FavouriteGamesWithRecommended;
