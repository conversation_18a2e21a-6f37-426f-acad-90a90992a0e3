import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import FavouriteGamesWithRecommended from '@/app/[lng]/[gamePageType]/(games-pages)/favourites/FavouriteGamesWithRecommended.tsx';

export const dynamic = 'force-dynamic';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const FavouriteGamesPage = async ({ params: { lng } }: IProps) => {
  const { t } = await getServerTranslation(lng);

  return (
    <>
      <div className='mb-4 flex items-center gap-4 max-md:hidden'>
        <Typography
          type='h1'
          text={`${t('favourite')} ${t('games')}`}
          iconName='heart'
          iconProps={{ width: 24, height: 24 }}
        />
      </div>
      <FavouriteGamesWithRecommended lng={lng} />
    </>
  );
};

export default FavouriteGamesPage;
