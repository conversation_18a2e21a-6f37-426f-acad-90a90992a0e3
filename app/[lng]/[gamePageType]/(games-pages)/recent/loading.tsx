import LoadingTitle from '@/app/components/Skeletons/LoadingTitle.tsx';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';

export default async function Loading() {
  return (
    <>
      <LoadingTitle
        iconName='clockIcon'
        iconProps={{
          width: 28,
          height: 28,
          fill: '#00AD92',
        }}
        titleKey='recent'
        secondTitleKey='games'
      />
      <LoadingGameCardsGrid />
    </>
  );
}
