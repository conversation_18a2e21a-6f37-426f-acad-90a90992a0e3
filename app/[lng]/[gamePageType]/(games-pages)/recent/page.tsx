import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import GamesListRecent from '@/app/components/Games/GamesListRecent.tsx';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const RecentGamesPage = async ({ params: { lng } }: IProps) => {
  const { t } = await getServerTranslation(lng);

  return (
    <>
      <div className='mb-4 flex items-center gap-4 max-md:hidden'>
        <Typography
          type='h1'
          text={`${t('recent')} ${t('games')}`}
          iconName='clockIcon'
          iconProps={{
            width: 28,
            height: 28,
            fill: '#00AD92',
          }}
        />
      </div>
      <GamesListRecent lng={lng} />
    </>
  );
};

export default RecentGamesPage;
