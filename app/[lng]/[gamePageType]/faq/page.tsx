import React from 'react';

import { getPosts, getTopics } from '@/app/api/getPosts';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import FaqListDataWrapper from '@/app/components/HelpCenter/FaqListDataWrapper.tsx';
import FaqListMobile from '@/app/components/HelpCenter/FaqListMobile.tsx';

interface IProps {
  params: {
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
}

const FaqPage: React.FC<IProps> = async ({ params: { lng } }) => {
  const { t } = await getServerTranslation(lng);
  const { items: topics } = await getTopics();
  const { items: faqs } = await getPosts(0, 200, {
    post_type: ['faq'],
  });

  return (
    <div className='faqPage'>
      <Typography text={t('faq')} type='h1' className='!mb-4' />
      <div className='faqsList hidden flex-col items-start gap-2 md:flex'>
        <FaqListDataWrapper topics={topics} faqs={faqs} lng={lng} />
      </div>
      <div className='faqsList flex flex-col items-start gap-2 md:hidden'>
        <FaqListMobile faqs={faqs} topics={topics} lng={lng} />
      </div>
    </div>
  );
};

export default FaqPage;
