import LoadingPromoCard from '@/app/components/Skeletons/LoadingPromoCard.tsx';

export default function Loading() {
  const promotions = Array.from({ length: 10 });

  return (
    <div className='promotionsPage'>
      {/* Title */}
      <div className='mb-5 h-6 w-1/4 animate-pulse rounded bg-gray-600 md:mb-6'></div>

      {/* Promotions Grid */}
      <div className='grid grid-cols-1 gap-2 md:grid-cols-2 md:gap-4'>
        {promotions.map((_, index) => (
          <LoadingPromoCard key={index} />
        ))}
      </div>
    </div>
  );
}
