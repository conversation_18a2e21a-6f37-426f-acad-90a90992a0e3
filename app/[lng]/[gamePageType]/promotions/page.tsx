import { getPromotions } from '@/app/api/getPromotions';
import PromotionsClientComponent from '@/app/components/Promotions/PromotionsClientComponent';
import { LanguagesType } from '@/types/global';
import { filterPromotions } from '@/utils/filterPromotions';
import { cookies } from 'next/headers';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const LIMIT = 32;

const PromotionsPage = async ({ params: { lng } }: IProps) => {
  const isUserAuthorized = !!cookies()?.get('token')?.value;
  const promotionsAll = await getPromotions(0, LIMIT);

  const promotionsFiltered = filterPromotions(
    promotionsAll.items,
    isUserAuthorized,
  );

  return (
    <PromotionsClientComponent
      promotionsInitial={promotionsFiltered}
      promotionsAll={promotionsAll}
      lng={lng}
      limit={LIMIT}
    />
  );
};

export default PromotionsPage;
