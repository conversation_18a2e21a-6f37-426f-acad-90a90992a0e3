import { StyledLevelCard } from '@/app/components/Levels/styled';

export default function Loading() {
  const faqItems = Array.from({ length: 12 });

  return (
    <div className='levelsPage'>
      {/* Title */}
      <div className='mb-3 h-6 w-[120px] animate-pulse rounded bg-gray-600 md:mb-6 md:h-7'></div>

      {/* Top Benefits Panels */}
      <div className='mb-5 flex gap-2 overflow-hidden md:mb-4 md:gap-4'>
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className='h-[108px] min-w-[214px] grow animate-pulse rounded-lg bg-[#1E293B]'
          ></div>
        ))}
      </div>

      <div className='overflow-x-hidden rounded-[6px] bg-[#1E293B] px-2 py-3 sm:max-md:mt-2 md:p-6 md:pb-[14px]'>
        {/* Progress Level Title */}
        <div className='mb-7 h-7 w-[230px] animate-pulse rounded bg-gray-600'></div>

        {/* Levels Card Section */}
        <div className='relative flex w-fit gap-2 overflow-x-hidden pb-[64px] md:gap-6'>
          {Array.from({ length: 5 }).map((_, index) => (
            <StyledLevelCard key={index} className='relative flex flex-col'>
              <div className='chevronCard z-20 flex flex-col gap-4 p-4 pt-6'>
                {/* Level Title */}
                <div className='mb-2 h-4 w-1/2 rounded bg-gray-600'></div>

                <div className='mb-2 flex'>
                  {/* Badge */}
                  <div className='h-10 w-10 rounded-full bg-gray-600'></div>

                  {/* Main Reward Info */}
                  <div className='flex grow flex-col items-center gap-2'>
                    <div className='h-4 w-3/4 rounded bg-gray-600'></div>
                    <div className='h-4 w-1/2 rounded bg-gray-600'></div>
                  </div>
                </div>
                {/* Benefits List */}
                <div className='flex flex-col gap-[10px]'>
                  <div className='h-5 w-2/3 rounded bg-gray-600'></div>
                  <div className='h-5 w-3/4 rounded bg-gray-600'></div>
                  <div className='h-5 w-full rounded bg-gray-600'></div>
                  <div className='h-5 w-full rounded bg-gray-600'></div>
                </div>
              </div>
              <div className='bgBottom' />
              {index > 0 && (
                <div className='absolute bottom-[-55px] right-20 h-4 w-20 animate-pulse rounded bg-gray-600'></div>
              )}
            </StyledLevelCard>
          ))}

          {/* Progress Bar */}
          <div className='absolute bottom-10 ml-[125px] mt-2 h-4 w-full rounded-full bg-gray-600'></div>
        </div>
      </div>

      {/* Paragraphs */}
      <div className='mt-3 space-y-2 md:mt-6'>
        <div className='h-4 w-full max-w-[300px] animate-pulse rounded bg-gray-600' />
        <div className='h-4 w-full max-w-[450px] animate-pulse rounded bg-gray-600' />
        <div className='h-4 w-full max-w-[550px] animate-pulse rounded bg-gray-600' />
        <div className='h-4 w-full max-w-[550px] animate-pulse rounded bg-gray-600 md:hidden' />
        <div className='h-4 w-full max-w-[650px] animate-pulse rounded bg-gray-600 md:hidden' />
        <div className='h-4 w-full max-w-[550px] animate-pulse rounded bg-gray-600 sm:hidden' />
        <div className='h-4 w-full max-w-[550px] animate-pulse rounded bg-gray-600 sm:hidden' />
      </div>

      {/* Feature List */}
      <div className='space-y-2 pb-[2px] pt-[10px]'>
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className='flex items-center space-x-3'>
            <div className='h-4 w-4 animate-pulse rounded-full bg-gray-600'></div>
            <div className='h-4 w-[150px] animate-pulse rounded bg-gray-600'></div>
          </div>
        ))}
      </div>

      {/* Final paragraphs */}
      <div className='space-y-2 pt-2'>
        <div className='h-4 w-full animate-pulse rounded bg-gray-600'></div>
        <div className='h-4 w-[400px] animate-pulse rounded bg-gray-600'></div>
        <div className='h-4 w-[300px] animate-pulse rounded bg-gray-600 md:hidden'></div>
      </div>

      {/* Level Description */}
      <div className='mt-3 md:mt-5'>
        <div className='mb-3 h-[23px] w-12 animate-pulse rounded bg-gray-600 md:mb-5'></div>
        <div className='flex w-full flex-wrap gap-2'>
          {faqItems.map((_, faqIndex) => (
            <div
              key={faqIndex}
              className='basis-full animate-pulse md:basis-[calc(50%-4px)]'
            >
              {/* FAQ Header */}
              <div className='flex h-12 items-center justify-between rounded-lg bg-[#1E293B] px-4'>
                <div className='h-4 w-2/3 rounded bg-gray-600'></div>
                <div className='h-4 w-6 rounded-full bg-gray-600'></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
