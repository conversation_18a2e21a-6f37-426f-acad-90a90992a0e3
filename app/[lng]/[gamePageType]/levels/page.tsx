import { cookies } from 'next/headers';
import { getBonuses } from '@/app/api/getBonuses.ts';
import { getLevels, ILevel } from '@/app/api/getLevels';
import LevelsList from '@/app/components/Levels/LevelsList';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { DEFAULT_CURRENCY } from '@/constants.ts';
import { LanguagesType } from '@/types/global';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const LevelsPage = async ({ params: { lng } }: IProps) => {
  const { t } = await getServerTranslation(lng);

  const userCurrency = cookies().get('userCurrency')?.value || DEFAULT_CURRENCY;

  const result = await getLevels(0, 100, {
    currency: [userCurrency],
  });

  const bonusesForLevels = await getBonuses(0, 150, {
    bonus_category: ['birthday', 'cashback', 'level_up'],
    currency: [userCurrency],
  });

  const itemsFiltered = result?.items
    ?.filter((level) => level.next_level_points || level.name === 'level0')
    ?.sort((a: ILevel, b: ILevel) => {
      const levelA = a.number || +a.name.replace('level', '');
      const levelB = b.number || +b.name.replace('level', '');
      if (levelA > levelB) {
        return 1;
      }
      if (levelA < levelB) {
        return -1;
      }
      return 0;
    });

  return (
    <div className='levelsPage'>
      <Typography text={t('levels')} type='h1' />
      <div className='mt-2 flex flex-col md:mt-4 md:gap-4'>
        <LevelsList
          lng={lng}
          currency={userCurrency}
          items={itemsFiltered.map((item, index) => {
            const newItem =
              index > 0
                ? {
                    ...item,
                    prev_cashback_interval_days:
                      itemsFiltered[index - 1].cashback_interval_days,
                    prev_cashback_percentage:
                      itemsFiltered[index - 1].cashback_percentage,
                    prev_max_daily_withdrawal:
                      itemsFiltered[index - 1].max_daily_withdrawal,
                    curr_level_points:
                      itemsFiltered[index - 1].next_level_points,
                    prev_birthday_bonus_id:
                      itemsFiltered[index - 1].bonus_birthday_external_id || 0,
                  }
                : {
                    ...item,
                  };
            return newItem;
          })}
          bonusesForLevels={bonusesForLevels?.items || []}
        />
      </div>
    </div>
  );
};

export default LevelsPage;
