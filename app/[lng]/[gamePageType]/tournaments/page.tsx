import React from 'react';
import { getTournaments } from '@/app/api/getTournaments';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import TournamentsPageList from '@/app/[lng]/[gamePageType]/tournaments/components/TournamentsPageList.tsx';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const LIMIT = 50;

const TournamentsPage: React.FC<IProps> = async ({ params: { lng } }) => {
  notFound();
  const { t } = await getServerTranslation(lng);
  const userCurrencyFromCookies = cookies().get('userCurrency')?.value || '';

  const { items: tournaments } = await getTournaments(0, LIMIT);
  const activeTournaments = tournaments.filter(
    (tournament) => tournament.status === 'active',
  );
  const plannedTournaments = tournaments.filter(
    (tournament) => tournament.status === 'planned',
  );
  const finishedTournaments = tournaments.filter(
    (tournament) => tournament.status === 'finished',
  );

  return (
    <div className='tournamentsPage'>
      <Typography
        text={t('tournaments')}
        type='h1'
        iconName='cup'
        iconProps={{
          width: 16,
          height: 16,
          fill: theme.color?.status.success,
        }}
        padding='2px 8px'
        className='!mb-2 md:!mb-4'
      />

      <TournamentsPageList
        lng={lng}
        activeTournaments={activeTournaments}
        plannedTournaments={plannedTournaments}
        finishedTournaments={finishedTournaments}
        userCurrencyFromCookies={userCurrencyFromCookies}
      />
    </div>
  );
};
export default TournamentsPage;
