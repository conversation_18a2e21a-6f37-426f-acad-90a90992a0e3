import React from 'react';
import {
  // getScoreBoard,
  getTournamentByIdOrSlug,
} from '@/app/api/getTournaments';
import TournamentCardImage from '@/app/components/Tournaments/components/TournamentCardImage';
import TournamentDescriptionCard from '@/app/components/Tournaments/components/TournamentDescriptionCard';
import TournamentGamesCard from '@/app/components/Tournaments/components/TournamentGamesCard';
import TournamentInfoCard from '@/app/components/Tournaments/components/TournamentInfoCard';
import TournamentTableCard from '@/app/components/Tournaments/components/TournamentTableCard';
import { StyledTournamentPage } from '@/app/components/Tournaments/styled';
// import { getServerTranslation } from '@/app/i18n';
import BonusHint from '@/app/components/Tournaments/components/BonusHint';
import TournamentsCardMobile from '@/app/components/Tournaments/TournamentsCardMobile';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';
import clsx from 'clsx';
import Link from 'next/link';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';

interface IProps {
  params: {
    lng: LanguagesType;
    slug: string;
  };
}

const TournamentPage: React.FC<IProps> = async ({ params: { lng, slug } }) => {
  notFound();
  const { t } = await getServerTranslation(lng);
  const userCurrencyFromCookies = cookies().get('userCurrency')?.value || '';
  const item = await getTournamentByIdOrSlug(slug);

  return (
    <StyledTournamentPage className='relative px-2 max-md:mb-2 md:pr-6'>
      <Link href={`/${lng}/casino/tournaments`}>
        <Typography
          text={t('backToTournaments')}
          iconName='chevronDown'
          iconProps={{ className: 'rotate-90', width: 11, height: 9 }}
          padding='12px 0'
        />
      </Link>
      <BonusHint className='mb-2 sm:hidden' />
      <TournamentCardImage
        name={item.name}
        url={item.photo_large_url}
        index={0}
        className={clsx(
          item.status === 'finished' && 'finishedCard',
          'max-md:hidden',
        )}
      />
      <TournamentsCardMobile
        item={item}
        index={0}
        isLandingCard={false}
        isPageCard={true}
        lng={lng}
        className='md:hidden'
        userCurrencyFromCookies={userCurrencyFromCookies}
      />
      <div className='flex flex-col gap-2 md:mt-[20%] md:gap-4 md:px-[5%] lg:px-[6%]'>
        <TournamentInfoCard item={item} lng={lng} />
        <TournamentTableCard item={item} lng={lng} />
        <TournamentGamesCard item={item} />
        <TournamentDescriptionCard description={item.description} lng={lng} />
      </div>
    </StyledTournamentPage>
  );
};
export default TournamentPage;
