import LoadingTournamentCard from '@/app/components/Skeletons/LoadingTournamentCard.tsx';
import LoadingTournamentMobileCard from '@/app/components/Skeletons/LoadingTournamentMobileCard.tsx';

export default function Loading() {
  return (
    <div className='tournamentsPage'>
      <div className='mb-4 h-6 w-1/4 animate-pulse rounded bg-gray-600 sm:mb-6 sm:h-8'></div>
      <div className='sm:hidden'>
        {Array.from({ length: 6 }).map((_, index) => (
          <LoadingTournamentMobileCard key={index} />
        ))}
      </div>
      <div className='max-sm:hidden'>
        {Array.from({ length: 10 }).map((_, index) => (
          <LoadingTournamentCard key={index} />
        ))}
      </div>
    </div>
  );
}
