'use client';
import { ITournament } from '@/app/api/getTournaments.ts';
import TournamentsCard from '@/app/components/Tournaments/TournamentsCard.tsx';
import TournamentsCardMobile from '@/app/components/Tournaments/TournamentsCardMobile.tsx';
import { useSystemData } from '@/app/wrappers/systemDataProvider.tsx';
import { LanguagesType } from '@/types/global';
import { sortItems } from '@/utils/sortItems.ts';
import { Fragment } from 'react';

const TournamentsPageList = ({
  lng,
  activeTournaments,
  plannedTournaments,
  finishedTournaments,
  userCurrencyFromCookies,
}: {
  lng: LanguagesType;
  activeTournaments: ITournament[];
  plannedTournaments: ITournament[];
  finishedTournaments: ITournament[];
  userCurrencyFromCookies: string;
}) => {
  const [{ constants }] = useSystemData();

  const tournamentsOrderResult = constants?.find(
    (constant) => constant.key === 'tournaments_order',
  );

  const activeTournamentsOrderArr = tournamentsOrderResult?.value
    ? JSON.parse(tournamentsOrderResult.value)['active'] || []
    : [];
  const sortedActiveTournaments = sortItems({
    items: activeTournaments,
    order: activeTournamentsOrderArr,
  });

  const plannedTournamentsOrderArr = tournamentsOrderResult?.value
    ? JSON.parse(tournamentsOrderResult.value)['planned'] || []
    : [];
  const sortedPlannedTournaments = sortItems({
    items: plannedTournaments,
    order: plannedTournamentsOrderArr,
  });

  return (
    <>
      {activeTournaments.length > 0 && (
        <div className='mb-4 flex flex-col gap-2 max-md:mb-2 md:gap-4'>
          {sortedActiveTournaments.map((tournament, idx: number) => (
            <Fragment key={tournament.id}>
              <TournamentsCardMobile
                item={tournament}
                key={idx}
                index={idx}
                isLandingCard={false}
                lng={lng}
                className='sm:hidden'
                userCurrencyFromCookies={userCurrencyFromCookies}
              />
              <TournamentsCard
                key={tournament.id}
                item={tournament}
                index={idx}
                isLandingCard={false}
                className='max-sm:hidden'
                userCurrencyFromCookies={userCurrencyFromCookies}
              />
            </Fragment>
          ))}
        </div>
      )}
      {plannedTournaments.length > 0 && (
        <div className='mb-4 flex flex-col gap-2 max-md:mb-2 md:gap-4'>
          {sortedPlannedTournaments.map((tournament, idx: number) => (
            <Fragment key={tournament.id}>
              <TournamentsCardMobile
                item={tournament}
                key={idx}
                index={idx}
                isLandingCard={false}
                lng={lng}
                className='sm:hidden'
                userCurrencyFromCookies={userCurrencyFromCookies}
              />
              <TournamentsCard
                key={tournament.id}
                item={tournament}
                index={idx}
                isLandingCard={false}
                className='max-sm:hidden'
                userCurrencyFromCookies={userCurrencyFromCookies}
              />
            </Fragment>
          ))}
        </div>
      )}
      {finishedTournaments.length > 0 && (
        <div className='flex flex-col gap-2 md:gap-4'>
          {finishedTournaments.map((tournament, idx: number) => (
            <Fragment key={tournament.id}>
              <TournamentsCardMobile
                item={tournament}
                key={idx}
                index={idx}
                isLandingCard={false}
                lng={lng}
                className='sm:hidden'
                userCurrencyFromCookies={userCurrencyFromCookies}
              />
              <TournamentsCard
                key={tournament.id}
                item={tournament}
                index={idx}
                isLandingCard={false}
                className='max-sm:hidden'
                userCurrencyFromCookies={userCurrencyFromCookies}
              />
            </Fragment>
          ))}
        </div>
      )}
    </>
  );
};

export default TournamentsPageList;
