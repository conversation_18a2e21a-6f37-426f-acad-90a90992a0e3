import { StyledTournamentPage } from '@/app/components/Tournaments/styled.ts';
import Slider from '@/app/components/Slider/Slider.tsx';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';
import TournamentSliderDoubleGameCard from '@/app/components/Tournaments/components/TournamentSliderDoubleGameCard.tsx';
import { GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';

export const LoadingPrizesTable = () => {
  return (
    <div className='px-2 max-md:mt-6 md:p-4'>
      <div className='md:px-2'>
        {Array.from({ length: 11 }).map((_, index) => (
          <div
            key={index}
            className={`flex items-center gap-2 ${index === 0 ? 'mb-2' : 'py-[9px]'}`}
          >
            <div className='flex items-center'>
              {index > 0 ? (
                <div className='mr-5 h-6 w-6 rounded-full bg-gray-600'></div>
              ) : (
                <div></div>
              )}
              {/* Place */}
              {index > 0 ? (
                <div className='h-4 w-[70px] rounded bg-gray-600'></div>
              ) : (
                <div className='h-4 w-[60px] rounded bg-gray-600'></div>
              )}
            </div>
            <div className='flex grow justify-end'>
              {/* Player Name */}
              {index > 0 ? (
                <div className='h-4 w-[70px] rounded bg-gray-600' />
              ) : (
                <div className='ml-[14px] h-4 w-[60px] rounded bg-gray-600'></div>
              )}
              {/* Prize */}
            </div>
            <div className='flex w-[43%] justify-end md:w-[44%]'>
              {index > 0 ? (
                <div className='h-4 w-[60px] rounded bg-gray-600'></div>
              ) : (
                <div className='h-4 w-[70px] rounded bg-gray-600'></div>
              )}
              {/* Points */}
            </div>
          </div>
        ))}
      </div>
      {/* Pagination */}
      <div className='mt-5 flex justify-center space-x-4 md:mt-4'>
        {Array.from({ length: 2 }).map((_, index) => (
          <div
            key={index}
            className='h-8 w-10 animate-pulse rounded-full bg-gray-600 md:h-10 md:w-[50px]'
          ></div>
        ))}
      </div>
    </div>
  );
};

const getDoubledItems = (items: any) => {
  const result: any = [];
  for (let i = 0; i < items.length; i += 2) {
    result.push([items[i], items[i + 1]]);
  }
  return result;
};

export default function Loading() {
  return (
    <StyledTournamentPage className='relative px-2 max-md:mb-2 md:pr-6'>
      {/* Back Link */}
      <div className='my-3 h-5 w-32 animate-pulse rounded bg-gray-600'></div>
      {/* Banner Image Placeholder */}
      <div className='gameCard tournamentImageCard h-[275px] rounded-none bg-[#242c3b] sm:h-48 sm:bg-[#1E293BA1] md:rounded-lg'></div>

      <div className='flex flex-col gap-2 md:mt-[20%] md:gap-4 md:px-[5%] lg:px-[6%]'>
        {/* Tournament Info Section */}
        <div className='rounded-lg bg-[#1E293B] px-2 py-4 sm:p-6'>
          <div className='flex justify-between sm:items-center'>
            <div className='ml-[-8px] h-[22px] w-20 animate-pulse rounded-e-[48px] bg-gray-600 sm:ml-[-24px]'></div>
            {/* Active Tag */}
            <div className='ml-4 h-4 w-[200px] animate-pulse rounded-full bg-gray-600 max-sm:hidden'></div>
            <div className='mr-[-8px] h-[22px] w-24 animate-pulse rounded-s-[48px] bg-gray-600 sm:hidden'></div>
            {/* Countdown Timer */}
          </div>

          <div className='flex gap-1 max-md:flex-col max-sm:hidden sm:mt-[40px] md:mt-5 md:gap-2 lg:gap-4 xl:gap-6'>
            {/* Tournament Title */}
            <div className='flex w-1/3 flex-col gap-1 md:gap-3'>
              <div className='h-[36px] w-full animate-pulse rounded bg-gray-600'></div>
              <div className='h-[36px] w-4/5 animate-pulse rounded bg-gray-600'></div>
              <div className='mt-2 h-4 w-[120px] animate-pulse rounded bg-gray-600'></div>
            </div>
            {/* End In */}
            <div className='flex w-1/4 flex-col gap-1 md:gap-3'>
              <div className='h-4 w-full animate-pulse rounded bg-gray-600'></div>
              <div className='h-[36px] w-full animate-pulse rounded bg-gray-600'></div>
            </div>
            <div className='flex w-1/4 flex-col gap-1 md:gap-3'>
              <div className='h-4 w-full animate-pulse rounded bg-gray-600'></div>
              <div className='h-[36px] w-full animate-pulse rounded bg-gray-600'></div>
            </div>
            <div className='flex w-1/4 flex-col gap-1 md:gap-3'>
              <div className='h-4 w-full animate-pulse rounded bg-gray-600'></div>
              <div className='h-[36px] w-full animate-pulse rounded bg-gray-600'></div>
            </div>
            <div className='flex w-1/6 flex-col gap-1 md:gap-3'>
              <div className='h-4 w-full animate-pulse rounded bg-gray-600'></div>
              <div className='h-[36px] w-full animate-pulse rounded bg-gray-600'></div>
            </div>
          </div>

          <div className='mt-[239px] flex flex-col gap-1 sm:hidden'>
            <div className='w-full max-w-md space-y-2 rounded-lg bg-[#1E293B] pt-4'>
              {/* Date Placeholder */}
              <div className='h-4 w-3/4 animate-pulse rounded bg-gray-600'></div>
              {/* Date Text */}

              {/* Title Placeholder */}
              <div className='h-5 w-1/2 animate-pulse rounded bg-gray-600'></div>
              {/* Tournament Title */}

              {/* Type Placeholder */}
              <div className='h-3 w-1/3 animate-pulse rounded bg-gray-600'></div>
              {/* Type Text */}
            </div>

            {/* Divider */}
            <div className='mb-2 mt-[6px] border-t border-[#334155]'></div>

            {/* Details Section */}
            <div className='flex gap-3'>
              {/* Prize Fund */}
              <div className='w-1/3 space-y-2'>
                <div className='h-4 w-1/2 animate-pulse rounded bg-gray-600'></div>
                {/* Prize Fund Label */}
                <div className='h-6 w-full animate-pulse rounded bg-gray-600'></div>
                {/* Prize Fund Value */}
              </div>

              {/* Bet Range */}
              <div className='grow space-y-2'>
                <div className='h-4 w-1/3 animate-pulse rounded bg-gray-600'></div>
                {/* Bet Label */}
                <div className='h-6 w-full animate-pulse rounded bg-gray-600'></div>
                {/* Bet Value */}
              </div>

              {/* Prize Places */}
              <div className='w-1/4 space-y-2'>
                <div className='h-4 w-full animate-pulse rounded bg-gray-600'></div>
                {/* Prize Places Label */}
                <div className='h-6 w-1/2 animate-pulse rounded bg-gray-600'></div>
                {/* Prize Places Value */}
              </div>
            </div>
          </div>
        </div>

        {/* Prizes Section */}
        <div className='rounded-lg bg-[#1E293B] px-2 py-4 md:p-6'>
          <div className='h-5 w-[80px] animate-pulse rounded bg-gray-600 md:h-[36px]'></div>
          {/* Prizes Title */}
          <div className='flex items-baseline justify-center gap-3 md:gap-10'>
            <div className='h-[108px] w-20 animate-pulse rounded bg-gray-600 md:h-[150px]'></div>
            {/* 2nd Place */}
            <div className='h-[126px] w-24 animate-pulse rounded bg-gray-600 md:h-[180px]'></div>
            {/* 1st Place */}
            <div className='h-[108px] w-20 animate-pulse rounded bg-gray-600 md:h-[150px]'></div>
            {/* 3rd Place */}
          </div>
          {/* Leaderboard Table */}
          <LoadingPrizesTable />
        </div>

        <div className='gameCarousel relatedCarousel flex flex-col gap-y-2 rounded-lg bg-[#1E293B] px-2 py-4 sm:p-6 md:gap-y-4'>
          <div className='h-7 w-[200px] animate-pulse rounded bg-gray-600 sm:h-[36px]'></div>
          <Slider
            items={getDoubledItems(
              Array(16).fill(
                <div className='flex flex-col gap-y-2'>
                  <LoadingGameCard className='bg-gray-600' />
                  <LoadingGameCard className='bg-gray-600' />
                </div>,
              ),
            )}
            total={16}
            Slide={TournamentSliderDoubleGameCard}
            isCarousel
            perView={6}
            withLoadMore
            breakpoints={GAMES_SLIDER_BREAKPOINTS()}
            buttonHref='/casino/games/tournament?tournament'
          />
        </div>
      </div>
    </StyledTournamentPage>
  );
}
