'use client';
import clsx from 'clsx';
import { useSelectedLayoutSegment } from 'next/navigation';
import { useEffect, useState } from 'react';
import Link from 'next/link';
import { withTheme } from 'styled-components';
import { useTranslation } from '@/app/i18n/client';
import { Tabs, Typography } from '@/atomic-design-components';
import { GamePageType, LanguagesType } from '@/types/global';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import useWindowSize from '@/hooks/useWindowSize.ts';

type TabsType = string[];

const getActiveTabIndexInitial = (tabName: string | null, tabs: TabsType) => {
  const activeTabIndex = tabs.findIndex((tab) => tab === tabName);
  return activeTabIndex === -1 ? 0 : activeTabIndex;
};

const ProfileTabs = ({
  lng,
  gamePageType,
  tabs,
  className,
  isForMobile,
  theme,
}: {
  lng: LanguagesType;
  gamePageType: GamePageType;
  tabs: TabsType;
  className?: string;
  isForMobile?: boolean;
  theme: any;
}) => {
  const { width } = useWindowSize();
  const isMobile = !!width && width < theme.breakpoints?.md;
  const { isTouchDevice } = useIsTouchMobileView();
  const isMobileView = isMobile || isTouchDevice;

  const { t } = useTranslation(lng);
  const pageName = useSelectedLayoutSegment();

  const [activeTab, setActiveTab] = useState(
    getActiveTabIndexInitial(pageName, tabs),
  );

  useEffect(() => {
    setActiveTab(getActiveTabIndexInitial(pageName, tabs));
  }, [pageName]);

  if (!isForMobile && isMobileView) {
    return null;
  }

  const onTabClick = (e: Event, idx: number) => {
    setActiveTab(idx);
  };

  const getTabTitle = (tab: any, i: number) => (
    <Link href={`/${lng}/${gamePageType}/profile/${tabs[i]}`}>
      <Typography
        text={t(tab || 'dashboard')}
        alignItems='center'
        justifyContent='center'
        type='body2'
        padding='8px 16px 6px'
        displayCssProp='block'
      />
    </Link>
  );

  if (isMobileView && pageName === 'my-bonuses' && isForMobile) return null;

  return (
    <Tabs
      activeTabProp={activeTab}
      onTabChange={onTabClick}
      getTabTitle={getTabTitle}
      tabsTitles={tabs}
      hideNavBtns
      className={clsx(
        className,
        'mx-2 md:ml-0 md:mr-6',
        isForMobile && !isTouchDevice && 'md:hidden',
        ((!isForMobile && !isTouchDevice) ||
          (isForMobile && pageName === 'my-bonuses')) &&
          'max-md:hidden',
      )}
      tabPadding='0'
    />
  );
};

export default withTheme(ProfileTabs);
