import { ReactNode } from 'react';

import EmailVerificationModal from '@/app/components/Verifications/EmailVerificationModal';
import PhoneVerificationModal from '@/app/components/Verifications/PhoneVerificationModal';
import { GamePageType, LanguagesType } from '@/types/global';
import ProfileTabs from './components/ProfileTabs/ProfileTabs';

const ProfileLayout = ({
  children,
  params: { lng, gamePageType },
}: {
  children: ReactNode;
  params: { lng: LanguagesType; gamePageType: GamePageType };
}) => {
  return (
    <>
      <EmailVerificationModal />
      <PhoneVerificationModal />
      <ProfileTabs
        lng={lng}
        gamePageType={gamePageType}
        tabs={['', 'personal-data', 'history']}
        isForMobile={true}
      />
      <ProfileTabs
        lng={lng}
        gamePageType={gamePageType}
        tabs={['', 'my-bonuses', 'personal-data', 'history']}
      />
      {children}
    </>
  );
};

export default ProfileLayout;
