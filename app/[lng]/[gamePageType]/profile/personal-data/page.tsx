import dayjs from 'dayjs';
import { getUserProfileServer } from '@/app/api/user/getUserProfileServer.ts';
import IdentityVerificationCard from '@/app/components/Profile/IdentityVerificationCard.tsx';
import PlayerProfileCard from '@/app/components/Profile/PlayerProfileCard.tsx';
import SafetyCard from '@/app/components/Profile/SafetyCard.tsx';
import SignOutModal from '@/app/components/Profile/SignOutModal';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const PersonalDataPage = async ({ params: { lng } }: IProps) => {
  const { t } = await getServerTranslation(lng);

  const user = await getUserProfileServer();

  return (
    <div className='flex flex-wrap gap-2 !px-2 max-md:flex-col md:gap-4 md:!pl-0 md:!pr-6'>
      <div className='flex flex-col gap-2 pt-2 md:w-[calc(50%-8px)] md:gap-4 md:pt-4'>
        <div className='flex flex-col gap-1 rounded-lg bg-[#1E293B] px-2 py-4 max-md:order-2 sm:p-4 md:gap-4'>
          <Typography text={t('identityVerification')} type='h3' />
          <IdentityVerificationCard lng={lng} />
        </div>

        <div className='flex flex-col gap-4 rounded-lg bg-[#1E293B] px-2 py-4 max-md:order-1 sm:p-4'>
          <div className='flex justify-between'>
            <Typography text={t('profile')} type='h3' />
            {/*<Typography text={t('accVerified')} iconName='shield' />*/}
          </div>
          <PlayerProfileCard
            lng={lng}
            user={{
              ...user,
              birth_date:
                user.birth_date === '0000-00-00' ||
                user.birth_date === '1900-01-01'
                  ? ''
                  : dayjs(user.birth_date).format('DD-MM-YYYY'),
            }}
          />
        </div>
      </div>

      <div className='flex gap-4 max-md:w-full md:w-[calc(50%-8px)] md:pt-4'>
        <div className='flex h-fit w-full flex-col gap-4 rounded-lg bg-[#1E293B] px-2 py-4 sm:p-4'>
          <Typography text={t('password')} type='h3' />
          <SafetyCard lng={lng} />
        </div>
      </div>
      <SignOutModal />
    </div>
  );
};

export default PersonalDataPage;
