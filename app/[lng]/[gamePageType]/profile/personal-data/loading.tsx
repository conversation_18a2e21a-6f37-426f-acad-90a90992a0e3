export default function Loading() {
  return (
    <div className='flex flex-wrap gap-2 !px-2 max-md:flex-col md:gap-4 md:!pl-0 md:!pr-6'>
      <div className='flex flex-col gap-2 pt-2 md:w-[calc(50%-8px)] md:gap-4 md:pt-4'>
        <div className='rounded-lg bg-[#1E293B] p-4 max-md:order-2'>
          {/* Title */}
          <div className='mb-4 h-6 w-1/3 animate-pulse rounded bg-gray-700'></div>

          {/* Info Message */}
          <div className='mb-4 h-4 w-2/3 animate-pulse rounded bg-gray-700'></div>

          {/* Icons Section */}
          <div className='mb-6 flex items-center justify-around'>
            <div className='h-16 w-16 animate-pulse rounded-lg bg-gray-700'></div>
            <div className='h-4 w-4 animate-pulse rounded-full bg-gray-700'></div>
            <div className='h-16 w-16 animate-pulse rounded-lg bg-gray-700'></div>
            <div className='h-4 w-4 animate-pulse rounded-full bg-gray-700'></div>
            <div className='h-16 w-16 animate-pulse rounded-lg bg-gray-700'></div>
          </div>

          {/* Status */}
          <div className='mx-auto h-4 w-1/4 rounded bg-gray-700'></div>
        </div>

        <div className='rounded-lg bg-[#1E293B] p-6 max-md:order-1'>
          {/* Title */}
          <div className='mb-6 h-6 w-1/4 animate-pulse rounded bg-gray-700'></div>
          {/* Full Name Input */}
          <div className='mb-6'>
            <div className='mb-2 h-4 w-1/6 animate-pulse rounded bg-gray-700'></div>
            <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
          </div>
          {/* Date of Birth Input */}
          <div className='mb-6'>
            <div className='mb-2 h-4 w-1/6 animate-pulse rounded bg-gray-700'></div>
            <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
          </div>
          {/* Nickname Input */}
          <div className='mb-6'>
            <div className='mb-2 h-4 w-1/6 animate-pulse rounded bg-gray-700'></div>
            <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
          </div>
          {/* Email Input */}
          <div className='mb-6'>
            <div className='mb-2 h-4 w-1/6 animate-pulse rounded bg-gray-700'></div>
            <div className='flex items-center gap-4'>
              <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
              <div className='h-10 w-20 animate-pulse rounded bg-gray-700'></div>
            </div>
          </div>
          {/* Phone Input */}
          <div className='mb-6'>
            <div className='mb-2 h-4 w-1/6 animate-pulse rounded bg-gray-700'></div>
            <div className='flex items-center gap-4'>
              <div className='h-10 w-16 animate-pulse rounded bg-gray-700'></div>
              <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
              <div className='h-10 w-20 animate-pulse rounded bg-gray-700'></div>
            </div>
          </div>
          {/* Sign Out Button */}
          <div className='mx-auto h-10 w-1/3 animate-pulse rounded bg-gray-700'></div>
        </div>
      </div>

      <div className='flex h-fit flex-col justify-start gap-2 rounded-lg bg-[#1E293B] p-4 md:mt-4 md:w-[calc(50%-8px)] md:gap-4'>
        {/* Title */}
        <div className='h-6 w-1/4 animate-pulse rounded bg-gray-700'></div>
        {/* Current Password Input */}
        <div className=''>
          <div className='mb-2 h-4 w-1/4 animate-pulse rounded bg-gray-700'></div>
          <div className='flex items-center gap-2'>
            <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
            <div className='h-6 w-6 animate-pulse rounded-full bg-gray-700'></div>
          </div>
        </div>
        {/* New Password Input */}
        <div>
          <div className='mb-2 h-4 w-1/4 animate-pulse rounded bg-gray-700'></div>
          <div className='flex items-center gap-2'>
            <div className='h-10 w-full animate-pulse rounded bg-gray-700'></div>
            <div className='h-6 w-6 animate-pulse rounded-full bg-gray-700'></div>
          </div>
          <div className='mt-2 h-4 w-1/3 animate-pulse rounded bg-gray-700'></div>
        </div>
        {/* Change Password Button */}
        <div className='h-12 w-full animate-pulse rounded bg-gray-700'></div>
      </div>
    </div>
  );
}
