import Slider from '@/app/components/Slider/Slider.tsx';
import GameCard from '@/app/components/Games/GameCard.tsx';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';
import { GAMES_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';

export default function Loading() {
  return (
    <div className='!flex w-full flex-wrap gap-2 !px-2 max-md:flex-col md:gap-4 md:!pl-0 md:!pr-6'>
      {/* Left Section (LevelCard Skeleton) */}
      <div className='flex h-[300px] flex-col gap-2 pt-2 max-md:w-full md:w-[calc(50%-8px)] md:gap-4 md:pt-4'>
        <div className='flex grow animate-pulse flex-col rounded-lg bg-gray-800 p-4'>
          {/* Level Title */}
          <div className='mb-4 h-6 w-1/4 rounded bg-gray-700'></div>

          {/* Progress Bar */}
          <div className='mb-4 h-2 rounded-full bg-gray-600'></div>

          {/* Likes and Navigation */}
          <div className='mb-4 flex justify-between'>
            <div className='h-4 w-1/3 rounded bg-gray-700'></div>
            <div className='h-4 w-1/6 rounded bg-gray-700'></div>
          </div>

          {/* Next Level Info */}
          {/*<div className='mb-2 h-4 w-1/2 rounded bg-gray-700'></div>*/}

          {/* Free Spins Section */}
          <div className='mb-2 flex items-center justify-between'>
            <div className='h-12 w-12 rounded-full bg-gray-700'></div>
            <div className='ml-2 flex-1'>
              <div className='mb-1 h-4 w-1/4 rounded bg-gray-700'></div>
              <div className='h-4 w-1/6 rounded bg-gray-700'></div>
            </div>
            <div className='h-8 w-1/6 rounded bg-gray-700'></div>
          </div>

          {/* Cashback Section */}
          <div className='mb-4 flex justify-between'>
            {/*<div className='h-4 w-1/4 rounded bg-gray-700'></div>*/}
          </div>

          {/* All Levels Button */}
          <div className='mx-auto mb-4 mt-auto h-8 w-1/4 rounded bg-gray-700'></div>
        </div>
      </div>

      {/* Right Section (CashbackCard, PendingWithdrawals, MyTournaments Skeleton) */}
      <div className='flex flex-col gap-2 max-md:w-full md:w-[calc(50%-8px)] md:gap-4 md:pt-4'>
        <div className='animate-pulse rounded-lg bg-gray-800 p-4'>
          {/* Title */}
          <div className='mb-4 flex justify-between'>
            <div className='h-6 w-1/3 rounded bg-gray-700'></div>
            <div className='h-4 w-1/6 rounded bg-gray-700'></div>
          </div>

          {/* Available Cashback */}
          <div className='mb-4 flex justify-between'>
            <div className='mb-4 h-4 w-1/4 rounded bg-gray-700'></div>
            {/* Take Cashback Button */}
            <div className='h-10 w-1/3 rounded bg-gray-700'></div>
          </div>
        </div>

        {/*/!* PendingWithdrawals Skeleton *!/*/}
        {/*<div className='mt-2 animate-pulse rounded-lg bg-gray-800 p-4'>*/}
        {/*  <div className='mb-4 h-6 rounded bg-gray-700'></div>*/}
        {/*  <div className='mb-2 h-4 w-1/3 rounded bg-gray-700'></div>*/}
        {/*  <div className='mb-2 h-4 w-1/4 rounded bg-gray-700'></div>*/}
        {/*  <div className='mb-2 h-4 w-1/2 rounded bg-gray-700'></div>*/}
        {/*</div>*/}
        {/* Title */}
        <div className='h-6 w-1/3 rounded bg-gray-700'></div>
        {/* MyTournaments Skeleton */}
        <div className='animate-pulse rounded-lg bg-gray-800 p-4'>
          {/* Tournament Info */}
          <div className='flex items-center'>
            {/* Icon */}
            {/*<div className='h-8 w-8 rounded-full bg-gray-700'></div>*/}
            {/* Text Placeholder */}
            <div className='ml-4 h-4 w-2/3 rounded bg-gray-700'></div>
            {/* Arrow Icon */}
            <div className='ml-auto h-4 w-4 rounded-full bg-gray-700'></div>
          </div>
        </div>
      </div>

      {/* Favourite Games Skeleton */}
      <div className='mt-4 flex w-full flex-col'>
        <div className='mb-4 flex items-center justify-between'>
          {/* Left Section: Title with Icon */}
          <div className='flex items-center gap-2'>
            <div className='h-6 w-6 animate-pulse rounded-full bg-gray-600'></div>
            <div className='h-6 w-[250px] animate-pulse rounded bg-gray-600'></div>
          </div>

          {/* Right Section: Buttons */}
          <div className='flex items-center gap-2'>
            <div className='h-10 w-14 animate-pulse rounded-full bg-[#1E293B] max-md:h-8'></div>
            <div className='h-10 w-10 animate-pulse rounded-[8px] bg-[#1E293B] max-md:hidden'></div>
            <div className='h-10 w-10 animate-pulse rounded-[8px] bg-[#1E293B] max-md:hidden'></div>
          </div>
        </div>
        <Slider
          items={Array(7).fill(<LoadingGameCard />)}
          total={7}
          Slide={GameCard}
          isCarousel
          perView={7}
          breakpoints={GAMES_SLIDER_BREAKPOINTS()}
        />
      </div>
    </div>
  );
}
