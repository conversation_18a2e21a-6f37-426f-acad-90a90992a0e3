import { GamePageType, LanguagesType } from '@/types/global';
import CashbackCard from '@/app/components/Profile/Dashboard/CashbackCard.tsx';
import LevelCard from '@/app/components/Profile/Dashboard/LevelCard.tsx';
import PendingWithdrawals from '@/app/components/Profile/Dashboard/PendingWithdrawals.tsx';
import FavouriteGames from '@/app/components/Profile/Dashboard/FavouriteGames.tsx';
import { UserLevelsProvider } from '@/app/components/Profile/Dashboard/UserLevelsProvider.tsx';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';

interface IProps {
  params: {
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
}

const ProfilePage = async ({ params: { lng } }: IProps) => {
  const userUpdated = await getCurrentUserServer();

  return (
    <UserLevelsProvider userUpdated={userUpdated}>
      <div className='!flex w-full flex-wrap gap-2 !px-2 max-md:flex-col md:gap-4 md:!pl-0 md:!pr-6'>
        <div className='flex flex-col gap-2 pt-2 max-md:w-full md:w-[calc(50%-8px)] md:gap-4 md:pt-4'>
          {/*<VerifyNotification type='email' />*/}
          {/*<VerifyNotification type='phone' />*/}
          <LevelCard />
        </div>
        <div className='flex flex-col gap-2 max-md:w-full md:w-[calc(50%-8px)] md:gap-4 md:pt-4'>
          <CashbackCard />
          <PendingWithdrawals lng={lng} />
          {/*<MyTournaments lng={lng} />*/}
        </div>
        <FavouriteGames />
      </div>
    </UserLevelsProvider>
  );
};

export default ProfilePage;
