'use client';
import styled from 'styled-components';

export const StyledCashbackPanel = styled.div`
  position: relative;
  background: linear-gradient(180deg, #21284e 0%, #284171 100%), #fff;
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: 16px;
  overflow: hidden;

  svg {
    filter: blur(10px);
  }

  .cashbackButton {
    flex-grow: 1;
  }

  .cashbackInfo {
    flex-grow: 1;
    flex-basis: 33%;
  }

  .halfWidth {
    flex-wrap: nowrap;
    .cashbackButton {
      flex-grow: 0;
      flex-basis: 49%;
    }

    .cashbackInfo {
      flex-basis: 49%;
      flex-grow: 0;
    }
  }

  @media screen and (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    padding-right: 8px;
    padding-left: 8px;
  }

  @media screen and (min-width: ${({ theme }) =>
      theme.breakpoints?.md}px) and (max-width: 1050px) {
    h2 {
      font-size: 20px;
    }
  }
`;
