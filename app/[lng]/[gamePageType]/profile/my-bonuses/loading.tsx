import { TilesGrid } from '@/atomic-design-components';
import {
  BONUSES_BREAKPOINTS,
  BONUSES_BREAKPOINTS_TOUCH,
} from '@/app/components/Bonuses/BonusesPage.tsx';
import LoadingBonusCard from '@/app/components/Skeletons/LoadingBonusCard.tsx';

export const LoadingActiveBonusCard = () => {
  return (
    <div className='mb-4 flex flex-col items-center gap-4 rounded-lg bg-[#1E293B] px-6 py-7'>
      {/* Icon */}
      <div className='h-12 w-12 animate-pulse rounded bg-gray-600'></div>

      {/* Title */}
      <div className='h-4 w-3/4 animate-pulse rounded bg-gray-600'></div>

      {/* Description */}
      <div className='h-4 w-5/6 animate-pulse rounded bg-gray-600'></div>
      <div className='h-4 w-2/3 animate-pulse rounded bg-gray-600'></div>
    </div>
  );
};

export default function Loading() {
  return (
    <>
      <div className='flex grow items-start gap-4 pr-4 pt-4 max-md:hidden'>
        <div className='min-w-[280px] max-w-[360px]' style={{ width: '25%' }}>
          {/* Bonus Message Panel */}
          <LoadingActiveBonusCard />

          {/* Promo Code Input Section */}
          <div className='rounded-lg bg-[#1E293B] p-4'>
            <div className='mb-4 h-4 w-1/4 animate-pulse rounded bg-gray-600'></div>

            <div className='flex items-center gap-4'>
              {/* Input Field */}
              <div className='h-10 w-full animate-pulse rounded bg-gray-600'></div>

              {/* Activate Button */}
              <div className='h-10 w-24 animate-pulse rounded bg-gray-600'></div>
            </div>
          </div>
        </div>
        <TilesGrid
          tiles={Array(30).fill(<LoadingBonusCard />)}
          breakpoints={BONUSES_BREAKPOINTS}
        />
      </div>

      <div className='flex flex-col gap-2 px-2 md:hidden'>
        <div className='h-[41px] w-full animate-pulse rounded border-b border-gray-600 bg-gray-600'></div>
        <TilesGrid
          tiles={Array(15).fill(<LoadingBonusCard />)}
          breakpoints={BONUSES_BREAKPOINTS_TOUCH}
        />
      </div>
    </>
  );
}
