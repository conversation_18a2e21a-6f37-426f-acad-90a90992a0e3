import BonusesPage from '@/app/components/Bonuses/BonusesPage.tsx';
import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';
import { getUserBonusesServer } from '@/app/api/user/getUserBonusesServer.ts';

const MyBonusesPage = async () => {
  const bonuses = await getUserBonusesServer();
  const userUpdated = await getCurrentUserServer();
  // console.log(bonuses);
  return <BonusesPage bonusesInitial={bonuses} userUpdated={userUpdated} />;
};

export default MyBonusesPage;
