import clsx from 'clsx';
import HistoryMobileSkeleton from '@/app/components/Skeletons/HistoryMobileSkeleton.tsx';

export default function Loading({
  withoutWrapper,
  className,
}: {
  withoutWrapper?: boolean;
  className?: string;
}) {
  return (
    <div
      className={clsx(withoutWrapper ? '' : 'historyPage w-full', className)}
    >
      <div
        className={clsx(
          'mt-2 h-full rounded-lg border-[1px] border-[#334155] p-4 pt-4 max-md:hidden',
        )}
      >
        <div className='mb-4 h-10 w-[224px] rounded bg-[#1e293b]'></div>
        {/* Table Header */}
        <div className='flex h-8 items-center justify-between rounded-[4px] bg-[#1e293b] py-2'>
          <div className='w-[25%] grow'>
            <div className='ml-2 h-3 w-20 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[25%] grow'>
            <div className='h-3 w-12 animate-pulse rounded bg-gray-600'></div>
          </div>
          <div className='w-[10%] grow'>
            <div className='h-3 w-10 animate-pulse rounded bg-gray-600'></div>{' '}
          </div>
          <div className='w-[10%] grow'>
            <div className='h-3 w-10 animate-pulse rounded bg-gray-600'></div>{' '}
          </div>
          <div className='w-[150px] grow'>
            <div className='h-3 w-16 animate-pulse rounded bg-gray-600'></div>{' '}
          </div>
        </div>

        {/* Table Rows */}
        {Array.from({ length: 8 }).map((_, index) => {
          const clsnm = index < 7 ? 'border-b' : '';
          return (
            <div
              key={index}
              className={clsx(
                'flex h-[57px] items-center justify-between border-gray-700 py-4',
                clsnm,
              )}
            >
              {/* Name */}
              <div className='w-[25%] grow'>
                <div className='ml-2 h-4 w-24 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Date */}
              <div className='w-[25%] grow'>
                <div className='h-4 w-32 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Type */}
              <div className='w-[10%] grow'>
                <div className='h-4 w-10 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Payment Method */}
              <div className='w-[10%] grow'>
                <div className='h-4 w-10 animate-pulse rounded bg-gray-600'></div>
              </div>
              {/* Amount */}
              <div className='w-[150px] grow'>
                <div className='h-4 w-16 animate-pulse rounded bg-gray-600'></div>
              </div>
            </div>
          );
        })}
      </div>
      <HistoryMobileSkeleton />
    </div>
  );
}
