import { LanguagesType } from '@/types/global';
import AccountHistoryTable from '@/app/components/Profile/AccountHistoryTable.tsx';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const AccountHistoryPage = ({ params: { lng } }: IProps) => {
  return (
    <div className='historyPage h-full w-full'>
      <AccountHistoryTable lng={lng} />
    </div>
  );
};

export default AccountHistoryPage;
