import { ReactNode } from 'react';
const Footer = dynamic(() => import('@/app/components/Footer/Footer.tsx'), {
  ssr: false,
});
import ButtonToTop from '@/app/components/ButtonToTop';
import dynamic from 'next/dynamic';
import { notFound } from 'next/navigation';
import { GamePageType } from '@/types/global';

const WithFooterLayout = async ({
  children,
  params: { gamePageType },
}: {
  children: ReactNode;
  params: {
    gamePageType: GamePageType;
  };
}) => {
  if (gamePageType && !['live-casino', 'casino'].includes(gamePageType)) {
    notFound();
  }

  return (
    <main className='grow'>
      <div className='content-wrapper flex flex-col pb-6 max-md:pb-2'>
        {children}
      </div>
      <Footer />
      <ButtonToTop />
    </main>
  );
};

export default WithFooterLayout;
