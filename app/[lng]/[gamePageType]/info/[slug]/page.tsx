import { notFound } from 'next/navigation';

import { getPost } from '@/app/api/getPosts.ts';
import PostCard from '@/app/components/HelpCenter/PostCard.tsx';
import Slider from '@/app/components/Slider/Slider.tsx';
import { Typography } from '@/atomic-design-components';
import { theme } from '@/theme.ts';
import { GamePageType, LanguagesType } from '@/types/global';
import { getAvailableTranslation } from '@/utils/getAvailableTranslation.ts';
import TypographyParsed from '@/app/components/HelpCenter/TypographyParsed.tsx';

const InfoPage = async ({
  params: { slug, lng },
}: {
  params: {
    slug: string;
    lng: LanguagesType;
    gamePageType: GamePageType;
  };
}) => {
  const post = await getPost(slug);
  const photos = post?.photos?.general?.active || [];

  if (post.post_type !== 'page') {
    notFound();
  }

  return (
    <div className='postPage max-w-[950px]'>
      <Typography text={post.translations[lng]} type='h2' className='!my-2' />

      <TypographyParsed
        textToParse={getAvailableTranslation(post.body, lng)}
        type='body2'
        className='postContent !mb-4'
        color={theme.color?.general.lighter}
      />
      <Slider
        items={photos}
        total={photos.length}
        Slide={PostCard}
        perView={2}
        renderMode='performance'
        loop={photos.length > 2}
        withArrows={photos.length > 3}
        loopedSlides={Math.floor(photos.length / 2)}
        breakpoints={{
          '(max-width: 1050px)': {
            slides: {
              perView: 2,
              spacing: 16,
              dragSpeed: 0,
              defaultAnimation: { duration: 4000 },
            },
          },
          '(max-width: 520px)': {
            slides: {
              perView: 1,
              spacing: 8,
              dragSpeed: 0,
              defaultAnimation: { duration: 4000 },
            },
          },
        }}
      />
    </div>
  );
};

export default InfoPage;
