import React from 'react';
import ProvidersList from '@/app/components/Providers/ProvidersList';
import { getServerTranslation } from '@/app/i18n';
import { Typography } from '@/atomic-design-components';
import { LanguagesType } from '@/types/global';

interface IProps {
  params: {
    lng: LanguagesType;
  };
}

const ProvidersPage: React.FC<IProps> = async ({ params: { lng } }) => {
  const { t } = await getServerTranslation(lng);

  return (
    <div className='providersPage'>
      <Typography
        text={t('providers')}
        type='h1'
        iconName='cyberNetwork'
        className='mb-2 md:mb-4'
      />
      <ProvidersList />
    </div>
  );
};
export default ProvidersPage;
