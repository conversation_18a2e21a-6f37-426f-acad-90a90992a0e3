import { TilesGrid } from '@/atomic-design-components';
import { PROVIDERS_CARDS_BREAKPOINTS } from '@/app/components/Providers/ProvidersList.tsx';
import LoadingProviderCard from '@/app/components/Skeletons/LoadingProviderCard.tsx';

export default function Loading() {
  return (
    <div className='providersPage'>
      {/* Title */}
      <div className='mb-3 h-6 w-1/4 animate-pulse rounded bg-gray-600 md:mb-7'></div>

      <TilesGrid
        tiles={Array(60).fill(<LoadingProviderCard />)}
        breakpoints={PROVIDERS_CARDS_BREAKPOINTS || 2}
      />
    </div>
  );
}
