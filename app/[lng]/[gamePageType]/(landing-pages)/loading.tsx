import Slider from '@/app/components/Slider/Slider.tsx';
import { TOP_WINS_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/topWinsBreakpoints.ts';
import LoadingTopWinsCard from '@/app/components/Skeletons/LoadingTopWinsCard.tsx';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';
import {
  GAMES_SLIDER_BREAKPOINTS,
  GET_GAMES_SLIDER_BREAKPOINTS,
} from '@/app/config/breakpoints/gamesSliderBreakpoints.ts';
import LoadingPromoCard from '@/app/components/Skeletons/LoadingPromoCard.tsx';
import { PROMO_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/promoBreakpoints.ts';
// import LoadingTournamentMobileCard from '@/app/components/Skeletons/LoadingTournamentMobileCard.tsx';
// import LoadingTournamentCard from '@/app/components/Skeletons/LoadingTournamentCard.tsx';
import { PROVIDERS_SLIDER_BREAKPOINTS } from '@/app/config/breakpoints/providersBreakpoints.ts';
import LoadingProviderCard from '@/app/components/Skeletons/LoadingProviderCard.tsx';
import LoadingRtpGamesCard from '@/app/components/Skeletons/LoadingRtpGamesCard.tsx';

const getDoubledItems = (items: any) => {
  const result: any = [];
  for (let i = 0; i < items.length; i += 2) {
    result.push([items[i], items[i + 1]]);
  }
  return result;
};

export default async function Loading() {
  return (
    <div>
      {/* Top wins */}
      <div className='mb-3 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:h-[36px]'></div>
      <Slider
        items={Array(6).fill(<LoadingTopWinsCard />)}
        total={6}
        Slide={LoadingTopWinsCard}
        isCarousel
        perView={4}
        buttonText='all'
        buttonHref='/casino/top-wins'
        breakpoints={TOP_WINS_SLIDER_BREAKPOINTS}
      />

      {/* Top */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingGameCard />)}
        total={8}
        Slide={LoadingGameCard}
        isCarousel
        perView={7}
        buttonText='all'
        buttonHref='/casino/games/top'
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(7)}
      />

      {/* Hot */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingGameCard />)}
        total={8}
        Slide={LoadingGameCard}
        isCarousel
        perView={8}
        buttonText='all'
        buttonHref='/casino/games/hot'
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(8)}
      />

      {/* Promo */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingPromoCard />)}
        total={8}
        Slide={LoadingPromoCard}
        isCarousel
        perView={3}
        buttonText='all'
        buttonHref='/casino/promotions'
        breakpoints={PROMO_SLIDER_BREAKPOINTS}
      />

      {/* Live */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingGameCard />)}
        total={8}
        Slide={LoadingGameCard}
        isCarousel
        perView={7}
        buttonText='all'
        buttonHref='/live-casino'
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(7)}
      />

      {/* Live RTP */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={getDoubledItems(
          Array(16).fill(
            <div className='flex flex-col gap-y-2'>
              <LoadingGameCard />
              <LoadingGameCard />
            </div>,
          ),
        )}
        total={16}
        Slide={LoadingRtpGamesCard}
        isCarousel
        perView={6}
        buttonText='all'
        buttonHref='/casino/games/rtp'
        breakpoints={GAMES_SLIDER_BREAKPOINTS()}
      />

      {/*/!* Tournaments *!/*/}
      {/*<div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>*/}
      {/*<div className='scrollBarHidden flex w-max gap-4 overflow-hidden md:hidden'>*/}
      {/*  {Array.from({ length: 6 }).map((_, index) => (*/}
      {/*    <LoadingTournamentMobileCard key={index} isLanding />*/}
      {/*  ))}*/}
      {/*</div>*/}
      {/*<div className='flex w-[calc(100vw-300px)] gap-5 overflow-hidden max-md:hidden xxl:w-[calc(1680px-300px)]'>*/}
      {/*  {Array.from({ length: 10 }).map((_, index) => (*/}
      {/*    <LoadingTournamentCard isLanding key={index} />*/}
      {/*  ))}*/}
      {/*</div>*/}

      {/* New */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingGameCard />)}
        total={8}
        Slide={LoadingGameCard}
        isCarousel
        perView={8}
        buttonText='all'
        buttonHref='/casino/games/new'
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(8)}
      />

      {/* Crash */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingGameCard />)}
        total={8}
        Slide={LoadingGameCard}
        isCarousel
        perView={8}
        buttonText='all'
        buttonHref='/casino/games/crash'
        breakpoints={GET_GAMES_SLIDER_BREAKPOINTS(8)}
      />

      {/* Providers */}
      <div className='mb-3 mt-4 h-[28px] w-40 animate-pulse rounded bg-gray-600 md:mb-4 md:mt-6 md:h-[36px]'></div>
      <Slider
        items={Array(8).fill(<LoadingProviderCard />)}
        total={8}
        Slide={LoadingProviderCard}
        isCarousel
        perView={7}
        buttonText='all'
        buttonHref='/casino/providers'
        breakpoints={PROVIDERS_SLIDER_BREAKPOINTS}
      />
    </div>
  );
}
