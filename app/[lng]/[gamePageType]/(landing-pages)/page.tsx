import GameCarousel from '@/app/components/Games/GameCarousel.tsx';
import GameCarouselRtp from '@/app/components/Games/GameCarouselRtp.tsx';
import PromotionsCarousel from '@/app/components/Promotions/PromotionsCarousel.tsx';
import ProvidersCarousel from '@/app/components/Providers/ProvidersCarousel';
import TopWinningsCarousel from '@/app/components/TopWinnigs/TopWinningsCarousel';
// import TournamentsComponent from '@/app/components/Tournaments/TournamentsComponent';
import { GamePageType, LanguagesType } from '@/types/global';
import { LANDING_GAMES_GROUPS } from '@/app/components/Games/config.ts';

const LIMIT_PROVIDERS = 500;

export type GameGroup =
  | keyof (typeof LANDING_GAMES_GROUPS)['live-casino']
  | keyof (typeof LANDING_GAMES_GROUPS)['casino'];

export default async function Home({
  params: { lng, gamePageType = 'casino' },
}: {
  params: { lng: LanguagesType; gamePageType: GamePageType };
}) {
  const GAMES_GROUPS = LANDING_GAMES_GROUPS[gamePageType || 'casino'];
  return (
    <>
      <TopWinningsCarousel lng={lng} gamePageType={gamePageType} />
      {Object.keys(GAMES_GROUPS || {})
        .slice(0, 2)
        .map((group) => {
          const typedGroup = group as GameGroup;
          return (
            <GameCarousel
              gamePageType={gamePageType}
              group={typedGroup}
              lng={lng}
              icon={GAMES_GROUPS[typedGroup].icon}
              iconProps={GAMES_GROUPS[typedGroup].iconProps}
              key={group}
            />
          );
        })}
      <PromotionsCarousel lng={lng} gamePageType={gamePageType} />
      {gamePageType === 'live-casino' &&
        Object.keys(GAMES_GROUPS || {})
          .slice(2, 4)
          .map((group) => {
            const typedGroup = group as GameGroup;
            return (
              <GameCarousel
                gamePageType={gamePageType}
                group={typedGroup}
                lng={lng}
                icon={GAMES_GROUPS[typedGroup].icon}
                iconProps={GAMES_GROUPS[typedGroup].iconProps}
                key={group}
              />
            );
          })}

      {gamePageType === 'casino' && (
        <>
          <GameCarousel
            gamePageType='casino'
            group='Live'
            lng={lng}
            icon={GAMES_GROUPS.Live.icon}
            iconProps={GAMES_GROUPS.Live.iconProps}
            key='Live'
          />
          <GameCarouselRtp lng={lng} />
        </>
      )}
      {/*<TournamentsComponent lng={lng} />*/}
      {Object.keys(GAMES_GROUPS || {})
        .slice(4, 6)
        .map((group) => {
          const typedGroup = group as GameGroup;
          return (
            <GameCarousel
              gamePageType={gamePageType}
              group={typedGroup}
              lng={lng}
              icon={GAMES_GROUPS[typedGroup].icon}
              iconProps={GAMES_GROUPS[typedGroup].iconProps}
              key={group}
            />
          );
        })}
      <ProvidersCarousel lng={lng} limit={LIMIT_PROVIDERS} gamePageType={gamePageType} />
    </>
  );
}
