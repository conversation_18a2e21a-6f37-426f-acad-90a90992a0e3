import GameCarousel, {
  LANDING_GAMES_GROUPS,
} from '@/app/components/Games/GameCarousel.tsx';
import GameCarouselRtp from '@/app/components/Games/GameCarouselRtp.tsx';
import PromotionsCarousel from '@/app/components/Promotions/PromotionsCarousel.tsx';
import ProvidersCarousel from '@/app/components/Providers/ProvidersCarousel';
import TopWinningsCarousel from '@/app/components/TopWinnigs/TopWinningsCarousel';
// import TournamentsComponent from '@/app/components/Tournaments/TournamentsComponent';
import { LanguagesType } from '@/types/global';

const LIMIT_PROVIDERS = 500;

export type GameGroup = keyof (typeof LANDING_GAMES_GROUPS)['casino'];

export default async function Home({
  params: { lng },
}: {
  params: { lng: LanguagesType };
}) {
  const GAMES_GROUPS = LANDING_GAMES_GROUPS.casino;

  return (
    <>
      <TopWinningsCarousel lng={lng} gamePageType='casino' />
      {Object.keys(GAMES_GROUPS)
        .slice(0, 2)
        .map((group) => {
          const typedGroup = group as GameGroup;
          return (
            <GameCarousel
              gamePageType='casino'
              group={typedGroup}
              lng={lng}
              icon={GAMES_GROUPS[typedGroup].icon}
              iconProps={GAMES_GROUPS[typedGroup].iconProps}
              key={group}
            />
          );
        })}
      <PromotionsCarousel lng={lng} gamePageType='casino' />
      <GameCarousel
        gamePageType='casino'
        group='Live'
        lng={lng}
        icon={GAMES_GROUPS.Live.icon}
        iconProps={GAMES_GROUPS.Live.iconProps}
        key='Live'
      />
      <GameCarouselRtp lng={lng} />
      {/*<TournamentsComponent lng={lng} />*/}
      {Object.keys(GAMES_GROUPS)
        .slice(4, 6)
        .map((group) => {
          const typedGroup = group as GameGroup;
          return (
            <GameCarousel
              gamePageType='casino'
              group={typedGroup}
              lng={lng}
              icon={GAMES_GROUPS[typedGroup].icon}
              iconProps={GAMES_GROUPS[typedGroup].iconProps}
              key={group}
            />
          );
        })}
      <ProvidersCarousel
        lng={lng}
        limit={LIMIT_PROVIDERS}
        gamePageType='casino'
      />
    </>
  );
}
