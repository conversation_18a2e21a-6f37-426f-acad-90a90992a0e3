import { ReactNode } from 'react';
import dynamic from 'next/dynamic';
import MobilePageTypeMenu from '@/app/components/NavMenu/MenuMobile/components/MobilePageTypeMenu';
import { StyledLine } from '@/app/components/NavMenu/styled.ts';
import { LanguagesType } from '@/types/global';
import PageMenuLoader from '@/app/components/Skeletons/PageMenuLoader.tsx';
import ButtonToTop from '@/app/components/ButtonToTop';
import Footer from '@/app/components/Footer/Footer.tsx';
import BannerClientComponent from '@/app/components/Banner/BannerClientComponent.tsx';

const PageMenu = dynamic(
  () => import('@/app/components/PageMenu/PageMenu.tsx'),
  {
    ssr: false,
    loading: () => <PageMenuLoader />,
  },
);

const GamesPagesLayout = async ({
  children,
  params: { lng },
}: {
  children: ReactNode;
  params: {
    lng: LanguagesType;
  };
}) => {
  return (
    <main className='grow'>
      <div className='content-wrapper flex flex-col pb-6 max-md:pb-2'>
        <div className='landingWrapper'>
          <MobilePageTypeMenu lng={lng} />
          <BannerClientComponent />
          <PageMenu />
          <StyledLine margin='0 0 25px 0' className='max-md:hidden' />
          {children}
        </div>
      </div>
      <Footer />
      <ButtonToTop />
    </main>
  );
};

export default GamesPagesLayout;
