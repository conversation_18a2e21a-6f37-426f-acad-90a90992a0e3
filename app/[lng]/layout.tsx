import type { Viewport } from 'next';
import { ReactNode, Suspense } from 'react';
import dynamic from 'next/dynamic';
import { headers } from 'next/headers';

import { getCurrentUserServer } from '@/app/api/user/getCurrentUserServer.ts';
import { getSystemDataServer } from '@/app/api/getSystemDataServer.ts';
import '@/app/components/Games/styles.css';
import '@/app/globals.css';
import { LANGUAGES } from '@/app/i18n/settings';
import { LanguagesType } from '@/types/global';
// import 'dayjs/locale/cs';
import 'dayjs/locale/en';
import 'dayjs/locale/kk';
// import 'dayjs/locale/pl';
import 'dayjs/locale/ru';
// import 'dayjs/locale/tr';
// import 'dayjs/locale/uz';
import 'react-texty/styles.css';
import * as Sentry from '@sentry/nextjs';

import SystemDataProvider from '@/app/wrappers/systemDataProvider.tsx';
import Header from '@/app/components/Header/Header.tsx';
import ThemeProvider from '@/app/wrappers/themeProvider.tsx';
import StyledComponentsRegistry from '@/app/wrappers/registry.tsx';
import GlobalStyles from '@/app/GlobalStyles';
import { AlertProvider } from '@/app/wrappers/AlertProvider.tsx';
import PopupAlertContainer from '@/atomic-design-components/molecules/PopupAlert';
import DrawerProvider from '@/app/wrappers/drawerProvider.tsx';
import UserForms from '@/app/components/UserForms/UserForms.tsx';
import NotificationToastWrapper from '@/app/components/Notifications/NotificationToastWrapper.tsx';
import NavMenuMobileDrawer from '@/app/components/NavMenu/MenuMobile/NavMenuMobileDrawer.tsx';
import MobileMenuBottom from '@/app/components/MobileMenuBottom/MobileMenuBottom.tsx';
import { NavigationProvider } from '@/app/wrappers/NavigationProvider.tsx';
import NavMenuDesktop from '@/app/components/NavMenu/NavMenuDesktop.tsx';
import { UnfinishedGameProvider } from '@/app/wrappers/UnfinishedGameProvider.tsx';
import { UserBonusesProvider } from '@/app/wrappers/UserBonusesProvider.tsx';
import { NewNotificationProvider } from '@/app/wrappers/NewNotificationProvider.tsx';
import MainLoader from '@/app/components/Skeletons/MainLoader.tsx';
import { TouchDevicesMobileViewProvider } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import GlobalNotificationProvider from '../wrappers/globalNotificationProvider';
import checkIsMobileDeviceServer from '@/utils/checkIsMobileDeviceServer.tsx';
import { getServerTranslation } from '../i18n';
import { theme } from '@/theme';
import { AppStoreProvider } from '@/app/providers/app-store-provider.tsx';
import MainLoaderWithChildren from '@/app/components/Skeletons/MainLoaderWithChildren.tsx';
import IntercomScript from '@/app/scripts/IntercomScript.ts';
import UserProviderWrapper from '@/app/wrappers/userProviderWrapper.tsx';
import YandexMetrika from '@/app/components/YandexMetrika/YandexMetrika.tsx';
import YandexMetrikaScript from '@/app/components/YandexMetrika/YandexMetricaScript.tsx';
import GoogleAnalytics from '@/app/components/GoogleAnalytics';
import OpenModalByUrlProvider from '@/app/wrappers/OpenModalByUrlProvider.tsx';
import { GoogleTagManager } from '@next/third-parties/google';
import AutoReloadOnReturn from '@/app/wrappers/AutoReloadOnReturn.tsx';

const FiltersDrawer = dynamic(() => import('@/app/components/FiltersDrawer/FiltersDrawer.tsx'), {
  ssr: false,
});
const SearchModal = dynamic(() => import('@/app/components/SearchModal/SearchModal.tsx'), {
  ssr: false,
});
const GameModal = dynamic(() => import('@/app/components/Games/GameModal.tsx'), { ssr: false });
const GameLaunchModal = dynamic(() => import('@/app/components/Games/GameLaunchModal.tsx'), {
  ssr: false,
});
const PromotionModal = dynamic(() => import('@/app/components/Promotions/PromotionModal.tsx'), {
  ssr: false,
});
const GamePageOverlayModal = dynamic(
  () => import('@/app/components/GamePageOverlay/GamePageOverlayModal.tsx'),
  { ssr: false },
);
const ActiveFreespinsInGameModal = dynamic(
  () => import('@/app/components/ModalDialog/ActiveFreespinsInGameModal.tsx'),
  { ssr: false },
);
const ContinueGameWithZeroWager = dynamic(
  () => import('@/app/components/ModalDialog/ContinueGameWithZeroWager.tsx'),
  { ssr: false },
);
const UnfinishedGameModal = dynamic(
  () => import('@/app/components/ModalDialog/UnfinishedGameModal.tsx'),
  { ssr: false },
);

export const viewport: Viewport = {
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover',
  width: 'device-width',
};

const nodeEnv = process.env.NODE_ENV || process.env.VERCEL_ENV;

export async function generateMetadata({ params }: { params: { lng: LanguagesType } }) {
  const { lng } = params;
  const { t } = await getServerTranslation(lng);

  const headersList = headers();
  const host = headersList.get('host') || '';

  const robotsContent = host.includes('likecasino.com') ? 'index,follow' : 'noindex,nofollow';

  return {
    title: t('metaTitle'),
    description: t('metaDescription'),
    metadataBase: new URL('https://likecasino.com'),
    robots: robotsContent,
    other: {
      ...Sentry.getTraceData(),
    },
  };
}

export async function generateStaticParams() {
  return LANGUAGES.map((lng) => ({ lng }));
}

const LayoutToBeSuspended = async ({ children }: { children: ReactNode }) => {
  const userInitial = await getCurrentUserServer();
  const systemDataInitial = await getSystemDataServer();

  return (
    <AppStoreProvider>
      <UserProviderWrapper userInitial={userInitial}>
        <SystemDataProvider systemDataInitial={systemDataInitial}>
          {/*<SystemDataLoader systemDataInitial={systemDataInitial}>*/}
          <GlobalNotificationProvider>
            <DrawerProvider>
              <UserBonusesProvider>
                <NewNotificationProvider>
                  <NavigationProvider>
                    <Header />
                    <NavMenuMobileDrawer />
                    <OpenModalByUrlProvider>
                      <UserForms />
                      <MainLoaderWithChildren>
                        <div className='flex w-full max-w-[1680px] grow xxl:mx-auto xxl:justify-end'>
                          <NavMenuDesktop />
                          <NotificationToastWrapper />
                          <UnfinishedGameProvider>
                            <SearchModal />
                            <GamePageOverlayModal />
                            <ActiveFreespinsInGameModal />
                            <ContinueGameWithZeroWager />
                            <UnfinishedGameModal />
                            {children}
                          </UnfinishedGameProvider>
                          <GameModal />
                          <GameLaunchModal />
                          <PromotionModal />
                        </div>
                      </MainLoaderWithChildren>
                    </OpenModalByUrlProvider>
                  </NavigationProvider>
                </NewNotificationProvider>
              </UserBonusesProvider>
              <MobileMenuBottom />
              <FiltersDrawer />
            </DrawerProvider>
          </GlobalNotificationProvider>
          {/*</SystemDataLoader>*/}
        </SystemDataProvider>
        <IntercomScript user={userInitial} />
      </UserProviderWrapper>
    </AppStoreProvider>
  );
};

const RootLayout = async ({
  children,
  params: { lng },
}: {
  children: ReactNode;
  params: { lng: LanguagesType };
}) => {
  const { isTouchDevice, isTabletDevice, agent } = checkIsMobileDeviceServer();
  return (
    <html lang={lng}>
      <head>
        {nodeEnv === 'production' && process.env.NEXT_PUBLIC_GA_ID && <GoogleAnalytics />}
        <meta httpEquiv='Permissions-Policy' content='fullscreen=(self)' />
        {/* iOS and Android status bar style */}
        <meta name='apple-mobile-web-app-capable' content='yes' />
        <meta name='apple-mobile-web-app-status-bar-style' content='black-translucent' />
        <meta name='theme-color' content={theme.color?.general.darkest} />
      </head>
      {process.env.NEXT_PUBLIC_GTM_ID && (
        <GoogleTagManager gtmId={process.env.NEXT_PUBLIC_GTM_ID as string} />
      )}
      <body>
        <AutoReloadOnReturn minutes={15} />
        {nodeEnv === 'production' && process.env.NEXT_PUBLIC_YM_ID && (
          <>
            <YandexMetrikaScript />
            <Suspense fallback={<></>}>
              <YandexMetrika />
            </Suspense>
          </>
        )}
        <StyledComponentsRegistry>
          <TouchDevicesMobileViewProvider
            isTouchDeviceServer={isTouchDevice}
            isTabletDeviceServer={isTabletDevice}
            agent={agent}
          >
            <ThemeProvider>
              <GlobalStyles />
              <AlertProvider>
                <PopupAlertContainer />
                <div className='shadowScreen' />
                <Suspense fallback={<MainLoader />}>
                  <LayoutToBeSuspended>{children}</LayoutToBeSuspended>
                </Suspense>
              </AlertProvider>
            </ThemeProvider>
          </TouchDevicesMobileViewProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
};

export default RootLayout;
