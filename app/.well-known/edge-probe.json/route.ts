export const dynamic = 'force-static'; // or omit; static is fine for a tiny probe
// export const revalidate = 60; // optional: ISR-style caching on some hosts

export async function GET() {
  const body = JSON.stringify({
    ok: true,
    service: 'router',
    ts: Date.now(),
  });

  return new Response(body, {
    headers: {
      'Content-Type': 'application/json; charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Cache-Control': 'public, max-age=60, stale-while-revalidate=300',
    },
  });
}
