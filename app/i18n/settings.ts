export const DEFAULT_LANGUAGE = 'en';
export const FALLBACK_LNG = 'en';
export const LNG_COOKIE_NAME = 'i18next';
export const DEFAULT_NS = 'common';
export const LANGUAGES_FULL_NAME: { [key: string]: string } = {
  en: 'English',
  ru: 'Русский',
  // cz: 'Češ<PERSON>',
  // tr: 'Türkçe',
  uz: 'O‘zbek',
  kk: 'Қазақ',
  // pl: 'Polski',
};
export const LANGUAGES = Object.keys(LANGUAGES_FULL_NAME);

export function getOptions(lng = FALLBACK_LNG, ns = DEFAULT_NS) {
  return {
    // debug: false,
    // nonExplicitSupportedLngs: true,
    supportedLngs: LANGUAGES,
    fallbackLng: FALLBACK_LNG,
    lng,
    fallbackNS: DEFAULT_NS,
    defaultNS: DEFAULT_NS,
    ns,
  };
}
