{"1Place": "1st", "2Place": "2nd", "3Place": "3rd", "4digitCode": "4-digit code", "404Header": "Oops, You're a Bit Off the Jackpot!", "404Text": "<p>Looks like this page hit the jackpot and went on vacation 🏝️</p><p>But don’t worry — luck is still close by!</p><p>Head back to the homepage — your big win might be just around the corner 🎉</p>", "Day": "Day", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "KYCpolicy": "KYC policy", "accVerified": "Account verified", "account": "Account", "action": "Action", "activate": "Activate", "active": "Active", "activeBonus": "Active Bonus", "activeBonusBetsDoNotCount": "You have an active bonus - bets do not count towards level progress.", "affiliateProgram": "Affiliate Program", "all": "All", "allBonuses": "All Bonuses", "allCategories": "All Categories Help Center", "allFaQ": "All questions", "allGames": "All Games", "allLevels": "All Levels", "allRightsReserved": "All Rights Reserved", "alreadyLeaving": "Already Leaving?", "amount": "Amount", "amountExceedsBalance": "Max amount to withdraw: {{amount}}", "amountExceedsDepositLimit": "Max amount to deposit: {{amount}}", "amountExceedsWithdrawalLimit": "Max amount to withdraw: {{amount}}", "appN": "Application No. ", "apply": "Apply", "approved": "Approved", "atNextLevel": "At the next level, you will get:", "autorizationErrorEmail": "Account not found", "autorizationErrorPhone": "Account not found", "availableCashback": "Available cashback:", "availableWithdrawalAmount": "Your available withdrawal amount is: <span className='orangeText'>{{amount}}</span>. ", "averageWithdrawalProcessing": "Average withdrawal processing time ~ ", "baccarat": "Baccarat", "back": "Back", "backToHelpCenter": "Back to Help Center", "backToMethods": "Back to all methods", "backToTournaments": "Back to all tournaments", "balance": "Balance", "bankName": "Bank name", "bet": "Bet", "betAmount": "Bet amount", "betGames": "BetGames", "betsHistory": "Bets History", "bets_quantity": "Bets quantity", "bets_sum": "Bets sum", "birthdayGift": "Birthday gift", "birthdayGiftText": "up to {{bonus}}", "blackjack": "Blackjack", "bonus": "Bonus", "bonusActivated": "Bonus activated", "bonusActivation": "Bonus Activation", "bonusActivationBefore": "Bonus activation is available before your first bet after deposit.", "bonusActivationNotPossibleWhileActiveBonus": "Bonus activation isn't possible. You have an active bonus — please wager or cancel it first.", "bonusAdded": "Bonus added", "bonusAmount": "Bonus amount", "bonusAmountInPercent": "Bonus amount in percentage", "bonusBlockedAmount": "{{blocked<PERSON><PERSON>}} will be deducted from your balance.", "bonusMustActivated": "The bonus must be activated prior to placing your first bet", "bonusName": "Bonus name", "bonusOnFirstDeposit": "Bonus on first deposit", "bonusTerms": "Bonus Terms", "bonusProgress": "Bonus Progress", "bonusWageringNotAvailableInGame": "Bonus wagering is not available in this game!", "bonusWillLost": "The bonus amount and the deposit used to activate this bonus will be lost.", "bonuses": "Bonuses", "bonuss": "Bonus", "byEmail": "By Email", "byPhone": "By Phone", "cancel": "Cancel", "cancelAnyway": "Cancel anyway", "cancelBonus": "Cancel Bonus", "cancelPayment": "Cancel payment", "cancelled": "Cancelled", "capital": "Uppercase", "cardNumber": "Card number", "casa": "Cashier", "cashback": "Cashback", "cashbackActivationNotAvailable": "Cashback Activation Not Available", "cashbackBonusText": "up to {{bonus}} everyday", "cashbackIsCreditedToBalance": "Cashback is credited to the balance", "cashbackTimer": "Cashback timer", "categories": "Categories", "categoriesOfGames": "Categories of games", "changePassword": "Change password", "changesSaved": "Changes have been saved", "checkBonuses": "Check bonuses", "checkEmail": "Check your email", "chooseBonusOrEnterPromoCode": "Choose a bonus from the available ones or enter a promo code to add a bonus", "chooseDay": "Choose a day", "chooseTopAmount": "Choose a top-up amount", "chooseTournament": "Choose tournament", "chooseWithdrawalAmount": "Choose a withdrawal amount", "clear": "Clear", "clickHeartToAddGameToLiked": "Click heart on the game icon to add it to this category.", "code": "Code", "codeResent": "Code re-sent", "cold": "Cold", "comeBackSoon": "Come back soon as there are  a lot of great bonuses and promotions waiting for you", "commission": "Commission", "completeOrCancelBonusBeforeCashback": "Please complete or cancel your current bonus before taking cashback.", "completeOrCancelCurrentBonus": "Please complete or cancel your current bonus before activating a new one.", "completeYourFreespinsFirst": "Please complete your free spins before entering another game.", "completed": "Completed", "confirm": "Confirm", "confirmEmailAndGet": "Confirm Email and get <span className='orangeText'>50 Freespins</span>", "confirmPhoneAndGet": "Confirm Phone and get <span className='orangeText'>50 Freespins</span>", "contacts": "Contacts", "copied": "<PERSON>pied", "copyAddress": "Copy address", "copyNumber": "Copy number", "country": "Country", "crash": "Crash", "createAcc": "Create an account", "createNewPass": "Create a new password", "currPassword": "Current password", "currency": "<PERSON><PERSON><PERSON><PERSON>", "dailyBonuses": "Daily Bonuses", "dailyWithdrawalLimitExceeded": "Daily withdrawal limit exceeded", "dashboard": "Dashboard", "date": "Date", "dateOfBirth": "Date of Birth", "dayShort": "d", "day_few": "days", "day_many": "days", "day_one": "day", "day_other": "days", "day_two": "days", "day_zero": "days", "demo": "Demo", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositAmount": "Deposit amount", "depositMin": "Deposit min", "depositNeedsToBeWagered": "Your deposit needs to be wagered before withdrawal. Please continue playing to meet the requirement.<br/>Remaining amount is <span className='orangeText'>{{amount}}</span>.", "depositToActivateBoth": "Make a deposit and get a {{depositPercent}} bonus up to {{currency}}{{maxAmount}}, plus {{freespins}} freespins!", "depositToActivateFreespins": "Make a deposit and get {{freespins}} freespins!", "depositToActivateWager": "Make a deposit and get a {{depositPercent}} bonus up to {{currency}}{{maxAmount}}!", "doYouWantToCancelTransaction": "Do you want to cancel transaction?", "dontUseBonus": "Don't use the bonus", "easyNavigation": "Easy navigation", "easyNavigationText": "Use the menu for quick navigation. You can also move it around the screen", "email": "Email", "emailConfirmed": "Email confirmed", "emailFormat": "Email format is not correct", "emailIsAlreadyInUse": "This email is already in use", "emailNotVerified": "Email not verified", "emailOrPhone": "Email or phone is required", "emailVerificationRequiredWithdrawal": "Email verification required for withdrawal.", "endIn": "End in", "enterCode": "Enter a code", "enterCodeSent": "Enter the code sent to your number: {{phone}}. \nYou may receive it in Telegram or as a voice call — the robot will read the code aloud.", "enterMinAmount": "Enter the minimum amount: {{amount}}", "enterMinAmountForBonus": "Min amount to activate bonus: {{amount}}", "error21AutorizationErrorEmail": "Account not found", "error21AutorizationErrorPhone": "Account not found", "failed": "Failed", "failedToSendCode": "Failed to send code", "faq": "FAQ", "favourite": "Favourite", "fillInRequiredFields": "Fill in the required fields", "filters": "Filters", "finished": "Finished", "firstLastName": "First and last name", "fixed": "fixed", "forBonus": "for bonus", "forgotBonus": "You forgot your <span className='orangeText'>welcome bonus.</span>", "forgotPassword": "Forgot password", "freeSpins": "Free Spins", "fullName": "Full Name", "fundsTransferRules": "Funds transfer rules", "fundsTransferRulesText": "Make the transfer strictly for the specified amount, in a single transaction, and within the given time frame. Otherwise, your request will NOT be processed! Do not enter anything in the payment description. Leave it blank.", "fundsWillBeReturned": "The funds will be returned to your balance.", "game": "Game", "gameName": "Game name", "games": "Games", "gamesWithBonus": "Games you can play with an active bonus", "gamesWithHighRtp": "Games with high RTP", "gamesWithLowRtp": "Games with low RTP", "genre": "Genre", "getCode": "Get Code", "getCodeWithTimer": "Get Code in <span className='w-[44px]'>{{timer}}</span>", "goHome": "Go to home page", "gotIt": "Got it", "haveAcc": "Already have an account?", "havePromoCode": "Have a promo code?", "helpCenter": "Help Center", "here": "here", "hideQr": "Hide QR", "history": "History", "homePage": "Home Page", "hot": "Hot", "hotTranslated": "Hot", "hour": "hour", "hourShort": "h", "hour_few": "hours", "hour_many": "hours", "hour_one": "hour", "hour_other": "hours", "hour_two": "hours", "hour_zero": "hours", "howToIncrease": "How to increase %?", "hrs": "hrs", "id": "ID", "identityVerification": "Identity verification", "ifChangeYourMindCancelBonus": "If you change your mind, you can cancel the bonus ", "ifOptionDoesntWork": "If one of the options doesn't work, try the other one", "ifYouMadePaymentDoNotCancel": "If you have already made a payment, do not cancel it! Wait for the funds to be credited to your account.", "in": "in", "inProgress": "In progress", "installOnPhone": "Install on your phone:", "invalidCode": "Invalid code", "invalidLink": "Invalid link", "jackpots": "Jackpots", "joinToTournaments": "Join our tournaments, earn points, and win cash prizes", "keepAnEyeOnHighRtpGames": "Keep an eye on high RTP games over the last 24 hours to increase your chances of winning", "keepAnEyeOnLowRtpGames": "Keep an eye on low RTP games over the last 24 hours to increase your chances of winning", "kycRequiredWithdrawal": "KYC required for withdrawal.", "lettersAndDigits": "6-20 characters, both letters and digits", "level": "Level", "levelUpBonus": "Level up bonuses", "levelUpBonusText": "up to {{bonus}}", "levels": "Levels", "levelsDescription": "When you reach the new status, you will have the opportunity to activate a gift bonus in your checkout. You can activate it within 7 days.<br/><br/>To reach new statuses you need to place bets for which you will receive points. Each status has a set amount of points required to achieve it. {{sum}} of your bets will give you 1 level point, regardless of whether the bet is played or not. Your current status and the amount of points are displayed in the cash register.<br/><br/>The more bets you make, the more points you earn and the higher your status and the more expensive your gifts are.", "levelsTextBlock": "<p>🎯 Welcome to the Likecasino Loyalty Program!</p><p>At Likecasino, your real-money bets turn into real rewards! 💸 With every bet, you earn Likes ❤️ — loyalty points that help you level up and unlock awesome bonuses 🎁.</p><p>The program has 25 exciting levels 🚀, and each one gives you guaranteed rewards — like no-deposit Free Spins 🎰 or cash bonuses 💵. The higher your level, the more perks you get:</p><p>🔹 Increased cashback 🔁</p><p>🔹 Faster cashback claims ⏱️</p><p>🔹 Bigger birthday gifts 🎂</p><p>🔹 Higher daily withdrawal limits 💳</p><p>It’s simple: the more you play, the more you get! 📈 Your progress never resets, and you can track it anytime in your Dashboard or on the Levels page. There are a few rules and details — check them out below 👇.&nbsp;</p><p>But most importantly: Play, earn, and enjoy the journey! 🥳</p>", "likeLeft": "{{likes}} Like left", "liked": "Liked", "likesLeft": "{{likes}} Likes left", "linkSent": "A link has been sent to your email, please click on it", "liveCasino": "Live Casino", "liveSupport": "Live Support", "login": "<PERSON><PERSON>", "loyaltyProgram": "Loyalty Program", "m": "m", "makeDeposit": "Make a deposit", "makeSureUsingCorrectNetwork": "Please make sure you are using the correct network, otherwise your deposit may be lost.", "markAllRead": "Mark everything as read", "max": "Max", "maxBonusAmount": "Maximum bonus amount", "maxWinAmount": "Maximum win amount", "menu": "<PERSON><PERSON>", "metaDescription": "Incredible selection of games and live casino! Welcome package and best weekly bonuses! Wager-free cashback, tournaments and Birthday bonus! Like casino - casino you will like!", "metaTitle": "LikeCasino - online casino slots and live dealers", "millions": "M", "min": "min", "minBalanceForCashbackActivation": "Your balance must not exceed the minimum required for cashback activation: <strong>{{minBalance}}</strong>.", "minDepositOf": "minimum deposit of", "minShort": "m", "minTransferUSDT": "The minimum transfer amount is 5.01 USDT", "minTransferUSDTtext": "Deposits of that amount, however, can be worked off", "minute_few": "minutes", "minute_many": "minutes", "minute_one": "minute", "minute_other": "minutes", "minute_two": "minutes", "minute_zero": "minutes", "minutes": "minutes", "month": "Month", "months": "Months", "more": "More...", "moreAboutLoyalty": "More about Loyalty program.", "moreGames": "More games", "moveIt": "Move it", "mustAgreeTerms": "You must agree to the terms and conditions", "my-bonuses": "My Bonuses", "myTournaments": "My Tournaments", "network": "Network", "new": "New", "newPassword": "New password", "nick_is_busy": "This nickname is already in use", "nickname": "Nickname", "no": "No", "noAccountHistory": "No bet history", "noActiveTournaments": "There are no active tournaments right now.", "noAvailablePaymentDetails": "Payment details are currently unavailable. Please try again later or choose a different method.", "noBonusesYet": "You don't have any bonuses", "noDemoForThisGame": "There is no demo for this game", "noGamesYet": "There are no games yet.", "noMatchedGames": "Didn't find anything like that - try changing the filters", "noNotificationsText": "No news just yet, but keep an eye out!", "noNotificationsTitle": "Quiet, like after a big win.", "noTransactionHistory": "No transaction history", "noTransactionHistoryText": "There are no transaction matching your request", "noWorriesWeFixing": "No worries, we’re fixing it! 🔧", "notRegistered": "Not registered yet?", "notStarted": "Not started", "nothingFound": "Nothing found...", "notifications": "Notifications", "numberOfFreeSpins": "Number of free spins", "offer": "Offer", "onDeposit": "On deposit", "onlyUSDTshouldSent": "Only USDT on the selected network should be sent to the specified address using", "onlyUSDTshouldSentText": "Transferring a different coin or using a different network may result in loss of money", "other": "Other", "p2pPaymentRejectText": "If you have made a transfer but the funds have not been credited, please wait a little while or contact support. Thank you.", "p2pPaymentRejectTitle": "Payment Expired or Cancelled", "p2pPaymentSuccessText": "If you have completed the payment, please wait a little while or contact support. Thank you.", "p2pPaymentSuccessTitle": "Payment Completed", "p2pTip": "- Perform a funds transfer<br>- Click the 'Transferred' button<br>- Wait for receipt of funds", "passKycVerification": "Pass KYC verification", "password": "Password", "passwordIncorrectEmail": "Wrong e-mail or password", "passwordIncorrectPhone": "Wrong phone number or password", "passwordUpdated": "Password updated", "passwordsDoNotMatch": "Passwords do not match", "payment": "Payment", "paymentMethod": "Payment method", "paymentMethods": "Payment methods", "payout": "Payout", "pending": "Pending", "perSpin": "Per spin", "percentage": "Percentage", "periodOfTime": "Period of time", "personal-data": "Personal data", "phone": "Phone", "phoneConfirmed": "Phone confirmed", "phoneIsAlreadyInUse": "This phone number is already in use", "phoneNotVerified": "Phone not verified", "phoneNumber": "Phone number", "phoneVerificationRequiredWithdrawal": "Phone verification required for withdrawal.", "place": "Place", "planned": "Planned", "play": "Play", "playForRealMoney": "Play for real money", "playLater": "Play later", "playWithoutWagering": "Play without wagering", "player": "Player", "pleaseSignupToPlayTheGame": "Please log in or sign up to play the game", "points": "Points", "privacyPolicy": "Privacy Policy", "prize": "Prize", "prizeFund": "Prize fund", "prizePlaces": "Prize places", "prizes": "Prizes", "processing": "Processing", "profile": "Profile", "progressLevel": "Level Progress", "promo": "Promo", "promoCode": "Promo code", "promotions": "Promotions", "provider": "Provider", "providers": "Providers", "quantity": "Quantity", "quickMenu": "Quick Menu", "rateMayChange": "The rate may change at the time of payment.", "readMore": "Read more", "recaptchaBlocked": "reCAPTCHA service is currently unreachable. Please try again later.", "recaptchaFailed": "reCAPTCHA validation failed, please try again.", "recaptchaNotice": "This site is protected by reCAPTCHA and the Google <privacyPolicy>Privacy Policy</privacyPolicy> and <termsOfService>Terms of Service</termsOfService> apply.", "recaptchaRequired": "Additional verification", "recent": "Recent", "recentGamesTitle": "Here you can find the most recent games you have played", "recommended": "Recommended", "recommendedGames": "Recommended games", "refreshPageOrGoBack": "👉 Refresh the page or go back to the homepage.", "rejected": "Rejected", "reload": "Reload", "remaining": "Remaining", "remainingTime": "Remaining time to pay:", "remainingTimeCashback": "Remaining time", "rememberCard": "Remember card for withdrawal", "rememberPhone": "Remember phone for withdrawal", "rememberWallet": "Remember wallet for withdrawal", "repeatPassword": "Repeat password", "required": "The field can't be empty", "resendSms": "Send SMS again", "resendSmsWithTimer": "Send SMS again in <span className='w-[44px]'>{{timer}}</span>", "reset": "Reset", "resetPassword": "Reset password", "responsibleGaming": "Responsible Gaming", "returnToGameBeforeCancel": "Please return to complete it first.", "returnToGameToComplete": "Please return to that game to complete it before starting a new one.", "roulette": "Roulette", "rtp": "Live RTP", "safety": "Safety", "save": "Save", "savingPaymentDataError": "Error saving payment data", "search": "Search", "searchChars": "Min. 3 chars", "searchGame": "Search game", "searchResults": "Search results", "sec": "sec", "secShort": "s", "selectBonus": "Select bonus", "selectFilter": "Select filter", "send": "Send", "sendCodeAgain": "Send code again", "sendCodeAgainWithTimer": "Send code again in <span className='w-[44px]'>{{timer}}</span>", "sendSms": "Send SMS", "sendSmsWithTimer": "Send SMS in <span className='w-[44px]'>{{timer}}</span>", "sendToAddress": "Send to the address", "sendToNumber": "Send to the number", "showLess": "Show less", "showMore": "Show more", "showQR": "Show QR", "showsGames": "TV Shows/Game shows", "signOut": "Sign out", "signUpSuccess": "Sign Up Success", "signup": "Sign Up", "slots": "Slots", "smsSent1": "SMS sent to: {{phone}}", "smsSent2": "SMS was sent to {{phone}}. \nIf you still haven’t received it or a call, please try again later or contact support.", "somethingWentWrong": "Hmm... Something’s off 🤔", "specialChar": "Special character", "startIn": "Start in", "startVerification": "Start verification", "status": "Status", "success": "Success", "system": "System", "tables": "Tables", "take": "Take", "takeBonus": "Take a bonus", "takeCashback": "Take cashback", "termsAgree": "Terms & conditions", "thanksForSticking": "Thanks for sticking with us! 💙", "thousands": "K", "timeLeft": "Time left", "timeoutError": "Request timed out, please try again.", "timeoutErrorEmail": "Request timed out, please try again.", "timeoutErrorPhone": "Request timed out, please try again.", "toAddress": "to address", "toGetBonusYouNeedTo": "To get the bonus, you need to", "toNumber": "to the number", "toPayment": "To payment", "top": "Top", "topWins": "Top Wins", "total_wins": "Total wins", "tournamentPage": "Tournament Page", "tournaments": "Tournaments", "tournamentss": "Tournament", "transactions": "Transactions", "transferred": "Transferred", "tryGames": "Try our other exciting games!", "type": "Type", "until": "Until", "upTo": "up to", "verificationSuccess": "Your verification has been successfully comleted", "verified": "Verified", "verify": "Verify", "verifyEmailForFullAccess": "Verify email for full access <span className='orangeText'> +50 Freespins</span>", "verifyIdentity": "Verify your identity to get all player's capabilities", "verifyPhone": "Verify phone", "verifyPhoneForFullAccess": "Verify phone for full access <span className='orangeText'> +50 Freespins</span>", "verifyYourEmail": "Verify your Email", "verifyYourMail": "Verify your mail", "verifyYourPhone": "Verify your phone", "videoPoker": "Video Poker", "vipSupport": "VIP support", "vipSupportText": "Instant withdrawal without limits", "voiceCallWasMade": "Voice call was made to {{phone}}.\nIf you still haven’t received it, please try again later or contact support.", "wager": "Wager", "wagerProgress": "Wager Progress", "waitForBonusesOrEnterPromoCode": "Wait for them to show up or add a promo code", "walletAddress": "Wallet address", "wantCancelBonus": "Do you want to cancel the bonus?", "wantSignOut": "You want to sign out?", "warning": "Warning!", "week": "Week", "welcomeBack": "Welcome back", "win": "Win", "withdrawal": "<PERSON><PERSON><PERSON>", "withdrawalAccountWrongStatus": "Withdrawals are blocked. Please complete KYC verification.", "withdrawalDetails": "Withdrawal details", "withdrawalLimit": "Withdrawal limit", "withdrawalProgress": "Your progress is <span className='orangeText'>{{amount}} out of {{neededAmount}}</span>.", "withdrawalProgressText": "You need to wager twice the amount of your deposit before you can withdraw funds. Please continue playing to meet this requirement.", "withdrawalRequestSuccess": "Withdrawal request successfully created. Processing will take {{withdrawalInterval}}.", "wrongPassword": "Wrong password", "wrongPasswordEmail": "Wrong e-mail or password", "wrongPasswordPhone": "Wrong phone number or password", "yes": "Yes", "you": "You", "youAreAuthorized": "You are already authorized", "youAreNotMemberInAnyTournament": "You are not a member of any tournaments yet.", "youCantCancelBonusDueToUnfinishedGame": "You can't cancel the bonus due to an unfinished game session in", "youDontHaveActiveBonus": "You don't have active bonus", "youHaveActiveBonus": "You have an active bonus.", "youHaveActiveBonusTournamentsHint": "You have an active bonus; bets won't count towards the tournament leaderboard.", "youHaveActiveFreespins": "You have active freespins.", "youHaveActiveFreespinsInGame": "You have active freespins in", "youHaveNoPendingWithdrawals": "You have no <span className='highlightedText withLeftMargin'>pending withdrawals</span>", "youHaveProcessingWithdrawals": "You have processing withdrawals", "youHaveUnfinishedGame": "You have an unfinished game session in", "youNeedDepositBeforeWithdrawals": "You need to make a deposit before withdrawals are allowed.", "youWillFindPendingWithdrawalsHere": "You will find pending withdrawals here", "yourBetsWillNotCount": "Your bets will not count towards the wagering requirement. Please choose from the recommended games where you can complete your wagering. Are you sure you want to continue?", "yourCashback": "Your cashback", "yourProgress": "Your Progress"}