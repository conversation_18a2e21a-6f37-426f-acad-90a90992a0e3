'use client';
import i18next from 'i18next';
import {
  initReactI18next,
  useTranslation as useTranslationOrg,
} from 'react-i18next';
import { useCookies } from 'react-cookie';
import resourcesToBackend from 'i18next-resources-to-backend';
import LanguageDetector from 'i18next-browser-languagedetector';
import { getOptions, LANGUAGES, LNG_COOKIE_NAME, DEFAULT_NS } from './settings';

const runsOnServerSide = typeof window === 'undefined';

i18next
  .use(initReactI18next)
  .use(LanguageDetector)
  .use(
    resourcesToBackend(
      (language, namespace = DEFAULT_NS) =>
        import(`./locales/${language}/${namespace}.json`),
    ),
  )
  .init({
    ...getOptions(),
    lng: undefined, // let detect the language on client side
    detection: {
      order: ['path', 'cookie', 'navigator', 'htmlTag'],
      lookupCookie: LNG_COOKIE_NAME,
      // caches: ['cookie'],
    },
    preload: runsOnServerSide ? LANGUAGES : [],
  });

export function useTranslation(lng, ns, options) {
  const [cookies] = useCookies([LNG_COOKIE_NAME]);
  const ret = useTranslationOrg(ns, options);
  const { i18n } = ret;

  // Handle server-side initial language
  if (runsOnServerSide && lng && i18n.resolvedLanguage !== lng) {
    i18n.changeLanguage(cookies.i18next || lng);
  }

  // Handle cookie language changes
  // useEffect(() => {
  //   if (
  //     !runsOnServerSide &&
  //     cookies.i18next &&
  //     i18n.resolvedLanguage !== cookies.i18next
  //   ) {
  //     setTimeout(() => {
  //       i18n.changeLanguage(cookies.i18next);
  //     }, 1000);
  //   }
  // }, [cookies.i18next, i18n]);

  return ret;
}
