import { GRID_BEFORE_MAX_WIDTH_KEY, GRID_MAX_WIDTH_KEY } from '@/constants.ts';

export const TOP_WINS_GRID_BREAKPOINTS = {
  '(min-width: 1700px)': {
    perView: 5,
    spacing: 16,
  },
  '(max-width: 1700px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 1410px)': {
    perView: 3,
    spacing: 16,
  },
  '(min-width: 960px) and (max-width: 1140px)': {
    perView: 2,
    spacing: 16,
  },
  '(max-width: 959px)': {
    perView: 3,
    spacing: 8,
  },
  '(max-width: 805px)': {
    perView: 2,
    spacing: 8,
  },
  '(max-width: 575px)': {
    perView: 1,
    spacing: 8,
  },
};

export const TOP_WINS_SLIDER_BREAKPOINTS = {
  [GRID_MAX_WIDTH_KEY]: {
    slides: {
      perView: 4,
      spacing: 16,
    },
  },
  [GRID_BEFORE_MAX_WIDTH_KEY]: {
    slides: {
      perView: 4,
      spacing: 16,
    },
  },
  '(max-width: 1460px)': {
    slides: {
      perView: 3.7,
      spacing: 16,
    },
  },
  '(max-width: 1300px)': {
    slides: {
      perView: 3,
      spacing: 16,
    },
  },
  '(max-width: 1150px)': {
    slides: {
      perView: 2.7,
      spacing: 16,
    },
  },
  '(max-width: 1030px)': {
    slides: {
      perView: 2,
      spacing: 8,
    },
  },
  '(max-width: 959px)': {
    slides: {
      perView: 3,
      spacing: 8,
    },
  },
  '(max-width: 890px)': {
    slides: {
      perView: 2.7,
      spacing: 8,
    },
  },
  '(max-width: 740px)': {
    slides: {
      perView: 2,
      spacing: 8,
    },
  },
  '(max-width: 560px)': {
    slides: {
      perView: 1.4,
      spacing: 8,
    },
  },
  '(max-width: 375px)': {
    slides: {
      perView: 1.2,
      spacing: 8,
    },
  },
};
