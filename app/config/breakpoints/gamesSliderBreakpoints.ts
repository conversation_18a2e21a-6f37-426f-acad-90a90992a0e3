export const GAMES_SLIDER_BREAKPOINTS = () => ({
  '(min-width: 1351px)': {
    slides: {
      perView: 7,
      spacing: 16,
    },
  },
  '(max-width: 1350px)': {
    slides: {
      perView: 6,
      spacing: 16,
    },
  },
  '(max-width: 1200px)': {
    slides: {
      perView: 5,
      spacing: 16,
    },
  },
  '(min-width: 960px) and (max-width: 1050px)': {
    slides: {
      perView: 4,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 959px)': {
    slides: {
      perView: 5,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 870px)': {
    slides: {
      perView: 5,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 770px)': {
    slides: {
      perView: 4,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 580px)': {
    slides: {
      perView: 3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 359px)': {
    slides: {
      perView: 2,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
});

export const GET_GAMES_SLIDER_BREAKPOINTS = (perViewMax: number) => ({
  '(max-width: 1350px)': {
    slides: {
      perView: perViewMax - 1,
      spacing: 16,
    },
  },
  '(max-width: 1200px)': {
    slides: {
      perView: perViewMax - 2,
      spacing: 16,
    },
  },
  '(min-width: 960px) and (max-width: 1050px)': {
    slides: {
      perView: perViewMax - 3,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 959px)': {
    slides: {
      perView: perViewMax - 2,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 870px)': {
    slides: {
      perView: perViewMax - 3,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 770px)': {
    slides: {
      perView: perViewMax - 4,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 580px)': {
    slides: {
      perView: 3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 359px)': {
    slides: {
      perView: 2,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
});
