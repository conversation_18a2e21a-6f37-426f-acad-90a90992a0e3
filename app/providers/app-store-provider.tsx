'use client';

import { createContext, type ReactNode, useContext, useRef } from 'react';
import { type StoreApi, useStore as useZustandStore } from 'zustand';
import { AppStore, createAppStore, initStore } from '@/app/store';

export const AppStoreContext = createContext<StoreApi<AppStore> | null>(null);

export interface AppStoreProviderProps {
  children: ReactNode;
}

export const AppStoreProvider = ({ children }: AppStoreProviderProps) => {
  const storeRef = useRef<StoreApi<AppStore>>();
  if (!storeRef.current) storeRef.current = createAppStore(initStore());

  return (
    <AppStoreContext.Provider value={storeRef.current}>
      {children}
    </AppStoreContext.Provider>
  );
};

// eslint-disable-next-line no-unused-vars
export const useStore = <T,>(selector: (store: AppStore) => T): T => {
  const appStoreContext = useContext(AppStoreContext);

  if (!appStoreContext) {
    throw new Error('useStore must be use within AppStoreProvider');
  }

  return useZustandStore(appStoreContext, selector);
};
