import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const host = request.headers.get('host') || '';
  const directives =
    host.includes('likecasino.com') || host.includes('likecasinomail.com')
      ? ['User-agent: *', 'Allow: /']
      : ['User-agent: *', 'Disallow: /'];

  return new Response(directives.join('\n'), {
    status: 200,
    headers: { 'Content-Type': 'text/plain' },
  });
}
