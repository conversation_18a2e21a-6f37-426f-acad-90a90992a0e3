'use client';
import React, { createContext, useContext, useState } from 'react';

type AlertContextType = {
  alerts: any[];
  showAlert: Function;
  showError: Function;
  hideAlert: Function;
};

const AlertContext = createContext<AlertContextType | undefined>(undefined);

export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert must be used within an AlertProvider');
  }
  return context;
};

export const AlertProvider = ({ children }: { children: React.ReactNode }) => {
  const [alerts, setAlerts] = useState<any[]>([]);

  const showAlert = (messageObject: any) => {
    setAlerts((prevAlerts) => {
      const newAlerts = [messageObject, ...prevAlerts];
      if (newAlerts.length > 3) {
        newAlerts.pop(); // Remove the oldest alert
      }
      return newAlerts;
    });
  };

  const hideAlert = (id: number) => {
    setAlerts(alerts.filter((alert) => alert.id !== id));
  };

  const showError = (error?: any) => {
    showAlert({
      content: error || error.message,
      id: Date.now().toString(),
      type: 'error',
      timeout: 5000,
    });
  };

  const value: AlertContextType = {
    alerts,
    showAlert,
    hideAlert,
    showError,
  };

  return (
    <AlertContext.Provider value={value}>{children}</AlertContext.Provider>
  );
};
