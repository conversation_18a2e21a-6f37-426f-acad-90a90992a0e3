'use client';
import { IGame } from '@/app/api/getGames.ts';
import { IProvider } from '@/app/api/getProvidersServer.ts';
import React, { createContext, useEffect, useState } from 'react';
import { IConstant } from '@/app/api/getConstantsServer.ts';
import { IBanner } from '@/app/api/getBannersServer.ts';

export type SystemDataType = {
  favourites?: IGame[] | null;
  newGames?: IGame[] | null;
  providers?: IProvider[] | null;
  banners?: IBanner[] | null;
  constants?: IConstant[] | null;
  // isLoaded: boolean;
};

const useSystemDataState = (initialSystemData: SystemDataType) =>
  useState<SystemDataType>(initialSystemData);

export const SystemDataContext = createContext<ReturnType<
  typeof useSystemDataState
> | null>(null);

export const useSystemData = () => {
  const systemData = React.useContext(SystemDataContext);
  if (!systemData) {
    throw new Error('useSystemData must be used within a SystemDataProvider');
  }
  return systemData;
};

const SystemDataProvider = ({
  children,
  systemDataInitial,
}: {
  children: React.ReactNode;
  systemDataInitial: SystemDataType;
}) => {
  const [systemData, setSystemData] = useState(
    systemDataInitial as SystemDataType,
  );

  useEffect(() => {
    // console.log('setSystemData');
    setSystemData(systemDataInitial);
  }, [systemDataInitial]);

  return (
    <SystemDataContext.Provider
      value={[
        {
          banners: systemData.banners || [],
          providers: systemData.providers || [],
          favourites: systemData.favourites || [],
          newGames: systemData.newGames || [],
          constants: systemData.constants || [],
        },
        setSystemData,
      ]}
    >
      {children}
    </SystemDataContext.Provider>
  );
};

export default SystemDataProvider;
