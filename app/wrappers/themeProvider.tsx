'use client';
import React from 'react';
import { ThemeProvider as ThemeStyledComponentsProvider } from 'styled-components';
import { theme } from '@/theme.ts';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

// const mobileViewBreakpoints = {
//   xxl: 1680,
//   xl: 1440,
//   lg: 1050,
//   md: 960,
//   sm: 620,
//   xs: 414,
//   xxs: 320,
// };

export default function ThemeProvider({ children }: { children: React.ReactNode }) {
  const { isTouchDevice } = useIsTouchMobileView();
  return (
    <ThemeStyledComponentsProvider theme={{ ...theme, ...(isTouchDevice && { isTouchDevice }) }}>
      {children}
    </ThemeStyledComponentsProvider>
  );
}
