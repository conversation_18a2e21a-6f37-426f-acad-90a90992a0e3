import { ReactNode } from 'react';
import UserProvider, { UserType } from '@/app/wrappers/userProvider.tsx';
import { getCookie } from '@/app/api/server-actions/getCookies.ts';

const UserProviderWrapper = async ({
  children,
  userInitial,
}: {
  children: ReactNode;
  userInitial: UserType | null;
}) => {
  const isBonusActiveCookieValue = await getCookie('isBonusActive');

  return (
    <UserProvider
      userInitial={userInitial}
      isBonusActiveCookieValue={isBonusActiveCookieValue}
    >
      {children}
    </UserProvider>
  );
};

export default UserProviderWrapper;
