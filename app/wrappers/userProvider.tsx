'use client';
import React, { createContext, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useCookies } from 'react-cookie';
import { ITranslations } from '@/types/global';
import { IBonusType } from '@/app/api/server-actions/bonuses.ts';
import { usePrevious } from '@/hooks/useReact';
import setCookieIsBonusActive from '@/utils/setCookieIsBonusActive.ts';
import { useResetGameCategoryState } from '@/app/store/selectors.ts';
import { captureAndStoreUrlParams } from '@/utils/localStorage.ts';

export interface ActiveWagerType {
  current_progress: number;
  deposit_amount: number;
  goal_sum: number;
  hours_to_play: number;
  time_to_play_seconds: number;
  max_win_sum: number;
  multiplier: number;
  template_id: number;
  wager_amount: number;
}

export interface ActiveFreespinsType {
  game_id: number;
  hours_to_play: number;
  time_to_play_seconds: number;
  spins: number;
  status: 'inactive' | 'active'; // active if the user started spinning
  template_id: number;
  game_provider_slug?: string;
  game_slug?: string;
  game_name?: string;
}

export interface UserType {
  account_balance: number;
  active_bonus_id: number;
  active_bonus_type: 'wager' | 'freespins';
  active_bonus_template_type: IBonusType;
  active_freespins: ActiveFreespinsType | null;
  active_wager: ActiveWagerType | null;
  active_bonus_photo?: string;
  active_bonus_title?: ITranslations;
  blocked_amount: number;
  cashback: number;
  currency: string;
  id: number;
  level_current_points: number;
  level_number: number;
  level_title?: ITranslations;
  min_balance: number;
  next_level_points: number;
  point_value: number;
  withdrawal_multiplicator: number;
  isBonusActiveCookie?: string;
  time_to_cashback: number;
  total_deposits_qty: number;
}

const useUserState = () => ({
  user: null as UserType | null,
  setUser: (() => null) as React.Dispatch<React.SetStateAction<UserType | null>>,
  nextPathAfterLogin: '' as string,
  setNextPathAfterLogin: (() => '') as React.Dispatch<React.SetStateAction<string>>,
  isFirstLoadDone: false as boolean,
  isBonusFilterActive: false as boolean,
  prevIsBonusFilterActive: null,
  setIsBonusFilterActive: (() => false) as React.Dispatch<React.SetStateAction<boolean>>,
  isRegistrationJustDone: false as boolean,
  setIsRegistrationJustDone: (() => false) as React.Dispatch<React.SetStateAction<boolean>>,
  inProgressCodeCheck: false as boolean,
  setInProgressCodeCheck: (() => false) as React.Dispatch<React.SetStateAction<boolean>>,
});

export const UserContext = createContext<ReturnType<typeof useUserState>>({
  user: null as UserType | null,
  setUser: (() => null) as React.Dispatch<React.SetStateAction<UserType | null>>,
  nextPathAfterLogin: '' as string,
  setNextPathAfterLogin: (() => '') as React.Dispatch<React.SetStateAction<string>>,
  isFirstLoadDone: false as boolean,
  isBonusFilterActive: false as boolean,
  prevIsBonusFilterActive: null,
  setIsBonusFilterActive: (() => false) as React.Dispatch<React.SetStateAction<boolean>>,
  isRegistrationJustDone: false as boolean,
  setIsRegistrationJustDone: (() => false) as React.Dispatch<React.SetStateAction<boolean>>,
  inProgressCodeCheck: false as boolean,
  setInProgressCodeCheck: (() => false) as React.Dispatch<React.SetStateAction<boolean>>,
});

export const useUser = () => {
  const user = React.useContext(UserContext);
  if (!user) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return user;
};

const UserProvider = ({
  children,
  userInitial,
  isBonusActiveCookieValue,
}: {
  children: React.ReactNode;
  userInitial: UserType | null;
  isBonusActiveCookieValue?: string;
}) => {
  const [user, setUser] = useState((userInitial || null) as UserType | null);
  const [nextPathAfterLogin, setNextPathAfterLogin] = useState<string>('');
  const isFirstLoadDone = useRef(true);
  const isFirstIntercomLoadDone = useRef(false);
  const [isRegistrationJustDone, setIsRegistrationJustDone] = useState(false);

  const searchParams = useSearchParams();
  const paramsCode = searchParams.get('code');
  const [inProgressCodeCheck, setInProgressCodeCheck] = useState(!!paramsCode);
  const [, setCookie] = useCookies(['isBonusActive']);

  const [isBonusFilterActive, setIsBonusFilterActive] = useState(
    isBonusActiveCookieValue === 'true',
  );
  const prevIsBonusFilterActive = usePrevious(isBonusFilterActive);

  const { resetGameCategoryState } = useResetGameCategoryState();

  useEffect(() => {
    isFirstIntercomLoadDone.current = true;
    if (!userInitial?.id) {
      captureAndStoreUrlParams();
    }
  }, []);

  useEffect(() => {
    // console.log('USER', userInitial);
    if (!userInitial?.id) {
      setUser(null);
      resetGameCategoryState('recommended');
      resetGameCategoryState('recent');
    }
    if (userInitial?.id) {
      setUser(userInitial);

      if (typeof (window as any).Intercom === 'function') {
        (window as any).Intercom('update', {
          user_id: userInitial.id.toString(),
          level: userInitial.level_number,
          currency: userInitial.currency,
        });
      }
    }
  }, [userInitial]);

  const prevHasActiveBonus = usePrevious(!!(user?.active_bonus_id && user?.active_wager));

  useEffect(() => {
    const hasActiveBonus = !!user?.active_wager;
    if (hasActiveBonus === prevHasActiveBonus) {
      return;
    }

    resetGameCategoryState('recommended');

    const isBonusFilterActivated = hasActiveBonus && isBonusActiveCookieValue !== 'false';
    const isBonusFilterDeactivated = !hasActiveBonus && isBonusActiveCookieValue !== 'undefined';

    if (isBonusFilterActivated || isBonusFilterDeactivated) {
      setIsBonusFilterActive(isBonusFilterActivated);
      setCookieIsBonusActive(isBonusFilterActivated || undefined, setCookie);
    }
  }, [user?.active_wager, prevHasActiveBonus]);

  return (
    <UserContext.Provider
      value={{
        user,
        setUser,
        nextPathAfterLogin,
        setNextPathAfterLogin,
        isFirstLoadDone: isFirstLoadDone.current,
        isBonusFilterActive,
        prevIsBonusFilterActive,
        setIsBonusFilterActive,
        isRegistrationJustDone,
        setIsRegistrationJustDone,
        inProgressCodeCheck,
        setInProgressCodeCheck,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export default UserProvider;
