'use client';
import { createContext, ReactNode, useContext, useState } from 'react';
import { isClientReady } from '@/utils';

// Custom hook to manage multiple drawer states
const useDrawers = (initialState: Record<string, boolean> = {}) => {
  const [state, setState] = useState(initialState);

  const open = (id: string) => {
    window.scrollTo(0, 0);
    setState((prevState) => ({ ...prevState, [id]: true }));
  };

  const close = (id: string) => {
    setState((prevState) => ({ ...prevState, [id]: false }));
  };

  const toggle = (id: string) => {
    setState((prevState) => ({ ...prevState, [id]: !prevState[id] }));
  };

  if (isClientReady()) {
    window.localStorage.setItem('drawerStates', JSON.stringify(state));
  }

  return {
    state,
    open,
    close,
    toggle,
  };
};

// Create context
const DrawerContext = createContext<ReturnType<typeof useDrawers> | undefined>(
  undefined,
);

// DrawerProvider component
const DrawerProvider = ({ children }: { children: ReactNode }) => {
  const currentDrawerStates =
    typeof window !== 'undefined' && localStorage.getItem('drawerStates')
      ? JSON.parse(localStorage.getItem('drawerStates') || '')
      : {};
  const drawers = useDrawers(currentDrawerStates);

  return (
    <DrawerContext.Provider value={drawers}>{children}</DrawerContext.Provider>
  );
};

// Custom hook to use drawer context by ID
export const useDrawerById = (id: string) => {
  const context = useContext(DrawerContext);

  if (!context) {
    throw new Error('useDrawerById must be used within a DrawerProvider');
  }

  return {
    isOpen: context.state[id] || false,
    open: () => context.open(id),
    close: () => context.close(id),
    toggle: () => context.toggle(id),
  };
};

export default DrawerProvider;
