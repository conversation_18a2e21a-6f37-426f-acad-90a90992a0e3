'use client';
import { createContext, ReactNode, useContext, useState } from 'react';
import { getFromLocalStorage, saveToLocalStorage } from '@/utils/localStorage.ts';
import { isClientReady } from '@/utils';

// Custom hook to manage multiple drawer states
const useDrawers = (initialState: Record<string, boolean> = {}) => {
  const [state, setState] = useState(initialState);

  const open = (id: string) => {
    window.scrollTo(0, 0);
    setState((prevState) => ({ ...prevState, [id]: true }));
  };

  const close = (id: string) => {
    setState((prevState) => ({ ...prevState, [id]: false }));
  };

  const toggle = (id: string) => {
    setState((prevState) => ({ ...prevState, [id]: !prevState?.[id] }));
  };

  saveToLocalStorage('drawerStates', state);

  return {
    state,
    open,
    close,
    toggle,
  };
};

// Create context
const DrawerContext = createContext<ReturnType<typeof useDrawers> | undefined>(undefined);

// DrawerProvider component
const DrawerProvider = ({ children }: { children: ReactNode }) => {
  const stateFromLs = getFromLocalStorage('drawerStates');
  const currentDrawerStates = isClientReady() ? stateFromLs || {} : {};
  const drawers = useDrawers(currentDrawerStates);

  return <DrawerContext.Provider value={drawers}>{children}</DrawerContext.Provider>;
};

// Custom hook to use drawer context by ID
export const useDrawerById = (id: string) => {
  const context = useContext(DrawerContext);

  if (!context) {
    throw new Error('useDrawerById must be used within a DrawerProvider');
  }

  return {
    isOpen: context.state?.[id] || false,
    open: () => context.open(id),
    close: () => context.close(id),
    toggle: () => context.toggle(id),
  };
};

export default DrawerProvider;
