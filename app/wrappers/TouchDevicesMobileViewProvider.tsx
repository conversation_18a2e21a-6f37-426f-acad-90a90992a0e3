'use client';
import { useContext, createContext, ReactNode, useState, useEffect } from 'react';
import { Image } from '@/atomic-design-components';
import { LOGO_PLACEHOLDER } from '@/constants.ts';
import { isClientReady } from '@/utils';
import { useParams } from 'next/navigation';

export const useIsTouchMobileView = () => useContext(TouchDevicesMobileViewContext);

export const TouchDevicesMobileViewProvider = ({
  children,
  isTouchDeviceServer,
  isTabletDeviceServer,
  agent,
}: {
  children: ReactNode;
  isTouchDeviceServer: boolean;
  isTabletDeviceServer: boolean;
  agent: any;
}) => {
  const { lng } = useParams();
  const canBeIpadInSafari =
    agent.device.model === 'Macintosh' && agent.browser.name.includes('Safari');

  const isWebsiteInsideGameIframe = isClientReady() && window.self !== window.top; // it's an error in some games when they redirect not to the top window, but to the iframe window

  const [isLoading, setIsLoading] = useState(
    (!isTouchDeviceServer && canBeIpadInSafari) || isWebsiteInsideGameIframe,
  );

  const [isClientTouchDevice, setIsClientTouchDevice] = useState(false); // only for ipads in safari browser (safari lies about them in user agent)

  useEffect(() => {
    if (isLoading && isClientReady()) {
      if (
        navigator.maxTouchPoints > 0 ||
        'ontouchstart' in window ||
        window.matchMedia('(pointer: coarse) and (hover: none)').matches
      ) {
        setIsClientTouchDevice(true);
      }
      if (isWebsiteInsideGameIframe && window.top) {
        window.top.location.href = `/${lng}`;
      }
      setIsLoading(false);
    }
  }, []);

  if (isWebsiteInsideGameIframe) {
    return null;
  }

  if (isLoading) {
    return (
      <div
        className={`mt-[-70px] flex w-full max-w-[1680px] grow items-center justify-center bg-general-darkest xxl:mx-auto`}
      >
        <Image
          alt='logo'
          src={LOGO_PLACEHOLDER}
          width={356}
          height={167}
          style={{ height: 84 }}
          unoptimized
        />
      </div>
    );
  }

  return (
    <TouchDevicesMobileViewContext.Provider
      value={{
        isTouchDevice: isTouchDeviceServer || isClientTouchDevice,
        isTabletDevice: isTabletDeviceServer || (canBeIpadInSafari && isClientTouchDevice),
        agentDeviceModel: agent.device.model,
      }}
    >
      {children}
    </TouchDevicesMobileViewContext.Provider>
  );
};

const useIsTouchMobileViewContext = (
  isTouchDevice: boolean,
  isTabletDevice: boolean,
  agentDeviceModel: string,
) => {
  return { isTouchDevice, isTabletDevice, agentDeviceModel };
};

const TouchDevicesMobileViewContext = createContext<ReturnType<typeof useIsTouchMobileViewContext>>(
  {
    isTouchDevice: false,
    isTabletDevice: false,
    agentDeviceModel: '',
  },
);
