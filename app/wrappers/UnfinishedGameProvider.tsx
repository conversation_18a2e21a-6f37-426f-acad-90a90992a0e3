'use client';
import { useContext, createContext, useState, useEffect, ReactNode } from 'react';
import { useParams } from 'next/navigation';
import { checkForUnfinishedGameClient } from '@/app/api/user/games/checkForUnfinishedGameClient.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';

export const useUnfinishedGame = () => useContext(UnfinishedGameContext);

export const UnfinishedGameProvider = ({ children }: { children: ReactNode }) => {
  const unfinishedGameContext = useUnfinishedGameContext();

  return (
    <UnfinishedGameContext.Provider value={unfinishedGameContext}>
      {children}
    </UnfinishedGameContext.Provider>
  );
};

const useUnfinishedGameContext = () => {
  const { lng } = useParams();
  const { user } = useUser();
  const [unfinishedGameName, setUnfinishedGameName] = useState<string>('');
  const [unfinishedGameUrl, setUnfinishedGameUrl] = useState<string>('');
  const [isUnfinishedChecked, setIsUnfinishedChecked] = useState(false);

  const getUnfinishedGame = () => {
    checkForUnfinishedGameClient()
      .then((unfinishedGame) => {
        if (unfinishedGame?.slug) {
          const url = `/${lng}/${unfinishedGame.is_live ? 'live-casino' : 'casino'}/${unfinishedGame.provider_slug}/game/${unfinishedGame.slug}`;
          setUnfinishedGameName(unfinishedGame.name);
          setUnfinishedGameUrl(url);
          setIsUnfinishedChecked(true);
        } else {
          setUnfinishedGameName('');
          setUnfinishedGameUrl('');
          setIsUnfinishedChecked(true);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  useEffect(() => {
    if (user?.id) {
      // setTimeout(() => {
      getUnfinishedGame();
      // }, 500);
    }
  }, []);

  return {
    unfinishedGameName,
    unfinishedGameUrl,
    getUnfinishedGame,
    isUnfinishedChecked,
  };
};

const UnfinishedGameContext = createContext<ReturnType<typeof useUnfinishedGameContext>>({
  unfinishedGameName: '',
  unfinishedGameUrl: '',
  isUnfinishedChecked: false,
  getUnfinishedGame: () => {},
});
