'use client';
import { createContext, ReactNode, useContext, useState } from 'react';

// Custom hook to manage global notification state
const useGlobalNotification = () => {
  const [isGlobalNotificationVisible, setIsGlobalNotificationVisible] =
    useState<boolean>(false);

  return {
    isGlobalNotificationVisible,
    setIsGlobalNotificationVisible,
  };
};

// Create context
const GlobalNotificationContext = createContext<
  ReturnType<typeof useGlobalNotification> | undefined
>(undefined);

// GlobalNotificationProvider component
const GlobalNotificationProvider = ({ children }: { children: ReactNode }) => {
  const globalNotification = useGlobalNotification();

  return (
    <GlobalNotificationContext.Provider value={globalNotification}>
      {children}
    </GlobalNotificationContext.Provider>
  );
};

// Custom hook to use global notification context
export const useGlobalNotificationState = () => {
  const context = useContext(GlobalNotificationContext);
  if (!context) {
    throw new Error(
      'useGlobalNotificationState must be used within a GlobalNotificationProvider',
    );
  }
  return context;
};

export default GlobalNotificationProvider;
