'use client';
import { createContext, useContext, useEffect, ReactNode } from 'react';
import Script from 'next/script';
import { RECAPTCHA_V3_THRESHOLD } from '@/constants';
import { getIp } from '@/app/api/getIP';
import { RecaptchaVerificationResponse } from '@/app/api/recaptcha/route.ts';

declare global {
  // eslint-disable-next-line no-unused-vars
  interface Window {
    recaptchaOptions?: {
      useRecaptchaNet: boolean;
    };
    grecaptcha: {
      // eslint-disable-next-line no-unused-vars
      ready: (callback: () => void) => void;
      execute: (
        // eslint-disable-next-line no-unused-vars
        siteKey: string,
        // eslint-disable-next-line no-unused-vars
        options: { action: string },
      ) => Promise<string>;
    };
  }
}

interface ReCaptchaContextType {
  // eslint-disable-next-line no-unused-vars
  checkRecaptchaV3: (action: string) => Promise<boolean>;
  // eslint-disable-next-line no-unused-vars
  checkRecaptchaV2: (token: string) => Promise<boolean>;
  // eslint-disable-next-line no-unused-vars
  // setRecaptchaToken: (token: string | null) => void;
  // recaptchaToken: string | null;
}

const ReCaptchaContext = createContext<ReCaptchaContextType | undefined>(undefined);

export const useReCaptcha = (): ReCaptchaContextType => {
  const context = useContext(ReCaptchaContext);
  if (!context) {
    throw new Error('useReCaptcha must be used within a ReCaptchaProvider');
  }
  return context;
};

export const ReCaptchaProvider = ({ children }: { children: ReactNode }) => {
  // if google.com is blocked/unavailable, use recaptcha.net
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.recaptchaOptions = {
        useRecaptchaNet: true,
      };
    }
  }, []);

  const checkRecaptchaV3 = async (action: string): Promise<boolean> => {
    // bypass recaptcha for specific ips
    if (process.env.NEXT_PUBLIC_RECAPTCHA_BYPASS_IPS) {
      const bypassIps = process.env.NEXT_PUBLIC_RECAPTCHA_BYPASS_IPS.split(',').map((ip) =>
        ip.trim(),
      );
      const ip = await getIp();
      if (bypassIps.includes(ip)) {
        return true;
      }
    }

    for (let attempt = 0; attempt < 3; attempt++) {
      if (typeof window.grecaptcha !== 'undefined') {
        try {
          await new Promise<void>((res) => window.grecaptcha.ready(() => res()));
          const token = await window.grecaptcha.execute(
            process.env.NEXT_PUBLIC_RECAPTCHA_V3_PUBLIC_KEY || '',
            { action },
          );

          const res: any = await fetch('/api/recaptcha', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'no-store',
            },
            body: JSON.stringify({ type: 'v3', token }),
            cache: 'no-store',
          });

          const response: RecaptchaVerificationResponse = await res.json();

          if (response?.score != null) {
            return response.score >= RECAPTCHA_V3_THRESHOLD;
          }

          // If backend says key/host invalid after a fresh deploy, drop to v2:
          if (
            !response ||
            response['error-codes']?.some((e: string) =>
              ['invalid-input-secret', 'bad-request', 'hostname-mismatch'].includes(e),
            )
          ) {
            return false; // will trigger v2 modal path
          }
        } catch {
          // try again; final failure falls through to return false
        }
      }
      await new Promise((r) => setTimeout(r, 1000));
    }
    return false;
  };

  const checkRecaptchaV2 = async (token: string): Promise<boolean> => {
    const res: any = await fetch('/api/recaptcha', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store',
      },
      body: JSON.stringify({ type: 'v2', token }),
      cache: 'no-store',
    });
    const response: RecaptchaVerificationResponse = await res.json();

    return response?.success ? true : false;
  };

  return (
    <>
      <Script
        src={`https://www.google.com/recaptcha/api.js?render=${process.env.NEXT_PUBLIC_RECAPTCHA_V3_PUBLIC_KEY}`}
        strategy='afterInteractive'
      />
      <ReCaptchaContext.Provider
        value={{
          checkRecaptchaV3,
          checkRecaptchaV2,
        }}
      >
        {children}
      </ReCaptchaContext.Provider>
    </>
  );
};

export default ReCaptchaProvider;
