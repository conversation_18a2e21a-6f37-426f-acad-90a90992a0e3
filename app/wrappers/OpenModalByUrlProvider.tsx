'use client';
import { createContext, ReactNode, useContext, useState } from 'react';
import { useOpenModalByUrl } from '@/hooks/useOpenModalByUrl.ts';

const OpenModalByUrlContext = createContext<ReturnType<typeof useRegisterModalToOpenByUrl>>({
  register: () => {},
  openModalByUrl: () => {},
});

export const useRegisterModalToOpenByUrl: any = () => useContext(OpenModalByUrlContext);

export function OpenModalByUrlProvider({ children }: { children: ReactNode }) {
  const [mods, setMods] = useState<Array<string>>([]);

  const { openModalByUrl } = useOpenModalByUrl(mods);

  const register = (id: string) => {
    if (mods.includes(id)) return;
    setMods((prev) => [...prev, id]);
  };

  return (
    <OpenModalByUrlContext.Provider value={{ register, openModalByUrl }}>
      {children}
    </OpenModalByUrlContext.Provider>
  );
}

export default OpenModalByUrlProvider;
