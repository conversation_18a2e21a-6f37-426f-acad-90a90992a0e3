'use client';
// import { IGame } from '@/app/api/getGames.ts';
// import { IProvider } from '@/app/api/getProviders.ts';
// import { IConstant } from '@/app/api/getConstants.ts';
import React, { useEffect } from 'react';
import { useStore } from '@/app/providers/app-store-provider.tsx';

// export type SystemDataType = {
//   favourites?: IGame[] | null;
//   newGames?: IGame[] | null;
//   providers?: IProvider[] | null;
//   constants?: IConstant[] | null;
//   // isLoaded: boolean;
// };

const SystemDataLoader = ({
  children,
  systemDataInitial,
}: {
  children: React.ReactNode;
  systemDataInitial: any;
}) => {
  const { saveSystemData } = useStore((state) => {
    return {
      saveSystemData: state.saveSystemData,
    };
  });
  useEffect(() => {
    // console.log('setSystemData');
    saveSystemData(systemDataInitial);
  }, [systemDataInitial]);

  return children;
};

export default SystemDataLoader;
