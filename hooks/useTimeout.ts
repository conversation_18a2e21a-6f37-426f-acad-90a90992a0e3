import { useEffect, useRef } from 'react';

export function useTimeout(
  callback: (() => void) | null,
  delay: number | null,
  deps: any[] = [],
) {
  const savedCallback = useRef(callback);

  // Remember the latest callback if it changes.
  useEffect(() => {
    if (callback) {
      savedCallback.current = callback;
    }
  }, [callback]);

  // Set up the timeout.
  useEffect(() => {
    // Don't schedule if no delay is specified.
    if (delay === null) {
      return;
    }

    const id = setTimeout(() => {
      if (savedCallback.current) {
        savedCallback.current();
      }
    }, delay);

    return () => clearTimeout(id);
  }, [delay, ...deps]);
}
