'use client';
import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';

const DEFAULT_THRESHOLD_MS = 15 * 60 * 1000;

export const useAutoReloadOnReturn = (thresholdMs = DEFAULT_THRESHOLD_MS) => {
  const router = useRouter();
  const hiddenAtRef = useRef<number | null>(null);

  useEffect(() => {
    const markHidden = () => {
      const now = Date.now();
      hiddenAtRef.current = now;
      // per-tab memory if BFCache restores the page
      sessionStorage.setItem('__hiddenAt', String(now));
    };

    const maybeReload = () => {
      const now = Date.now();
      const fromRef = hiddenAtRef.current ?? Number(sessionStorage.getItem('__hiddenAt') || 0);

      if (fromRef && now - fromRef >= thresholdMs) {
        window.location.reload();
        // // Prefer a soft refresh (re-render + revalidate server data)
        // try {
        //   router.refresh();
        // } catch {
        //   // Worst case: hard reload
        //   window.location.reload();
        // }
      }
      hiddenAtRef.current = null;
      sessionStorage.removeItem('__hiddenAt');
    };

    const onVisibilityChange = () => {
      if (document.visibilityState === 'hidden') markHidden();
      else if (document.visibilityState === 'visible') {
        maybeReload();
      }
    };

    const onFocus = () => {
      maybeReload();
    }; // covers some mobile/old Safari cases
    const onPageShow = (e: PageTransitionEvent) => {
      // Returned from BFCache => treat as a "resume"
      if ((e as any).persisted) {
        maybeReload();
      }
    };

    document.addEventListener('visibilitychange', onVisibilityChange);
    window.addEventListener('focus', onFocus);
    window.addEventListener('pageshow', onPageShow as any);

    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange);
      window.removeEventListener('focus', onFocus);
      window.removeEventListener('pageshow', onPageShow as any);
    };
  }, [router, thresholdMs]);
};
