import React, { useEffect } from 'react';

function useClickOutside(ref: React.RefObject<HTMLElement>, callback?: any) {
  useEffect(() => {
    let animation: any;

    function customHandleClickOutside(event: MouseEvent) {
      const { target } = event;
      if (
        (ref?.current &&
          (target as HTMLButtonElement)?.matches('div.shadowScreen')) ||
        (target as HTMLButtonElement)?.matches('html') ||
        (target as HTMLButtonElement)?.matches('div.reCaptchaShadowScreen')
      ) {
        if (callback) {
          callback();
        } else {
          ref.current?.classList.add('pulse-animated');
          animation = setTimeout(() => {
            if (ref.current) {
              ref.current.classList.remove('pulse-animated');
              ref.current.classList.remove('opened');
            }
          }, 600);
        }
      }
    }

    window.addEventListener('mousedown', customHandleClickOutside);
    return () => {
      window.removeEventListener('mousedown', customHandleClickOutside);
      clearTimeout(animation);
    };
  }, [ref]);
}
export default useClickOutside;
