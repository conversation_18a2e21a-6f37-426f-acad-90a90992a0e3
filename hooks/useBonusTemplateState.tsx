import { useParams } from 'next/navigation';
import { activateBonus, IBonus } from '@/app/api/server-actions/bonuses.ts';
import { openModal } from '@/utils/openModal.ts';
import { UserType, useUser } from '@/app/wrappers/userProvider.tsx';
import { useTranslation } from '@/app/i18n/client';
import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { closeModal } from '@/utils/closeModal.ts';
import { useState } from 'react';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { changeUserSettings } from '@/app/api/server-actions/changeUserSettings.ts';

export const useBonusTemplateState = (
  bonus: IBonus,
  isDepositBonus: boolean,
  isModalOpen?: boolean,
  setBonuses?: Function,
  setActiveBonusData?: Function,
) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);
  const { user, setUser } = useUser();
  const { showError } = useAlert();
  const { getFilteredBonuses, setInProgressBonuses } = useUserBonuses();

  const [inProgress, setInProgress] = useState(false);

  const hasActiveBonus = !!user?.active_bonus_id;

  if (!bonus) {
    return {};
  }

  const saveDepositBonus = (bonusId: number) => {
    changeUserSettings({ bonusId: bonusId }).then((result: any) => {
      if (!result || result.detail) {
        showError(result?.detail?.message || result?.detail || t('error'));
        return;
      }
    });
  };

  // 54 -  passport must be verified
  // 55 - e-mail must be verified
  // 63 - phone must be verified

  const isVerificationNeeded = !!bonus?.errors?.some((error) =>
    [54, 55, 63].includes(error),
  );

  const isDepositNeeded =
    isDepositBonus &&
    bonus.errors?.length &&
    (bonus.errors.includes(51) || // bets after deposit
      bonus.errors.includes(57) || // cashback interval incorrect
      bonus.errors.includes(61) || // pays after deposit
      bonus.errors.includes(47) || // template incorrect
      (bonus.bonus_category?.includes('deposit_') &&
        bonus.errors.includes(56))); // deposit_n and not available

  const onButtonClick = () => {
    if (hasActiveBonus && !isDepositNeeded) {
      openModal('newBonusWhileActive', true, isModalOpen, isModalOpen);
      return;
    }

    if (isDepositNeeded) {
      if (!hasActiveBonus) {
        saveDepositBonus(bonus.external_id);
      }
      openModal('cashierModal', true, isModalOpen, isModalOpen);
      return;
    }

    setInProgress(true);
    setInProgressBonuses(true);

    activateBonus(
      bonus.id || bonus.external_id,
      bonus.bonus_global_type === 'wager_no_deposit'
        ? 'wager'
        : bonus.bonus_global_type,
    )
      .then((result) => {
        if (!result || result.error) {
          showError(result?.error || t('error'));
        } else {
          window.scrollTo(0, 0);

          if (setActiveBonusData) {
            setActiveBonusData(undefined);
          }
          // setIsBonusFilterActive(true);

          if (isModalOpen) {
            closeModal('bonusMoreInfoModal');
          }
          if (
            bonus.bonus_type === 'freespins_no_deposit' ||
            bonus.bonus_type === 'freespins_deposit' ||
            (bonus.bonus_type === 'wager_freespins_deposit' &&
              bonus.bonus_global_type === 'freespins')
          ) {
            openModal('freespinsActivatedModal');
          }

          setUser(result.user as UserType);

          if (setBonuses) {
            // console.log('getFilteredBonuses on activate bonus');
            const activeBonusGlobalType = result.user?.active_bonus_type;
            getFilteredBonuses(bonus.external_id || 0, activeBonusGlobalType);
          }
        }
      })
      .catch((error) => {
        showError(error || t('error'));
      })
      .finally(() => {
        setInProgress(false);
        setInProgressBonuses(false);
      });
  };

  return {
    isDepositNeeded,
    onButtonClick,
    inProgress,
    isVerificationNeeded,
  };
};
