import { RefObject, useEffect, useState } from 'react';

const useIsDialogOpen = (ref: RefObject<HTMLDialogElement>) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    if (!ref.current) return;

    const mutationObserver = new MutationObserver((mutationList) => {
      for (const mutation of mutationList) {
        if (mutation.type !== 'attributes') {
          break;
        }

        if (mutation.attributeName === 'open') {
          setIsDialogOpen((prevState: boolean) => !prevState);
          break;
        }
      }
    });

    mutationObserver.observe(ref.current, { attributes: true });

    return () => {
      mutationObserver.disconnect();
    };
  }, [ref]);

  return { isDialogOpen, setIsDialogOpen };
};

export default useIsDialogOpen;
