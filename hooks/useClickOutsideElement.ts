import React, { useEffect } from 'react';

function useClickOutsideElement(
  ref: React.RefObject<HTMLElement>,
  onClickOutside: any,
) {
  useEffect(() => {
    const handleOutSideClick = (event: any) => {
      if (!ref.current?.contains(event.target)) {
        onClickOutside(event.target);
      }
    };
    window.addEventListener('mousedown', handleOutSideClick);
    return () => {
      window.removeEventListener('mousedown', handleOutSideClick);
    };
  }, [ref]);
}
export default useClickOutsideElement;
