import { useEffect, useState } from 'react';

export const useDebounce = (
  value: string,
  delay: number,
  minValueLength?: number,
) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    if (minValueLength && value?.length < minValueLength) {
      setDebouncedValue(value);
      return;
    }
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay, minValueLength]);

  return debouncedValue;
};
