import { RefObject, useEffect, useState } from 'react';

const useHorizontalMenuPosition = (ref: RefObject<HTMLElement>) => {
  const [menuStartPosition, setMenuStartPosition] = useState<any | undefined>(
    undefined,
  );

  useEffect(() => {
    if (ref?.current) {
      setMenuStartPosition(ref.current.getClientRects()?.[0]);
    }
  }, [ref]);

  return { menuStartPosition };
};
export default useHorizontalMenuPosition;
