import { useEffect, useState } from 'react';

export const useIsLastSliderElementFullyVisible = (id: string) => {
  const [isLastElementFullyVisible, setIsLastElementFullyVisible] =
    useState(false);

  useEffect(() => {
    const element = document.getElementById(id);

    if (!element) {
      console.warn(`Element with id ${id} not found.`);
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.intersectionRatio === 1) {
            setIsLastElementFullyVisible(true);
          } else {
            setIsLastElementFullyVisible(false);
          }
        });
      },
      {
        threshold: [0, 1], // 1 means “fully visible”
      },
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [id, setIsLastElementFullyVisible]);

  return isLastElementFullyVisible;
};
