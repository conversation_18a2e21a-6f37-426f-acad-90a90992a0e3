import { IGame } from '@/app/api/getGames.ts';
import { toggleFavouriteGame } from '@/app/api/server-actions/toggleFavouriteGame.ts';
import { useEffect, useRef } from 'react';
import { SystemDataType } from '@/app/wrappers/systemDataProvider.tsx';

// const [isFavourite, setIsFavourite] = useState(isGameFavourite);

// useEffect(() => {
//   setIsFavourite(isGameFavourite);
// }, [isGameFavourite]);

const useIsGameFavourite = (
  item: IGame,
  favourites: IGame[] | null,
  setSystemData: any,
  setTableData?: any,
) => {
  const favouritesInitial = useRef(favourites);
  const isGameFavourite = favourites?.some(
    (fav) => fav.external_id === item?.external_id,
  );

  useEffect(() => {
    favouritesInitial.current = favourites;
  }, []);

  const onToggleFavourite = async (e: Event) => {
    e.stopPropagation();
    if (isGameFavourite) {
      setSystemData((prev: any) => ({
        ...prev,
        favourites: favourites!.filter(
          (fav) => fav.external_id !== item.external_id,
        ),
      }));
      if (setTableData) {
        setTableData((prev: IGame[]) =>
          prev!.filter((fav: IGame) => fav.external_id !== item.external_id),
        );
      }
    } else {
      setSystemData((prev: any) => ({
        ...prev,
        favourites: [item, ...prev.favourites!],
      }));
    }

    const success = await toggleFavouriteGame(item.external_id);

    if (!success) {
      setSystemData((prev: SystemDataType) => ({
        ...prev,
        favourites: favouritesInitial.current,
      }));
      return;
    }
  };

  return { isGameFavourite, onToggleFavourite };
};

export default useIsGameFavourite;
