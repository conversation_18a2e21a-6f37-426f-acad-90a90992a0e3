import { openModal } from '@/utils/openModal.ts';
import { UserType } from '@/app/wrappers/userProvider.tsx';
import { useUnfinishedGame } from '@/app/wrappers/UnfinishedGameProvider.tsx';
import { isClientReady } from '@/utils';
import { usePathname } from 'next/navigation';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';

export const useGameCheckBeforeOpen = (
  gameUrl: string,
  user: UserType | null,
  wager_percentage?: number,
  isDemo?: boolean,
) => {
  const pathname = usePathname();
  const { previousRoute, setActiveAuthTab } = useNavigation();

  const { unfinishedGameUrl, isUnfinishedChecked } = useUnfinishedGame();

  const isUserAuthorized = !!user?.id;
  const isFreespinsBonusBeingPlayed = user?.active_freespins?.status === 'active';
  const isGameWithZeroWager = !wager_percentage || wager_percentage === 0;
  const isGameUnfinished = unfinishedGameUrl && unfinishedGameUrl !== gameUrl;

  const canGameBeOpenedByLink =
    isUserAuthorized &&
    (isDemo ||
      (!isFreespinsBonusBeingPlayed &&
        !(user?.active_wager && isGameWithZeroWager) &&
        !isGameUnfinished));

  const { isTouchDevice } = useIsTouchMobileView();
  const isSearchOpened =
    isClientReady() && document.getElementById('searchModal')?.classList.contains('opened');
  const zIndex = isSearchOpened ? '212' : '';

  const checkForGameWithZeroWager = () => {
    if (user?.active_wager && isGameWithZeroWager) {
      document
        .getElementById('continueGameWithZeroWager')
        ?.setAttribute('data-game-url-clicked', gameUrl);

      setTimeout(() => {
        openModal(
          'continueGameWithZeroWager',
          true,
          false,
          isTouchDevice || isSearchOpened,
          zIndex,
        );
      }, 100);
    }
  };

  const onGameClick = () => {
    if (!isUserAuthorized) {
      setActiveAuthTab('signUp');
      openModal('authModal');
      return;
    }
    if (isDemo) {
      return;
    }

    const isContinueGameOpened =
      isClientReady() &&
      document.getElementById('continueGameWithZeroWager')?.classList.contains('opened');

    // Accessing game page by direct link
    if (pathname.includes('/game/') && !previousRoute && pathname === gameUrl) {
      if (isFreespinsBonusBeingPlayed) {
        openModal(
          'activeFreespinsInGame',
          true,
          false,
          isTouchDevice || isSearchOpened,
          (isTouchDevice && isContinueGameOpened ? '212' : '') || zIndex,
        );
      } else {
        checkForGameWithZeroWager();
      }
      return;
    }

    if (isUnfinishedChecked) {
      if (isFreespinsBonusBeingPlayed) {
        openModal('activeFreespinsInGame', true, false, isTouchDevice || isSearchOpened, zIndex);
      } else if (isGameUnfinished) {
        openModal(
          'unfinishedGameModal',
          true,
          false,
          isTouchDevice || isSearchOpened,
          (isTouchDevice && isContinueGameOpened ? '212' : '') || zIndex,
        );
      } else {
        checkForGameWithZeroWager();
      }
    }
  };

  return { onGameClick, canGameBeOpenedByLink, isUserAuthorized };
};
