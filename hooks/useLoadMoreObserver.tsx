import { RefObject, useEffect } from 'react';

export const useLoadMoreObserver = ({
  loadMoreRef,
  loadNextPage,
  withLoadMore,
}: {
  loadMoreRef: RefObject<HTMLDivElement>;
  loadNextPage?: Function;
  withLoadMore?: boolean;
}) => {
  useEffect(() => {
    if (!withLoadMore || !loadNextPage) return;

    const observer = new IntersectionObserver(
      async (entries) => {
        if (entries[0].isIntersecting) {
          await loadNextPage();
        }
      },
      { threshold: 0, rootMargin: '0px 0px 0px 0px' },
    );

    if (loadMoreRef?.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef?.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loadMoreRef, loadNextPage, withLoadMore]);
};
