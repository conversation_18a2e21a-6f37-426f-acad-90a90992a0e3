'use client';
import parseRichText, { domToReact, HTMLReactParserOptions, DOMNode } from 'html-react-parser';
import Link from 'next/link';
import { Element } from 'domhandler';
import { usePathname } from 'next/navigation';
import { useRegisterModalToOpenByUrl } from '@/app/wrappers/OpenModalByUrlProvider.tsx';
import { ALL_PATHS_WITH_AUTHORIZATION } from '@/app/config/navMenuEntities.ts';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';
import { openModal } from '@/utils/openModal.ts';
import { LANGUAGES } from '@/app/i18n/settings.ts';
import { closeAllOpenedModals } from '@/utils/closeAllOpenedModals.ts';

const useParseRichText = () => {
  const pathname = usePathname();
  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const { setActiveAuthTab } = useNavigation();
  const { openModalByUrl } = useRegisterModalToOpenByUrl();

  const openSignUpModal = () => {
    setActiveAuthTab('signUp');
    openModal('authModal');
  };

  const getPagesUrlComparedValues = (targetLink: string) => {
    if (!targetLink.includes('likecasino.com')) {
      return {
        isSamePage: false,
        withOpenModalParam: false,
        isPathRestricted: false,
        formattedLink: targetLink,
      };
    }
    const pathWithNoLng = '/' + pathname.split('/').slice(2).join('/');

    let targetLinkWithNoLng = targetLink;

    LANGUAGES.forEach((lang) => {
      if (targetLink.includes(`/${lang}/`)) {
        targetLinkWithNoLng = targetLink.replace(`/${lang}/`, '/');
      } else if (targetLink.endsWith(`/${lang}`)) {
        targetLinkWithNoLng = targetLink.slice(0, -3);
      } else if (targetLink.includes(`/${lang}?`)) {
        targetLinkWithNoLng = targetLink.replace(`/${lang}?`, '?');
      }
    });

    const innerLinkHref = targetLinkWithNoLng
      .replace('likecasino.com', '')
      .replace('https://', '')
      .replace('www.', '');
    const formattedInnerLink = innerLinkHref.startsWith('/') ? innerLinkHref : '/' + innerLinkHref;

    const isSamePage = formattedInnerLink.split('?')[0] === pathWithNoLng;
    const withOpenModalParam = targetLink.includes('modal=');

    const isPathRestricted =
      !isUserAuthorized &&
      ALL_PATHS_WITH_AUTHORIZATION.some((path) => formattedInnerLink.includes(path));

    return {
      isSamePage,
      withOpenModalParam,
      isPathRestricted,
      formattedLink: formattedInnerLink,
    };
  };

  const parse = (text: string) => {
    if (text.includes('likecasino.com')) {
      const options: HTMLReactParserOptions = {
        replace: (domNode) => {
          const node = domNode as Element;
          if (
            node.name === 'a' &&
            node.attribs.href.includes('likecasino.com') &&
            !node.attribs.href.includes('mailto:')
          ) {
            const node = domNode as Element;
            const { isSamePage, withOpenModalParam, isPathRestricted, formattedLink } =
              getPagesUrlComparedValues(node.attribs.href);

            return isSamePage || isPathRestricted ? (
              <button
                key={node.attribs.href}
                className='link'
                onClick={() => {
                  if (isPathRestricted) {
                    openSignUpModal();
                    return;
                  }

                  if (withOpenModalParam) {
                    const modalUrlParam = node.attribs.href.split('modal=')[1].split('&')[0];
                    openModalByUrl(modalUrlParam);
                  } else {
                    closeAllOpenedModals();
                  }
                }}
              >
                {domToReact(node.children as DOMNode[])}
              </button>
            ) : (
              <Link href={formattedLink}>{domToReact(node.children as DOMNode[])}</Link>
            );
          }
        },
      };
      return parseRichText(text, options);
    }
    return parseRichText(text);
  };

  return { parse, getPagesUrlComparedValues };
};

export default useParseRichText;
