import { useLayoutEffect } from 'react';
import { URL_PARAM_TO_MODAL_ID } from '@/constants.ts';
import { openModal } from '@/utils/openModal.ts';
import { useNewNotification } from '@/app/wrappers/NewNotificationProvider.tsx';
import { useUserBonuses } from '@/app/wrappers/UserBonusesProvider.tsx';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@/app/wrappers/userProvider.tsx';
import { closeAllOpenedModals } from '@/utils/closeAllOpenedModals.ts';
import { useNavigation } from '@/app/wrappers/NavigationProvider.tsx';

export const useOpenModalByUrl = (mods: Array<string>) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const searchString = searchParams.toString();

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;

  const { inProgress: inProgressNotifications } = useNewNotification();
  const { inProgress: inProgressBonuses } = useUserBonuses();

  const { setActiveAuthTab } = useNavigation();

  const openModalByUrl = (modalUrlParam: string, searchString?: string) => {
    const withAuthOnly =
      modalUrlParam &&
      URL_PARAM_TO_MODAL_ID[modalUrlParam as keyof typeof URL_PARAM_TO_MODAL_ID]?.withAuth;
    const withoutAuthOnly =
      modalUrlParam &&
      URL_PARAM_TO_MODAL_ID[modalUrlParam as keyof typeof URL_PARAM_TO_MODAL_ID]?.withoutAuth;

    const removeOpenParamFromUrl = () => {
      if (!searchString) {
        return;
      }
      const filteredParams = new URLSearchParams(searchString);
      filteredParams.delete('modal');
      const newSearchString = filteredParams.toString();
      router.replace(`${pathname}${newSearchString ? '?' : ''}${newSearchString}`);
    };

    if ((withAuthOnly && !isUserAuthorized) || (withoutAuthOnly && isUserAuthorized)) {
      removeOpenParamFromUrl();
      if (withAuthOnly && !isUserAuthorized) {
        closeAllOpenedModals();
        setActiveAuthTab('signUp');
        openModal('authModal');
      }
      return;
    }

    const modalId =
      modalUrlParam &&
      URL_PARAM_TO_MODAL_ID[modalUrlParam as keyof typeof URL_PARAM_TO_MODAL_ID]?.id;

    if (modalId && mods.includes(modalId)) {
      const isCurrentlyOpen = document.getElementById(modalId)?.attributes?.getNamedItem('open');

      if (isCurrentlyOpen) {
        return;
      }

      closeAllOpenedModals();

      openModal(modalId, true, false, false, undefined, () => {
        removeOpenParamFromUrl();
      });
    }
  };

  useLayoutEffect(() => {
    if (
      (isUserAuthorized && (inProgressNotifications || inProgressBonuses)) ||
      !searchString ||
      !searchString.includes('modal')
    ) {
      return;
    }

    const modalUrlParam = searchParams.get('modal') || '';
    openModalByUrl(modalUrlParam, searchString);
  }, [searchString, inProgressNotifications, inProgressBonuses, isUserAuthorized, mods]);

  return { openModalByUrl };
};
