import React from 'react';

interface ModalHook {
  ref: React.RefObject<HTMLDialogElement>;
  onOpen: () => void;
  onClose: () => void;
}

function useModal(): ModalHook {
  const ref: React.RefObject<HTMLDialogElement> = React.useRef(null);

  const onOpen = (): void => {
    if (ref.current) {
      ref.current.show();
      ref.current?.classList.add('opened');
    }
  };

  const onClose = (): void => {
    if (ref.current) {
      ref.current.classList.add('close');
      ref.current?.classList.remove('opened');

      setTimeout(() => {
        if (ref.current) {
          ref.current.close();
          ref.current.classList.remove('close');
        }
      }, 200); // this value will match your CSS animation timing
    }
  };

  return { ref, onOpen, onClose };
}

export default useModal;
