import { useState } from 'react';

export const useIsPasswordShown = () => {
  const [isPasswordShown, setPasswordShown] = useState(false);

  const handlePasswordShown = () => {
    setPasswordShown(!isPasswordShown);
  };

  const type: 'text' | 'password' = isPasswordShown ? 'text' : 'password';
  const iconRightProps = {
    name: isPasswordShown ? 'eyeSignCross' : 'eyeSign',
    onClick: (e: any) => {
      e.stopPropagation();
      handlePasswordShown();
    },
  };
  return { type, iconRightProps };
};
