import { useEffect, useRef } from 'react';

export function useInterval(
  callback: (() => void) | null,
  delay: number | null,
  deps: unknown[] = [],
) {
  const savedCallback = useRef(callback);
  const timeoutId = useRef('');

  useEffect(() => {
    if (callback) {
      savedCallback.current = callback;
    }
  }, [callback]);

  useEffect(() => {
    if (delay === null || !callback) {
      return;
    }

    const id = setInterval(() => {
      if (savedCallback.current) {
        savedCallback.current();
      }
    }, delay);
    timeoutId.current = id.toString();

    return () => clearInterval(timeoutId.current);
  }, [delay, ...deps]);

  return timeoutId.current;
}
