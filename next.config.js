import withBundleAnalyzer from '@next/bundle-analyzer';
import { withSentryConfig } from '@sentry/nextjs';

const withBundleAnalyzerFunc = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  // logging: {
  //   fetches: {
  //     fullUrl: true,
  //   },
  // },
  experimental: {
    staleTimes: {
      dynamic: 5,
    },
  },
  compiler: {
    styledComponents: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        // TODO: change
        hostname: 'cdn-image-store-dev.b-cdn.net',
        port: '',
        pathname: '/**',
      },
    ],
  },
  webpack(config) {
    config.resolve.extensions.push('.ts', '.tsx');
    return config;
  },
};

// Injected content via Sentry wizard below

const sentryOptions = {
  // For all available options, see:
  // https://github.com/getsentry/sentry-webpack-plugin#options

  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  authToken: process.env.SENTRY_AUTH_TOKEN,
  // Only print logs for uploading source maps in CI
  silent: process.env.NEXT_PUBLIC_SENTRY_PRINT_SOURCEMAPS !== 'true',

  disableClientWebpackPlugin: false,
  disableServerWebpackPlugin: false,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Transpiles SDK to be compatible with IE11 (increases bundle size)
  transpileClientSDK: true,

  reactComponentAnnotation: {
    enabled: true,
  },

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,
};

export default withSentryConfig(withBundleAnalyzerFunc(nextConfig), sentryOptions);
