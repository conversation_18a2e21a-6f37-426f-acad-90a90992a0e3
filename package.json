{"name": "app", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "PORT=3002 next dev", "build": "next build", "prestart": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/bundle-analyzer": "^14.1.4", "@next/third-parties": "^15.3.3", "@nextui-org/react": "^2.3.6", "@sentry/nextjs": "^8.2.1", "@stylexjs/stylex": "^0.5.1", "accept-language": "^3.0.18", "client-only": "^0.0.1", "clsx": "^2.1.0", "countries-and-timezones": "^3.6.0", "dayjs": "^1.11.11", "deepmerge": "^4.3.1", "detect-it": "^4.0.1", "dot-object": "^2.1.4", "framer-motion": "^11.2.4", "html-react-parser": "^5.1.8", "i18next": "^23.7.18", "i18next-browser-languagedetector": "^7.2.0", "i18next-resources-to-backend": "^1.2.0", "imagekitio-react": "^1.0.10", "js-regex-pl": "^1.0.1", "keen-slider": "^6.8.6", "lodash.clonedeep": "^4.5.0", "next": "^14.2.30", "node": "^18.17.0", "prop-types": "^15.8.1", "qrcode.react": "^3.1.0", "react": "^18.3.1", "react-cookie": "^7.0.1", "react-countries": "^1.1.1", "react-customizable-progressbar": "^1.2.0", "react-datepicker": "^6.9.0", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-i18next": "^14.0.1", "react-password-checklist": "^1.8.1", "react-phone-number-input": "^3.3.12", "react-rnd": "^10.4.12", "react-select": "^5.8.0", "react-switch": "^7.0.0", "react-tabs-scrollable": "^2.0.6", "react-texty": "^0.6.0", "react-use-websocket": "^4.9.0", "sane-email-validation": "^3.0.1", "server-only": "^0.0.1", "sharp": "^0.33.5", "styled-components": "^5.3.3", "uuid4": "^2.0.3", "zustand": "^5.0.3"}, "devDependencies": {"@types/jest": "^29.5.9", "@types/node": "^20.10.1", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/react-google-recaptcha": "^2.1.9", "@types/styled-components": "^5.1.32", "@types/uuid4": "^2.0.3", "autoprefixer": "^10.4.19", "eslint": "8.54.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-storybook": "^0.6.15", "postcss": "^8.4.38", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^3.4.3", "typescript": "^5.3.2"}}