import { NextResponse } from 'next/server';
import acceptLanguage from 'accept-language';
import { FALLBACK_LNG, LANGUAGES, LNG_COOKIE_NAME } from './app/i18n/settings';
import { setRtFailCookie, verifyToken } from './app/api/server-actions/verifyToken.ts';
import { ALL_PATHS_WITH_AUTHORIZATION } from './app/config/navMenuEntities.ts';
import { COOKIES_HTTP_ONLY_OPTIONS } from './constants.ts';

acceptLanguage.languages(LANGUAGES);

export const config = {
  // Run middleware for everything EXCEPT these API routes
  matcher: ['/((?!api/recaptcha|api/user/ws-token).*)'],
};

const SKIP_PREFIXES = [
  '/_next/static',
  '/_next/image',
  '/assets',
  '/logos',
  '/favicon.ico',
  '/robots.txt',
  '/manifest.json',
  '/sitemap.xml',
  '/.well-known',
  '/api/recaptcha',
  '/api/user/ws-token',
];

const SKIP_EXTENSIONS = /\.(?:jpg|jpeg|png|gif|bmp|svg|webmanifest|webp)$/i;

export async function middleware(req) {
  const { pathname } = req.nextUrl;

  // Skip all image/static routes
  if (SKIP_PREFIXES.some((p) => pathname.startsWith(p)) || SKIP_EXTENSIONS.test(pathname)) {
    return NextResponse.next();
  }

  const isRestrictedPath = ALL_PATHS_WITH_AUTHORIZATION.some((path) => pathname.includes(path));
  const isUserAuthorized = req.cookies.get('token')?.value || req.cookies.get('refresh')?.value;

  // for /api route handlers
  if (isUserAuthorized && pathname.startsWith('/api')) {
    const requestHeaders = new Headers(req.headers);

    const { accessToken, refreshToken, expires, isNew } = await verifyToken(
      req.cookies,
      undefined,
      pathname,
    );

    if (accessToken) {
      requestHeaders.set('x-access-token', accessToken);
    }

    const res = NextResponse.next({ request: { headers: requestHeaders } });

    if (!accessToken) {
      const prevRtFail = +(req.cookies.get('rt_fail')?.value ?? 0);
      const nextRtFail = prevRtFail + 1;

      if (+nextRtFail >= 2) {
        res.cookies.delete('token');
        res.cookies.delete('refresh');
        res.cookies.delete('expires');
        res.cookies.delete('rt_fail');
      } else {
        setRtFailCookie(res.cookies, nextRtFail);
      }

      return res;
    }

    setRtFailCookie(res.cookies, 0);

    if (isNew) {
      if (accessToken) {
        console.log('new api route access', accessToken);
        res.cookies.set('token', accessToken, COOKIES_HTTP_ONLY_OPTIONS);
      }
      if (refreshToken) {
        console.log('new api route refresh', refreshToken);
        res.cookies.set('refresh', refreshToken, COOKIES_HTTP_ONLY_OPTIONS);
      }
      if (expires) {
        console.log('new api route expires', (Date.now() + expires * 1000).toString());
        res.cookies.set(
          'expires',
          (Date.now() + expires * 1000).toString(),
          COOKIES_HTTP_ONLY_OPTIONS,
        );
      }
    }

    return res;
  }

  if (!isUserAuthorized && pathname.startsWith('/api')) {
    // TODO: add currency header logic here if needed (check requests)
    return NextResponse.next();
  }

  if (isRestrictedPath && !isUserAuthorized) {
    return NextResponse.redirect(new URL('/not-found', req.url));
  }

  const currencyFromCookies = !isUserAuthorized && req.cookies.get('userCurrency')?.value;
  const currencyFromUrl = !isUserAuthorized && req.nextUrl.searchParams.get('currency');
  const currencyToSet =
    currencyFromUrl && currencyFromCookies !== currencyFromUrl && currencyFromUrl?.toUpperCase();

  const lngInUrl = pathname.split('/')[1];
  const lngInUrlIsSupported = LANGUAGES.includes(lngInUrl);
  const lngFromCookie = req.cookies.get(LNG_COOKIE_NAME)?.value;

  // if language in path is supported, and no cookie is set or lang in cookies and in url differ, set cookie
  if (lngInUrlIsSupported && (!lngFromCookie || lngFromCookie !== lngInUrl)) {
    const response = NextResponse.next();

    if (currencyToSet) {
      response.cookies.set('userCurrency', currencyToSet);
    }

    response.cookies.set(LNG_COOKIE_NAME, lngInUrl);
    const refreshTokenResult = await verifyToken(req.cookies, response.cookies, pathname);
    if ((!refreshTokenResult?.accessToken || refreshTokenResult.error) && isRestrictedPath) {
      return NextResponse.redirect(new URL('/not-found', req.url));
    }

    return response;
  }

  // if no language in url, set cookie to Accept-Language, lng in referer url or fallback
  if (!lngInUrlIsSupported) {
    let lng = lngFromCookie || acceptLanguage.get(req.headers.get('Accept-Language'));

    if (!lng && req.headers.has('referer')) {
      const refererUrl = new URL(req.headers.get('referer'));
      const lngInReferer = LANGUAGES.find((l) => refererUrl.pathname.startsWith(`/${l}`));

      lng = lngInReferer;
    }

    if (!lng) {
      lng = FALLBACK_LNG;
    }

    const urlSearchParams = req.nextUrl.searchParams.toString();

    const response = NextResponse.redirect(
      new URL(`/${lng}${pathname}${urlSearchParams ? '?' + urlSearchParams : ''}`, req.url),
    );

    if (!lngFromCookie) {
      response.cookies.set(LNG_COOKIE_NAME, lng);
    }

    if (currencyToSet) {
      response.cookies.set('userCurrency', currencyToSet);
    }

    const refreshTokenResult = await verifyToken(req.cookies, response.cookies, pathname);
    if ((!refreshTokenResult?.accessToken || refreshTokenResult.error) && isRestrictedPath) {
      return NextResponse.redirect(new URL('/not-found', req.url));
    }
    return response;
  }

  const response = NextResponse.next();

  const refreshTokenResult = await verifyToken(req.cookies, response.cookies, pathname);
  if ((!refreshTokenResult?.accessToken || refreshTokenResult.error) && isRestrictedPath) {
    return NextResponse.redirect(new URL('/not-found', req.url));
  }

  if (currencyToSet) {
    response.cookies.set('userCurrency', currencyToSet);
  }

  return response;
}
