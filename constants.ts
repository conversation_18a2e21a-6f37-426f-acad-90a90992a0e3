import { LanguagesType } from '@/types/global';

export const MAX_CONTAINER_WIDTH = '1680px';

export const GRID_MAX_WIDTH_KEY = `(min-width: ${MAX_CONTAINER_WIDTH})`;
export const GRID_BEFORE_MAX_WIDTH_KEY = `(max-width: ${MAX_CONTAINER_WIDTH})`;

export const HEADER_HEIGHT = '56px';
export const NAV_MENU_WIDTH = '272px';

export const FIRST_PHONE_CODE_TIMER_SEC = 3 * 60; // 3
// export const SECOND_PHONE_CODE_TIMER_SEC = 5 * 60; // 5
export const LAST_PHONE_CODE_TIMER_SEC = 15 * 60; // 30

export const CURRENCY = [
  // { id: 'UAH', code: 'UAH', symbol: '₴' },
  { id: 'USDT', code: 'USDT', symbol: '₮', label: 'Tether (USDT)' },
  { id: 'USD', code: 'USD', symbol: '$', label: 'US dollar (USD)' },
  { id: 'EUR', code: 'EUR', symbol: '€', label: 'Euro (EUR)' },
  { id: 'RUB', code: 'RUB', symbol: '₽', label: 'Russian ruble (RUB)' },
  // { id: 'TRY', code: 'TRY', symbol: '₺', label: 'Turkish lira (TRY)' },
  { id: 'UZS', code: 'UZS', symbol: '\uE803', label: 'Uzbekistani som (UZS)' },
  { id: 'KZT', code: 'KZT', symbol: '₸', label: 'Kazahstan tenge (KZT)' },
  // { id: 'PLN', code: 'PLN', symbol: 'zł', label: 'Polish złoty (PLN)' },
  // { id: 'CZK', code: 'CZK', symbol: 'Kč', label: 'Czech crown (CZK)' },
];

export const DEFAULT_CURRENCY = 'USD';
export const DEFAULT_POINT_VALUE = 5;

export const LOGO_PLACEHOLDER =
  "data:image/svg+xml,%3Csvg width='356' height='167' viewBox='0 0 356 167' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E @keyframes mascot-animation %7B 0%25 %7B transform: translate3d(-100%25, 0, 0); %7D 100%25 %7B transform: translate3d(100%25, 0, 0); %7D %7D .mascot__gradient-stop %7B stop-color: white; %7D %3C/style%3E%3Cdefs%3E%3ClinearGradient id='linear-mascot' x1='-47' x2='26' y1='-16' y2='-16' gradientUnits='userSpaceOnUse'%3E%3Cstop class='mascot__gradient-stop' offset='0' stop-opacity='0'/%3E%3Cstop class='mascot__gradient-stop' offset='.5' stop-opacity='.2'/%3E%3Cstop class='mascot__gradient-stop' offset='1' stop-opacity='0'/%3E%3C/linearGradient%3E%3Cg id='path-mascot' fill-rule='evenodd' clip-rule='evenodd'%3E%3Cpath d='M192.02 113.49C191.79 113.78 190.68 113.49 190.78 113.14C193.08 105.05 204.32 68.15 210.83 46.97C217.11 26.55 222.32 18.26 216.36 11.95C210.73 5.99 201.19 0.329998 196.68 0.00999816C190.05 -0.460002 191.48 12.41 186.87 40.52C181.65 72.36 161.56 139.3 158.24 144.13C154.92 148.96 168.4 156.77 177.24 161.6C186.09 166.43 195.66 145.77 201.74 138.8C207.82 131.83 208.16 138.74 217.56 147.32C226.96 155.9 243.55 164.49 255.71 159.12C267.87 153.76 239.38 141.07 232.74 132.49C226.1 123.91 221.98 112.99 221.98 112.99C221.98 112.99 242.29 87.5 252.25 81.07C262.2 74.63 263.45 67.93 255.71 60.96C247.97 53.99 243.54 51.13 235.7 59.43C227.86 67.73 197.95 105.96 192 113.51L192.02 113.49Z' fill='%23394F69'/%3E%3Cpath d='M126.2 72.08C127.86 64.65 136.28 68.98 144.49 74.48C156.4 82.47 152.96 84.49 149.47 98.45C146.35 110.92 132.78 156.16 126.89 160.07C121 163.98 105.31 154.31 107.3 148.41C119.38 112.47 126.21 72.08 126.21 72.08H126.2Z' fill='%23394F69'/%3E%3Cpath d='M120.73 37.45C110.83 21.09 119.8 13.43 126.81 13.21C133.82 12.99 133.91 23.83 139.59 19.88C145.27 15.93 154.78 2.11999 163.72 14.99C174.73 30.84 157.34 44.47 144.64 55.8C136.02 63.49 120.73 37.45 120.73 37.45Z' fill='%230083FF'/%3E%3Cpath d='M350 101.94C364.6 88.3 344.84 61.56 324.83 51.59C316.23 47.3 297.55 56.3 280.25 77.81C262.95 99.32 249.4 144.76 287.28 156.5C326.21 168.57 354.33 137.09 347.84 132.89C341.35 128.69 322.43 136.04 299.72 138.14C292.91 138.77 279.94 132.6 288.05 115.81C308.06 118.96 335.4 115.58 350 101.95V101.94ZM315.06 76.72C319.39 75.67 334.75 81.61 329.89 90C325.42 97.71 308.45 98.85 297.27 97.3C292.52 96.64 311.63 77.55 315.06 76.72Z' fill='%23394F69'/%3E%3Cpath d='M86.56 108.36C92.16 107.59 103.3 131.48 100.11 136.66C87.64 156.92 24.37 168.55 20.38 166.56C14.29 163.51 6.53002 157.57 4.04002 155C-2.04998 148.72 1.90735e-05 151.33 15.88 113.09C34.73 67.7 42.09 16.46 42.09 5.30001C42.09 -5.85999 61.27 4.34001 73.89 14.2C84.22 22.27 49.04 112.34 34.2 132.7C30.21 138.18 28.76 139.51 36.32 136.65C73.72 122.51 79.44 109.33 86.55 108.35L86.56 108.36Z' fill='%23394F69'/%3E%3C/g%3E%3Cmask id='mask-mascot' width='356' height='167' x='0' y='0' mask-type='alpha' maskUnits='userSpaceOnUse'%3E%3Cuse href='%23path-mascot'/%3E%3C/mask%3E%3C/defs%3E%3Cuse href='%23path-mascot'/%3E%3Cg mask='url(%23mask-mascot)'%3E%3Cpath fill='url(%23linear-mascot)' d='M-47-16h356v187H-47z' style='animation: mascot-animation 2s ease-in-out infinite; fill: url(%23linear-mascot)'/%3E%3C/g%3E%3C/svg%3E";

export const LOGO_PLACEHOLDER_HORIZONTAL =
  "data:image/svg+xml, %3Csvg xmlns='http://www.w3.org/2000/svg' width='369' height='180' viewBox='0 0 369 180' version='1.1' %3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' %3E%3Cg id='LIKE1' fill-rule='nonzero'%3E%3Cpath d='M196.41,125.9 C196.41,125.9 215.12,78.14 218.59,52.1 C222.06,26.06 223.49,26.31 215.39,15.32 C207.29,4.33 203.24,-2.04 196.87,0.85 C190.5,3.74 192.64,16.33 190.33,51.05 C188.02,85.77 165.34,153.14 161.87,158.34 C158.4,163.55 172.5,171.97 181.76,177.18 C191.02,182.39 201.04,160.11 207.4,152.58 C213.77,145.06 214.12,152.51 223.95,161.77 C233.79,171.03 251.15,180.29 263.88,174.5 C276.61,168.71 246.79,155.03 239.85,145.77 C232.91,136.51 228.59,124.73 228.59,124.73 C228.59,124.73 249.85,97.24 260.26,90.3 C270.68,83.36 271.98,76.13 263.88,68.61 C255.78,61.09 250.57,57.62 240.73,67.45 C230.89,77.3 196.41,125.9 196.41,125.9 Z' id='Path' fill='%23394F69' %3E%3C/path%3E%3Cpath d='M126.7,83.19 C128.44,75.17 136.91,80.5 146.24,85.66 C159.47,92.97 155.03,106.37 150.61,119.46 C146.19,132.55 135.68,172.18 129.06,175.54 C122.44,178.9 107.21,176.16 108.26,169.53 C109.31,162.9 126.7,83.19 126.7,83.19 Z' id='Path' fill='%23394F69' %3E%3C/path%3E%3Cpath d='M169.11,40.38 C177.39,21.61 167.13,14.53 159.83,15.18 C152.52,15.83 147.98,29.92 141.6,26.41 C135.21,22.9 127.91,9.95 120.7,25.13 C113.49,40.31 131.92,52.52 146.51,63.06 C156.4,70.2 169.11,40.38 169.11,40.38 Z' id='Path' fill='%230083FF' %3E%3C/path%3E%3Cpath d='M362.53,112.84 C377.81,98.13 360.83,73.23 339.9,62.48 C330.9,57.86 307.65,63.61 289.54,86.81 C271.43,110.01 257.26,159.02 296.9,171.68 C337.64,184.69 367.06,150.74 360.27,146.22 C353.48,141.69 333.68,149.61 309.91,151.88 C302.79,152.56 290.67,145.66 299.16,127.55 C320.1,130.94 347.26,127.55 362.53,112.84 Z M325.98,85.63 C330.51,84.5 346.59,90.9 341.5,99.95 C336.82,108.27 319.06,109.5 307.36,107.83 C302.39,107.12 322.38,86.53 325.98,85.63 Z' id='Shape' fill='%23394F69' %3E%3C/path%3E%3Cpath d='M83.16,123.2 C89.02,122.37 123.48,140.53 79.41,163.22 C40.61,183.2 17.29,180.61 9.08,177.58 C-1.46,173.69 -4.4,165.82 12.89,124.86 C33.13,76.92 38.76,22.04 38.76,10.01 C38.76,-2.03 49.84,0.72 71.28,15.01 C84.41,23.76 52.9,109.17 44.39,123.82 C35.88,138.47 24.56,156.78 52.52,144.46 C66.33,138.38 75.72,124.25 83.16,123.2 Z' id='Path' fill='%23394F69' %3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E ";

export const URL_PARAM_TO_MODAL_ID = {
  cashier: { id: 'cashierModal', withAuth: true, withoutAuth: false },
  signUp: { id: 'authModal', withoutAuth: true, withAuth: false },
  resetPassword: {
    id: 'resetPasswordModal',
    withoutAuth: true,
    withAuth: false,
  },
  search: { id: 'searchModal', withoutAuth: false, withAuth: false },
  notifications: {
    id: 'notificationsModal',
    withAuth: true,
    withoutAuth: false,
  },
};

// export const BONUS_TYPES = []
export const GENRES = [
  { id: 'ancient', label: { en: 'Ancient', ru: 'Древность', uz: 'Qadimgilar', kk: 'Ежелгі' } },
  {
    id: 'adventure',
    label: { en: 'Adventure', ru: 'Приключения', uz: 'Sarguzashtlar', kk: 'Шытырман оқиғалар' },
  },
  { id: 'animals', label: { en: 'Animals', ru: 'Животные', uz: 'Hayvonlar', kk: 'Хайуанаттар' } },
  { id: 'asia', label: { en: 'Asia', ru: 'Азия', uz: 'Osiyo', kk: 'Азия' } },
  { id: 'aztec', label: { en: 'Aztec', ru: 'Ацтеки', uz: 'Asteklar', kk: 'Ацтектер' } },
  { id: 'books', label: { en: 'Books', ru: 'Книги', uz: 'Kitoblar', kk: 'Кітаптар' } },
  { id: 'bank', label: { en: 'Bank', ru: 'Банк', uz: 'Bank', kk: 'Банк' } },
  {
    id: 'criminal',
    label: { en: 'Criminal', ru: 'Криминал', uz: 'Jinoyatchilik', kk: 'Криминал' },
  },
  {
    id: 'classic_slots',
    label: {
      en: 'Classic slots',
      ru: 'Классические слоты',
      uz: 'Klassik slotlar',
      kk: 'Классикалық слоттар',
    },
  },
  { id: 'dragons', label: { en: 'Dragons', ru: 'Драконы', uz: 'Ajdarlar', kk: 'Айдаһарлар' } },
  { id: 'egypt', label: { en: 'Egypt', ru: 'Египет', uz: 'Misr', kk: 'Мысыр' } },
  {
    id: 'erotic_18+',
    label: { en: 'Erotic 18+', ru: 'Эротика 18+', uz: 'Erotika 18+', kk: 'Эротика 18+' },
  },
  { id: 'fruits', label: { en: 'Fruits', ru: 'Фрукты', uz: 'Mevalar', kk: 'Жемістер' } },
  { id: 'fantasy', label: { en: 'Fantasy', ru: 'Фэнтези', uz: 'Fantastika', kk: 'Фэнтези' } },
  { id: 'gods', label: { en: 'Gods', ru: 'Боги', uz: 'Hudolar', kk: 'Құдайлар' } },
  {
    id: 'gems',
    label: { en: 'Gems', ru: 'Драгоценные камни', uz: 'Qimmatbaho toshlar', kk: 'Асыл тастар' },
  },
  {
    id: 'historical',
    label: { en: 'Historical', ru: 'Исторические', uz: 'Tarixiy', kk: 'Тарихи' },
  },
  { id: 'holidays', label: { en: 'Holidays', ru: 'Праздники', uz: 'Bayramona', kk: 'Мереке' } },
  { id: 'horror', label: { en: 'Horror', ru: 'Ужасы', uz: 'Dahshatli', kk: 'Үрейлер' } },
  { id: 'irish', label: { en: 'Irish', ru: 'Ирландские', uz: 'Irlandcha', kk: 'Ирландиялық' } },
  { id: 'joker', label: { en: 'Joker', ru: 'Джокер', uz: 'Joker', kk: 'Джокер' } },
  { id: 'luxury', label: { en: 'Luxury', ru: 'Роскошь', uz: 'Lyuks', kk: 'Люкс' } },
  { id: 'music', label: { en: 'Music', ru: 'Музыка', uz: 'Musiqa', kk: 'Музыка' } },
  { id: 'movies', label: { en: 'Movies', ru: 'Фильмы', uz: 'Kino', kk: 'Кино' } },
  {
    id: 'pirates',
    label: { en: 'Pirates', ru: 'Пираты', uz: 'Piratlar', kk: 'Теңіз қарақшылары' },
  },
  { id: 'party', label: { en: 'Party', ru: 'Вечеринка', uz: 'Oqshom', kk: 'Сауық кеші' } },
  { id: 'sea', label: { en: 'Sea', ru: 'Море', uz: 'Dengiz', kk: 'Теңіз' } },
  { id: 'sports', label: { en: 'Sports', ru: 'Спорт', uz: 'Sport', kk: 'Спорт' } },
  { id: 'space', label: { en: 'Space', ru: 'Космос', uz: 'Koinot', kk: 'Ғарыш' } },
  { id: 'vikings', label: { en: 'Vikings', ru: 'Викинги', uz: 'Vikinglar', kk: 'Викингтер' } },
  { id: 'vampires', label: { en: 'Vampires', ru: 'Вампиры', uz: 'Vampirlar', kk: 'Вампирлер' } },
  {
    id: 'wild_west',
    label: { en: 'Wild West', ru: 'Дикий Запад', uz: 'Yovvoyi G‘arb', kk: 'Жабайы Батыс' },
  },
];

export const GAME_PAGE_TYPES = ['casino', 'live-casino'];
type PaymentMethodsGroup = {
  [key in LanguagesType]: string;
};

export const PAYMENT_METHODS_GROUPS: { [key: string]: PaymentMethodsGroup } = {
  card_transfer: {
    en: 'Card transfer',
    ru: 'Перевод на карту',
    kk: 'Картаны аудару',
  },
  instant_payment: {
    en: 'SBP',
    ru: 'СБП',
    kk: 'СБП',
  },
  sbb: {
    en: 'Sberbank',
    ru: 'Сбербанк',
    kk: 'Сбербанк',
  },
  humo: {
    en: 'Humo',
    ru: 'Humo',
    kk: 'Humo',
  },
  uzcard: {
    en: 'Uzcard',
    ru: 'Uzcard',
    kk: 'Uzcard',
  },
  click: {
    en: 'Click',
    ru: 'Click',
    kk: 'Click',
  },
  pay_me: {
    en: 'PayMe',
    ru: 'PayMe',
    kk: 'PayMe',
  },
};

export const NOTIFICATIONS_TO_SHOW = {
  system: [
    'deposit_completed',
    'deposit_completed_tournament',
    // 'deposit_failed',
    'withdrawal_rollback',
    'withdrawal_rejected',
    'withdrawal_completed',
    'wager_open',
    'wager_cancel',
    'wager_lose',
    'wager_win',
    'wager_win_cut',
    'freespins_open',
    'freespins_cancel',
    'level_up',
    'confirm_email',
    'confirm_phone',
  ],
  offer: ['wager_new', 'freespins_new', 'new_bonus', 'new_bonuses'],
};

export const PAYMENT_SETTINGS_BANKS = [
  { id: 'sberbank', label: 'Сбербанк', currency: 'RUB' },
  { id: 'tinkoff', label: 'Т-Банк (Тинькофф)', currency: 'RUB' },
  { id: 'alfabank', label: 'Альфа-Банк', currency: 'RUB' },
  { id: 'vtb', label: 'ВТБ', currency: 'RUB' },
  { id: 'raiffeisen', label: 'Райффайзенбанк', currency: 'RUB' },
  { id: 'psb', label: 'Промсвязьбанк', currency: 'RUB' },
  { id: 'gazprombank', label: 'Газпромбанк', currency: 'RUB' },
  { id: 'otkritie', label: 'Банк Открытие', currency: 'RUB' },
  { id: 'rshb', label: 'Россельхозбанк', currency: 'RUB' },
  { id: 'pochta', label: 'Почта Банк', currency: 'RUB' },
  { id: 'sovkombank', label: 'Совкомбанк', currency: 'RUB' },
  { id: 'mts', label: 'МТС Банк', currency: 'RUB' },
  { id: 'yandex', label: 'Яндекс Банк', currency: 'RUB' },
  { id: 'ozon', label: 'Озон Банк', currency: 'RUB' },
  { id: 'tochka', label: 'Банк Точка', currency: 'RUB' },
  { id: 'humo', label: 'Humo', currency: 'UZS' },
  { id: 'uzcard', label: 'Uzcard', currency: 'UZS' },
  { id: 'payme', label: 'PayMe', currency: 'UZS' },
  { id: 'click', label: 'Click', currency: 'UZS' },
  { id: 'clickqr', label: 'ClickQR', currency: 'UZS' },
];

// difficulty level for whether to show checkbox
// from 0 to 1, 1 is the highest difficulty, increments of 0.1
export const RECAPTCHA_V3_THRESHOLD =
  Math.round(parseFloat(process.env.NEXT_PUBLIC_RECAPTCHA_V3_DIFFICULTY || '0.5') * 10) / 10;

//z-index values for Components
// desktop: {
//   alertPopup: 1000,
//   modals: 200,
//   drawer: 120,
//   shadowScreen: 110,
//   header: 100,
//   .onTop.shadowScreen: 210,
//   .onTop modals: 211,
// }
// mobile: {
//   alertPopup: 1000,
//   modals: 200,
//   drawer: 100,
//   drawerMenu: 100,
//   shadowScreenMenu: 90, (.blured)
//   shadowScreen: 110,
//   header: 100,
//   pageMenuFixed: 50,
//   bottomMenu: 100,
//   .onTop.shadowScreen: 210,
//   .onTop modals: 211,
// }
// for small elements inside components use index 1-10
