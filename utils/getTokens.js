import { css } from 'styled-components';

const getTokenValues = (theme, token) => {
  let componentTokens = theme.components;
  const keys = token.split('-');
  for (let i = 0; i < keys.length; i++) {
    const newKeyObject = componentTokens?.[keys[i]];
    if (!newKeyObject) {
      console.error(`No "${token}" token object found. Key: "${keys[i]}"`);
      return null;
    } else {
      componentTokens = newKeyObject;
    }
  }
  return componentTokens;
};

export const getTokens = (tokenName, theme) => {
  const cssObject = getTokenValues(theme, tokenName);

  if (cssObject) {
    return css`
      ${Object.keys(cssObject).reduce((accString, cssKey) => {
        return accString + `${cssKey}: ${cssObject[cssKey]};`;
      }, '')};
    `;
  }
};

export default getTokens;
