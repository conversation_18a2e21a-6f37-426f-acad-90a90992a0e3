export function toCamelCase(str: string): string {
  // Remove punctuation
  const cleanedStr = str.replace(/[^\w\s]/g, '');

  // Split into words
  const words = cleanedStr.split(/\s+/);

  // Convert to camelCase
  const camelCaseStr = words
    .map((word, index) => {
      if (index === 0) {
        return word.toLowerCase();
      }
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join('');

  return camelCaseStr;
}
