import { useAlert } from '@/app/wrappers/AlertProvider.tsx';
import { useTranslation } from '@/app/i18n/client';
import { LanguagesType } from '@/types/global';

export const useCopyToClipboard = (lng: LanguagesType) => {
  const { t } = useTranslation(lng);
  const { showAlert } = useAlert();

  const copy = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        showAlert({
          content: t('copied'),
          id: Date.now().toString(),
          type: 'success',
          timeout: 3000,
        });
      })
      .catch(() => {
        console.log('Error copying to clipboard:', text);
      });
  };

  return copy;
};
