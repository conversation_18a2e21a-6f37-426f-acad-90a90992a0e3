import { IPaymentTransaction } from '@/app/api/payments/getPaymentTransactionsClient.ts';

export const deleteSubsequentDuplicateObjectsInPayments = (
  array: IPaymentTransaction[],
) => {
  if (!array) {
    return;
  }
  const seen = new Set();

  const uniqueArray = array.filter((obj: IPaymentTransaction) => {
    const keyToCompare = obj.payment_cascade_id
      ? 'payment_cascade_id'
      : 'payment_setting_id';

    if (seen.has(obj[keyToCompare])) {
      return false; // Skip if this id has been seen
    } else {
      seen.add(obj[keyToCompare]); // Add this id to the set and include the object
      return true;
    }
  });

  return uniqueArray;
};
