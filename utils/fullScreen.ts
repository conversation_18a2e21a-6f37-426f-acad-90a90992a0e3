export const isFullScreen = () =>
  document.fullscreenElement ||
  (document as any).mozFullScreenElement ||
  (document as any).webkitFullscreenElement ||
  (document as any).msFullscreenElement;

function simulateIOSFullscreen() {
  window.scrollTo(0, 1); // Hide address bar
  document.documentElement.style.height = '100%';
  document.documentElement.style.overflow = 'hidden';
  document.body.style.height = '100%';
  document.body.style.overflow = 'hidden';
  console.log('Simulated fullscreen for iOS');
}

export const enterFullScreen = (elem: any) => {
  if (!elem) {
    return;
  }

  // Android/Desktop Fullscreen API
  if (elem.requestFullscreen) {
    elem.requestFullscreen().catch((err: any) => {
      console.log('Fullscreen request failed:', err);
      // simulateIOSFullscreen(); // Fallback for iOS
    });
  } else if (elem.mozRequestFullScreen) {
    // Firefox
    elem.mozRequestFullScreen();
  } else if (elem.webkitRequestFullscreen) {
    // Safari/Older WebKit
    elem.webkitRequestFullscreen();
  } else if (elem.msRequestFullscreen) {
    // Older Microsoft Edge/IE
    elem.msRequestFullscreen();
  }
  // else {
  //   simulateIOSFullscreen(); // Fallback for iOS if no fullscreen API
  // }
};

export const closeFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if ((document as any).webkitExitFullscreen) {
    /* Safari */
    (document as any).webkitExitFullscreen();
  } else if ((document as any).msExitFullscreen) {
    /* IE11 */
    (document as any).msExitFullscreen();
  }
};
