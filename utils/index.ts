import validators from './formValidationRules';

export const isClientReady = () => typeof window !== 'undefined';

const checkField = (value: any, rules: any, onError: any, data: any) => {
  for (let ruleIndex = 0; ruleIndex < rules?.length; ruleIndex += 1) {
    const ruleItem = rules[ruleIndex];
    const ruleName = ruleItem.type || ruleItem;
    const ruleParams = ruleItem.type ? ruleItem : {};
    const customRuleName = ruleItem.customRuleName;

    if (!validators[ruleName]) {
      throw new Error(`FormValidationRules: Invalid validator: ${ruleName}`);
    }
    if (!validators[ruleName](value, ruleParams, data)) {
      onError(customRuleName || ruleName);
      break;
    } else if (
      data?.ruleWithSuccess &&
      data?.ruleWithSuccess?.type === ruleName
    ) {
      onError('success');
      break;
    }
  }
};

export const isEqualArraysById = (newValueArr: any, initialArr: any) => {
  let foundSameObjectsCount = 0;

  for (let i = 0; i < newValueArr.length; i++) {
    const index = initialArr.findIndex(
      (initialArrValue: any) =>
        (initialArrValue.id || initialArrValue) === newValueArr[i].id,
    );

    if (index !== -1) {
      foundSameObjectsCount += 1;
    }
  }

  return foundSameObjectsCount === newValueArr.length;
};

export const validateOneField =
  (fieldName: any, rules: any) => (value: any, data: any) => {
    const error: any = {};
    checkField(
      value,
      rules[fieldName],
      (ruleName: any) => {
        error[fieldName] = ruleName;
      },
      data,
    );

    return error;
  };
