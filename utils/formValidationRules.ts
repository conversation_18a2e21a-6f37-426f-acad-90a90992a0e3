'use client';
import pL from 'js-regex-pl'; // any letter from any language
import dot from 'dot-object';
import { isPossiblePhoneNumber } from 'react-phone-number-input';
import isEmail from 'sane-email-validation';
import dayjs from 'dayjs';

const checkUrl = (value: any) => {
  const res = value.match(
    /(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})/gi,
  );
  return res !== null;
};

export const validators: any = {
  requiredPrice: (value: any) => {
    if (+value === 0 || !value || (Array.isArray(value) && !value.length)) {
      return false;
    }

    if (typeof value.trim === 'function') {
      return value.trim();
    }

    return value;
  },
  required: (value: any) => {
    if (value === 0) {
      return true;
    }

    if (!value || (Array.isArray(value) && !value.length)) {
      return false;
    }

    if (typeof value.trim === 'function') {
      return value.trim();
    }

    return value;
  },
  requiredIfFieldsEmpty: (value: any, ruleParams: any, data: any) => {
    return (
      // value ||
      ruleParams.fields.some(
        (field: any) => data?.values && dot.pick(field, data.values),
      )
    );
  },
  requiredIfFieldsNotEmpty: (value: any, ruleParams: any) => {
    return (
      value ||
      ruleParams.fields.every(
        (field: any) =>
          ruleParams?.values && !dot.pick(field, ruleParams.values),
      )
    );
  },
  requiredCity: (value: any) => {
    if (!value || (Array.isArray(value) && !value.length)) {
      return false;
    }

    return value;
  },
  requiredStringWithPresetPart: (value: any, ruleParams: any) => {
    if (
      !value ||
      value === ruleParams.presetPart ||
      value === ruleParams.presetPart.slice(0, ruleParams.presetPart.length - 1)
    ) {
      return false;
    }

    return value;
  },
  minLength: (value: any, ruleParams: any) =>
    !value || value.length >= ruleParams.value,
  maxLength: (value: any, ruleParams: any) =>
    !value || value.length <= ruleParams.value,
  maxNumber: (value: any, ruleParams: any) =>
    !value || value <= ruleParams.value,
  minNumber: (value: any, ruleParams: any) =>
    !value || value >= ruleParams.value,
  vinCode: (value: any) =>
    !value || new RegExp(/^[a-zA-Z0-9]{17}$/, 'i').test(value),
  // equalTo: (value, ruleParams, data) => !value || data[ruleParams.field] === value,
  regex: (value: any, ruleParams: any) =>
    !value || new RegExp(ruleParams.value, 'i').test(value),
  // ^[a-zA-Z\u0400-\u04FF\s'\-.]+$ - old name regex
  customIdSymbols: (value: any) => new RegExp(/^[a-zA-Z0-9'_-]+$/).test(value),
  specialCharacters: (value: any) => new RegExp('^[a-zA-Z0-9]*$').test(value),
  nickname: (value: any) =>
    !value || new RegExp('^[a-zA-Z0-9_-]*$').test(value),
  fullName: (value: any) =>
    !value || new RegExp(`^[${pL}'’]+(?:\\s[${pL}'’]+)+$`).test(value),
  birthDate: (value: any) => {
    if (!value) {
      return true;
    }
    const regex = /^(0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])-(19\d\d|20\d\d)$/;
    const match = value.match(regex);

    if (!match) return false;

    const year = parseInt(match[3], 10);
    return year <= dayjs().year() - 18;
  },
  onlyDigits: (value: any) =>
    !value || new RegExp('^(0|[1-9][0-9]{0,30})$').test(value),
  jobTime: (value: any) =>
    value === '00:00'
      ? false
      : !value || new RegExp('^[0-9]+:[0-5][0-9]$').test(value),
  price: (value: any) => new RegExp('^[0-9]+,[0-9][0-9]$').test(value),
  notInBaseType: (value: any) =>
    !value.size ||
    (Array.isArray(value)
      ? value.findIndex((obj) => obj.notValid) === -1
      : !value.notValid),
  keyInListAbsent: (value: any, ruleParams: any, data: any) => {
    if (!value) {
      return true;
    }
    const { listKey, searchKey, filterListBy, transformValue } =
      ruleParams.dataForValidation;

    const valueToCheck = transformValue ? transformValue(value) : value;
    const initialValue = transformValue
      ? transformValue(data.initialValue)
      : data.initialValue;
    const listInitial =
      data.list || (data[listKey] && Object.values(data[listKey]));

    if (!listInitial) {
      return true;
    }
    const list = filterListBy
      ? listInitial.filter(
          (item: any) =>
            dot.pick(filterListBy, item) ===
            dot.pick(filterListBy, data.values),
        )
      : listInitial;
    if (
      list.findIndex(
        (item: any) =>
          dot.pick(searchKey, item) === valueToCheck &&
          initialValue !== valueToCheck,
      ) !== -1
    ) {
      return false;
    }
    return value;
  },
  email: (value: any) => {
    return !value || isEmail(value);
  },
  password: (value: any, ruleParams: any) => {
    const regex = /^(?=.*[A-Za-z])(?=.*\d).{6,20}$/;
    return !value || regex.test(value);
  },
  phone: (value: any) => {
    return !value || !isPossiblePhoneNumber || isPossiblePhoneNumber(value);
  },
  website: (value: any) => {
    return !value || checkUrl(value);
  },
  recaptcha: (value: any) => {
    return !!value; // Returns true if the recaptcha token is present
  },
  valueEqualTo: (value: any, ruleParams: any, data: any) => {
    const formValues = data.values;
    if (value || ruleParams.field) {
      return value === formValues?.[ruleParams.field];
    }
    return true;
  },
  year: (value: any) => {
    if (!value) {
      return true;
    }
    const enteredYear = parseInt(value, 10);
    const currentYear = new Date().getFullYear();
    return (
      /^\d{4}$/.test(value) && enteredYear >= 1900 && enteredYear <= currentYear
    );
  },
  linkRelative: (value: any) => {
    if (!value) {
      return true;
    }
    const pattern = /^[a-zA-Z0-9_-]+$/;
    return pattern.test(value);
  },
  // defaultTranslationRequired: (value, ruleParams) => {
  //   const { chosenLng, defaultLng } = ruleParams
  //   if (!value[chosenLng] && chosenLng === defaultLng) {
  //     return false
  //   }
  //   return true
  // },
};

const checkField = (value: any, rules: any, onError: any, data: any) => {
  for (let ruleIndex = 0; ruleIndex < rules?.length; ruleIndex += 1) {
    const ruleItem = rules[ruleIndex];
    const ruleName = ruleItem.type || ruleItem;
    const ruleParams = ruleItem.type ? ruleItem : {};

    if (!validators[ruleName]) {
      throw new Error(`FormValidationRules: Invalid validator: ${ruleName}`);
    }
    if (!validators[ruleName](value, ruleParams, data)) {
      onError(ruleName);
      break;
    } else if (
      data?.ruleWithSuccess &&
      data?.ruleWithSuccess?.type === ruleName
    ) {
      onError('success');
      break;
    }
  }
};

// per field validation
export const rule = (fieldName: any, rules: any) => (value: any, data: any) => {
  const error: any = {};
  checkField(
    value,
    rules[fieldName],
    (ruleName: any) => {
      error[fieldName] = ruleName;
    },
    data,
  );

  return error;
};

const validate: any = (allRules: any) => (values: any, data: any) => {
  const errors: any = {};
  Object.keys(allRules).forEach((field) => {
    if (!allRules[field]) {
      return;
    }

    const rules = allRules[field].filter((rule: any) => !rule.withSuccess);
    checkField(
      dot.pick(field, values),
      rules,
      (ruleName: any) => {
        errors[field] = ruleName;
      },
      data,
    );
  });

  return errors;
};

export default validate;
