import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
dayjs.extend(timezone);
dayjs.extend(utc);

const firstOrderBonuses = [
  'deposit_1',
  'deposit_2',
  'deposit_3',
  'deposit_4',
  'deposit_5',
];

export const sortUserBonuses = (bonuses: IBonus[]) => {
  const firstOrderBonusesFiltered = bonuses.filter(
    (bonus) => firstOrderBonuses.indexOf(bonus.bonus_category) !== -1,
  );
  const otherBonuses = bonuses.filter(
    (bonus) => firstOrderBonuses.indexOf(bonus.bonus_category) === -1,
  );

  const firstOrderBonusesSorted = firstOrderBonuses
    .map((category) => {
      const categoryBonuses = firstOrderBonusesFiltered.filter(
        (bonus) => bonus.bonus_category === category,
      );

      return categoryBonuses.sort((a, b) => {
        // Ensure freespins_deposit bonuses are at the end
        if (
          a.bonus_type === 'freespins_deposit' &&
          b.bonus_type !== 'freespins_deposit'
        ) {
          return 1;
        }
        if (
          b.bonus_type === 'freespins_deposit' &&
          a.bonus_type !== 'freespins_deposit'
        ) {
          return -1;
        }

        if (
          a.bonus_type === 'wager_deposit' ||
          a.bonus_type === 'wager_freespins_deposit'
        ) {
          if (
            b.bonus_type === 'wager_deposit' ||
            b.bonus_type === 'wager_freespins_deposit'
          ) {
            const percentageComparison =
              a.deposit_amount_percentage - b.deposit_amount_percentage;
            if (percentageComparison !== 0) {
              return percentageComparison;
            }
          }
        }

        if (
          a.bonus_type === 'freespins_deposit' &&
          b.bonus_type === 'freespins_deposit'
        ) {
          const freespinsComparison = a.freespins_amount - b.freespins_amount;
          if (freespinsComparison !== 0) {
            return freespinsComparison;
          }
        }

        // Fallback to timer-based sorting if other criteria are equal
        if (!a.time_to_activate) {
          return 1;
        }
        if (!b.time_to_activate) {
          return -1;
        }

        const aActivationDate = a.time_to_activate
          ? dayjs().add(a.time_to_activate, 'second')
          : dayjs().add(a.hours_to_activate || 0, 'hour');
        const bActivationDate = b.time_to_activate
          ? dayjs().add(b.time_to_activate, 'second')
          : dayjs().add(b.hours_to_activate || 0, 'hour');

        return aActivationDate.isBefore(bActivationDate) ? -1 : 1;
      });
    })
    .flat();

  const otherBonusesSorted = otherBonuses.sort((a, b) => {
    if (!a.time_to_activate) {
      return 1;
    }
    if (!b.time_to_activate) {
      return -1;
    }

    const aActivationDate = a.time_to_activate
      ? dayjs().add(a.time_to_activate, 'second')
      : dayjs().add(a.hours_to_activate, 'hour');
    const bActivationDate = b.time_to_activate
      ? dayjs().add(b.time_to_activate, 'second')
      : dayjs().add(b.hours_to_activate, 'hour');

    return aActivationDate.isBefore(bActivationDate) ? -1 : 1;
  });

  return [...firstOrderBonusesSorted, ...otherBonusesSorted];
};
