import { userAgent } from 'next/server';
import { headers } from 'next/headers';

function checkIsMobileDeviceServer(userAgentArg?: string) {
  const regexMobile =
    /(mobi|ipod|phone|blackberry|opera mini|fennec|minimo|symbian|psp|nintendo ds|archos|skyfire|puffin|s40Ovibrowser|blazer|bolt|gobrowser|iris|maemo|semc|teashark|uzard )/;
  const regexTablet =
    /(ipad|tablet|(android(?!.*mobile))|(windows(?!.*phone)(.*touch))|kindle|playbook|silk|(puffin(?!.*(IP|AP|WP))))/;

  const fullInfo = userAgent({ headers: headers() });
  const agent = userAgentArg || fullInfo.ua.toLowerCase();

  const isMobileDevice = regexMobile.test(agent);
  const isTabletDevice = regexTablet.test(agent);

  return {
    isTouchDevice: isMobileDevice || isTabletDevice,
    isTabletDevice,
    agent: fullInfo,
  };
}

export default checkIsMobileDeviceServer;
