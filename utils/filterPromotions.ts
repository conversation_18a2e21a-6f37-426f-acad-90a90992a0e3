import { IPromotion } from '@/app/api/getPromotionsServer.ts';
import { sortItems } from '@/utils/sortItems.ts';

const filterPromotions = (
  promotions: IPromotion[],
  isAuthorized: boolean,
  promotionsOrderArray?: number[],
) => {
  if (!promotions?.length) {
    return {
      items: [],
      total: 0,
    };
  }

  const promotionsFiltered = promotions.filter((promo) => {
    return isAuthorized
      ? ['signed_in', 'all'].includes(promo.visibility)
      : ['guest', 'all'].includes(promo.visibility);
  });

  return {
    items: promotionsOrderArray
      ? sortItems({
          items: promotionsFiltered,
          order: promotionsOrderArray,
        })
      : promotionsFiltered,
    total: promotionsFiltered.length,
  };
};

export { filterPromotions };
