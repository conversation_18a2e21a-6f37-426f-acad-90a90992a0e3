interface SortItemsProps {
  items: any[];
  order: number[];
}

const sortItems = ({ items, order }: SortItemsProps) => {
  if (!items) {
    return [];
  }
  if (!order?.length) {
    return items;
  }

  const itemsNotInOrderArr = items.filter((el) => !order.includes(el.id));
  const itemsInOrderArr = items.filter((el) => order.includes(el.id));

  const sortedArray = itemsInOrderArr.sort((a, b) => {
    const indexA = order.indexOf(a.id);
    const indexB = order.indexOf(b.id);

    return indexA - indexB;
  });

  const sortedArrayNotInOrder =
    itemsNotInOrderArr?.sort((a, b) => {
      const indexA = a.id;
      const indexB = b.id;

      return indexB - indexA;
    }) || [];

  return [...sortedArray, ...sortedArrayNotInOrder];
};

const sortByActiveId = (arr: any, activeId: any) => {
  if (!arr) {
    return [];
  }
  if (!activeId) {
    return arr;
  }
  const sortedList = [...arr].sort((a: any, b: any) => {
    if (a.id == activeId) return -1;
    if (b.id == activeId) return 1;
    return 0;
  });
  return sortedList;
};

export { sortByActiveId, sortItems };
