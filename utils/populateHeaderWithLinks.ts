export function populateHeaderWithLinks() {
  const menu = document.getElementById('menu');
  if (!menu) {
    return;
  }
  const nav = document.getElementById('headerNavMenu');
  const items = menu!.getElementsByClassName('menuItem');
  const moreWrapper = document.getElementById('moreItems');
  const moreItemsDropdown = document.getElementById('moreItemsDropdown');
  const moreLink = document.getElementById('more');
  const elAfterNavLinks = document.getElementById('afterNavLinks');
  const navOffsetRight =
    window.innerWidth - (nav!.offsetWidth + nav!.offsetLeft);

  if (navOffsetRight - elAfterNavLinks!.offsetWidth > 10) {
    Array.from(moreItemsDropdown!.children).forEach((child) =>
      menu!.insertBefore(child, menu!.lastChild),
    );
    moreLink!.style.display = 'none';
  }

  for (let i = items.length; i--; ) {
    let $this = items[i];
    if (!($this instanceof HTMLElement && items[0] instanceof HTMLElement)) {
      menu!.style.opacity = '1';
      return;
    }
    if (
      $this.offsetTop > items[0].offsetTop ||
      (moreLink!.style.display === 'flex' &&
        moreWrapper!.offsetTop > items[0].offsetTop)
    ) {
      $this.classList.add('moreItem');
      moreItemsDropdown!.insertBefore($this, moreItemsDropdown!.firstChild);
      moreLink!.style.display = 'flex';
    } else {
      i = 0;
    }
  }
  menu!.style.opacity = '1';
}
