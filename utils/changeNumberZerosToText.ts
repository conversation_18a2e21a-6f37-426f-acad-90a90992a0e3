import { addSpacesToLongNumbers } from '@/utils/addSpacesToLongNumbers.ts';

export const changeZerosToText = (number: number, t: Function) => {
  const numberStringified = number.toString();

  if (numberStringified.endsWith('000000')) {
    return `${addSpacesToLongNumbers(numberStringified.replace('000000', ''))}${t('millions')}`;
  }

  if (number > 1000 && number < 1000000) {
    if (numberStringified.endsWith('000')) {
      return `${addSpacesToLongNumbers(numberStringified.replace('000', ''))}${t('thousands')}`;
    }
    if (numberStringified.endsWith('00')) {
      return addSpacesToLongNumbers((number / 1000).toString()) + t('thousands');
    }
  }
  if (
    number > 1000000 &&
    (numberStringified.endsWith('00000') ||
      numberStringified.endsWith('0000') ||
      numberStringified.endsWith('000'))
  ) {
    return addSpacesToLongNumbers((number / 1000000).toString()) + t('millions');
  }
  return addSpacesToLongNumbers(numberStringified);
};
