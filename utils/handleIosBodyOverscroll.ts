import { isIOSWithTouch } from '@/utils/checkIos.ts';

export const disableScroll = () => {
  if (!isIOSWithTouch()) {
    return;
  }
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
  document.documentElement.style.position = 'fixed';
  if (scrollTop) {
    document.documentElement.style.top = `-${scrollTop}px`;
  }
  document.documentElement.style.width = '100vw';
};

export const enableScroll = () => {
  if (!isIOSWithTouch()) {
    return;
  }
  const scrollTop = Math.abs(parseInt(document.documentElement.style.top || '0', 10));
  document.documentElement.style.position = '';
  document.documentElement.style.top = '';
  document.documentElement.style.width = '';
  if (scrollTop) {
    window.scrollTo(0, scrollTop);
  }
};
