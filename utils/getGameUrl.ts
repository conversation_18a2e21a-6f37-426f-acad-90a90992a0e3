import { DEFAULT_LANGUAGE } from '@/app/i18n/settings';
import { isClientReady } from '@/utils/index.ts';

interface IGameUrl {
  authToken?: string;
  currency?: string;
  gameId: number;
  lng: string;
  userId?: string | number;
}

export const getGameUrl = ({
  authToken = '',
  currency,
  gameId,
  lng = DEFAULT_LANGUAGE,
  userId,
}: IGameUrl) => {
  if (!isClientReady()) {
    return '';
  }
  const appBaseUrl = window.location.origin + '/' + lng + '/';
  const GAME_BASE_URL =
    appBaseUrl?.includes('staging') ||
    appBaseUrl?.includes('test') ||
    appBaseUrl?.includes('localhost')
      ? 'https://frontgame.igroslots.com/online/'
      : 'https://frontgambling.com/online/';

  const encodedUrl = appBaseUrl ? encodeURIComponent(appBaseUrl) : '';

  return `${GAME_BASE_URL}?currency=${currency}&operator_id=${userId === 'demo' ? 0 : process.env.NEXT_PUBLIC_EXTERNAL_API_OPERATOR_ID}&auth_token=${authToken}&user_id=${userId}&game_id=${gameId}&denomination=1&lang=${lng}&lobby=${encodedUrl}`;
};
