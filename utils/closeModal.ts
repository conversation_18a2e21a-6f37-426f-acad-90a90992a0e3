import { enableScroll } from '@/utils/handleIosBodyOverscroll.ts';

export const queryAllDialogs = () => {
  if (typeof window === 'undefined') return [];
  return Array.from(document.querySelectorAll('dialog[open]'));
};

export const closeModal = (
  id: string,
  closeShadowAlso: boolean = true,
  noCloseShadowScreenOnTop?: boolean,
): void => {
  if (typeof window === 'undefined') return;

  if (id) {
    const ref: HTMLElement | HTMLDialogElement | null = document.getElementById(id);
    const shadowScreen = document.querySelector('.shadowScreen');
    const isShadowScreenOnTop = shadowScreen?.classList.contains('onTop');
    const isBluredShadowShown =
      shadowScreen?.classList.contains('shown') && shadowScreen?.classList.contains('blured');

    (ref as HTMLDialogElement)?.classList.add('close');
    (ref as HTMLDialogElement)?.classList.remove('opened');
    shadowScreen?.classList.remove('zIndexIncreased');

    if (!isShadowScreenOnTop && closeShadowAlso) {
      if (shadowScreen?.classList.contains('modalScreen')) {
        if (!isBluredShadowShown) {
          shadowScreen?.classList.remove('shown', 'modalScreen');
        }
        enableScroll();
        const body = window.document.body;
        const html = document.documentElement;
        html?.classList.remove('blocked');
        body?.classList.remove('blocked');
      }
    } else if (closeShadowAlso) {
      const openedDialogs = Array.from(document.querySelectorAll('dialog.opened'));
      if (!openedDialogs.length) {
        enableScroll();
        shadowScreen?.classList.remove('shown', 'modalScreen');
        const body = window.document.body;
        const html = document.documentElement;
        html?.classList.remove('blocked');
        body?.classList.remove('blocked');
      }
    }

    if (isShadowScreenOnTop && !noCloseShadowScreenOnTop) {
      // if (shadowScreen?.classList.contains('modalScreen')) {
      shadowScreen?.classList.remove('onTop');
      // }
    }

    setTimeout(() => {
      if (ref as HTMLDialogElement) {
        (ref as HTMLDialogElement).close();
        (ref as HTMLDialogElement).classList.remove('close');
      }
    }, 200); // this value will match your CSS animation timing
  }
};
