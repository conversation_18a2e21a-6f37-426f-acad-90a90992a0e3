import { IBonus } from '@/app/api/server-actions/bonuses.ts';
import { sortUserBonuses } from '@/utils/sortUserBonuses.ts';

type Raw = {
  templates?: IBonus[];
  freespins?: IBonus[];
  wagers?: IBonus[];
};

export const transformUserBonuses = (raw?: Raw) => {
  const templates =
    raw?.templates?.map((bonus: IBonus) => ({
      ...bonus,
      bonus_global_type: 'template' as const,
    })) || [];

  const freespins =
    raw?.freespins?.map((bonus: IBonus) => ({
      ...bonus,
      bonus_global_type: 'freespins' as const,
    })) || [];

  const wagers =
    raw?.wagers?.map((bonus: IBonus) => ({
      ...bonus,
      bonus_global_type: !bonus.bonus_type ? ('wager_no_deposit' as const) : ('wager' as const),
    })) || [];

  return sortUserBonuses([...wagers, ...freespins, ...templates]);
};
