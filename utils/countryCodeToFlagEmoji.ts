export const countryCodeToFlagEmoji = (countryCode: string) => {
  if (countryCode.length !== 2) {
    return ''; // Country code should be 2 characters
  }
  let code = countryCode;
  if (countryCode === 'en') {
    code = 'gb';
  } else if (countryCode === 'kk') {
    code = 'kz';
  }
  return code
    .toUpperCase()
    .split('')
    .map((letter) =>
      String.fromCodePoint(0x1f1e6 + letter.charCodeAt(0) - 'A'.charCodeAt(0)),
    )
    .join('');
};
