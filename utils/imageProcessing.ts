export const calculateImageHeight = (
  width: number,
  height: number,
  type: string,
) => {
  if (!width && !height) {
    return '';
  } else if (type === 'square') {
    return width;
  } else if (type === 'vertical') {
    return Math.round((width * 7) / 6);
  } else if (type === 'horizontal') {
    return Math.round(width / 1.5);
  }
  return height || '';
};

export const getPathnameFromUrl = (src: string) => {
  try {
    return new URL(src).pathname;
  } catch (e) {
    return '';
  }
};

export const getProcessedUrl = (
  src: string,
  params: any,
  imagekitUrl: string,
) => {
  const pathname = /^https/.test(src) ? getPathnameFromUrl(src) : src;

  const paramsString = params
    ? Object.keys(params).reduce(
        (acc, curr) => `${acc}${curr}-${params[curr]},`,
        '',
      )
    : 'w-auto,h-auto';

  return `${imagekitUrl}${pathname}/tr:${paramsString}`;
};
