import { isClientReady } from '@/utils/index.ts';

type SameSite = 'Lax' | 'Strict' | 'None';

interface CookieOpts {
  maxAgeSeconds?: number;
  path?: string;
  sameSite?: SameSite;
  secure?: boolean;
}

export const setCookie = (
  key: string,
  value: string,
  {
    maxAgeSeconds = 34560000,
    path = '/',
    sameSite = 'Lax',
    secure = typeof window !== 'undefined' ? window.location.protocol === 'https:' : false,
  }: CookieOpts = {},
) => {
  try {
    const parts = [
      `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
      `Path=${path}`,
      `Max-Age=${Math.max(0, Math.floor(maxAgeSeconds))}`,
      `SameSite=${sameSite}`,
    ];
    if (secure) parts.push('Secure');
    document.cookie = parts.join('; ');
  } catch {
    // no-op
  }
};

export const getCookie = (key: string): string | null => {
  try {
    const name = encodeURIComponent(key) + '=';
    const found = document.cookie.split('; ').find((row) => row.startsWith(name));
    return found ? decodeURIComponent(found.slice(name.length)) : null;
  } catch {
    return null;
  }
};

export const deleteCookie = (
  key: string,
  {
    path = '/',
    sameSite = 'Lax',
    secure = typeof window !== 'undefined' ? window.location.protocol === 'https:' : false,
  }: Omit<CookieOpts, 'maxAgeSeconds'> = {},
) => {
  setCookie(key, '', { maxAgeSeconds: 0, path, sameSite, secure });
};

let _lsUsable: boolean | null = null;

export function isLocalStorageUsable(): boolean {
  if (_lsUsable !== null) return _lsUsable;
  try {
    if (typeof window === 'undefined') return (_lsUsable = false);
    const ls = window.localStorage; // can throw SecurityError
    const probe = `__ls_probe__${Math.random()}`;
    ls.setItem(probe, '1');
    ls.removeItem(probe);
    return (_lsUsable = true);
  } catch {
    return (_lsUsable = false);
  }
}

// BACKUP IN COOKIES
export const localStorageKeys = [
  'clickid',
  'landing',
  'subid',
  'sub1',
  'sub2',
  'sub3',
  'sub4',
  'sub5',
  'tracker',
];

export const saveToLocalStorage = (key: string, value: any) => {
  const json = JSON.stringify(value);
  const lsOk = isLocalStorageUsable();
  if (lsOk) {
    try {
      window.localStorage.setItem(key, json);
    } catch {
      if (localStorageKeys.includes(key)) {
        setCookie(key, json);
      }
    }
  }
  if (localStorageKeys.includes(key)) {
    setCookie(key, json);
  }
};

export const getFromLocalStorage = (key: string) => {
  const lsOk = isLocalStorageUsable();
  if (lsOk) {
    try {
      const raw = window.localStorage.getItem(key);
      return raw ? JSON.parse(raw) : null;
    } catch {
      if (localStorageKeys.includes(key)) {
        const cookieVal = getCookie(key);
        return cookieVal ? JSON.parse(cookieVal) : null;
      }
    }
  }
  if (localStorageKeys.includes(key)) {
    const cookieVal = getCookie(key);
    return cookieVal ? JSON.parse(cookieVal) : null;
  }
};

export const removeFromLocalStorage = (key: string) => {
  if (isLocalStorageUsable()) {
    try {
      window.localStorage.removeItem(key);
    } catch {
      deleteCookie(key);
    }
  }
  deleteCookie(key);
};

export const captureAndStoreUrlParams = () => {
  if (isClientReady()) {
    const urlParams = new URLSearchParams(window.location.search);

    const clickidUrlParam = urlParams.get('clickid');
    const subidUrlParam = urlParams.get('subid');
    const trackerUrlParam = urlParams.get('tracker');
    const clickidLS = getFromLocalStorage('clickid');
    const subidLS = getFromLocalStorage('subid');
    const trackerLS = getFromLocalStorage('tracker');

    if (
      trackerUrlParam &&
      trackerUrlParam === trackerLS &&
      clickidUrlParam &&
      clickidUrlParam === clickidLS &&
      subidUrlParam &&
      subidUrlParam === subidLS
    ) {
      return;
    }
    // fire-and-forget tracker (won’t block)
    fetch(`https://frontgambling.com/counter/${window.location.search}`, { method: 'GET' }).catch(
      () => {},
    );

    localStorageKeys.forEach((key) => {
      const value = urlParams.get(key);
      if (value && getFromLocalStorage(key) !== value) {
        saveToLocalStorage(key, value);
      }
    });

    // if tracker is present, save click_time in UTC+3
    if (urlParams.get('tracker')) {
      const UTCPlus3 = new Date(Date.now() + 3 * 60 * 60 * 1000);
      const formattedDateTime = UTCPlus3.toISOString().slice(0, 19).replace('T', ' ');
      saveToLocalStorage('click_time', formattedDateTime);
    }
  }
};

export const clearSignupLocalStorage = () => {
  if (isClientReady()) {
    const keysToClear = [
      'clickid',
      'click_time',
      'landing',
      'subid',
      'sub1',
      'sub2',
      'sub3',
      'sub4',
      'sub5',
      'tracker',
    ];
    keysToClear.forEach((key) => {
      removeFromLocalStorage(key);
    });
  }
};

// Promote cookie values back into LS when it becomes available again.
export function tryPromoteCookiesToLS(keys: string[] = localStorageKeys) {
  if (!isLocalStorageUsable()) return;
  try {
    keys.forEach((key) => {
      const cookieVal = getCookie(key);
      if (cookieVal) {
        try {
          window.localStorage.setItem(key, cookieVal);
          deleteCookie(key);
        } catch {
          // no-op
        }
      }
    });
  } catch {
    // no-op
  }
}
