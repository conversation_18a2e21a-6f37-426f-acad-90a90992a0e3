const localStorageKeys = [
  'clickid',
  'landing',
  'subid',
  'sub1',
  'sub2',
  'sub3',
  'sub4',
  'sub5',
  'tracker',
];

export const saveToLocalStorage = (key: string, value: any) => {
  if (typeof window !== 'undefined') {
    window.localStorage.setItem(key, JSON.stringify(value));
  }
};

export const getFromLocalStorage = (key: string) => {
  if (typeof window !== 'undefined') {
    const value = window.localStorage.getItem(key);
    return value ? JSON.parse(value) : null;
  }
  return null;
};

export const removeFromLocalStorage = (key: string) => {
  if (typeof window !== 'undefined') {
    window.localStorage.removeItem(key);
  }
};

export const captureAndStoreUrlParams = () => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);

    const clickidUrlParam = urlParams.get('clickid');
    const subidUrlParam = urlParams.get('subid');
    const trackerUrlParam = urlParams.get('tracker');
    const clickidLS = getFromLocalStorage('clickid');
    const subidLS = getFromLocalStorage('subid');
    const trackerLS = getFromLocalStorage('tracker');

    if (
      trackerUrlParam &&
      trackerUrlParam === trackerLS &&
      clickidUrlParam &&
      clickidUrlParam === clickidLS &&
      subidUrlParam &&
      subidUrlParam === subidLS
    ) {
      return;
    }
    // fire-and-forget tracker (won’t block)
    fetch(`https://frontgambling.com/counter/${window.location.search}`, { method: 'GET' }).catch(
      () => {},
    );

    localStorageKeys.forEach((key) => {
      const value = urlParams.get(key);
      if (value && getFromLocalStorage(key) !== value) {
        saveToLocalStorage(key, value);
      }
    });

    // if tracker is present, save click_time in UTC+3
    if (urlParams.get('tracker')) {
      const UTCPlus3 = new Date(Date.now() + 3 * 60 * 60 * 1000);
      const formattedDateTime = UTCPlus3.toISOString().slice(0, 19).replace('T', ' ');
      saveToLocalStorage('click_time', formattedDateTime);
    }
  }
};

export const clearSignupLocalStorage = () => {
  if (typeof window !== 'undefined') {
    const keysToClear = [
      'clickid',
      'click_time',
      'landing',
      'subid',
      'sub1',
      'sub2',
      'sub3',
      'sub4',
      'sub5',
      'tracker',
    ];
    keysToClear.forEach((key) => {
      removeFromLocalStorage(key);
    });
  }
};
