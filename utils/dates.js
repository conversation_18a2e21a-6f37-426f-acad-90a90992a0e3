'use client';
import { useTranslation } from '@/app/i18n/client';
import { useState } from 'react';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import relativeTime from 'dayjs/plugin/relativeTime';
import utc from 'dayjs/plugin/utc';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/ru';
import 'dayjs/locale/en';
// import 'dayjs/locale/cs';
import 'dayjs/locale/kk';
// import 'dayjs/locale/pl';
// import 'dayjs/locale/tr';
// import 'dayjs/locale/uz';
import { useInterval } from '@/hooks/useInterval.ts';

dayjs.extend(duration);
dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.extend(relativeTime);
dayjs.extend(localeData);

//TODO: check setState in useEffect for max update exceeded (bonuses)
export const useTransformDateFromNowInSeconds = (
  dateEnd,
  lng,
  isArray,
  withMonths,
) => {
  const { t } = useTranslation(lng);

  // eslint-disable-next-line no-unused-vars
  const [currentTime, setCurrentTime] = useState(dayjs().utc());
  useInterval(() => setCurrentTime(dayjs().utc()), 1000, []);

  if (!dateEnd) {
    return '';
  }

  const now = dayjs().utc();
  const endDate = dayjs.utc(dateEnd);
  const diff = endDate.diff(now);
  const dur = dayjs.duration(diff); // ms

  const seconds = String(dur.seconds()).padStart(2, '0');
  const minutes = String(dur.minutes()).padStart(2, '0');
  const hours = String(dur.hours()).padStart(2, '0');
  const days = withMonths
    ? String(dur.days()).padStart(2, '0')
    : String(Math.floor(dur.asDays()));
  const months = String(dur.months()).padStart(2, '0');

  if (isArray) {
    if (withMonths) {
      return [months, days, hours, minutes, seconds];
    } else {
      return [days, hours, minutes, seconds];
    }
  }

  return `${days} ${t('dayShort')} ${hours}:${minutes}:${seconds}`;
};

export const transformDate = (date, format, lng = 'en') => {
  if (!date) {
    return '';
  }

  const timezone = dayjs.tz.guess();

  if (lng) {
    return dayjs
      .utc(date)
      .tz(timezone)
      .locale(lng)
      .format(format || 'DD/MM/YYYY');
  }
  return dayjs
    .utc(date)
    .tz(timezone)
    .format(format || 'DD/MM/YYYY');
};

export const getStartDateTime = (date) => {
  const newDate = dayjs(date)
    .set('hour', 0)
    .set('minute', 0)
    .set('second', 0)
    .millisecond(0).$d;
  return transformDate(newDate, 'YYYY-MM-DD HH:mm:ss');
};

export const getEndDateTime = (date) => {
  const newDate = dayjs(date)
    .set('hour', 23)
    .set('minute', 59)
    .set('second', 59)
    .millisecond(0).$d;
  return transformDate(newDate, 'YYYY-MM-DD HH:mm:ss');
};

export const useTransformDateFromNow = (date, lng) => {
  const [currentTime, setCurrentTime] = useState(dayjs.utc());
  useInterval(() => setCurrentTime(dayjs.utc()), 60000, []);

  const messageDate = dayjs(date);
  const diffHours = currentTime.diff(messageDate, 'hours');
  const diffWeek = currentTime.diff(messageDate, 'week');

  const timezone = dayjs.tz.guess();

  if (diffHours > 24) {
    return dayjs.utc(date).tz(timezone).locale(lng).format('DD MMM, HH:mm');
  }
  if (diffWeek === 0 && diffHours >= 24) {
    return dayjs.utc(date).tz(timezone).locale(lng).format('dddd');
  }
  if (diffHours < 24) {
    return dayjs.utc(date).tz(timezone).locale(lng).fromNow();
  }
};
