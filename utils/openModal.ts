import { disableScroll } from '@/utils/handleIosBodyOverscroll.ts';

export const openModal = (
  id: string,
  withShadowScreen: boolean = true,
  isBlockingModal: boolean = false,
  isShadowScreenOnTop?: boolean,
  zIndexIncreasedForRef?: string,
  callback?: Function,
): void => {
  if (id) {
    const ref: HTMLElement | HTMLDialogElement | null = document.getElementById(id);

    if (!ref) return;

    const shadowScreen = document.querySelector('.shadowScreen') as HTMLElement;

    setTimeout(() => {
      const modalRef = document.getElementById(id);
      if (modalRef && !modalRef.classList.contains('opened')) {
        if (withShadowScreen) {
          shadowScreen?.classList.add('shown', 'modalScreen');
          const body = window.document.body;
          const html = document.documentElement;
          body?.classList.add('blocked');
          html?.classList.add('blocked');
          disableScroll();
        }

        ref?.classList.add('opened');
        if (id !== 'popupContainer' && isBlockingModal) {
          (ref as HTMLDialogElement)?.showModal();
        } else {
          (ref as HTMLDialogElement)?.show();
        }
      }

      const openedDialogs = Array.from(document.querySelectorAll('dialog.opened'));
      const dialogsFiltered = openedDialogs?.filter((dialog) => dialog.id !== id);

      if (isShadowScreenOnTop || (isShadowScreenOnTop !== false && dialogsFiltered?.length > 0)) {
        shadowScreen?.classList.add('onTop');
        if (zIndexIncreasedForRef) {
          ref!.style.zIndex = zIndexIncreasedForRef;
        } else {
          ref!.style.zIndex = '211';
        }
        if (zIndexIncreasedForRef) {
          shadowScreen?.classList.add('zIndexIncreased');
        }
      }

      if (callback) {
        callback();
      }
    }, 100);
  }
};
