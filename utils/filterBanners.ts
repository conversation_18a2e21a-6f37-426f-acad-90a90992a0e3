import { IBanner } from '@/app/api/getBannersServer.ts';

const filterBanners = (banners: IBanner[], isAuthorized?: boolean) => {
  const bannersFiltered = banners.filter((banner) => {
    if (banner.target_type === 'sign_up_bonus') return false;

    const isBannerVisible = isAuthorized
      ? ['signed_in', 'all'].includes(banner.visibility)
      : ['guest', 'all'].includes(banner.visibility);
    return isBannerVisible;
  });

  return {
    items: bannersFiltered,
    total: bannersFiltered.length,
  };
};

export { filterBanners };
