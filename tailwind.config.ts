/** @type {import('tailwindcss').Config} */

module.exports = {
  content: [
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    screens: {
      xxs: '320px',
      xs: '414px',
      sm: '620px',
      md2: '748px',
      md: '960px',
      lg: '1050px',
      xl: '1440px',
      xxl: '1680px',
    },
    extend: {
      colors: {
        'custom-light': 'rgba(255, 255, 165, 0.5)',
        'custom-dark': 'rgba(102, 163, 255, 0.5)',
        'general-lightest': '#F8FAFC',
        primary: {
          darker: '#003C80',
          dark: '#0059B7',
          main: '#0083FF',
          light: '#60A5FA',
        },
        secondary: {
          main: '#FB923C',
          dark: '#FF8800',
        },
        general: {
          black: '#00031A',
          white: '#ffffff',
          lightest: '#F8FAFC',
          lighter: '#CBD5E1',
          light: '#94A3B8',
          dark: '#334155',
          darker: '#1E293B',
          darkest: '#0F172A',
        },
        status: {
          success: '#489F37',
          warning: '#B3870E',
          error: '#EB5645',
          new: '#3392EA',
          neutral: '#8B8D8F',
        },
      },
      // backgroundImage: (theme) => ({}),
    },
  },

  safelist: [
    // necessary classes to disable auth forms when in progress
    'pointer-events-none',
    'cursor-not-allowed',
  ],
  plugins: [],
};
