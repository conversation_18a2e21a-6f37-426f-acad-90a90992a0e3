import PropTypes from 'prop-types';

import Typography from '../Typography';
import { StyledListItem } from './styled';

const ListItem = (props) => {
  const { children, text, className, ...otherProps } = props;
  return (
    <StyledListItem className={className} {...otherProps}>
      {children || <Typography type='label2'>{text}</Typography>}
    </StyledListItem>
  );
};

export default ListItem;

ListItem.propsTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  text: PropTypes.string,
};
