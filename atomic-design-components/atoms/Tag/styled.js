'use client';
import styled from 'styled-components';
import { getTokens } from '../../';

export const StyledTag = styled.span`
  ${({ type, size, theme }) =>
    getTokens(`tag-standard-${type}-${size}`, theme)};
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  font-size: ${({ fontSize }) => fontSize};
  font-weight: ${({ fontWeight }) => fontWeight};
  background-color: ${({ backgroundColor }) => backgroundColor};
  color: ${({ color }) => color};

  .crossIcon {
    cursor: pointer;
    margin-left: 8px;

    div {
      background-color: transparent;
      margin-top: 0;
    }

    span {
      height: 1px;
      margin-bottom: -1px;
    }
  }
`;
