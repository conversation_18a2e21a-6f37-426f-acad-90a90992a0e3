import clsx from 'clsx';

import { StyledTag } from './styled';
import { CloseIcon } from '@/atomic-design-components';

const Tag = ({
  text,
  type = 'gray',
  withCrossIcon,
  size = 'large',
  ...rest
}) => {
  const {
    backgroundColor,
    className,
    children,
    color,
    fontSize,
    fontWeight,
    iconRight,
    onCrossClick,
    removeProps = {},
  } = rest;
  return (
    <StyledTag
      className={clsx(className, 'tag', type, size)}
      backgroundColor={backgroundColor}
      fontSize={fontSize}
      fontWeight={fontWeight}
      color={color}
      type={type}
      size={size}
      {...rest}
    >
      {children || text}
      {iconRight}
      {withCrossIcon && <CloseIcon onClick={onCrossClick} {...removeProps} />}
    </StyledTag>
  );
};

export default Tag;

// Tag.propsTypes = {
//   backgroundColor: T.string,
//   color: T.string,
//   type: T.oneOf(['primary', 'gray', 'lightGray']),
//   withCrossIcon: T.bool,
//   size: T.oneOf(['small', 'medium', 'large']),
// };
