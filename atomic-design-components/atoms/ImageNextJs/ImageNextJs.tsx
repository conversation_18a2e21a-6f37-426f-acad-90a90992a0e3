'use client';
import Image from 'next/image';

const imageLoader = ({ src }: { src: string; width?: number; quality?: number }) => {
  // console.log(width, quality);
  return `${process.env.NEXT_PUBLIC_BUNNY_CDN_URL}/${src}?format=auto`;
};

const ImageNextJs = (props: any) => {
  if (!props.src) {
    return null;
  }
  // const unoptimized = process.env.VERCEL_ENV !== 'production' || props.unoptimized;
  const src = props.src.replace(`https://${process.env.NEXT_PUBLIC_IMAGES_BUCKET_DOMAIN}/`, '');

  return (
    <Image
      {...props}
      alt={props.alt || 'Image'}
      src={!props.unoptimized && props.src.startsWith('https://') ? src : props.src}
      unoptimized={props.unoptimized}
      loader={!props.unoptimized && props.src.startsWith('https://') ? imageLoader : undefined}
    />
  );
};

export default ImageNextJs;
