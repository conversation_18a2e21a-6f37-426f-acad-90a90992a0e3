'use client';
import styled from 'styled-components';
import { getTokens } from '../../';

export const StyledBadgeWrapper = styled.div<{ margin: string }>`
  position: relative;
`;

export const StyledBadge = styled.div<{
  backgroundColor: string;
  border: number;
  borderColor: string;
  width: number;
  height: number;
}>`
  position: absolute;
  box-sizing: border-box;
  ${({ theme }) => getTokens('badge-standard-primary-large', theme)};
  background-color: ${({ backgroundColor }) => backgroundColor};
  color: ${({ color, theme }) => color || theme.color?.general.lightest};
  width: ${({ width }) => width}px;
  height: ${({ height }) => height}px;
  border: ${({ border }) => border}px solid ${({ borderColor }) => borderColor};
  line-height: 16px;
  text-align: center;
  overflow: hidden;
  &.invisible {
    transition: transform 195ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  }

  &.anchorTopRight {
    top: 0;
    right: 0;
    // transform: translate(50%, -25%);
    //transform-origin: 100% 0%;
    &.invisible {
      transform: scale(0) translate(50%, -50%);
    }
  }
  &.anchorTopLeft {
    top: 0;
    left: 0;
    transform: scale(1) translate(0%, -50%);
    transform-origin: 0% 0%;
    &.invisible {
      transform: scale(0) translate(-50%, -50%);
    }
  }
  &.anchorBottomRight {
    bottom: 0;
    right: 0;
    transform: scale(1) translate(50%, 50%);
    transform-origin: 100% 100%;
    &.invisible {
      transform: scale(0) translate(50%, 50%);
    }
  }
  &.anchorBottomLeft {
    bottom: 0;
    left: 0;
    transform: scale(1) translate(-50%, 50%);
    transform-origin: 0% 100%;
    &.invisible {
      transform: scale(0) translate(-50%, 50%);
    }
  }
`;
