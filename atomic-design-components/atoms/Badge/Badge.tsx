import clsx from 'clsx';

import capitalize from '../../../utils/capitalize';
import { StyledBadge, StyledBadgeWrapper } from './styled';
import { theme } from '@/theme';

interface BadgeProps {
  anchorOrigin?: {
    vertical: 'top' | 'bottom';
    horizontal: 'left' | 'right';
  };
  backgroundColor?: string;
  badgeContent: any;
  border?: number;
  borderColor?: string;
  children?: any;
  className?: string;
  color?: string;
  fontSize?: number;
  invisible?: boolean;
  margin?: string;
  width?: number;
  height?: number;
  onClick?: () => void;
}

const Badge = ({
  anchorOrigin = {
    vertical: 'top',
    horizontal: 'right',
  },
  backgroundColor = '',
  badgeContent,
  border = 1,
  borderColor = backgroundColor || '',
  children,
  className,
  color,
  fontSize = 10,
  invisible,
  margin = '1px',
  width = 16,
  height = 16,
  onClick,
  ...otherProps
}: BadgeProps) => {
  const { vertical, horizontal } = anchorOrigin;

  if (!badgeContent) {
    return children;
  }

  return (
    <StyledBadgeWrapper
      className={className}
      margin={margin}
      onClick={onClick}
      style={onClick ? { cursor: 'pointer' } : undefined}
      {...otherProps}
    >
      {children}
      <StyledBadge
        className={clsx(
          `anchor${capitalize(vertical)}${capitalize(horizontal)}`,
          invisible && 'invisible',
        )}
        backgroundColor={backgroundColor}
        border={border}
        borderColor={borderColor}
        color={color}
        width={width}
        height={height}
      >
        <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 13'>
          <text
            x='50%'
            y='56%'
            dominantBaseline='middle'
            textAnchor='middle'
            fontSize={fontSize}
            fill={theme.color?.general.lightest}
          >
            {badgeContent}
          </text>
        </svg>
      </StyledBadge>
    </StyledBadgeWrapper>
  );
};

export default Badge;
