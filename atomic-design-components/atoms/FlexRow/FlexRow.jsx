'use client';
import { PropTypes as T } from 'prop-types';
import styled from 'styled-components';
import {
  ALIGN_JUSTIFY_CONTENT_TYPES,
  ALIGN_JUSTIFY_ITEMS_TYPES,
} from '@/types/types.ts';

const StyledRow = styled.div`
  display: flex;
  align-items: ${({ alignItems }) => alignItems};
  justify-content: ${({ justifyContent }) => justifyContent};
  flex-direction: ${({ flexDirection }) => flexDirection};
  padding: ${({ padding }) => padding};
  flex-wrap: ${({ flexWrap }) => flexWrap};
  gap: ${({ gap }) => gap};
  margin: ${({ margin }) => margin};
  width: ${({ fullWidth }) => (fullWidth ? '100%' : '')};
`;

const FlexRow = ({
  alignItems = 'center',
  children,
  className,
  style,
  id,
  justifyContent = 'start',
  flexDirection = 'row',
  padding,
  flexWrap,
  gap,
  margin,
  onClick,
  fullWidth,
}) => {
  return (
    <StyledRow
      className={className}
      id={id}
      justifyContent={justifyContent}
      alignItems={alignItems}
      padding={padding}
      flexWrap={flexWrap}
      flexDirection={flexDirection}
      gap={gap}
      style={style}
      margin={margin}
      onClick={onClick}
      fullWidth={fullWidth}
    >
      {children}
    </StyledRow>
  );
};

export default FlexRow;

FlexRow.propTypes = {
  alignItems: T.oneOf(ALIGN_JUSTIFY_ITEMS_TYPES),
  children: T.node,
  justifyContent: T.oneOf(ALIGN_JUSTIFY_CONTENT_TYPES),
  flexWrap: T.oneOf(['wrap', 'nowrap']),
  padding: T.string,
  gap: T.string,
  flexDirection: T.oneOf(['column', 'row']),
  fullWidth: T.bool,
};
