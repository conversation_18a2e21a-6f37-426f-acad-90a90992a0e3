'use client';
import styled from 'styled-components';
import { getTokens } from '../../';

export const StyledLabel = styled.div`
  display: inline-flex;
  ${({ theme, variant }) =>
    getTokens(`typography-${variant}-black-large`, theme)};
  color: ${({ color }) => color};
  font-weight: ${({ fontWeight }) => fontWeight};
  text-transform: ${({ $textTransform }) => $textTransform};

  @media only screen and (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
    ${({ theme, variant }) =>
      getTokens(`typography-${variant}-black-large`, theme)};
    color: ${({ color }) => color};
    font-weight: ${({ fontWeight }) => fontWeight};
    text-transform: ${({ $textTransform }) => $textTransform};
  }
`;
