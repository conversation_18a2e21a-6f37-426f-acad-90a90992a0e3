import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';

import { StyledLabel } from './styled';

const Label = ({
  as,
  children,
  className,
  color,
  fontWeight,
  onClick,
  text,
  textTransform,
  icon,
  variant = 'label1',
}) => {
  return (
    <StyledLabel
      as={as}
      color={color}
      fontWeight={fontWeight}
      $textTransform={textTransform}
      onClick={onClick}
      className={clsx(className, 'label')}
      variant={variant}
    >
      {icon}
      {children || text}
    </StyledLabel>
  );
};

export default Label;

Label.propTypes = {
  fontWeight: T.number,
  textTransform: T.string,
  variant: T.oneOf(['label1', 'label2']),
};
