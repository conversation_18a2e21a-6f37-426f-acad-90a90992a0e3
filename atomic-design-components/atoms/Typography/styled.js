'use client';
import styled from 'styled-components';

import { getTokens } from '../../';

export const StyledTypography = styled.span`
  display: ${({ $displayCssProp }) => $displayCssProp};
  justify-content: ${({ $justifyContent }) => $justifyContent};
  align-items: ${({ $alignItems }) => $alignItems};
  ${({ type, theme }) => getTokens(`typography-${type}-black-small`, theme)};
  color: ${({ color }) => color};
  background-color: ${({ $backgroundColor }) => $backgroundColor};
  text-align: ${({ $textAlign }) => $textAlign};
  text-decoration: ${({ $textDecoration }) => $textDecoration};
  text-transform: ${({ $textTransform }) => $textTransform};
  font-weight: ${({ fontWeight }) => fontWeight};
  font-family: ${({ fontFamily }) => fontFamily};
  font-size: ${({ fontSize }) => fontSize};
  line-height: ${({ $lineHeight }) => $lineHeight};
  //margin-block-start: 0;
  //margin-block-end: 0;
  margin: ${({ margin }) => margin};
  padding: ${({ padding }) => padding};
  .icon {
    display: inline-block;
  }
  @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    ${({ type, theme }) => getTokens(`typography-${type}-black-medium`, theme)};
    color: ${({ color }) => color};
    font-weight: ${({ fontWeight }) => fontWeight};
    font-family: ${({ fontFamily }) => fontFamily};
    font-size: ${({ fontSize }) => fontSize};
    line-height: ${({ $lineHeight }) => $lineHeight};
  }

  @media only screen and (min-width: ${({ theme }) => theme.breakpoints?.lg}px) {
    ${({ type, theme }) => getTokens(`typography-${type}-black-large`, theme)};
    color: ${({ color }) => color};
    font-weight: ${({ fontWeight }) => fontWeight};
    font-family: ${({ fontFamily }) => fontFamily};
    font-size: ${({ fontSize }) => fontSize};
    line-height: ${({ $lineHeight }) => $lineHeight};
  }

  &.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
`;
