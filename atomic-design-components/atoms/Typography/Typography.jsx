import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';

import { TEXT_ALIGN_TYPES } from '../../../types/types.ts';
import Icon from '../Icon';
import { StyledTypography } from './styled';

export const defaultTypeMapping = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  sub1: 'span',
  sub2: 'span',
  sub3: 'span',
  body1: 'span',
  body2: 'span',
  label1: 'span',
  label2: 'span',
};

const Typography = (props) => {
  const {
    alignItems = 'center',
    backgroundColor,
    children,
    className,
    color,
    component,
    displayCssProp = 'flex',
    fontFamily,
    fontSize,
    fontWeight,
    iconName,
    iconProps,
    justifyContent,
    lineHeight,
    margin,
    padding,
    textAlign,
    textDecoration,
    textTransform,
    text,
    theme,
    type = 'body1',
    withEllipsis,
    ...otherProps
  } = props;
  const Component = component || defaultTypeMapping[type] || 'span';

  return (
    <StyledTypography
      as={Component}
      type={type}
      $alignItems={alignItems}
      $textAlign={textAlign}
      $textDecoration={textDecoration}
      $textTransform={textTransform}
      color={color}
      $backgroundColor={backgroundColor}
      margin={margin}
      padding={padding}
      theme={theme}
      className={clsx(
        className,
        type,
        'typography',
        withEllipsis && 'ellipsis',
      )}
      fontWeight={fontWeight}
      fontFamily={fontFamily}
      fontSize={fontSize}
      $lineHeight={lineHeight}
      $justifyContent={justifyContent}
      $displayCssProp={displayCssProp}
      {...otherProps}
    >
      {iconName && <Icon name={iconName} margin='0 8px 0 0' {...iconProps} />}
      {children || text}
    </StyledTypography>
  );
};

export default Typography;

Typography.propTypes = {
  className: T.string,
  color: T.string,
  component: T.oneOfType([T.string, T.elementType]),
  displayCssProp: T.string,
  fontFamily: T.string,
  fontSize: T.string,
  fontWeight: T.oneOfType([T.number, T.string]),
  lineHeight: T.string,
  margin: T.oneOfType([T.number, T.string]),
  padding: T.oneOfType([T.number, T.string]),
  textAlign: T.oneOf(TEXT_ALIGN_TYPES),
  textDecoration: T.string,
  textTransform: T.string,
  text: T.oneOfType([T.node, T.string, T.number]),
  type: T.oneOf([
    'h1',
    'h2',
    'h3',
    'sub1',
    'sub2',
    'sub3',
    'body1',
    'body2',
    'label1',
    'label2',
  ]),
};
