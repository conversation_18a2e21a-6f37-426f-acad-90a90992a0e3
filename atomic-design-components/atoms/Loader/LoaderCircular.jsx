import clsx from 'clsx';
import { StyledLoaderCircular, StyledOverlay } from './styled';

const LoaderCircular = ({ left, size, top, withOverlay, className, active = true }) => {
  const Loader = (
    <StyledLoaderCircular
      active={active}
      top={top}
      left={left}
      withOverlay={withOverlay}
      size={size}
      className={clsx(className, 'loader')}
    >
      <div className='loaderBg' />
      <div className='spinnerHolder animate1'>
        <div className='loaderSpinner1' />
      </div>
      <div className='spinnerHolder animate2'>
        <div className='loaderSpinner2' />
      </div>
    </StyledLoaderCircular>
  );

  return withOverlay ? <StyledOverlay active={active}>{Loader}</StyledOverlay> : Loader;
};

export default LoaderCircular;
