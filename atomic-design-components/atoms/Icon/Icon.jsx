import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';
import { withTheme } from 'styled-components';

import { Image } from '@/atomic-design-components';
import Icons from '../../icons.ts';
import { IconWrapper } from './styled';
import { theme as themeDefault } from '@/theme';

const Icon = ({
  backgroundImage,
  backgroundSize,
  borderRadius,
  className,
  dataTooltip,
  disabled,
  disableImagekit,
  fill,
  height,
  imagekitParams,
  margin,
  name,
  onClick,
  style,
  title,
  theme = themeDefault,
  width,
  wrapperColor,
  wrapperComponent,
  wrapperHeight,
  wrapperWidth,
  strokeWidth,
  ...otherProps
}) => {
  const iconName = typeof name === 'string' ? name : name?.id || name?.slug;
  const IconComponent = Icons[iconName];

  if (!backgroundImage && !IconComponent) return null;

  return (
    <IconWrapper
      data-tooltip={dataTooltip}
      title={title}
      width={wrapperWidth}
      height={wrapperHeight}
      margin={margin}
      className={clsx(className, 'icon', disabled && 'disabled', backgroundImage && 'withBgImage')}
      onClick={disabled ? undefined : onClick}
      $wrapperColor={wrapperColor}
      borderRadius={borderRadius}
      style={style}
      as={wrapperComponent}
    >
      {backgroundImage ? (
        <Image
          width={width}
          height={height}
          asBackground
          src={backgroundImage}
          radius={borderRadius}
          backgroundSize={backgroundSize}
          imagekitParams={imagekitParams}
          disableImagekit={disableImagekit}
        />
      ) : (
        <IconComponent
          width={width}
          height={height}
          fill={fill}
          $wrapperColor={wrapperColor}
          theme={theme}
          strokeWidth={strokeWidth}
          {...otherProps}
        />
      )}
    </IconWrapper>
  );
};

export default withTheme(Icon);

Icon.propTypes = {
  borderRadius: T.string,
  className: T.string,
  fill: T.string,
  height: T.number,
  margin: T.string,
  name: T.oneOfType([T.object, T.string]).isRequired,
  onClick: T.func,
  title: T.string,
  width: T.number,
  wrapperColor: T.string,
  wrapperHeight: T.oneOfType([T.number, T.string]),
  wrapperWidth: T.oneOfType([T.number, T.string]),
};
