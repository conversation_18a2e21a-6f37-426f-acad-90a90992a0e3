import React from 'react'

export const safe = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      secondary: { main },
    },
  },
}) => (
  <svg width={width || 73} height={height || 73} viewBox="0 0 73 73" fill="none">
    <circle cx="36.5" cy="36.5" r="36.5" fill={fill || '#292D3A'} />
    <circle cx="36.5" cy="36.5" r="32" fill={fill || '#292D3A'} stroke={stroke || '#0083FF'} />
    <path
      d="M44.1643 36.1257C44.1643 36.1212 44.1613 36.1167 44.1613 36.1122C44.1583 33.9163 43.2688 31.926 41.8349 30.4754C41.8244 30.4634 41.8214 30.4484 41.8109 30.4379C41.8004 30.4274 41.7854 30.4229 41.7734 30.4124C40.3244 28.9784 38.3355 28.0905 36.1411 28.086C36.1351 28.086 36.1306 28.083 36.1246 28.083C36.1186 28.083 36.1126 28.086 36.1066 28.086C33.9137 28.092 31.9246 28.9799 30.4759 30.4139C30.4654 30.4229 30.4534 30.4259 30.4444 30.4349C30.4354 30.4439 30.4324 30.4559 30.4234 30.4664C28.9819 31.9198 28.0895 33.9177 28.0895 36.1213V36.1258C28.091 38.3322 28.9849 40.3315 30.4309 41.7849C30.4369 41.7909 30.4384 41.7984 30.4429 41.8044C30.4489 41.8104 30.4564 41.8119 30.4624 41.8164C31.9174 43.2623 33.9183 44.1578 36.1262 44.1578C38.3341 44.1578 40.3319 43.2639 41.7853 41.8209C41.7943 41.8134 41.8033 41.8119 41.8123 41.8029C41.8198 41.7939 41.8228 41.7834 41.8303 41.7759C43.2672 40.3255 44.1582 38.3336 44.1627 36.136C44.1627 36.1315 44.1657 36.127 44.1657 36.1225L44.1643 36.1257ZM41.3624 40.5116L39.6164 38.7701C40.0724 38.1687 40.3619 37.4697 40.4639 36.7257H42.9313C42.8053 38.1597 42.2383 39.4676 41.3624 40.5116ZM29.318 36.7257H31.7809C31.8829 37.4697 32.1724 38.1702 32.6283 38.7716L30.8884 40.5115C30.0125 39.4676 29.444 38.1597 29.318 36.7257ZM30.8884 31.7355L32.6283 33.4784C32.1723 34.0799 31.8828 34.7804 31.7808 35.5243H29.3179C29.4439 34.0889 30.011 32.7795 30.8884 31.7355ZM36.1232 39.313C35.2698 39.313 34.4688 38.9815 33.8703 38.383C33.2674 37.7771 32.9359 36.9746 32.9359 36.1271C32.9359 35.2992 33.2524 34.5147 33.8283 33.9133C33.8433 33.9013 33.8583 33.8878 33.8733 33.8728C34.4733 33.2713 35.2727 32.9398 36.1232 32.9398C36.9737 32.9398 37.7777 33.2713 38.3761 33.8698C38.9791 34.4757 39.3106 35.2782 39.3106 36.1257C39.3106 36.9746 38.9791 37.7696 38.3761 38.3771C38.3626 38.3906 38.3491 38.4056 38.3371 38.4206C37.7402 38.9965 36.9572 39.313 36.1232 39.313ZM38.7706 32.6294C38.1706 32.1734 37.4717 31.8839 36.7232 31.7835V29.3176C38.1586 29.4436 39.4681 30.0105 40.512 30.888L38.7691 32.6309L38.7706 32.6294ZM35.5233 31.7835C34.7778 31.8855 34.0773 32.1749 33.4789 32.6309L31.739 30.888C32.7829 30.012 34.0909 29.445 35.5248 29.3191V31.785L35.5233 31.7835ZM33.4759 39.6205C34.0758 40.0765 34.7748 40.366 35.5233 40.4665V42.9294C34.0893 42.8034 32.7814 42.2349 31.7374 41.359L33.4773 39.6191L33.4759 39.6205ZM36.7232 40.4665C37.4702 40.3645 38.1706 40.075 38.7706 39.6205L40.5135 41.3605C39.4696 42.2379 38.1586 42.8064 36.7232 42.9309V40.4665ZM40.464 35.5258C40.362 34.7818 40.0726 34.0814 39.6166 33.4799L41.3595 31.737C42.237 32.7809 42.8055 34.0904 42.9299 35.5258H40.4625H40.464Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M50.1098 26.9819H49.2504V23.5981C49.2504 23.2666 48.9819 22.9981 48.6504 22.9981H23.5955C23.264 22.9981 22.9956 23.2666 22.9956 23.5981V48.653C22.9956 48.9845 23.264 49.2529 23.5955 49.2529H48.6504C48.9819 49.2529 49.2504 48.9845 49.2504 48.653V45.2631H50.1098C50.4413 45.2631 50.7098 44.9946 50.7098 44.6632V39.9114C50.7098 39.5799 50.4413 39.3114 50.1098 39.3114H49.2504V32.9365H50.1098C50.4413 32.9365 50.7098 32.668 50.7098 32.3365V27.5802C50.7098 27.2487 50.4413 26.9802 50.1098 26.9802V26.9819ZM49.5099 44.063H47.9079V40.5112H49.5099V44.063ZM48.0504 39.3128H47.308C46.9765 39.3128 46.708 39.5813 46.708 39.9128V44.6645C46.708 44.996 46.9765 45.2645 47.308 45.2645H48.0504V48.0544L24.1955 48.0529V24.198H48.0504V26.9818H47.308C46.9765 26.9818 46.708 27.2503 46.708 27.5818V32.3382C46.708 32.6697 46.9765 32.9382 47.308 32.9382H48.0504V39.3128ZM49.5099 31.738H47.9079V28.1816H49.5099V31.738Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M51.1896 18.125H21.0589C19.4419 18.125 18.125 19.4404 18.125 21.0589V51.1911C18.125 52.8081 19.4404 54.125 21.0589 54.125H51.1911C52.8081 54.125 54.125 52.8096 54.125 51.1911L54.1235 21.0589C54.1235 19.4419 52.808 18.125 51.1896 18.125ZM52.9235 51.1894C52.9235 52.1448 52.1466 52.9233 51.1896 52.9233H21.0589C20.1034 52.9233 19.3249 52.1463 19.3249 51.1894V21.0586C19.3249 20.1032 20.1019 19.3247 21.0589 19.3247H51.1911C52.1466 19.3247 52.9251 20.1017 52.9251 21.0586V51.1909L52.9235 51.1894Z"
      fill={stroke || '#0083FF'}
    />
  </svg>
)
