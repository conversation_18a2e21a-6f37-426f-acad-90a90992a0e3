export const stars = ({
  fill,
  width,
  height,
  theme: {
    color: {
      secondary: { dark },
    },
  },
}) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={width || 21}
    height={height || 25}
    viewBox='0 0 21 25'
    fill='none'
  >
    <path
      d='M14.7677 11.4329C13.4728 9.87957 12.8252 9.10291 12.0809 9.22254C11.3367 9.34216 10.9885 10.2789 10.2921 12.1522L10.1119 12.6368C9.91396 13.1692 9.815 13.4354 9.62246 13.6314C9.43004 13.8276 9.16571 13.9313 8.63703 14.1389L8.15587 14.3279C6.29562 15.0583 5.36553 15.4234 5.25946 16.1699C5.15338 16.9163 5.94158 17.5497 7.51796 18.8163L7.92579 19.1441C8.37374 19.5041 8.59779 19.6841 8.72733 19.9304C8.85701 20.1767 8.87761 20.4616 8.91896 21.0312L8.95649 21.5499C9.10179 23.5546 9.1745 24.557 9.85319 24.8987C10.532 25.2404 11.3673 24.6951 13.038 23.6046L13.4702 23.3225C13.9449 23.0126 14.1823 22.8576 14.4549 22.8138C14.7277 22.77 15.0048 22.8422 15.5588 22.9868L16.0633 23.1184C18.0133 23.627 18.9883 23.8813 19.5138 23.346C20.0394 22.8108 19.7676 21.8404 19.2237 19.8997L19.083 19.3976C18.9283 18.8462 18.8512 18.5704 18.89 18.297C18.9289 18.0235 19.0794 17.7834 19.3806 17.303L19.6548 16.8658C20.7147 15.1753 21.2446 14.3301 20.8907 13.6576C20.5367 12.9851 19.5334 12.9307 17.5266 12.8218L17.0074 12.7935C16.4371 12.7626 16.152 12.7472 15.9033 12.622C15.6547 12.4968 15.4707 12.2762 15.1027 11.8347L14.7677 11.4329Z'
      fill={fill || dark}
    />
    <path
      d='M14.2772 4.16741L14.5174 4.85401C14.7814 5.60817 14.9133 5.98526 15.17 6.26304C15.4267 6.54082 15.7791 6.68783 16.4838 6.98185L17.1255 7.24957C19.6059 8.28428 20.8459 8.80164 20.9874 9.8591C21.0462 10.2993 20.8985 10.7117 20.5544 11.1726C20.4381 11.1411 20.3262 11.1154 20.2209 11.0938C19.5303 10.9532 18.6519 10.9057 17.7567 10.8574L17.1139 10.8225C16.9903 10.8158 16.8942 10.8105 16.8119 10.8051C16.7573 10.7412 16.6945 10.6658 16.6135 10.5687L16.1993 10.0718C15.6212 9.37814 15.0558 8.69947 14.5299 8.22833C13.9691 7.72588 13.0315 7.07055 11.7694 7.27338C10.4941 7.47837 9.81369 8.40871 9.44671 9.07275C9.10664 9.68797 8.80083 10.5114 8.49108 11.345L8.2673 11.9474C8.22937 12.0491 8.19905 12.1307 8.17241 12.2007C8.10232 12.2288 8.02094 12.2609 7.91923 12.3008L7.32145 12.5355C6.49342 12.8604 5.67645 13.1809 5.06815 13.5318C4.41355 13.9093 3.49307 14.6068 3.31055 15.8912C3.12976 17.1633 3.8056 18.092 4.31719 18.6428C4.69847 19.0533 5.21075 19.4862 5.75772 19.93C3.69897 20.4899 2.60389 20.6988 1.9815 20.0253C1.28076 19.267 1.64333 17.8924 2.36847 15.143L2.55608 14.4318C2.76214 13.6505 2.86517 13.2599 2.81337 12.8725C2.76155 12.4851 2.56076 12.1449 2.15919 11.4645L1.79359 10.8449C0.380425 8.45013 -0.326146 7.25278 0.145763 6.30005C0.61766 5.34733 1.95552 5.2702 4.63124 5.11595L5.32348 5.07604C6.08384 5.03221 6.46402 5.01029 6.79554 4.83296C7.12705 4.65563 7.37237 4.34297 7.86302 3.71766L8.30969 3.14834C10.0363 0.947818 10.8997 -0.152458 11.892 0.0170155C12.8844 0.186489 13.3488 1.51346 14.2772 4.16741Z'
      fill={fill || dark}
    />
  </svg>
);
