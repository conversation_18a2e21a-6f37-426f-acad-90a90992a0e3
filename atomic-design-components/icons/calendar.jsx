export const calendar = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      d='M15.6944 5.7812V4.57831C15.6944 4.26217 15.43 4 15.1111 4C14.7922 4 14.5278 4.26217 14.5278 4.57831V5.73494H9.47222V4.57831C9.47222 4.26217 9.20778 4 8.88889 4C8.57 4 8.30556 4.26217 8.30556 4.57831V5.7812C6.20556 5.97398 5.18667 7.21542 5.03111 9.05831C5.01556 9.28193 5.20222 9.46699 5.42 9.46699H18.58C18.8056 9.46699 18.9922 9.27422 18.9689 9.05831C18.8133 7.21542 17.7944 5.97398 15.6944 5.7812Z'
      fill={fill || lightest}
    />
    <path
      d='M18.2222 10.6236H5.77778C5.35 10.6236 5 10.9706 5 11.3947V16.1446C5 18.4578 6.16667 20 8.88889 20H15.1111C17.8333 20 19 18.4578 19 16.1446V11.3947C19 10.9706 18.65 10.6236 18.2222 10.6236ZM9.83 17.0776C9.79111 17.1084 9.75222 17.147 9.71333 17.1701C9.66667 17.201 9.62 17.2241 9.57333 17.2395C9.52667 17.2627 9.48 17.2781 9.43333 17.2858C9.37889 17.2935 9.33222 17.3012 9.27778 17.3012C9.17667 17.3012 9.07556 17.2781 8.98222 17.2395C8.88111 17.201 8.80333 17.147 8.72556 17.0776C8.58556 16.9311 8.5 16.7306 8.5 16.5301C8.5 16.3296 8.58556 16.1292 8.72556 15.9827C8.80333 15.9133 8.88111 15.8593 8.98222 15.8207C9.12222 15.759 9.27778 15.7436 9.43333 15.7745C9.48 15.7822 9.52667 15.7976 9.57333 15.8207C9.62 15.8361 9.66667 15.8593 9.71333 15.8901C9.75222 15.921 9.79111 15.9518 9.83 15.9827C9.97 16.1292 10.0556 16.3296 10.0556 16.5301C10.0556 16.7306 9.97 16.9311 9.83 17.0776ZM9.83 14.3788C9.68222 14.5176 9.48 14.6024 9.27778 14.6024C9.07556 14.6024 8.87333 14.5176 8.72556 14.3788C8.58556 14.2323 8.5 14.0318 8.5 13.8313C8.5 13.6308 8.58556 13.4304 8.72556 13.2839C8.94333 13.068 9.28556 12.9986 9.57333 13.1219C9.67444 13.1605 9.76 13.2145 9.83 13.2839C9.97 13.4304 10.0556 13.6308 10.0556 13.8313C10.0556 14.0318 9.97 14.2323 9.83 14.3788ZM12.5522 17.0776C12.4044 17.2164 12.2022 17.3012 12 17.3012C11.7978 17.3012 11.5956 17.2164 11.4478 17.0776C11.3078 16.9311 11.2222 16.7306 11.2222 16.5301C11.2222 16.3296 11.3078 16.1292 11.4478 15.9827C11.7356 15.6973 12.2644 15.6973 12.5522 15.9827C12.6922 16.1292 12.7778 16.3296 12.7778 16.5301C12.7778 16.7306 12.6922 16.9311 12.5522 17.0776ZM12.5522 14.3788C12.5133 14.4096 12.4744 14.4405 12.4356 14.4713C12.3889 14.5022 12.3422 14.5253 12.2956 14.5407C12.2489 14.5639 12.2022 14.5793 12.1556 14.587C12.1011 14.5947 12.0544 14.6024 12 14.6024C11.7978 14.6024 11.5956 14.5176 11.4478 14.3788C11.3078 14.2323 11.2222 14.0318 11.2222 13.8313C11.2222 13.6308 11.3078 13.4304 11.4478 13.2839C11.5178 13.2145 11.6033 13.1605 11.7044 13.1219C11.9922 12.9986 12.3344 13.068 12.5522 13.2839C12.6922 13.4304 12.7778 13.6308 12.7778 13.8313C12.7778 14.0318 12.6922 14.2323 12.5522 14.3788ZM15.2744 17.0776C15.1267 17.2164 14.9244 17.3012 14.7222 17.3012C14.52 17.3012 14.3178 17.2164 14.17 17.0776C14.03 16.9311 13.9444 16.7306 13.9444 16.5301C13.9444 16.3296 14.03 16.1292 14.17 15.9827C14.4578 15.6973 14.9867 15.6973 15.2744 15.9827C15.4144 16.1292 15.5 16.3296 15.5 16.5301C15.5 16.7306 15.4144 16.9311 15.2744 17.0776ZM15.2744 14.3788C15.2356 14.4096 15.1967 14.4405 15.1578 14.4713C15.1111 14.5022 15.0644 14.5253 15.0178 14.5407C14.9711 14.5639 14.9244 14.5793 14.8778 14.587C14.8233 14.5947 14.7689 14.6024 14.7222 14.6024C14.52 14.6024 14.3178 14.5176 14.17 14.3788C14.03 14.2323 13.9444 14.0318 13.9444 13.8313C13.9444 13.6308 14.03 13.4304 14.17 13.2839C14.2478 13.2145 14.3256 13.1605 14.4267 13.1219C14.5667 13.0602 14.7222 13.0448 14.8778 13.0757C14.9244 13.0834 14.9711 13.0988 15.0178 13.1219C15.0644 13.1373 15.1111 13.1605 15.1578 13.1913C15.1967 13.2222 15.2356 13.253 15.2744 13.2839C15.4144 13.4304 15.5 13.6308 15.5 13.8313C15.5 14.0318 15.4144 14.2323 15.2744 14.3788Z'
      fill={fill || lightest}
    />
  </svg>
);
