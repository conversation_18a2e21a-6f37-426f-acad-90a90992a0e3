export const walletTime = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg
    width={width || 48}
    height={height || 49}
    viewBox='0 0 48 49'
    fill='none'
  >
    <path
      d='M35.48 46H15.28C14.66 46 14.1 45.62 13.88 45.02C13.66 44.42 13.82 43.78 14.3 43.36C15.7 42.12 16.52 40.34 16.52 38.48C16.52 34.9 13.6 31.98 10.02 31.98C8.54002 31.98 7.14002 32.48 5.96002 33.42C5.50002 33.78 4.90004 33.84 4.38004 33.6C3.86004 33.36 3.54004 32.82 3.54004 32.24V23.5C3.54004 18.52 7.58003 14.48 12.56 14.48H35.52C40.5 14.48 44.54 18.52 44.54 23.5V26.38C44.54 27.2 43.86 27.88 43.04 27.88H39C38.3 27.88 37.66 28.14 37.2 28.62L37.18 28.64C36.62 29.18 36.36 29.9201 36.42 30.6801C36.54 32.0001 37.8 33.0601 39.24 33.0601H43.04C43.86 33.0601 44.54 33.7401 44.54 34.5601V36.9399C44.5 41.9599 40.46 46 35.48 46ZM18.36 43H35.48C38.8 43 41.5 40.3 41.5 36.98V36.1H39.2C36.18 36.1 33.62 33.86 33.38 30.98C33.22 29.34 33.82 27.72 35.02 26.54C36.06 25.48 37.46 24.9 38.96 24.9H41.5V23.52C41.5 20.2 38.8 17.5 35.48 17.5H12.52C9.20002 17.5 6.50003 20.2 6.50003 23.52V29.6801C7.62003 29.2401 8.80003 29 10 29C15.24 29 19.5 33.26 19.5 38.5C19.5 40.08 19.1 41.64 18.36 43Z'
      fill={fill || light}
    />
    <path
      d='M5 25.0202C4.18 25.0202 3.5 24.3402 3.5 23.5202V16.1802C3.5 13.2002 5.36001 10.4801 8.14001 9.4401L24.02 3.4401C25.66 2.8401 27.5 3.06022 28.92 4.06022C30.34 5.06022 31.2 6.68019 31.2 8.40019V16.0002C31.2 16.8202 30.52 17.5002 29.7 17.5002C28.88 17.5002 28.2 16.8202 28.2 16.0002V8.40019C28.2 7.64019 27.84 6.96018 27.22 6.52018C26.58 6.08018 25.8 5.98015 25.08 6.24015L9.20001 12.2402C7.56001 12.8602 6.5 14.4002 6.5 16.1802V23.5202C6.5 24.3402 5.82 25.0202 5 25.0202Z'
      fill={fill || light}
    />
    <path
      d='M39.2 36.0999C36.18 36.0999 33.6199 33.8599 33.3799 30.9799C33.2199 29.3199 33.8199 27.6999 35.0199 26.5199C36.0399 25.4799 37.4399 24.8999 38.9399 24.8999H43.0999C45.0799 24.9599 46.5999 26.5198 46.5999 28.4398V32.5599C46.5999 34.4799 45.0799 36.0399 43.1599 36.0999H39.2ZM43.0599 27.8999H38.9599C38.2599 27.8999 37.6199 28.1599 37.1599 28.6399C36.5799 29.1999 36.2999 29.9598 36.3799 30.7198C36.4999 32.0398 37.76 33.0999 39.2 33.0999H43.1199C43.3799 33.0999 43.6199 32.8599 43.6199 32.5599V28.4398C43.6199 28.1398 43.3799 27.9199 43.0599 27.8999Z'
      fill={fill || light}
    />
    <path
      d='M28 26H14C13.18 26 12.5 25.32 12.5 24.5C12.5 23.68 13.18 23 14 23H28C28.82 23 29.5 23.68 29.5 24.5C29.5 25.32 28.82 26 28 26Z'
      fill={fill || light}
    />
    <path
      d='M10 48C4.76 48 0.5 43.74 0.5 38.5C0.5 35.58 1.8 32.88 4.06 31.08C5.74 29.74 7.86 29 10 29C15.24 29 19.5 33.26 19.5 38.5C19.5 41.22 18.32 43.82 16.28 45.62C14.52 47.16 12.3 48 10 48ZM10 32C8.52 32 7.12 32.4999 5.94 33.4399C4.4 34.6599 3.5 36.52 3.5 38.5C3.5 42.08 6.42 45 10 45C11.56 45 13.08 44.42 14.3 43.38C15.7 42.14 16.5 40.38 16.5 38.5C16.5 34.92 13.58 32 10 32Z'
      fill={fill || light}
    />
    <path
      d='M7.99977 42C7.49977 42 6.99977 41.74 6.71977 41.28C6.29977 40.56 6.51976 39.64 7.23976 39.22L9.01976 38.16V36C9.01976 35.18 9.69975 34.5 10.5198 34.5C11.3398 34.5 12.0198 35.18 12.0198 36V39C12.0198 39.52 11.7398 40.02 11.2998 40.28L8.79975 41.78C8.53975 41.94 8.25977 42 7.99977 42Z'
      fill={fill || light}
    />
  </svg>
);
