export const reloadArrows = ({
  stroke,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || '24'}
    height={height || '24'}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      d='M20 12C20 16.416 16.416 20 12 20C7.584 20 4.888 15.552 4.888 15.552M4.888 15.552H8.504M4.888 15.552V19.552M4 12C4 7.584 7.552 4 12 4C17.336 4 20 8.448 20 8.448M20 8.448V4.448M20 8.448H16.448'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);
