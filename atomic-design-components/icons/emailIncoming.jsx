export const emailIncoming = ({
  width,
  height,
  theme: {
    color: {
      general: { lightest },
      primary: { main },
    },
  },
}) => (
  <svg
    width={width || 64}
    height={height || 64}
    viewBox='0 0 64 64'
    fill='none'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M20.2592 23.2887H15.9998C13.0543 23.2887 10.6665 25.5306 10.6665 28.2961V28.741L31.9997 40.4129L53.3332 28.741V28.2961C53.3332 25.5306 50.9454 23.2887 47.9996 23.2887H43.7404C43.4822 24.1562 42.9899 24.9661 42.2821 25.6306L36.0286 31.502C33.8036 33.5911 30.1961 33.5911 27.971 31.502L21.7175 25.6306C21.0097 24.9661 20.5174 24.1562 20.2592 23.2887ZM10.6665 48.326V33.1634L30.9752 44.2747C31.6084 44.6211 32.3911 44.6211 33.0243 44.2747L53.3332 33.1634V48.326C53.3332 51.0915 50.9454 53.3333 47.9996 53.3333H15.9998C13.0543 53.3333 10.6665 51.0915 10.6665 48.326Z'
      fill={lightest}
    />
    <path
      d='M31.9998 10.6667C33.3483 10.6667 34.4416 11.6931 34.4416 12.9593V20.3196H38.2533C38.9118 20.3196 39.5054 20.692 39.7573 21.2631C40.0093 21.8342 39.87 22.4916 39.4044 22.9288L33.1509 28.8002C32.5152 29.3971 31.4844 29.3971 30.8487 28.8002L24.5952 22.9288C24.1296 22.4916 23.9903 21.8342 24.2423 21.2631C24.4943 20.692 25.0878 20.3196 25.7463 20.3196H29.5579V12.9593C29.5579 11.6931 30.6512 10.6667 31.9998 10.6667Z'
      fill={lightest}
    />
    <path
      d='M32.0001 10.6667C33.3487 10.6667 34.4419 11.6931 34.4419 12.9593V20.3196H38.2537C38.9121 20.3196 39.5057 20.692 39.7576 21.2631C40.0096 21.8342 39.8703 22.4916 39.4047 22.9288L33.1512 28.8002C32.5155 29.3971 31.4848 29.3971 30.849 28.8002L24.5955 22.9288C24.1299 22.4916 23.9906 21.8342 24.2426 21.2631C24.4946 20.692 25.0882 20.3196 25.7466 20.3196H29.5583V12.9593C29.5583 11.6931 30.6515 10.6667 32.0001 10.6667Z'
      fill={main}
    />
  </svg>
);
