export const heartShape = ({
  width,
  height,
  stroke,
  fill,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 20}
    height={height || 18}
    viewBox='0 0 20 18'
    fill={fill || 'none'}
  >
    <path
      d='M10.558 16.9191C10.252 17.027 9.748 17.027 9.442 16.9191C6.832 16.0292 1 12.3169 1 6.02472C1 3.24719 3.241 1 6.004 1C7.642 1 9.091 1.79101 10 3.01348C10.909 1.79101 12.367 1 13.996 1C16.759 1 19 3.24719 19 6.02472C19 12.3169 13.168 16.0292 10.558 16.9191Z'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);
