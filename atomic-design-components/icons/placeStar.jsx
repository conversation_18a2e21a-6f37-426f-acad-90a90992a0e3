import { Typography } from '@/atomic-design-components';

export const placeStar = ({ level, fill, stroke, width, height, theme }) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      d='M15.9391 3.44316L15.9409 3.44418L19.1313 5.27747C20.22 5.90302 20.8901 7.06376 20.8875 8.31932L20.88 11.999L20.88 12.001L20.8875 15.6807C20.8901 16.9362 20.22 18.097 19.1313 18.7225L15.9409 20.5558L15.9391 20.5568L12.7562 22.4032C11.6702 23.0332 10.3298 23.0332 9.24379 22.4032L6.06089 20.5568L6.05911 20.5558L2.86866 18.7225C1.78003 18.097 1.10988 16.9362 1.11245 15.6807L1.12 12.001L1.12 11.999L1.11245 8.31932C1.10988 7.06376 1.78003 5.90302 2.86867 5.27747L6.05911 3.44418L6.05911 3.44418L6.06089 3.44316L9.24379 1.59679C10.3298 0.966785 11.6702 0.966785 12.7562 1.59679L15.9391 3.44316Z'
      fill={fill || '#272C39'}
      stroke={stroke || '#D3B517'}
    />
    <foreignObject x='0' y='0' width='24' height='24'>
      <Typography
        displayCssProp='flex'
        component='div'
        fontWeight={theme.font.weight.bold}
        fontSize='10px'
        color='white'
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          width: '100%',
          padding: '1px 2px 0 0',
        }}
      >
        {level}
      </Typography>
    </foreignObject>
  </svg>
);
