export const cross = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox='0 0 16 16'
    fill='none'
  >
    <g clipPath='url(#clip0_6471_3108)'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.05665 8L15.4717 1.58498C15.7635 1.29319 15.7635 0.820114 15.4717 0.528326C15.1799 0.23654 14.7068 0.23654 14.415 0.528327L8 6.94334L1.58498 0.528327C1.29319 0.23654 0.820114 0.23654 0.528327 0.528327C0.23654 0.820114 0.236541 1.29319 0.528328 1.58498L6.94334 8L0.528327 14.415C0.23654 14.7068 0.23654 15.1799 0.528326 15.4717C0.820113 15.7635 1.29319 15.7635 1.58498 15.4717L8 9.05665L14.415 15.4717C14.7068 15.7635 15.1799 15.7635 15.4717 15.4717C15.7635 15.1799 15.7635 14.7068 15.4717 14.415L9.05665 8Z'
        fill={fill || lightest}
      />
    </g>
    <defs>
      <clipPath id='clip0_6471_3108'>
        <rect width='16' height='16' fill='white' />
      </clipPath>
    </defs>
  </svg>
);
