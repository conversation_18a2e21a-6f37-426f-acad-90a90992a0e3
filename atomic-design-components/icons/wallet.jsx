export const wallet = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { white },
    },
  },
}) => (
  <svg
    width={width || 17}
    height={height || 16}
    viewBox='0 0 17 16'
    fill='none'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M15.6489 4.78874C15.6025 4.78568 15.5519 4.7857 15.4996 4.78571L13.4313 4.78572C11.7373 4.78572 10.2878 6.18077 10.2878 8C10.2878 9.8192 11.7373 11.2143 13.4313 11.2143H15.4996C15.5519 11.2143 15.6025 11.2144 15.6489 11.2113C16.337 11.1658 16.9455 10.6025 16.9967 9.8156C17.0001 9.764 17 9.70837 17 9.65686V6.34316C17 6.29161 17.0001 6.23599 16.9967 6.1844C16.9455 5.39755 16.337 4.8342 15.6489 4.78874ZM13.2491 8.85714C13.6852 8.85714 14.0387 8.4734 14.0387 8C14.0387 7.5266 13.6852 7.14286 13.2491 7.14286C12.813 7.14286 12.4594 7.5266 12.4594 8C12.4594 8.4734 12.813 8.85714 13.2491 8.85714Z'
      fill={fill || white}
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M15.499 12.5C15.6212 12.4967 15.7137 12.615 15.6805 12.7381C15.516 13.3487 15.2549 13.8693 14.8361 14.3075C14.223 14.9489 13.4455 15.2336 12.485 15.3688C11.5516 15.5 10.3591 15.5 8.85344 15.5H7.12247C5.61682 15.5 4.42425 15.5 3.49092 15.3688C2.53039 15.2336 1.75293 14.9489 1.13981 14.3075C0.526697 13.6661 0.254599 12.8526 0.125465 11.8477C-2.41546e-05 10.8713 -1.60143e-05 9.6236 3.71263e-07 8.04834V7.95166C-1.60143e-05 6.37643 -2.41546e-05 5.12874 0.125465 4.15227C0.254599 3.14734 0.526697 2.33395 1.13981 1.69249C1.75293 1.05104 2.53039 0.766366 3.49092 0.631263C4.42425 0.499975 5.61682 0.499983 7.12247 0.5H8.85344C10.3591 0.499983 11.5516 0.499975 12.485 0.631263C13.4455 0.766366 14.223 1.05104 14.8361 1.69249C15.2549 2.13073 15.516 2.65121 15.6805 3.26187C15.7137 3.38493 15.6212 3.50332 15.499 3.5L13.4312 3.50001C11.1073 3.50001 9.05883 5.42078 9.05883 8C9.05883 10.5792 11.1073 12.5 13.4312 12.5H15.499ZM3.07229 3.92857C2.73294 3.92857 2.45783 4.21639 2.45783 4.57143C2.45783 4.92647 2.73294 5.21429 3.07229 5.21429H6.3494C6.68874 5.21429 6.96385 4.92647 6.96385 4.57143C6.96385 4.21639 6.68874 3.92857 6.3494 3.92857H3.07229Z'
      fill={fill || white}
    />
  </svg>
);
