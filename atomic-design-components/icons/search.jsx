export const search = ({
  stroke,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 15}
    height={height || 15}
    viewBox='0 0 15 15'
    fill='none'
  >
    <path
      d='M6.6665 11.6667C9.42793 11.6667 11.6665 9.42811 11.6665 6.66669C11.6665 3.90526 9.42793 1.66669 6.6665 1.66669C3.90508 1.66669 1.6665 3.90526 1.6665 6.66669C1.6665 9.42811 3.90508 11.6667 6.6665 11.6667Z'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M13.6667 13.6667L11 11'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);
