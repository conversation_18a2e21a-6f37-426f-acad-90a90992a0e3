export const discountTicket = ({
  fill,
  // stroke,
  // strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 21}
    height={height || 13}
    fill='none'
    viewBox='0 0 21 13'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M0 4.25726C0 4.56348 0.195349 4.836 0.488372 4.93807C1.34107 5.23563 1.95349 6.03874 1.95349 6.98148C1.95349 7.92422 1.34107 8.72733 0.488372 9.02489C0.195349 9.12696 0 9.39948 0 9.7057V11.3148C0 12.246 0.765767 13 1.7093 13H19.2907C20.2342 13 21 12.246 21 11.3148V9.7057C21 9.39948 20.8047 9.12696 20.5116 9.02489C19.6589 8.72733 19.0465 7.92422 19.0465 6.98148C19.0465 6.03874 19.6589 5.23563 20.5116 4.93807C20.8047 4.836 21 4.56348 21 4.25726V1.68519C21 0.754 20.2342 0 19.2907 0H1.7093C0.765767 0 0 0.754 0 1.68519V4.25726ZM14.1804 7.67096C13.608 7.10763 12.6801 7.10763 12.1077 7.67096C11.5363 8.23526 11.5363 9.15007 12.1077 9.71341C12.6801 10.2777 13.608 10.2777 14.1804 9.71341C14.7518 9.15007 14.7518 8.23526 14.1804 7.67096ZM12.9126 3.10074L7.05209 8.87852C6.76591 9.1597 6.76591 9.61807 7.05209 9.89926C7.3373 10.1814 7.80223 10.1814 8.08744 9.89926L13.9479 4.12148C14.2341 3.8403 14.2341 3.38193 13.9479 3.10074C13.6627 2.81859 13.1978 2.81859 12.9126 3.10074ZM8.89228 3.43489C8.31991 2.87059 7.392 2.87059 6.81963 3.43489C6.24823 3.99822 6.24823 4.914 6.81963 5.47733C7.392 6.04067 8.31991 6.04067 8.89228 5.47733C9.46367 4.914 9.46367 3.99822 8.89228 3.43489Z'
      fill={fill || lightest}
    />
  </svg>
);
