export const cardPlus = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox='0 0 16 16'
    fill='none'
  >
    <path
      d='M14.3764 0H12.0244C11.0084 0 10.4004 0.645098 10.4004 1.72309V4.2186C10.4004 5.2966 11.0084 5.94169 12.0244 5.94169H14.3764C15.3924 5.94169 16.0004 5.2966 16.0004 4.2186V1.72309C16.0004 0.645098 15.3924 0 14.3764 0ZM14.5524 2.80957C14.4564 2.91143 14.3284 2.96236 14.2004 2.96236C14.0724 2.96236 13.9444 2.91143 13.8484 2.80957L13.7044 2.65679V4.55813C13.7044 4.85521 13.4804 5.09288 13.2004 5.09288C12.9204 5.09288 12.6964 4.85521 12.6964 4.55813V2.65679L12.5524 2.80957C12.3604 3.01329 12.0404 3.01329 11.8484 2.80957C11.6564 2.60586 11.6564 2.26633 11.8484 2.06262L12.8484 1.0016C12.8884 0.959159 12.9444 0.925207 13.0004 0.899742C13.0164 0.891254 13.0324 0.891254 13.0484 0.882766C13.0884 0.86579 13.1284 0.857302 13.1764 0.857302C13.1924 0.857302 13.2084 0.857302 13.2244 0.857302C13.2804 0.857302 13.3284 0.86579 13.3844 0.891254C13.3924 0.891254 13.3924 0.891254 13.4004 0.891254C13.4564 0.916718 13.5044 0.950671 13.5444 0.993112C13.5524 1.0016 13.5524 1.0016 13.5604 1.0016L14.5604 2.06262C14.7524 2.26633 14.7524 2.60586 14.5524 2.80957Z'
      fill={fill || lightest}
    />
    <path
      d='M0 8.24191V12.486C0 14.4298 1.48 16.0001 3.312 16.0001H12.68C14.512 16.0001 16 14.4213 16 12.4775V8.24191C16 7.6732 15.568 7.21484 15.032 7.21484H0.968C0.432 7.21484 0 7.6732 0 8.24191ZM4.8 13.1565H3.2C2.872 13.1565 2.6 12.8679 2.6 12.5199C2.6 12.1719 2.872 11.8833 3.2 11.8833H4.8C5.128 11.8833 5.4 12.1719 5.4 12.5199C5.4 12.8679 5.128 13.1565 4.8 13.1565ZM10 13.1565H6.8C6.472 13.1565 6.2 12.8679 6.2 12.5199C6.2 12.1719 6.472 11.8833 6.8 11.8833H10C10.328 11.8833 10.6 12.1719 10.6 12.5199C10.6 12.8679 10.328 13.1565 10 13.1565Z'
      fill={fill || lightest}
    />
    <path
      d='M9.2 2.42721V4.91423C9.2 5.48294 8.768 5.9413 8.232 5.9413H0.968C0.424 5.9413 0 5.46596 0 4.89726C0.008 3.9381 0.368 3.06382 0.968 2.42721C1.568 1.7906 2.4 1.40015 3.312 1.40015H8.232C8.768 1.40015 9.2 1.85851 9.2 2.42721Z'
      fill={fill || lightest}
    />
  </svg>
);
