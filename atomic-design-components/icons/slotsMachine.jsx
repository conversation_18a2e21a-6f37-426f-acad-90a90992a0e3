import React from 'react'

export const slotsMachine = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      secondary: { main },
    },
  },
}) => (
  <svg width={width || 73} height={height || 73} viewBox="0 0 73 73" fill="none">
    <circle cx="36.5" cy="36.5" r="36.5" fill={fill || '#292D3A'} />
    <circle cx="36.5" cy="36.5" r="32" fill={fill || '#292D3A'} stroke={stroke || '#0083FF'} />
    <path
      d="M52.0968 49.5161V28.0323C52.0968 26.4314 50.7944 25.129 49.1935 25.129H43.9677V22.8065H45.7097C46.6701 22.8065 47.4516 22.0249 47.4516 21.0645V18.7419C47.4516 17.7815 46.6701 17 45.7097 17H28.2903C27.3299 17 26.5484 17.7815 26.5484 18.7419V21.0645C26.5484 22.0249 27.3299 22.8065 28.2903 22.8065H30.0323V25.129H24.8065C23.2056 25.129 21.9032 26.4314 21.9032 28.0323V49.5161C20.3024 49.5161 19 50.8185 19 52.4194V53H55V52.4194C55 50.8185 53.6976 49.5161 52.0968 49.5161ZM27.7097 21.0645V18.7419C27.7097 18.422 27.9698 18.1613 28.2903 18.1613H45.7097C46.0302 18.1613 46.2903 18.422 46.2903 18.7419V21.0645C46.2903 21.3845 46.0302 21.6452 45.7097 21.6452H28.2903C27.9698 21.6452 27.7097 21.3845 27.7097 21.0645ZM31.1935 22.8065H42.8065V25.129H31.1935V22.8065ZM23.0645 28.0323C23.0645 27.0719 23.8461 26.2903 24.8065 26.2903H49.1935C50.1539 26.2903 50.9355 27.0719 50.9355 28.0323V49.5161H32.2683C32.6905 49.1033 32.9355 48.6057 32.9355 48.0645C32.9355 47.0147 32.0355 46.1217 30.6129 45.7013V42.6575C31.6523 42.0543 32.3548 40.9313 32.3548 39.6452C32.3548 39.0105 32.1812 38.4165 31.8834 37.9032H48.6129C49.2534 37.9032 49.7742 37.3824 49.7742 36.7419V28.6129C49.7742 27.9725 49.2534 27.4516 48.6129 27.4516H25.3871C24.7466 27.4516 24.2258 27.9725 24.2258 28.6129V36.7419C24.2258 37.3824 24.7466 37.9032 25.3871 37.9032H25.8586C25.5607 38.4165 25.3871 39.0105 25.3871 39.6452C25.3871 40.9313 26.0897 42.0543 27.129 42.6575V45.7013C25.7065 46.1217 24.8065 47.0147 24.8065 48.0645C24.8065 48.6057 25.0515 49.1033 25.4736 49.5161H23.0645V28.0323ZM25.9677 48.0645C25.9677 47.636 26.4294 47.2046 27.129 46.9259V48.3548H30.6129V46.9259C31.3126 47.2046 31.7742 47.636 31.7742 48.0645C31.7742 48.7514 30.5815 49.5161 28.871 49.5161C27.1604 49.5161 25.9677 48.7514 25.9677 48.0645ZM48.6129 28.6129V36.7419H41.6452V28.6129H48.6129ZM40.4839 36.7419H33.5161V28.6129H40.4839V36.7419ZM32.3548 36.7419H30.7929C30.2413 36.3755 29.5811 36.1613 28.871 36.1613C28.1608 36.1613 27.5006 36.3755 26.949 36.7419H25.3871V28.6129H32.3548V36.7419ZM28.871 37.3226C30.1519 37.3226 31.1935 38.3643 31.1935 39.6452C31.1935 40.9261 30.1519 41.9677 28.871 41.9677C27.5901 41.9677 26.5484 40.9261 26.5484 39.6452C26.5484 38.3643 27.5901 37.3226 28.871 37.3226ZM28.871 43.129C29.069 43.129 29.2623 43.1087 29.4516 43.0768V47.1935H28.2903V43.0768C28.4796 43.1087 28.673 43.129 28.871 43.129ZM20.2606 51.8387C20.5004 51.1628 21.1461 50.6774 21.9032 50.6774H52.0968C52.8539 50.6774 53.4996 51.1628 53.7394 51.8387H20.2606Z"
      fill={stroke || '#0083FF'}
    />
    <path d="M30.0323 19.3226H28.871V20.4839H30.0323V19.3226Z" fill={stroke || '#0083FF'} />
    <path d="M42.8065 19.3226H31.1935V20.4839H42.8065V19.3226Z" fill={stroke || '#0083FF'} />
    <path d="M45.129 19.3226H43.9677V20.4839H45.129V19.3226Z" fill={stroke || '#0083FF'} />
    <path
      d="M27.7097 30.9355H29.9277L27.2173 35.2729L28.2021 35.8884L31.1935 31.1021V29.7742H26.5484V31.5161H27.7097V30.9355Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M35.8387 31.5161V30.9355H38.0568L35.3463 35.2729L36.3311 35.8884L39.3226 31.1021V29.7742H34.6774V31.5161H35.8387Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M43.9677 31.5161V30.9355H46.1858L43.4754 35.2729L44.4601 35.8884L47.4516 31.1021V29.7742H42.8065V31.5161H43.9677Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M45.129 42.5484H49.7742V39.0645H45.129V42.5484ZM46.2903 40.2258H48.6129V41.3871H46.2903V40.2258Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M39.3226 42.5484H43.9677V39.0645H39.3226V42.5484ZM40.4839 40.2258H42.8065V41.3871H40.4839V40.2258Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M33.5161 42.5484H38.1613V39.0645H33.5161V42.5484ZM34.6774 40.2258H37V41.3871H34.6774V40.2258Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M47.4516 43.7097C46.1707 43.7097 45.129 44.7514 45.129 46.0323C45.129 47.3132 46.1707 48.3548 47.4516 48.3548C48.7325 48.3548 49.7742 47.3132 49.7742 46.0323C49.7742 44.7514 48.7325 43.7097 47.4516 43.7097ZM47.4516 47.1935C46.8112 47.1935 46.2903 46.6727 46.2903 46.0323C46.2903 45.3918 46.8112 44.871 47.4516 44.871C48.0921 44.871 48.6129 45.3918 48.6129 46.0323C48.6129 46.6727 48.0921 47.1935 47.4516 47.1935Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M41.6452 43.7097C40.3643 43.7097 39.3226 44.7514 39.3226 46.0323C39.3226 47.3132 40.3643 48.3548 41.6452 48.3548C42.9261 48.3548 43.9677 47.3132 43.9677 46.0323C43.9677 44.7514 42.9261 43.7097 41.6452 43.7097ZM41.6452 47.1935C41.0047 47.1935 40.4839 46.6727 40.4839 46.0323C40.4839 45.3918 41.0047 44.871 41.6452 44.871C42.2856 44.871 42.8065 45.3918 42.8065 46.0323C42.8065 46.6727 42.2856 47.1935 41.6452 47.1935Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M35.8387 43.7097C34.5578 43.7097 33.5161 44.7514 33.5161 46.0323C33.5161 47.3132 34.5578 48.3548 35.8387 48.3548C37.1196 48.3548 38.1613 47.3132 38.1613 46.0323C38.1613 44.7514 37.1196 43.7097 35.8387 43.7097ZM35.8387 47.1935C35.1983 47.1935 34.6774 46.6727 34.6774 46.0323C34.6774 45.3918 35.1983 44.871 35.8387 44.871C36.4792 44.871 37 45.3918 37 46.0323C37 46.6727 36.4792 47.1935 35.8387 47.1935Z"
      fill={stroke || '#0083FF'}
    />
  </svg>
)
