export const verificationPlanet = ({
  fill,
  width,
  height,
  theme: {
    color: {
      status: { neutral },
    },
  },
}) => (
  <svg
    width={width || 68}
    height={height || 68}
    viewBox='0 0 68 68'
    fill='none'
  >
    <rect x='0.5' y='0.5' width='67' height='67' rx='4.5' stroke='#303445' />
    <path
      d='M29.855 29.8709H26.2311C26.3652 31.4121 26.9508 32.8243 27.8505 33.9785C28.5736 33.617 29.3645 33.32 30.2132 33.0976C30.0129 32.1148 29.889 31.0302 29.8534 29.8709H29.855Z'
      fill={fill || neutral}
    />
    <path
      d='M30.2148 25.2862C29.3678 25.0638 28.5769 24.7668 27.8504 24.4052C26.9491 25.5612 26.3652 26.9717 26.2328 28.5129H29.8566C29.8923 27.3553 30.0162 26.2689 30.2165 25.2862H30.2148Z'
      fill={fill || neutral}
    />
    <path
      d='M31.481 21.8117C30.4915 22.1512 29.5918 22.6841 28.8263 23.3648C29.361 23.6041 29.933 23.8061 30.5407 23.9673C30.7953 23.1305 31.1144 22.4075 31.481 21.8117Z'
      fill={fill || neutral}
    />
    <path
      d='M36.1083 24.2285C35.555 22.4717 34.7521 21.3871 34.0002 21.3871C33.2465 21.3871 32.4437 22.4717 31.892 24.2285C33.2788 24.4407 34.7368 24.439 36.11 24.2285H36.1083Z'
      fill={fill || neutral}
    />
    <path
      d='M39.1735 23.3647C38.4063 22.6823 37.5084 22.151 36.5188 21.8116C36.8854 22.4091 37.2045 23.1322 37.4592 23.9672C38.0702 23.806 38.6422 23.6023 39.1735 23.3647Z'
      fill={fill || neutral}
    />
    <path
      d='M28.8282 35.0187C29.5937 35.6994 30.4917 36.2307 31.4813 36.5701C31.1146 35.9744 30.7955 35.253 30.5426 34.4162C29.9332 34.5774 29.3612 34.7794 28.8299 35.0187H28.8282Z'
      fill={fill || neutral}
    />
    <path
      d='M36.7871 28.513C36.7497 27.4436 36.6309 26.4524 36.4493 25.568C35.66 25.697 34.8385 25.7649 34 25.7649C33.1615 25.7649 32.3433 25.697 31.549 25.568C31.3691 26.4541 31.2468 27.4453 31.2112 28.5147H36.7853L36.7871 28.513Z'
      fill={fill || neutral}
    />
    <path
      d='M26.9576 42.4874H41.0424V45.8805H26.9576V42.4874Z'
      fill={fill || neutral}
    />
    <path
      d='M37.7852 33.096C38.6339 33.32 39.4248 33.6153 40.1479 33.9769C41.0492 32.8227 41.6331 31.4105 41.7672 29.8692H38.1434C38.1077 31.0269 37.9838 32.1115 37.7835 33.0943L37.7852 33.096Z'
      fill={fill || neutral}
    />
    <path
      d='M37.7852 25.2862C37.9855 26.2689 38.1094 27.3536 38.145 28.5112H41.7689C41.6365 26.97 41.0509 25.5594 40.1496 24.4036C39.4265 24.7651 38.6356 25.0621 37.7852 25.2862Z'
      fill={fill || neutral}
    />
    <path
      d='M46.3858 15H21.6142C20.7248 15 20 15.7248 20 16.6142V51.4069C20 52.2964 20.7248 53.0211 21.6142 53.0211H46.3858C47.2752 53.0211 48 52.2963 48 51.4069V16.6142C48 15.7248 47.2752 15 46.3858 15ZM42.4004 46.5577C42.4004 46.9328 42.0965 47.2366 41.7214 47.2366H26.2787C25.9036 47.2366 25.5997 46.9328 25.5997 46.5577V41.8065C25.5997 41.4314 25.9036 41.1276 26.2787 41.1276H41.7214C42.0965 41.1276 42.4004 41.4314 42.4004 41.8065V46.5577ZM40.9067 35.1886C40.8642 35.2599 40.8082 35.3159 40.7471 35.3652C39.0701 37.1949 36.67 38.3509 34.0002 38.3509C31.3285 38.3509 28.9418 37.2001 27.265 35.3788C27.2412 35.3618 27.2276 35.3346 27.2056 35.3143C25.7407 33.6899 24.8394 31.5478 24.8394 29.1952V29.1901C24.8394 26.8698 25.7136 24.7532 27.1411 23.1372C27.1784 23.0879 27.2158 23.037 27.265 23.0014C28.9403 21.1784 31.3353 20.0275 34.0002 20.0275C36.6651 20.0275 39.0616 21.1783 40.7371 23.0014C40.7626 23.02 40.7796 23.0506 40.8016 23.0727C42.2631 24.6971 43.161 26.8375 43.161 29.1882C43.161 31.483 42.3072 33.5778 40.9069 35.1869L40.9067 35.1886Z'
      fill={fill || neutral}
    />
    <path
      d='M31.8917 34.1549C32.445 35.91 33.2462 36.9929 33.9998 36.9929C34.7535 36.9929 35.5546 35.91 36.108 34.1549C34.7229 33.9444 33.2648 33.9444 31.8917 34.1549Z'
      fill={fill || neutral}
    />
    <path
      d='M31.2129 29.8709C31.2503 30.9402 31.3708 31.9315 31.5507 32.8175C33.1293 32.5612 34.8555 32.5595 36.4492 32.8175C36.6308 31.9315 36.7513 30.9402 36.7869 29.8709H31.2129Z'
      fill={fill || neutral}
    />
    <path
      d='M36.519 36.5687C37.5085 36.2292 38.4065 35.698 39.172 35.0173C38.639 34.778 38.067 34.576 37.4576 34.4147C37.203 35.2499 36.8839 35.9729 36.5173 36.5687H36.519Z'
      fill={fill || neutral}
    />
  </svg>
);
