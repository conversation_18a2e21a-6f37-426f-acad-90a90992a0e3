import React from 'react'

export const smile = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      secondary: { main },
    },
  },
}) => (
  <svg width={width || 18} height={height || 18} viewBox="0 0 18 18" fill="none">
    <path
      d="M6.50371 11.0993C7.02866 11.9474 7.95038 12.5099 8.99975 12.5099C10.0491 12.5099 10.9708 11.9474 11.4958 11.0993M7.34869 6.74999H5.8498M16.1998 8.99999C16.1998 12.9764 12.9763 16.2 8.9998 16.2C5.02335 16.2 1.7998 12.9764 1.7998 8.99999C1.7998 5.02354 5.02335 1.79999 8.9998 1.79999C12.9763 1.79999 16.1998 5.02354 16.1998 8.99999ZM11.1598 6.74999H11.2236V6.80764H11.1598V6.74999Z"
      stroke={stroke || main}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
