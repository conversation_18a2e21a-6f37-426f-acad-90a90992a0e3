export const newIcon = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      d='M18.6531 12.0025L19.8892 10.1466C19.9463 10.0553 19.9824 9.9524 19.995 9.84542C20.0076 9.73844 19.9963 9.63 19.9619 9.52792C19.9307 9.42484 19.8745 9.33106 19.7984 9.25489C19.7223 9.17872 19.6286 9.12251 19.5256 9.09124L17.3807 8.21787L17.1989 6.07084C17.1956 5.96735 17.1714 5.86559 17.1276 5.77176C17.0839 5.67793 17.0215 5.59397 16.9444 5.52499C16.8561 5.46175 16.7547 5.41912 16.6477 5.40022C16.5408 5.38133 16.431 5.38666 16.3263 5.41582L14.0359 5.88889L12.5453 4.21494C12.3973 4.07681 12.2024 4 12 4C11.7976 4 11.6027 4.07681 11.4547 4.21494L9.96408 5.88889L7.67366 5.41582C7.56904 5.38666 7.45922 5.38133 7.35227 5.40022C7.24531 5.41912 7.14395 5.46175 7.05561 5.52499C6.97846 5.59397 6.91612 5.67793 6.87237 5.77176C6.82862 5.86559 6.80438 5.96735 6.80112 6.07084L6.61935 8.21787L4.47436 9.09124C4.37137 9.12251 4.27768 9.17872 4.20158 9.25489C4.12548 9.33106 4.06933 9.42484 4.03809 9.52792C4.00371 9.63 3.99241 9.73844 4.00498 9.84542C4.01755 9.9524 4.0537 10.0553 4.1108 10.1466L5.34689 12.0025L4.1108 13.8584C4.05595 13.9496 4.02175 14.0517 4.01061 14.1575C3.99948 14.2634 4.01167 14.3704 4.04633 14.471C4.081 14.5716 4.13729 14.6634 4.21125 14.7399C4.2852 14.8164 4.37501 14.8757 4.47436 14.9137L6.61935 15.7871L6.80112 17.9341C6.80438 18.0376 6.82862 18.1393 6.87237 18.2332C6.91612 18.327 6.97846 18.411 7.05561 18.4799C7.14395 18.5432 7.24531 18.5858 7.35227 18.6047C7.45922 18.6236 7.56904 18.6183 7.67366 18.5891L9.96408 18.116L11.4547 19.7536C11.5229 19.831 11.6068 19.8931 11.7008 19.9355C11.7949 19.978 11.8968 20 12 20C12.1032 20 12.2051 19.978 12.2992 19.9355C12.3932 19.8931 12.4771 19.831 12.5453 19.7536L14.0359 18.116L16.3263 18.5891C16.431 18.6183 16.5408 18.6236 16.6477 18.6047C16.7547 18.5858 16.8561 18.5432 16.9444 18.4799C17.0215 18.411 17.0839 18.327 17.1276 18.2332C17.1714 18.1393 17.1956 18.0376 17.1989 17.9341L17.3807 15.7871L19.5256 14.9137C19.625 14.8757 19.7148 14.8164 19.7888 14.7399C19.8627 14.6634 19.919 14.5716 19.9537 14.471C19.9883 14.3704 20.0005 14.2634 19.9894 14.1575C19.9782 14.0517 19.9441 13.9496 19.8892 13.8584L18.6531 12.0025ZM9.81865 14.5862L8.29171 13.4217L7.8918 13.0942H7.85544L8.07358 13.6036L8.69162 15.0592L7.92815 15.3504L6.6557 12.2208L7.41917 11.9297L8.98247 13.0942L9.38238 13.4217C9.31931 13.2597 9.24646 13.1017 9.16425 12.9486L8.58256 11.4566L9.34603 11.1291L10.6185 14.2587L9.81865 14.5862ZM11.0184 14.1131L9.74594 10.9471L12.1454 10.001L12.3999 10.656L10.8003 11.2747L11.0184 11.8569L12.3999 11.2747L12.6544 11.8933L11.2729 12.4391L11.5274 13.1306L13.1634 12.4755L13.4179 13.1306L11.0184 14.1131ZM16.0718 12.0389L15.1266 10.6924L14.7994 10.1466H14.763C14.8237 10.3497 14.8723 10.5563 14.9085 10.7652L15.1993 12.4028L14.4358 12.7303L12.2908 9.92821L13.0907 9.6007L13.9269 10.8016L14.3995 11.5294C14.3365 11.2531 14.288 10.9737 14.2541 10.6924L13.9996 9.2368L14.7267 8.94568L15.5629 10.1829L15.9991 10.8744C15.9991 10.8744 15.9264 10.3649 15.8537 10.001L15.6356 8.58177L16.399 8.25426L16.8353 11.7477L16.0718 12.0389Z'
      fill={fill || lightest}
    />
  </svg>
);
