import { Typography } from '@/atomic-design-components';

export const levelCup = ({
  level,
  fill,
  stroke,
  width,
  height,
  theme,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      d='M22 8.162V8.235C22 9.095 22 9.526 21.793 9.878C21.586 10.23 21.209 10.439 20.457 10.858L19.664 11.298C20.21 9.45 20.393 7.464 20.46 5.766L20.47 5.545L20.472 5.493C21.123 5.719 21.489 5.888 21.717 6.204C22 6.597 22 7.119 22 8.162ZM2 8.162V8.235C2 9.095 2 9.526 2.207 9.878C2.414 10.23 2.791 10.439 3.543 10.858L4.337 11.298C3.79 9.45 3.607 7.464 3.54 5.766L3.53 5.545L3.529 5.493C2.877 5.719 2.511 5.888 2.283 6.204C2 6.597 2 7.12 2 8.162Z'
      fill={fill || light}
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M16.377 2.34701C14.9302 2.10988 13.4661 1.99381 12 2.00001C10.217 2.00001 8.74701 2.15701 7.62301 2.34701C6.48401 2.53901 5.91501 2.63501 5.43901 3.22101C4.96401 3.80701 4.98901 4.44001 5.03901 5.70601C5.21201 10.054 6.15001 15.486 11.25 15.966V19.5H9.82001C9.58891 19.5001 9.365 19.5803 9.18634 19.7269C9.00768 19.8735 8.8853 20.0774 8.84001 20.304L8.65001 21.25H6.00001C5.80109 21.25 5.61033 21.329 5.46968 21.4697C5.32902 21.6103 5.25001 21.8011 5.25001 22C5.25001 22.1989 5.32902 22.3897 5.46968 22.5303C5.61033 22.671 5.80109 22.75 6.00001 22.75H18C18.1989 22.75 18.3897 22.671 18.5303 22.5303C18.671 22.3897 18.75 22.1989 18.75 22C18.75 21.8011 18.671 21.6103 18.5303 21.4697C18.3897 21.329 18.1989 21.25 18 21.25H15.35L15.16 20.304C15.1147 20.0774 14.9923 19.8735 14.8137 19.7269C14.635 19.5803 14.4111 19.5001 14.18 19.5H12.75V15.966C17.85 15.486 18.789 10.055 18.961 5.70601C19.011 4.44001 19.037 3.80601 18.561 3.22101C18.085 2.63501 17.516 2.53901 16.377 2.34701Z'
      fill={fill || light}
    />
    <foreignObject x='0' y='0' width='24' height='24'>
      <Typography
        displayCssProp='flex'
        component='div'
        fontWeight={theme.font.weight.bold}
        fontSize='10px'
        color='white'
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          height: '80%',
          width: '100%',
        }}
      >
        {level}
      </Typography>
    </foreignObject>
  </svg>
);
