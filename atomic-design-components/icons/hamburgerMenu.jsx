import styled from 'styled-components';

const StyledMenuIcon = styled.div`
  height: 22px;
  width: 22px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  & span {
    display: block;
    width: ${({ width }) => width || 22}px;
    height: 2px;
    background-color: ${({ fill, theme }) => fill || theme.color?.primary.main};
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
  }
`;

const hamburgerMenu = ({ wrapperColor, fill, width }) => {
  return (
    <StyledMenuIcon width={width} wrapperColor={wrapperColor} fill={fill}>
      <span />
      <span />
      <span />
    </StyledMenuIcon>
  );
};

export default hamburgerMenu;
