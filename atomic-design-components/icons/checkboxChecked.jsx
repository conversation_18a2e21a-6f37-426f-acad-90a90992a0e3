export const checkboxChecked = ({
  width,
  height,
  fill,
  stroke,
  theme: {
    color: {
      general: { lightest },
      primary: { main },
    },
  },
}) => (
  <svg width={width || 24} height={height || 24} viewBox='0 0 24 24'>
    <path
      d='M0 11C0 8.20887 0 6.81331 0.344477 5.67772C1.12008 3.12091 3.12091 1.12008 5.67772 0.344477C6.81331 0 8.20887 0 11 0C13.7911 0 15.1867 0 16.3223 0.344477C18.8791 1.12008 20.8799 3.12091 21.6555 5.67772C22 6.81331 22 8.20887 22 11C22 13.7911 22 15.1867 21.6555 16.3223C20.8799 18.8791 18.8791 20.8799 16.3223 21.6555C15.1867 22 13.7911 22 11 22C8.20887 22 6.81331 22 5.67772 21.6555C3.12091 20.8799 1.12008 18.8791 0.344477 16.3223C0 15.1867 0 13.7911 0 11Z'
      fill={fill || main}
      className="fill"
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M16.0579 7.92668L9.79489 15.1226L5.93994 10.9593L7.04058 9.94014L9.76036 12.8775L14.9265 6.94189L16.0579 7.92668Z'
      fill={stroke || lightest}
    />
  </svg>
);
