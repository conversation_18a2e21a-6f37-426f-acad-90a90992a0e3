export const cherry = ({
  width,
  height,
  fill,
  theme: {
    color: {
      general: { white },
    },
  },
}) => {
  return (
    <svg
      width={width || 16}
      height={height || 18}
      viewBox='0 0 16 18'
      fill='none'
    >
      <path
        d='M15.9353 0.288394C15.8246 0.080768 15.594 -0.0319154 15.3649 0.00794537C15.2656 0.0252476 12.9061 0.445428 10.3872 1.73466C7.10298 3.41549 5.09795 5.80429 4.56833 8.65762C4.49363 8.65379 4.41861 8.65116 4.34293 8.65116C1.94444 8.65116 0 10.6123 0 13.0315C0 15.4506 1.94444 17.4118 4.34293 17.4118C5.33909 17.4118 6.256 17.0723 6.98854 16.5033C6.21778 15.5578 5.75439 14.3482 5.75439 13.0315C5.75439 11.7147 6.21778 10.5052 6.98854 9.55963C6.58769 9.24819 6.13135 9.00618 5.63734 8.85035C6.47217 4.37872 11.2733 2.31593 13.8751 1.51565C10.4418 5.64036 10.3693 8.11238 10.4157 8.7207C8.38327 9.08634 6.84012 10.8766 6.84012 13.0315C6.84012 15.4506 8.78456 17.4118 11.1831 17.4118C13.5815 17.4118 15.526 15.4506 15.526 13.0315C15.526 10.72 13.7505 8.82812 11.5003 8.66408C11.4908 8.59246 11.1847 5.9242 15.8524 0.922881C16.0125 0.751392 16.0458 0.495802 15.9353 0.288394ZM4.34293 11.4984C3.50475 11.4984 2.82291 12.1861 2.82291 13.0315C2.82291 13.3339 2.57992 13.579 2.28004 13.579C1.98016 13.579 1.73717 13.3339 1.73717 13.0315C1.73717 11.5823 2.90618 10.4033 4.34293 10.4033C4.64281 10.4033 4.8858 10.6484 4.8858 10.9508C4.8858 11.2533 4.64281 11.4984 4.34293 11.4984ZM11.6173 10.9508C11.6173 11.2533 11.3744 11.4984 11.0745 11.4984C10.2363 11.4984 9.55445 12.1861 9.55445 13.0315C9.55445 13.3339 9.31147 13.579 9.01159 13.579C8.71171 13.579 8.46872 13.3339 8.46872 13.0315C8.46872 11.5823 9.63773 10.4033 11.0745 10.4033C11.3744 10.4033 11.6173 10.6484 11.6173 10.9508Z'
        fill={fill || white}
      />
    </svg>
  );
};
