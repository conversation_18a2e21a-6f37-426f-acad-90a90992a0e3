export const clock = ({ height, fill, width , theme:{color:{general:{lightest}}}}) => (
  <svg
    width={width || '29'}
    height={height || '35'}
    viewBox='0 0 29 35'
    fill='none'
  >
    <path
      d='M18.8583 24.379L16.3237 22.1161C16.3128 22.1067 16.2971 22.1036 16.2862 22.0942H16.2846C16.5142 21.7132 16.636 21.279 16.6376 20.8355C16.6438 19.7299 15.927 18.7491 14.8713 18.4196C13.8141 18.0916 12.6678 18.4914 12.0447 19.4066C11.4216 20.3202 11.4715 21.5352 12.1649 22.3972C12.8583 23.2593 14.0343 23.5654 15.0603 23.1531C15.1009 23.2296 15.1524 23.2983 15.2133 23.3592L17.7479 25.6221C18.0915 25.9251 18.6162 25.8938 18.9207 25.5518C19.2268 25.2098 19.1987 24.6866 18.8583 24.379ZM14.1388 21.6695C13.8014 21.6695 13.4985 21.4665 13.3688 21.1541C13.2408 20.8433 13.3111 20.4857 13.55 20.2468C13.7874 20.0094 14.1466 19.9376 14.4573 20.0672C14.7681 20.1953 14.9711 20.4998 14.9711 20.8356C14.9711 21.2962 14.5979 21.6679 14.1388 21.6695Z'
      fill={fill || lightest}
    />
    <path
      d='M13.8373 34.9954C13.9466 34.9985 14.056 35.0001 14.1668 35.0001L14.1653 34.9985C17.8258 35.0032 21.3459 33.5868 23.9837 31.049C26.6215 28.5113 28.1707 25.049 28.3078 21.3917C28.4437 17.7327 27.1569 14.1643 24.716 11.4378L26.1621 9.99164L26.8226 10.6522C27.1475 10.977 27.6753 10.977 28.0001 10.6522C28.325 10.3258 28.325 9.79954 28.0001 9.47471L25.5015 6.97603C25.1766 6.64964 24.6488 6.64964 24.324 6.97603C23.9976 7.30086 23.9976 7.82714 24.324 8.15353L24.9845 8.8141L23.5431 10.2555H23.5415C21.1662 8.14417 18.146 6.89795 14.9708 6.72148V5.84382C14.9662 5.80166 14.9584 5.75793 14.9459 5.71577C16.342 5.31442 17.2275 3.94638 17.0244 2.5081C16.8214 1.06978 15.5908 6.10352e-05 14.1384 6.10352e-05C12.686 6.10352e-05 11.4555 1.06982 11.2524 2.5081C11.0494 3.94642 11.9349 5.31442 13.331 5.71577C13.3185 5.75793 13.3107 5.80166 13.306 5.84382V6.70745C12.1566 6.77616 11.0197 6.9823 9.92031 7.32277C9.4799 7.45863 9.23315 7.92401 9.36904 8.36442C9.5049 8.80326 9.97184 9.04997 10.4107 8.91413C13.7995 7.84749 17.4806 8.27226 20.5366 10.0838C23.5912 11.8938 25.7322 14.9189 26.4242 18.403C27.1145 21.8871 26.2915 25.4993 24.1598 28.3398C22.0266 31.1805 18.7862 32.9794 15.2473 33.2872C11.7101 33.5948 8.20741 32.383 5.61637 29.953C3.02554 27.5246 1.5905 24.1077 1.67001 20.5564C1.74029 17.3456 3.04897 14.2877 5.32433 12.0217C5.48049 11.8655 5.56795 11.6531 5.56795 11.4329C5.56795 11.2127 5.4805 11.0003 5.32433 10.8441L3.29416 8.81398L3.95473 8.15341H3.95317C4.27956 7.82702 4.27956 7.30074 3.95317 6.97591C3.62834 6.64952 3.1005 6.64952 2.77567 6.97591L1.54664 8.20494C1.53883 8.21119 1.52946 8.21431 1.52321 8.22056C1.5154 8.22837 1.51384 8.23773 1.5076 8.24398L0.277004 9.47457C-0.0478245 9.7994 -0.0478245 10.3257 0.277004 10.6521C0.601833 10.9769 1.12968 10.9769 1.4545 10.6521L2.11508 9.9915L3.58462 11.461C1.17497 14.1627 -0.105634 17.6874 0.00682659 21.3059C0.119267 24.9258 1.6169 28.3629 4.19062 30.91C6.76434 33.4571 10.2172 34.9186 13.8373 34.9954ZM12.8894 2.92948C12.8894 2.4235 13.1939 1.9675 13.6609 1.7754C14.1278 1.58175 14.665 1.68795 15.0226 2.04557C15.3787 2.4032 15.4865 2.94042 15.2928 3.40734C15.0991 3.87425 14.6447 4.17881 14.1387 4.17881C13.4484 4.17725 12.8894 3.61819 12.8894 2.92948Z'
      fill={fill || lightest}
    />
    <path
      d='M14.1388 32.0797C14.5994 32.0797 14.9711 31.708 14.9711 31.2473C14.9711 30.7867 14.5994 30.415 14.1388 30.415C11.0638 30.4134 8.1747 28.9361 6.37565 26.4436C4.57504 23.9496 4.0831 20.7434 5.04979 17.8246C6.01804 14.9042 8.3293 12.6273 11.2621 11.7042C14.1949 10.7797 17.3949 11.32 19.8608 13.1566C22.3267 14.9947 23.7618 17.9041 23.7166 20.9788C23.6713 24.0536 22.1517 26.9193 19.6311 28.6828C19.2563 28.9483 19.1657 29.4667 19.4296 29.8431C19.6936 30.2195 20.212 30.31 20.5884 30.0477C23.5462 27.9769 25.3311 24.6113 25.3843 20.9992C25.4374 17.3887 23.7523 13.9717 20.8571 11.8149C17.9601 9.65822 14.2042 9.02415 10.7592 10.1095C7.31567 11.1949 4.60162 13.8685 3.46622 17.2966C2.3293 20.7244 2.90872 24.4896 5.02164 27.416C7.13617 30.3442 10.5279 32.0792 14.1388 32.0792L14.1388 32.0797Z'
      fill={fill || lightest}
    />
    <path
      d='M14.9711 13.3396C14.9711 14.4499 13.3064 14.4499 13.3064 13.3396C13.3064 12.2293 14.9711 12.2293 14.9711 13.3396Z'
      fill={fill || lightest}
    />
    <path
      d='M18.7191 30.415C18.7191 31.5253 17.0544 31.5253 17.0544 30.415C17.0544 29.3047 18.7191 29.3047 18.7191 30.415Z'
      fill={fill || lightest}
    />
    <path
      d='M7.30956 10.374C7.59066 10.1912 7.73433 9.85391 7.67187 9.52284C7.61096 9.19177 7.35485 8.93095 7.02533 8.86227C6.69581 8.79356 6.35692 8.92942 6.16642 9.2074C5.9759 9.48538 5.97277 9.85082 6.15705 10.1319C6.40848 10.5176 6.92384 10.6254 7.30956 10.374Z'
      fill={fill || lightest}
    />
  </svg>
);
