export const checkboxInCircle = ({
  height,
  fill,
  width,
  theme: {
    color: {
      status: { success },
    },
  },
}) => {
  return (
    <svg
      width={width || 24}
      height={height || 24}
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12ZM10.8572 15.0939L15.8327 10.1184L15.0246 9.31027L10.8572 13.4776L8.97553 11.596L8.16741 12.4041L10.8572 15.0939Z'
        fill={fill || success}
      />
    </svg>
  );
};
