export const favoriteChart = ({ height, fill, width, theme:{color:{general:{lightest}}} }) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox='0 0 16 16'
    fill='none'
  >
    <path
      d='M13.6961 10.3521L13.9521 10.8721C14.0801 11.1281 14.4081 11.3681 14.6801 11.4161L15.0241 11.4721C16.0721 11.6481 16.3121 12.4161 15.5681 13.1681L15.2481 13.4881C15.0321 13.7041 14.9201 14.1201 14.9841 14.4241L15.0241 14.6161C15.3121 15.8801 14.6401 16.3681 13.5441 15.7041L13.3121 15.5601C13.0321 15.3921 12.5681 15.3921 12.2881 15.5601L12.0561 15.7041C10.952 16.3681 10.288 15.8801 10.576 14.6161L10.616 14.4241C10.68 14.1281 10.568 13.7041 10.352 13.4881L10.032 13.1681C9.28802 12.4081 9.52803 11.6481 10.576 11.4721L10.92 11.4161C11.2 11.3681 11.52 11.1281 11.6481 10.8721L11.9041 10.3521C12.4001 9.35205 13.2001 9.35205 13.6961 10.3521Z'
      fill={fill || lightest}
    />
    <path
      d='M11.3521 0H4.64805C1.73602 0 0 1.73602 0 4.64805V11.3521C0 14.2642 1.73602 16.0002 4.64805 16.0002H8.8721C9.15211 16.0002 9.35211 15.7122 9.32011 15.4322C9.28811 15.1282 9.30411 14.7682 9.40011 14.3522C9.41611 14.2962 9.40011 14.2322 9.35211 14.1842L9.17611 14.0082C8.4961 13.3202 8.2481 12.4881 8.4881 11.7281C8.7361 10.9761 9.42411 10.4481 10.3761 10.2881L10.6161 10.2481L10.8321 9.81611C11.2721 8.9201 11.9921 8.4001 12.8001 8.4001C13.6082 8.4001 14.3282 8.9201 14.7682 9.81611L14.8882 10.0641C14.9442 10.1841 15.0562 10.2641 15.1842 10.2881C15.2562 10.3041 15.3282 10.3201 15.4002 10.3361C15.6802 10.4081 16.0002 10.1841 16.0002 9.88811V4.64805C16.0002 1.73602 14.2642 0 11.3521 0ZM11.4081 5.56806L9.56011 7.95209C9.32811 8.2481 9.0001 8.4401 8.6241 8.4801C8.25609 8.5281 7.88009 8.4241 7.59209 8.19209L6.12807 7.05608C6.07207 7.00808 6.00807 7.00808 5.97607 7.01608C5.94407 7.01608 5.88807 7.03208 5.84007 7.09608L3.93605 9.56811C3.81604 9.72011 3.64004 9.80011 3.45604 9.80011C3.32804 9.80011 3.20004 9.76011 3.08804 9.67211C2.82403 9.47211 2.77603 9.09611 2.97603 8.8321L4.88006 6.36007C5.11206 6.06407 5.44006 5.87207 5.81607 5.82407C6.19207 5.77607 6.56008 5.88007 6.85608 6.11207L8.3201 7.26408C8.3761 7.31208 8.4321 7.30408 8.4721 7.30408C8.5041 7.30408 8.5601 7.28808 8.6081 7.22408L10.4561 4.84006C10.6561 4.57605 11.0321 4.52805 11.2961 4.73605C11.5681 4.93606 11.6081 5.31206 11.4081 5.56806Z'
      fill={fill || lightest}
    />
  </svg>
);
