export const roulette = ({
  fill,
  width,
  height,
  theme: {
    color: {
      secondary: { dark },
    },
  },
}) => (
  <svg
    width={width || 28}
    height={height || 28}
    viewBox='0 0 19 18'
    fill='none'
  >
    <path
      d='M11.7547 2.06522C11.0537 1.83785 10.2958 1.70522 9.5 1.70522V3.95995C10.0495 3.95995 10.5611 4.05469 11.0537 4.20627L11.7547 2.06522Z'
      fill={fill || dark}
    />
    <path
      d='M13.5737 6.04417L15.3926 4.71785C14.9379 4.09259 14.4074 3.56206 13.7821 3.10732L12.4558 4.92627C12.8916 5.22943 13.2705 5.60838 13.5737 6.04417Z'
      fill={fill || dark}
    />
    <path
      d='M13.5737 11.9557L15.3926 13.2821C15.8474 12.6757 16.1884 11.9936 16.4347 11.2547L14.2937 10.5536C14.1421 11.0652 13.8958 11.5389 13.5737 11.9557Z'
      fill={fill || dark}
    />
    <path
      d='M7.24526 15.9347C7.96526 16.1621 8.72316 16.2947 9.5 16.2947V14.04C8.95053 14.04 8.43895 13.9452 7.94632 13.7936L7.24526 15.9347Z'
      fill={fill || dark}
    />
    <path
      d='M5.42632 11.9557L3.60737 13.2821C4.06211 13.9073 4.59263 14.4378 5.21789 14.8926L6.54421 13.0736C6.10842 12.7705 5.72947 12.3915 5.42632 11.9557Z'
      fill={fill || dark}
    />
    <path
      d='M4.46 8.99995H2.20526C2.20526 9.79574 2.33789 10.5536 2.56526 11.2547L4.70632 10.5536C4.53579 10.061 4.46 9.54943 4.46 8.99995Z'
      fill={fill || dark}
    />
    <path
      d='M5.42632 6.04417L3.60737 4.71785C3.15263 5.32416 2.81158 6.00627 2.56526 6.74522L4.70632 7.44627C4.87684 6.93469 5.10421 6.46101 5.42632 6.04417Z'
      fill={fill || dark}
    />
    <path
      d='M9.5 -4.57764e-05C4.53579 -4.57764e-05 0.5 4.03574 0.5 8.99995C0.5 13.9642 4.53579 18 9.5 18C14.4642 18 18.5 13.9642 18.5 8.99995C18.5 4.03574 14.4642 -4.57764e-05 9.5 -4.57764e-05ZM9.5 16.8063C5.19895 16.8063 1.69368 13.301 1.69368 8.99995C1.69368 4.6989 5.19895 1.19364 9.5 1.19364C13.8011 1.19364 17.3063 4.6989 17.3063 8.99995C17.3063 13.301 13.8011 16.8063 9.5 16.8063Z'
      fill={fill || dark}
    />
    <path
      d='M7.94632 4.20627L7.24526 2.06522C6.50632 2.31153 5.82421 2.65259 5.21789 3.10732L6.54421 4.92627C6.96105 4.60417 7.43474 4.3768 7.94632 4.20627Z'
      fill={fill || dark}
    />
    <path
      d='M14.54 8.99995H16.7947C16.7947 8.20416 16.6621 7.44627 16.4347 6.74522L14.2937 7.44627C14.4642 7.9389 14.54 8.45048 14.54 8.99995Z'
      fill={fill || dark}
    />
    <path
      d='M11.0537 13.7936L11.7547 15.9347C12.4937 15.6884 13.1758 15.3473 13.7821 14.8926L12.4558 13.0736C12.0389 13.3957 11.5653 13.6231 11.0537 13.7936Z'
      fill={fill || dark}
    />
    <path
      d='M4.97158 8.99995C4.97158 11.501 6.99895 13.5284 9.5 13.5284C12.0011 13.5284 14.0284 11.501 14.0284 8.99995C14.0284 6.4989 12.0011 4.47153 9.5 4.47153C6.99895 4.47153 4.97158 6.4989 4.97158 8.99995ZM6.94211 8.45048C7.05579 8.45048 7.15053 8.48838 7.22632 8.52627C7.49158 8.6968 7.81368 8.73469 8.13579 8.73469H8.43895C8.53368 8.3368 8.83684 8.03364 9.23474 7.9389V7.63574C9.23474 7.31364 9.17789 6.99153 9.02632 6.72627C8.96947 6.65048 8.95053 6.5368 8.95053 6.44206C8.95053 6.1389 9.19684 5.89259 9.5 5.89259C9.80316 5.89259 10.0495 6.1389 10.0495 6.44206C10.0495 6.55574 10.0116 6.65048 9.97368 6.72627C9.80316 6.99153 9.76526 7.31364 9.76526 7.63574V7.9389C10.1632 8.03364 10.4663 8.3368 10.5611 8.73469H10.8642C11.1863 8.73469 11.5084 8.67785 11.7737 8.52627C11.8495 8.46943 11.9632 8.45048 12.0579 8.45048C12.3611 8.45048 12.6074 8.6968 12.6074 8.99995C12.6074 9.30311 12.3611 9.54943 12.0579 9.54943C11.9442 9.54943 11.8495 9.51153 11.7737 9.47364C11.5084 9.30311 11.1863 9.26522 10.8642 9.26522H10.5611C10.4663 9.66311 10.1632 9.96627 9.76526 10.061V10.3642C9.76526 10.6863 9.82211 11.0084 9.97368 11.2736C10.0305 11.3494 10.0495 11.4631 10.0495 11.5578C10.0495 11.861 9.80316 12.1073 9.5 12.1073C9.19684 12.1073 8.95053 11.861 8.95053 11.5578C8.95053 11.4442 8.98842 11.3494 9.02632 11.2736C9.19684 11.0084 9.23474 10.6863 9.23474 10.3642V10.061C8.83684 9.96627 8.53368 9.66311 8.43895 9.26522H8.13579C7.81368 9.26522 7.49158 9.32206 7.22632 9.47364C7.15053 9.53048 7.03684 9.54943 6.94211 9.54943C6.63895 9.54943 6.39263 9.30311 6.39263 8.99995C6.39263 8.6968 6.63895 8.45048 6.94211 8.45048Z'
      fill={fill || dark}
    />
  </svg>
);
