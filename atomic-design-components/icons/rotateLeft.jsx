export const rotateLeft = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg
    width={width || 18}
    height={height || 16}
    viewBox='0 0 18 16'
    fill='none'
  >
    <path
      d='M3.98712 12.1145C4.97791 13.4782 6.42752 14.4386 8.06963 14.8192C9.71174 15.1998 11.436 14.9751 12.9257 14.1863C14.4155 13.3976 15.5705 12.0978 16.1787 10.5257C16.7869 8.95356 16.8074 7.21485 16.2363 5.62884C15.6654 4.04286 14.5413 2.71618 13.0706 1.89254C11.5999 1.0689 9.88132 0.803632 8.23072 1.14545C6.58012 1.48728 5.10828 2.41324 4.08563 3.75323C3.06299 5.09321 2.55821 6.75723 2.66406 8.4395M2.66406 8.4395L1.35156 7.127M2.66406 8.4395L3.97656 7.127M9.65023 4.49999V7.99999L12.2752 10.625'
      stroke={stroke || light}
      strokeWidth='2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);
