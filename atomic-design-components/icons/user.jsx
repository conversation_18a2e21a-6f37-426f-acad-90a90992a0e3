export const user = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg
    width={width || 11}
    height={height || 14}
    viewBox='0 0 11 14'
    fill='none'
  >
    <path
      d='M5.49907 0C3.73357 0 2.29178 1.44319 2.29178 3.20878C2.29178 4.97428 3.73424 6.41607 5.49907 6.41607C7.26457 6.41607 8.70785 4.97361 8.70785 3.20878C8.70785 1.44328 7.26466 0 5.49907 0Z'
      fill={fill || light}
    />
    <path
      d='M3.75112 7.58417C2.6813 7.58417 1.77484 8.13184 1.17614 8.9012C0.577431 9.67055 0.25 10.6616 0.25 11.6664V12.252C0.25 13.2117 1.03905 14 1.99874 14H9.001C9.96069 14 10.7497 13.2117 10.7497 12.252V11.6664C10.7497 10.6615 10.4223 9.67053 9.8236 8.9012C9.2249 8.13184 8.31771 7.58417 7.24863 7.58417H3.75112Z'
      fill={fill || light}
    />
  </svg>
);
