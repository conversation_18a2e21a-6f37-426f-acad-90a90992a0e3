export const infoIcon = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11.4286 16.5714V10.8571H12.5714V16.5714H11.4286ZM11.5238 7.57303C11.6648 7.47884 11.8305 7.42857 12 7.42857C12.2273 7.42857 12.4453 7.51888 12.6061 7.67962C12.7668 7.84037 12.8571 8.05839 12.8571 8.28571C12.8571 8.45524 12.8069 8.62096 12.7127 8.76192C12.6185 8.90287 12.4846 9.01274 12.328 9.07761C12.1714 9.14249 11.9991 9.15946 11.8328 9.12639C11.6665 9.09331 11.5138 9.01168 11.3939 8.89181C11.274 8.77193 11.1924 8.6192 11.1593 8.45294C11.1263 8.28667 11.1432 8.11432 11.2081 7.9577C11.273 7.80108 11.3828 7.66721 11.5238 7.57303Z'
      fill={fill || lightest}
    />
  </svg>
);
