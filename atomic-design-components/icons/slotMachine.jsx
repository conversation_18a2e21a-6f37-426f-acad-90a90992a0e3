export const slotMachine = ({ fill, width, height }) => (
  <svg
    width={width || 81}
    height={height || 80}
    fill='none'
    viewBox='0 0 81 80'
  >
    <path
      d='M57.6955 30.5947H7.25581C5.97629 30.5947 4.93532 31.6357 4.93532 32.9152V57.9369C4.93532 59.2164 5.97629 60.2573 7.25581 60.2573H57.6955C58.975 60.2573 60.016 59.2164 60.016 57.9369V32.9152C60.016 31.6357 58.975 30.5947 57.6955 30.5947ZM58.8661 57.9369C58.8661 58.5824 58.3411 59.1074 57.6955 59.1074H7.25581C6.61028 59.1074 6.08522 58.5824 6.08522 57.9369V32.9152C6.08522 32.2697 6.61028 31.7446 7.25581 31.7446H57.6955C58.3411 31.7446 58.8661 32.2697 58.8661 32.9152V57.9369Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M75.5108 13.7527C72.7595 13.7527 70.5213 15.9909 70.5213 18.7421C70.5213 20.5623 71.5017 22.1573 72.9617 23.0288L70.7822 37.2942C70.7342 37.6081 70.9498 37.9015 71.2638 37.9494C71.2931 37.9539 71.3223 37.9561 71.3512 37.9561C71.6305 37.9561 71.8755 37.7523 71.9189 37.4679L74.051 23.513C74.5129 23.6546 75.003 23.7313 75.5108 23.7313C75.5726 23.7313 75.6338 23.7289 75.6951 23.7266L72.8298 42.4711C71.9494 41.1127 70.4821 40.1688 68.789 40.0034C68.6248 39.1186 67.9676 38.3811 67.0778 38.1363L64.4513 37.4139V33.1083C64.4513 29.6809 61.9566 26.8271 58.6879 26.2627L57.6131 23.7506C57.0051 22.3296 55.6939 21.3619 54.1868 21.1764C53.8992 9.45042 44.2704 0 32.4753 0C20.6815 0 11.0536 9.45034 10.7659 21.1762C9.25826 21.3613 7.94642 22.3291 7.33826 23.7507L6.2636 26.2627C2.99495 26.8271 0.5 29.6809 0.5 33.1083V57.7438C0.5 61.5754 3.6173 64.6926 7.44895 64.6926H17.851V67.3675C17.851 69.8503 19.8708 71.8701 22.3537 71.8701H42.5977C45.0805 71.8701 47.1004 69.8503 47.1004 67.3675V64.6926H53.7739V74.9362H11.1775V67.1884C11.1775 66.8708 10.92 66.6134 10.6025 66.6134C10.285 66.6134 10.0276 66.8708 10.0276 67.1884V74.9362H7.60104C6.93208 74.9362 6.33323 75.3602 6.11073 75.9912L5.44001 77.8947C5.2698 78.3774 5.34471 78.915 5.64037 79.3328C5.93619 79.7505 6.41825 80 6.93016 80H58.0212C58.5331 80 59.0152 79.7505 59.311 79.3328C59.6066 78.915 59.6815 78.3774 59.5113 77.8948L58.8406 75.9912C58.6184 75.3602 58.0194 74.9362 57.3503 74.9362H54.9238V64.6926H57.5025C61.3342 64.6926 64.4513 61.5754 64.4513 57.7438V53.4381L67.0776 52.7157C67.9672 52.4711 68.6242 51.7341 68.7888 50.8497C71.5438 50.5806 73.7041 48.251 73.7041 45.4258C73.7041 45.1772 73.6859 44.933 73.6534 44.6932L76.8875 23.5366C78.9708 22.9374 80.5 21.0155 80.5 18.7421C80.5 15.9909 78.2618 13.7527 75.5108 13.7527ZM11.6463 78.0859C11.5526 78.3517 11.5787 78.622 11.6874 78.8501H6.93016C6.78866 78.8501 6.66065 78.7839 6.57899 78.6684C6.49734 78.5531 6.47745 78.4104 6.52445 78.2769L7.19517 76.3735C7.25581 76.2016 7.41896 76.0861 7.60104 76.0861H13.2604C12.5265 76.5388 11.9465 77.2338 11.6463 78.0859ZM32.4753 1.1499C43.6246 1.1499 52.7298 10.0658 53.0354 21.1414H22.4978C22.7968 15.8963 27.1572 11.7205 32.4757 11.7205C36.6716 11.7205 40.4501 14.3695 41.8782 18.3124C41.9863 18.6109 42.316 18.765 42.6147 18.6572C42.9132 18.549 43.0675 18.2193 42.9594 17.9208C41.3671 13.5244 37.1539 10.5706 32.4757 10.5706C26.5228 10.5706 21.645 15.2618 21.3449 21.1414H11.9174C12.2229 10.0658 21.3272 1.1499 32.4753 1.1499ZM8.39544 24.203C8.89227 23.0417 10.0295 22.2913 11.2926 22.2913H53.6587C54.9219 22.2913 56.0591 23.0417 56.5559 24.203L57.393 26.1594H7.55844L8.39544 24.203ZM22.5673 70.2837C22.5352 70.4358 22.5473 70.5848 22.5924 70.7202H22.3537C20.505 70.7202 19.0009 69.2162 19.0009 67.3675V64.6926H21.9577V66.4997C21.9577 67.1965 22.5247 67.7633 23.2214 67.7633H24.3525C23.4608 68.3086 22.7958 69.2026 22.5673 70.2837ZM41.8437 66.4997C41.8437 66.5624 41.7927 66.6134 41.73 66.6134H23.2214C23.1587 66.6134 23.1076 66.5624 23.1076 66.4997V64.6926H41.8437V66.4997ZM63.3014 57.7438C63.3014 60.9414 60.7001 63.5427 57.5025 63.5427H7.44896C4.25129 63.5427 1.6499 60.9414 1.6499 57.7438V33.1083C1.6499 29.9107 4.25129 27.3093 7.44896 27.3093H57.5025C60.7001 27.3093 63.3014 29.9107 63.3014 33.1083V57.7438ZM67.6801 50.4162C67.6801 50.9704 67.307 51.4601 66.7726 51.607L64.4513 52.2455V38.6065L66.7726 39.245C67.307 39.3919 67.6801 39.8816 67.6801 40.4358V50.4162ZM68.83 49.6867V41.1663C70.9295 41.4483 72.5542 43.2503 72.5542 45.4258C72.5542 47.602 70.9295 49.4047 68.83 49.6867ZM77.2772 16.0509C76.991 16.0279 76.697 16.0323 76.3978 16.0667C74.3995 16.2966 72.7875 17.8952 72.5405 19.8914C72.5384 19.9088 72.5363 19.9262 72.5343 19.9437C72.4761 20.4632 72.5633 20.9671 72.7601 21.4168C72.087 20.7248 71.6712 19.7813 71.6712 18.7421C71.6712 16.625 73.3937 14.9026 75.5108 14.9026C76.8099 14.9026 77.9596 15.5519 78.6547 16.5423C78.2581 16.2693 77.7893 16.092 77.2772 16.0509Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M54.0469 34.0684H43.8358C43.0531 34.0684 42.4186 34.7029 42.4186 35.4856V55.3665C42.4186 56.1491 43.0531 56.7836 43.8358 56.7836H54.0469C54.8295 56.7836 55.464 56.1491 55.464 55.3665V35.4856C55.464 34.7029 54.8295 34.0684 54.0469 34.0684ZM48.0375 44.1348C48.2647 44.3732 48.5858 44.5221 48.9414 44.5221C49.2555 44.5221 49.5594 44.5695 49.8452 44.6581C51.0926 45.0447 52.0016 46.2094 52.0016 47.5824C52.0016 48.9563 51.0926 50.1202 49.8452 50.5067V51.2745C49.8452 51.7738 49.4406 52.1784 48.9414 52.1784C48.442 52.1784 48.0375 51.7738 48.0375 51.2745V50.5067C46.7901 50.1202 45.881 48.9563 45.881 47.5824C45.881 47.0831 46.2856 46.6785 46.7849 46.6785C47.2842 46.6785 47.6888 47.0831 47.6888 47.5824C47.6888 47.9181 47.8214 48.2229 48.0375 48.4484C48.2656 48.6869 48.5867 48.8349 48.9414 48.8349C49.296 48.8349 49.6171 48.6869 49.8452 48.4484C50.0613 48.2229 50.1938 47.9181 50.1938 47.5824C50.1938 47.2475 50.0613 46.9419 49.8452 46.7173C49.6179 46.4788 49.2969 46.3299 48.9414 46.3299C48.6271 46.3299 48.3233 46.2825 48.0375 46.1939C46.7901 45.8074 45.881 44.6426 45.881 43.2696C45.881 41.8957 46.7901 40.7319 48.0375 40.3453V39.5775C48.0375 39.0782 48.442 38.6736 48.9414 38.6736C49.4406 38.6736 49.8452 39.0782 49.8452 39.5775V40.3453C51.0926 40.7319 52.0016 41.8957 52.0016 43.2696C52.0016 43.7689 51.597 44.1735 51.0977 44.1735C50.5984 44.1735 50.1938 43.7689 50.1938 43.2696C50.1938 42.9339 50.0613 42.6291 49.8452 42.4036C49.6171 42.1652 49.296 42.0171 48.9414 42.0171C48.5867 42.0171 48.2656 42.1652 48.0375 42.4036C47.8214 42.6291 47.6888 42.9339 47.6888 43.2696C47.6888 43.6045 47.8214 43.9101 48.0375 44.1348Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M37.5812 34.0684H27.3701C26.5875 34.0684 25.953 34.7029 25.953 35.4856V55.3665C25.953 56.1491 26.5875 56.7836 27.3701 56.7836H37.5812C38.3639 56.7836 38.9984 56.1491 38.9984 55.3665V35.4856C38.9984 34.7029 38.3639 34.0684 37.5812 34.0684ZM31.5723 44.1348C31.7996 44.3732 32.1207 44.5221 32.4762 44.5221C32.7904 44.5221 33.0942 44.5695 33.3801 44.6581C34.6274 45.0447 35.5365 46.2094 35.5365 47.5824C35.5365 48.9563 34.6274 50.1202 33.3801 50.5067V51.2745C33.3801 51.7738 32.9755 52.1784 32.4762 52.1784C31.9769 52.1784 31.5723 51.7738 31.5723 51.2745V50.5067C30.3249 50.1202 29.4159 48.9563 29.4159 47.5824C29.4159 47.0831 29.8205 46.6785 30.3198 46.6785C30.8191 46.6785 31.2237 47.0831 31.2237 47.5824C31.2237 47.9181 31.3562 48.2229 31.5723 48.4484C31.8004 48.6869 32.1215 48.8349 32.4762 48.8349C32.8308 48.8349 33.152 48.6869 33.3801 48.4484C33.5961 48.2229 33.7287 47.9181 33.7287 47.5824C33.7287 47.2475 33.5961 46.9419 33.3801 46.7173C33.1528 46.4788 32.8317 46.3299 32.4762 46.3299C32.162 46.3299 31.8581 46.2825 31.5723 46.1939C30.3249 45.8074 29.4159 44.6426 29.4159 43.2696C29.4159 41.8957 30.3249 40.7319 31.5723 40.3453V39.5775C31.5723 39.0782 31.9769 38.6736 32.4762 38.6736C32.9755 38.6736 33.3801 39.0782 33.3801 39.5775V40.3453C34.6274 40.7319 35.5365 41.8957 35.5365 43.2696C35.5365 43.7689 35.1319 44.1735 34.6326 44.1735C34.1333 44.1735 33.7287 43.7689 33.7287 43.2696C33.7287 42.9339 33.5961 42.6291 33.3801 42.4036C33.152 42.1652 32.8308 42.0171 32.4762 42.0171C32.1215 42.0171 31.8004 42.1652 31.5723 42.4036C31.3562 42.6291 31.2237 42.9339 31.2237 43.2696C31.2237 43.6045 31.3562 43.9101 31.5723 44.1348Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M21.1155 34.0684H10.9044C10.1218 34.0684 9.4873 34.7029 9.4873 35.4856V55.3665C9.4873 56.1491 10.1218 56.7836 10.9044 56.7836H21.1155C21.8982 56.7836 22.5327 56.1491 22.5327 55.3665V35.4856C22.5327 34.7029 21.8982 34.0684 21.1155 34.0684ZM15.1072 44.1348C15.3344 44.3732 15.6555 44.5221 16.011 44.5221C16.3252 44.5221 16.6291 44.5695 16.9149 44.6581C18.1623 45.0447 19.0713 46.2094 19.0713 47.5824C19.0713 48.9563 18.1623 50.1202 16.9149 50.5067V51.2745C16.9149 51.7738 16.5103 52.1784 16.011 52.1784C15.5118 52.1784 15.1072 51.7738 15.1072 51.2745V50.5067C13.8598 50.1202 12.9508 48.9563 12.9508 47.5824C12.9508 47.0831 13.3553 46.6785 13.8546 46.6785C14.354 46.6785 14.7585 47.0831 14.7585 47.5824C14.7585 47.9181 14.8911 48.2229 15.1072 48.4484C15.3353 48.6869 15.6564 48.8349 16.011 48.8349C16.3657 48.8349 16.6868 48.6869 16.9149 48.4484C17.131 48.2229 17.2636 47.9181 17.2636 47.5824C17.2636 47.2475 17.131 46.9419 16.9149 46.7173C16.6876 46.4788 16.3666 46.3299 16.011 46.3299C15.6968 46.3299 15.393 46.2825 15.1072 46.1939C13.8598 45.8074 12.9508 44.6426 12.9508 43.2696C12.9508 41.8957 13.8598 40.7319 15.1072 40.3453V39.5775C15.1072 39.0782 15.5118 38.6736 16.011 38.6736C16.5103 38.6736 16.9149 39.0782 16.9149 39.5775V40.3453C18.1623 40.7319 19.0713 41.8957 19.0713 43.2696C19.0713 43.7689 18.6668 44.1735 18.1674 44.1735C17.6682 44.1735 17.2636 43.7689 17.2636 43.2696C17.2636 42.9339 17.131 42.6291 16.9149 42.4036C16.6868 42.1652 16.3657 42.0171 16.011 42.0171C15.6564 42.0171 15.3353 42.1652 15.1072 42.4036C14.8911 42.6291 14.7585 42.9339 14.7585 43.2696C14.7585 43.6045 14.8911 43.9101 15.1072 44.1348Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M47.3174 18.6985C48.5698 18.6985 49.585 17.6833 49.585 16.4309C49.585 15.1785 48.5698 14.1632 47.3174 14.1632C46.065 14.1632 45.0497 15.1785 45.0497 16.4309C45.0497 17.6833 46.065 18.6985 47.3174 18.6985Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M41.8724 11.1456C43.1247 11.1456 44.14 10.1303 44.14 8.87793C44.14 7.62555 43.1247 6.6103 41.8724 6.6103C40.62 6.6103 39.6047 7.62555 39.6047 8.87793C39.6047 10.1303 40.62 11.1456 41.8724 11.1456Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M23.0804 11.1456C24.3327 11.1456 25.348 10.1303 25.348 8.87794C25.348 7.62557 24.3327 6.6103 23.0804 6.6103C21.828 6.6103 20.8127 7.62555 20.8127 8.87794C20.8127 10.1303 21.828 11.1456 23.0804 11.1456Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M17.6354 18.6985C18.8877 18.6985 19.903 17.6833 19.903 16.4309C19.903 15.1785 18.8877 14.1632 17.6354 14.1632C16.383 14.1632 15.3677 15.1785 15.3677 16.4309C15.3677 17.6833 16.383 18.6985 17.6354 18.6985Z'
      fill={fill || '#F8FAFC'}
    />
    <path
      d='M32.4764 8.02115C33.7288 8.02115 34.744 7.00589 34.744 5.75352C34.744 4.50114 33.7288 3.48589 32.4764 3.48589C31.2239 3.48589 30.2087 4.50114 30.2087 5.75352C30.2087 7.00589 31.224 8.02115 32.4764 8.02115Z'
      fill={fill || '#F8FAFC'}
    />
  </svg>
);
