export const star2 = ({
  fill,
  stroke,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox='0 0 16 16'
    fill='none'
  >
    <path
      d='M12.3443 5.97427L12.3443 5.97428L12.348 5.97487C13.0066 6.08074 13.3563 6.40423 13.4611 6.74292C13.5684 7.08998 13.461 7.57095 12.9966 8.04025L11.6775 9.3732C11.6006 9.44233 11.554 9.5192 11.5284 9.56703C11.4944 9.63062 11.4698 9.69589 11.4524 9.75443C11.4239 9.85048 11.389 10.0152 11.4213 10.178L11.4211 10.178L11.424 10.1911L11.7974 11.8441L11.7978 11.8457C11.9229 12.3911 11.8738 12.7391 11.7957 12.952C11.7194 13.16 11.6021 13.2756 11.5198 13.3356C11.4054 13.4117 11.2379 13.4978 11.0049 13.4978C10.7861 13.4978 10.4667 13.4384 10.0395 13.1764L10.0395 13.1763L10.0359 13.1741L8.46604 12.2295C8.36935 12.1657 8.27109 12.1411 8.2179 12.1302C8.14611 12.1154 8.0739 12.1098 8.00889 12.1098C7.94388 12.1098 7.87167 12.1154 7.79988 12.1302C7.74697 12.141 7.64944 12.1655 7.55323 12.2285L5.96624 13.1729L5.96583 13.1731C5.49125 13.4561 5.15296 13.5102 4.93272 13.4985C4.71257 13.4868 4.5622 13.4068 4.46751 13.3392C4.38504 13.2804 4.26439 13.1643 4.1865 12.952C4.10844 12.7391 4.05928 12.3911 4.18442 11.8457L4.18479 11.8441L4.55762 10.1936C4.58188 10.0895 4.57336 9.99368 4.56798 9.94725C4.56085 9.88573 4.54768 9.82335 4.53103 9.7654C4.51457 9.70813 4.49113 9.64264 4.45852 9.57861C4.43528 9.53296 4.38858 9.44969 4.30705 9.37537L3.00577 8.04264L3.00578 8.04263L3.00342 8.04025C2.54193 7.57392 2.43298 7.09606 2.53693 6.74952C2.66075 6.39428 3.0084 6.07833 3.65198 5.97487L3.65198 5.97489L3.65565 5.97427L5.34223 5.69022C5.45138 5.67806 5.53575 5.63746 5.57341 5.61823C5.6335 5.58753 5.68828 5.55034 5.73418 5.51404C5.78011 5.47772 5.82909 5.43285 5.87305 5.38112C5.89989 5.34955 5.95233 5.28426 5.98913 5.19256L6.919 3.31327L6.91901 3.31327L6.92009 3.31106C7.20878 2.72032 7.6356 2.5 7.99111 2.5C8.34662 2.5 8.77344 2.72032 9.06213 3.31106L9.06212 3.31106L9.06322 3.31327L10.0056 5.21782C10.048 5.30346 10.1056 5.36814 10.1405 5.40434C10.1815 5.44694 10.2269 5.48636 10.2717 5.52035C10.3158 5.55377 10.3693 5.58944 10.4283 5.61923C10.4647 5.63763 10.5488 5.67804 10.6577 5.6902L12.3443 5.97427Z'
      fill={fill || lightest}
      stroke={stroke || lightest}
    />
  </svg>
);
