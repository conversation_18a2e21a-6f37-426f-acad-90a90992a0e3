'use client';
import { useState } from 'react';

export const play = ({ width = '48px', height = '48px', theme: { color } }) => {
  const [isHovered, setIsHovered] = useState(false);
  return (
    <svg
      width={width}
      height={height}
      viewBox='0 0 48 48'
      fill='none'
      onMouseOver={() => setIsHovered(true)}
      onMouseOut={() => setIsHovered(false)}
    >
      <path
        d='M0 13.2C0 8.34162 0 5.91243 1.02003 4.09103C1.74093 2.80377 2.80377 1.74093 4.09103 1.02003C5.91243 0 8.34162 0 13.2 0H34.8C39.6584 0 42.0876 0 43.909 1.02003C45.1962 1.74093 46.2591 2.80377 46.98 4.09103C48 5.91243 48 8.34162 48 13.2V34.8C48 39.6584 48 42.0876 46.98 43.909C46.2591 45.1962 45.1962 46.2591 43.909 46.98C42.0876 48 39.6584 48 34.8 48H13.2C8.34162 48 5.91243 48 4.09103 46.98C2.80377 46.2591 1.74093 45.1962 1.02003 43.909C0 42.0876 0 39.6584 0 34.8V13.2Z'
        fill={isHovered ? color.primary.dark : color.primary.main}
      />
      <path
        d='M29 22.268C30.3333 23.0378 30.3333 24.9623 29 25.7321L23 29.1962C21.6667 29.966 20 29.0037 20 27.4641L20 20.5359C20 18.9963 21.6667 18.034 23 18.8038L29 22.268Z'
        fill='#F8FAFC'
      />
    </svg>
  );
};
