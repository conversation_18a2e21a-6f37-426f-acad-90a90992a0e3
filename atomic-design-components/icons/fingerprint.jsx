export const fingerprint = ({
  fill,

  width,
  height,
  theme: {
    color: {
      status: { neutral },
    },
  },
}) => (
  <svg
    width={width || 68}
    height={height || 68}
    viewBox='0 0 68 68'
    fill='none'
  >
    <rect x='0.5' y='0.5' width='67' height='67' rx='4.5' stroke='#303445' />
    <path
      d='M33.9601 16.0004C30.9926 16.0004 28.2002 16.9768 25.8835 18.8236C23.6359 20.6151 22.0302 23.1329 21.3608 25.9114C21.3009 26.1632 21.2472 26.4196 21.2011 26.6744C21.1965 26.7005 21.1934 26.7266 21.1919 26.7527L21.0015 29.6327C21 29.648 21 29.6634 21 29.6803V39.04C21 41.4748 21.6786 43.8465 22.9635 45.9023C23.0679 46.0681 23.1769 46.2339 23.2874 46.3951C23.2997 46.4135 23.312 46.4304 23.3258 46.4473L24.7366 48.1421C24.7504 48.159 24.7642 48.1744 24.7796 48.1897C27.2297 50.646 30.4903 52 33.9598 52C41.1062 52 46.9199 46.1863 46.9199 39.0399V28.9601C46.9199 21.8137 41.1062 16 33.9598 16L33.9601 16.0004ZM45.4798 39.0403C45.4798 45.3928 40.3126 50.56 33.9601 50.56C30.8867 50.56 27.9974 49.3656 25.8237 47.1949L24.4559 45.5508C24.3638 45.4157 24.2732 45.2775 24.1857 45.1393C23.0451 43.314 22.4417 41.2046 22.4417 39.0415L22.4402 29.7029L22.6259 26.8874C22.6643 26.674 22.7104 26.4591 22.761 26.2487C24.0122 21.063 28.6176 17.4399 33.9601 17.4399C40.3126 17.4399 45.4799 22.6071 45.4799 28.9597L45.4798 39.0403Z'
      fill={fill || neutral}
    />
    <path
      d='M37.2885 47.0154C36.2338 47.456 35.1147 47.6802 33.9602 47.6802C30.9988 47.6802 28.2769 46.1895 26.679 43.692C26.5408 43.477 26.4119 43.2529 26.2937 43.0272C26.1094 42.6741 25.675 42.5375 25.3219 42.7217C24.9688 42.906 24.8321 43.3404 25.0164 43.6935C25.1545 43.9576 25.305 44.2185 25.4662 44.4688C27.3299 47.381 30.5061 49.1204 33.9602 49.1204C35.3065 49.1204 36.613 48.8594 37.8442 48.3451C38.2111 48.1916 38.3845 47.7694 38.231 47.4025C38.0775 47.0356 37.6554 46.8619 37.2885 47.0154Z'
      fill={fill || neutral}
    />
    <path
      d='M41.6268 24.9734C42.2731 26.2123 42.6001 27.554 42.6001 28.9604V39.0402C42.6001 41.4151 41.656 43.6305 39.9412 45.2744C39.6542 45.5492 39.6449 46.0052 39.9197 46.2922C40.061 46.4396 40.2498 46.5133 40.4386 46.5133C40.6182 46.5133 40.7979 46.4473 40.9375 46.3122C42.9379 44.3932 44.0386 41.8095 44.0386 39.0385L44.0401 28.9599C44.0401 27.3418 43.6471 25.7329 42.9056 24.3083L42.8903 24.2791L42.2501 24.6076L41.6161 24.9469L41.6268 24.9734Z'
      fill={fill || neutral}
    />
    <path
      d='M39.9703 22.7537C39.9949 22.7767 40.0194 22.8013 40.044 22.8259C40.0655 22.8473 40.087 22.8673 40.107 22.8888C40.13 22.9118 40.1515 22.9349 40.1745 22.9579L40.196 22.9809C40.2282 23.0147 40.2589 23.0485 40.2912 23.0822C40.4324 23.2357 40.6258 23.3125 40.8193 23.3125C40.9943 23.3125 41.1709 23.2496 41.309 23.1206C41.6007 22.8504 41.6176 22.3945 41.3474 22.1028C41.3106 22.0629 41.2737 22.0245 41.2353 21.9831L41.2123 21.9585C41.1862 21.9324 41.1601 21.9048 41.134 21.8787C41.1094 21.8541 41.0849 21.828 41.0588 21.8034C41.0311 21.7758 41.002 21.7482 40.9743 21.7205L40.959 21.7067C40.6719 21.4319 40.216 21.4411 39.9412 21.7267C39.6664 22.0138 39.6756 22.4697 39.9612 22.7445L39.9703 22.7537Z'
      fill={fill || neutral}
    />
    <path
      d='M25.3203 39.0402V28.9604C25.3203 24.1968 29.1966 20.3206 33.9601 20.3206C35.6165 20.3206 37.2239 20.7904 38.6117 21.6777C38.9464 21.8926 39.3916 21.7944 39.6065 21.4597C39.8214 21.125 39.7231 20.6798 39.3885 20.4649C37.7689 19.4272 35.8914 18.8806 33.9603 18.8806C28.4028 18.8806 23.8805 23.4033 23.8805 28.9604V39.0402C23.8805 39.4378 24.2029 39.7602 24.6005 39.7602C24.9981 39.7602 25.3203 39.4378 25.3203 39.0402Z'
      fill={fill || neutral}
    />
    <path
      d='M40.44 37.6002C40.8376 37.6002 41.1599 37.2778 41.1599 36.8802V28.9604C41.1599 24.9903 37.9299 21.7606 33.9601 21.7606C29.9903 21.7606 26.7603 24.9906 26.7603 28.9604C26.7603 29.358 27.0826 29.6804 27.4802 29.6804C27.8778 29.6804 28.2002 29.358 28.2002 28.9604C28.2002 25.7842 30.7839 23.2005 33.9601 23.2005C37.1363 23.2005 39.72 25.7842 39.72 28.9604V36.8802C39.72 37.2779 40.0424 37.6002 40.44 37.6002Z'
      fill={fill || neutral}
    />
    <path
      d='M27.4802 31.8404C27.0826 31.8404 26.7603 32.1627 26.7603 32.5603V39.0402C26.7603 39.8631 26.8984 40.6705 27.1702 41.4397C27.2745 41.736 27.5524 41.9202 27.8487 41.9202C27.9285 41.9202 28.0099 41.9064 28.0882 41.8788C28.4628 41.7467 28.6593 41.3353 28.5273 40.9592C28.3093 40.3436 28.2003 39.6973 28.2003 39.0387V32.5604C28.2003 32.1628 27.8778 31.8404 27.4802 31.8404Z'
      fill={fill || neutral}
    />
    <path
      d='M40.4384 39.7771C40.05 39.6896 39.6646 39.9321 39.5772 40.3205C38.9785 42.9579 36.6696 44.8 33.9599 44.8C32.8147 44.8 31.7078 44.4653 30.7607 43.8297C30.5365 43.6793 30.3216 43.512 30.1205 43.3324C29.8242 43.0668 29.3698 43.0929 29.1042 43.3892C28.8387 43.6855 28.8647 44.1399 29.161 44.4054C29.4112 44.6296 29.6814 44.8384 29.9609 45.0256C31.146 45.8193 32.5292 46.2384 33.9616 46.2384C35.6043 46.2384 37.2101 45.6704 38.4843 44.6403C39.7401 43.6256 40.6274 42.204 40.9821 40.6379C41.0696 40.2511 40.827 39.8658 40.4386 39.7767L40.4384 39.7771Z'
      fill={fill || neutral}
    />
    <path
      d='M33.9601 24.6405C31.5775 24.6405 29.6402 26.5779 29.6402 28.9604V39.039C29.6402 41.4216 31.5776 43.3589 33.9601 43.3589C36.3426 43.3589 38.28 41.4215 38.28 39.039V28.9592C38.28 26.5782 36.3426 24.6405 33.9601 24.6405Z'
      fill={fill || neutral}
    />
  </svg>
);
