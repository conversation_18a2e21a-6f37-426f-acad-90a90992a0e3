export const lock = ({
  stroke,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox='0 0 16 16'
    fill='none'
  >
    <rect
      x='2.66663'
      y='6'
      width='10.6667'
      height='8'
      rx='2'
      stroke={stroke || lightest}
      strokeWidth='1.5'
    />
    <path
      d='M8 10.6667L8 9.33337'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M10.6666 6V4.66667C10.6666 3.19391 9.47268 2 7.99992 2V2C6.52716 2 5.33325 3.19391 5.33325 4.66667L5.33325 6'
      stroke={stroke || lightest}
      strokeWidth='1.5'
    />
  </svg>
);
