export const radioUnchecked = ({
  width,
  height,
  fill,
  stroke,
  theme: {
    color: {
      general: { dark },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    fill='none'
    viewBox='0 0 24 24'
  >
    <path
      d='M0.5 12C0.5 5.64873 5.64873 0.5 12 0.5C18.3513 0.5 23.5 5.64873 23.5 12C23.5 18.3513 18.3513 23.5 12 23.5C5.64873 23.5 0.5 18.3513 0.5 12Z'
      fill={fill || '#1e293b'}
    />
    <path
      d='M0.5 12C0.5 5.64873 5.64873 0.5 12 0.5C18.3513 0.5 23.5 5.64873 23.5 12C23.5 18.3513 18.3513 23.5 12 23.5C5.64873 23.5 0.5 18.3513 0.5 12Z'
      stroke={stroke || dark}
    />
  </svg>
);
