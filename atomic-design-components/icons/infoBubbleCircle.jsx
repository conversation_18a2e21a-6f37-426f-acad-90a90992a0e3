export const infoBubbleCircle = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { white },
    },
  },
}) => (
  <svg
    width={width || 128}
    height={height || 127}
    fill='none'
    viewBox='0 0 128 127'
  >
    <g clipPath='url(#clip0_1195_3827)'>
      <circle cx='64.129' cy='63.7391' r='63.4415' fill='#255896' />
      <g filter='url(#filter0_f_1195_3827)'>
        <circle cx='64.9194' cy='63.4104' r='31.9194' fill='#0183FF' />
      </g>
      <path
        d='M65.0004 31.0002C46.8035 31.0002 32 42.2271 32 56.0272C32 66.4442 40.45 75.707 53.1691 79.3946C52.9141 83.2942 51.8278 86.973 49.933 90.3528C48.3836 93.1157 48.035 94.7188 48.7322 95.8834C49.1543 96.5944 49.9225 97.0002 50.8377 97.0002C50.8409 97.0002 50.8441 97.0002 50.8473 97.0002C52.0643 97.0002 55.3019 96.9595 75.7907 79.6799C89.0908 76.2044 98 66.7368 98 56.0264C98.0008 42.2271 83.1949 31.0002 65.0004 31.0002ZM74.6004 76.6133L74.2268 76.7066L73.9338 76.9545C60.0527 88.678 54.3545 92.2166 52.1305 93.2799C52.2927 92.9252 52.517 92.4812 52.8237 91.9336C55.1849 87.7223 56.4317 83.0997 56.5262 78.198L56.5512 76.9083L55.2858 76.5878C43.3334 73.5428 35.303 65.2796 35.303 56.0272C35.303 44.0278 48.6249 34.2645 64.9996 34.2645C81.3735 34.2645 94.6945 44.0278 94.6945 56.0272C94.6945 65.3195 86.6198 73.593 74.6004 76.6133ZM67.4779 40.8034C67.6764 41.0043 67.7837 41.2753 67.7757 41.5551L67.0824 63.0556C67.0679 63.6192 66.5999 64.0687 66.0293 64.0687H63.9707C63.3985 64.0687 62.9321 63.6199 62.9176 63.0556L62.2243 41.5551C62.2163 41.2745 62.3236 41.0043 62.5221 40.8034C62.7206 40.6033 62.9918 40.4893 63.2759 40.4893H66.7241C67.0082 40.4901 67.2794 40.6033 67.4779 40.8034ZM67.7313 68.7145V72.6085C67.7313 73.1824 67.26 73.648 66.6781 73.648H63.321C62.7392 73.648 62.2679 73.1824 62.2679 72.6085V68.7145C62.2679 68.1405 62.7392 67.675 63.321 67.675H66.6781C67.26 67.6758 67.7313 68.1413 67.7313 68.7145Z'
        fill={fill || white}
      />
    </g>
    <defs>
      <filter
        id='filter0_f_1195_3827'
        x='-8.34328'
        y='-9.85238'
        width='146.522'
        height='146.525'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='BackgroundImageFix'
          result='shape'
        />
        <feGaussianBlur
          stdDeviation='20.6716'
          result='effect1_foregroundBlur_1195_3827'
        />
      </filter>
      <clipPath id='clip0_1195_3827'>
        <rect
          width='126.63'
          height='126.63'
          fill={fill || white}
          transform='translate(0.835938 0.297546)'
        />
      </clipPath>
    </defs>
  </svg>
);
