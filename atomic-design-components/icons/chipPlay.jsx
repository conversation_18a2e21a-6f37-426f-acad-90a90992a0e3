export const chipPlay = ({
  fill,
  width,
  height,
  theme: {
    color: {
      secondary: { dark },
    },
  },
}) => (
  <svg
    width={width || 28}
    height={height || 28}
    viewBox='0 0 17 18'
    fill='none'
  >
    <path
      d='M8.50071 0.860352C6.24839 0.862835 4.08891 1.75809 2.4955 3.34994C0.902085 4.94178 0.00470146 7.10038 0 9.35269V9.36666C3.35923e-08 11.621 0.895533 13.783 2.48959 15.3771C4.08365 16.9711 6.24566 17.8667 8.5 17.8667C10.7543 17.8667 12.9163 16.9711 14.5104 15.3771C16.1045 13.783 17 11.621 17 9.36666V9.35269C16.9954 7.1006 16.0982 4.94217 14.5051 3.35035C12.912 1.75853 10.7528 0.86313 8.50071 0.860352ZM11.3314 2.344C12.576 2.84684 13.6645 3.67144 14.4856 4.73334L13.4333 5.47519C12.7677 4.62977 11.8966 3.969 10.903 3.55593L11.3314 2.344ZM5.67001 2.344L6.09842 3.55593C5.10468 3.96794 4.23343 4.6283 3.56817 5.47371L2.51438 4.73334C3.33599 3.67138 4.42502 2.8468 5.67001 2.344ZM5.67001 16.3754C4.42501 15.8726 3.33598 15.048 2.51438 13.986L3.56818 13.2456C4.23343 14.091 5.10468 14.7514 6.09842 15.1634L5.67001 16.3754ZM11.3314 16.3754L10.903 15.1634C11.8966 14.7504 12.7677 14.0896 13.4333 13.2442L14.4856 13.986C13.6646 15.0479 12.576 15.8725 11.3314 16.3754ZM15.1129 10.1924H14.4194C14.2358 10.1935 14.0577 10.2548 13.9122 10.3667C13.7667 10.4787 13.6618 10.6352 13.6137 10.8124C13.2974 11.9248 12.627 12.9037 11.7042 13.6007C10.7814 14.2977 9.65646 14.6748 8.49999 14.6748C7.34352 14.6748 6.21859 14.2977 5.29577 13.6007C4.37295 12.9037 3.70257 11.9248 3.38629 10.8124C3.33815 10.6352 3.23332 10.4787 3.08782 10.3667C2.94232 10.2547 2.76416 10.1935 2.58057 10.1924H1.88714C1.6663 10.1924 1.45451 10.1046 1.29835 9.94847C1.14219 9.79231 1.05446 9.58052 1.05446 9.35968C1.05446 9.13884 1.14219 8.92704 1.29835 8.77088C1.45451 8.61473 1.6663 8.527 1.88714 8.527H2.58057C2.76417 8.52585 2.94233 8.46462 3.08783 8.35265C3.23333 8.24068 3.33816 8.08413 3.38629 7.90696C3.70257 6.79457 4.37294 5.81566 5.29576 5.11864C6.21858 4.42162 7.34352 4.04452 8.49999 4.04452C9.65647 4.04452 10.7814 4.42162 11.7042 5.11864C12.627 5.81566 13.2974 6.79457 13.6137 7.90696C13.6618 8.08413 13.7667 8.24067 13.9122 8.35264C14.0577 8.46461 14.2358 8.52585 14.4194 8.527H15.1129C15.3337 8.527 15.5455 8.61473 15.7017 8.77088C15.8578 8.92704 15.9455 9.13884 15.9455 9.35968C15.9455 9.58052 15.8578 9.79231 15.7017 9.94847C15.5455 10.1046 15.3337 10.1924 15.1129 10.1924Z'
      fill={fill || dark}
    />
    <path
      d='M10.7262 8.91036C11.1759 9.17001 11.1759 9.81915 10.7262 10.0788L7.89286 11.7146C7.44312 11.9743 6.88095 11.6497 6.88095 11.1304V7.85875C6.88095 7.33944 7.44312 7.01488 7.89286 7.27453L10.7262 8.91036Z'
      fill={fill || dark}
    />
    <path
      d='M8.50071 0.860352C6.24839 0.862835 4.08891 1.75809 2.4955 3.34994C0.902085 4.94178 0.00470146 7.10038 0 9.35269V9.36666C3.35923e-08 11.621 0.895533 13.783 2.48959 15.3771C4.08365 16.9711 6.24566 17.8667 8.5 17.8667C10.7543 17.8667 12.9163 16.9711 14.5104 15.3771C16.1045 13.783 17 11.621 17 9.36666V9.35269C16.9954 7.1006 16.0982 4.94217 14.5051 3.35035C12.912 1.75853 10.7528 0.86313 8.50071 0.860352ZM11.3314 2.344C12.576 2.84684 13.6645 3.67144 14.4856 4.73334L13.4333 5.47519C12.7677 4.62977 11.8966 3.969 10.903 3.55593L11.3314 2.344ZM5.67001 2.344L6.09842 3.55593C5.10468 3.96794 4.23343 4.6283 3.56817 5.47371L2.51438 4.73334C3.33599 3.67138 4.42502 2.8468 5.67001 2.344ZM5.67001 16.3754C4.42501 15.8726 3.33598 15.048 2.51438 13.986L3.56818 13.2456C4.23343 14.091 5.10468 14.7514 6.09842 15.1634L5.67001 16.3754ZM11.3314 16.3754L10.903 15.1634C11.8966 14.7504 12.7677 14.0896 13.4333 13.2442L14.4856 13.986C13.6646 15.0479 12.576 15.8725 11.3314 16.3754ZM15.1129 10.1924H14.4194C14.2358 10.1935 14.0577 10.2548 13.9122 10.3667C13.7667 10.4787 13.6618 10.6352 13.6137 10.8124C13.2974 11.9248 12.627 12.9037 11.7042 13.6007C10.7814 14.2977 9.65646 14.6748 8.49999 14.6748C7.34352 14.6748 6.21859 14.2977 5.29577 13.6007C4.37295 12.9037 3.70257 11.9248 3.38629 10.8124C3.33815 10.6352 3.23332 10.4787 3.08782 10.3667C2.94232 10.2547 2.76416 10.1935 2.58057 10.1924H1.88714C1.6663 10.1924 1.45451 10.1046 1.29835 9.94847C1.14219 9.79231 1.05446 9.58052 1.05446 9.35968C1.05446 9.13884 1.14219 8.92704 1.29835 8.77088C1.45451 8.61473 1.6663 8.527 1.88714 8.527H2.58057C2.76417 8.52585 2.94233 8.46462 3.08783 8.35265C3.23333 8.24068 3.33816 8.08413 3.38629 7.90696C3.70257 6.79457 4.37294 5.81566 5.29576 5.11864C6.21858 4.42162 7.34352 4.04452 8.49999 4.04452C9.65647 4.04452 10.7814 4.42162 11.7042 5.11864C12.627 5.81566 13.2974 6.79457 13.6137 7.90696C13.6618 8.08413 13.7667 8.24067 13.9122 8.35264C14.0577 8.46461 14.2358 8.52585 14.4194 8.527H15.1129C15.3337 8.527 15.5455 8.61473 15.7017 8.77088C15.8578 8.92704 15.9455 9.13884 15.9455 9.35968C15.9455 9.58052 15.8578 9.79231 15.7017 9.94847C15.5455 10.1046 15.3337 10.1924 15.1129 10.1924Z'
      fill={fill || dark}
    />
    <path
      d='M10.7262 8.91036C11.1759 9.17001 11.1759 9.81915 10.7262 10.0788L7.89286 11.7146C7.44312 11.9743 6.88095 11.6497 6.88095 11.1304V7.85875C6.88095 7.33944 7.44312 7.01488 7.89286 7.27453L10.7262 8.91036Z'
      fill={fill || dark}
    />
  </svg>
);
