export const info = ({
  fill,
  circleFill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      general: { light },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <circle cx='12' cy='12' r='11.5' fill={circleFill||'#253A5E'} stroke='#2D4672' />
    <path
      d='M13.2306 8.69559C14.1336 8.61665 14.8019 7.82039 14.7224 6.91741C14.644 6.01442 13.8478 5.34634 12.9448 5.42528C12.0418 5.50425 11.3734 6.30021 11.4524 7.20321C11.5314 8.10677 12.3276 8.77456 13.2306 8.69559Z'
      fill={fill || light}
    />
    <path
      d='M13.7299 15.2501C13.3396 15.393 12.6818 15.7956 12.6818 15.7956C12.6818 15.7956 13.1805 13.49 13.2011 13.3896C13.4118 12.3776 14.0107 10.9495 13.1995 10.0812C12.6345 9.47793 11.9083 9.6403 11.2684 10.2525C10.8458 10.6564 10.6217 10.9147 10.2303 11.349C10.0718 11.5249 10.0117 11.6893 10.0868 11.947C10.1674 12.2256 10.4932 12.3637 10.7813 12.2584C11.1716 12.1158 11.8288 11.7132 11.8288 11.7132C11.8288 11.7132 11.5241 13.1163 11.3723 13.818C11.3512 13.9181 10.4999 16.559 11.3117 17.4272C11.8766 18.0308 12.6023 17.8682 13.2428 17.2563C13.6654 16.8523 13.8895 16.594 14.2809 16.1595C14.4394 15.9838 14.4989 15.8195 14.4244 15.5618C14.3432 15.2835 14.0179 15.145 13.7299 15.2501Z'
      fill={fill || light}
    />
  </svg>
);
