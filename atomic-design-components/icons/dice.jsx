export const dice = ({
  fill,
  width,
  height,
  // theme: {
  //   color: {
  //     general: { light },
  //   },
  // },
}) => (
  <svg
    width={width || 17}
    height={height || 16}
    viewBox='0 0 17 16'
    fill='none'
  >
    <path
      d='M8.95459 0.541414C9.30137 0.194751 9.7717 0 10.2621 0C10.7525 0 11.2229 0.194751 11.5696 0.541414L16.4584 5.42956C16.8052 5.77625 17 6.24644 17 6.73671C17 7.22698 16.8052 7.69718 16.4584 8.04386L13.1545 11.3476V7.2397C13.1545 6.34083 12.7974 5.47877 12.1616 4.84317C11.5258 4.20758 10.6635 3.8505 9.76438 3.8505H5.64531L8.95459 0.541414Z'
      fill={fill || '#00AD92'}
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M2.84919 5.39105C2.35875 5.39105 1.8884 5.58581 1.54161 5.9325C1.19482 6.27919 1 6.7494 1 7.2397V14.1513C1 14.6416 1.19482 15.1119 1.54161 15.4585C1.8884 15.8052 2.35875 16 2.84919 16H9.76437C10.2548 16 10.7252 15.8052 11.0719 15.4585C11.4187 15.1119 11.6136 14.6416 11.6136 14.1513V7.2397C11.6136 6.7494 11.4187 6.27919 11.0719 5.9325C10.7252 5.58581 10.2548 5.39105 9.76437 5.39105H2.84919ZM6.30639 11.5532C6.07171 11.5532 5.8423 11.6228 5.64718 11.7531C5.45205 11.8835 5.29996 12.0687 5.21015 12.2855C5.12035 12.5022 5.09685 12.7408 5.14263 12.9709C5.18842 13.201 5.30142 13.4123 5.46737 13.5782C5.63331 13.7441 5.84474 13.8571 6.07491 13.9029C6.30508 13.9486 6.54366 13.9251 6.76047 13.8354C6.97729 13.7456 7.1626 13.5935 7.29298 13.3985C7.42336 13.2034 7.49295 12.9741 7.49295 12.7394C7.49295 12.4248 7.36794 12.1231 7.14542 11.9007C6.9229 11.6782 6.62109 11.5532 6.30639 11.5532ZM5.11983 8.65161C5.11983 8.417 5.18942 8.18765 5.3198 7.99258C5.45019 7.79751 5.6355 7.64546 5.85232 7.55568C6.06913 7.4659 6.30771 7.44241 6.53788 7.48818C6.76805 7.53395 6.97948 7.64693 7.14542 7.81282C7.31136 7.97872 7.42437 8.19008 7.47016 8.42019C7.51594 8.65029 7.49244 8.8888 7.40263 9.10555C7.31283 9.32231 7.16074 9.50757 6.96561 9.63791C6.77048 9.76825 6.54107 9.83782 6.30639 9.83782C6.15054 9.83793 5.9962 9.80731 5.8522 9.74774C5.70819 9.68816 5.57735 9.60079 5.46714 9.49061C5.35694 9.38044 5.26954 9.24964 5.20995 9.10567C5.15035 8.96171 5.11973 8.80741 5.11983 8.65161Z'
      fill={fill || '#00AD92'}
    />
  </svg>
);
