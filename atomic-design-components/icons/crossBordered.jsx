export const crossBordered = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { white },
    },
  },
}) => (
  <svg
    width={width || 18}
    height={height || 18}
    viewBox='0 0 18 18'
    fill='none'
  >
    <path
      d='M6.35231 6.35223C6.57198 6.13256 6.92814 6.13256 7.14781 6.35223L9.00007 8.20449L10.8523 6.35224C11.072 6.13257 11.4281 6.13257 11.6478 6.35224C11.8675 6.57191 11.8675 6.92806 11.6478 7.14773L9.79556 8.99998L11.6478 10.8522C11.8675 11.0719 11.8675 11.4281 11.6478 11.6477C11.4281 11.8674 11.072 11.8674 10.8523 11.6477L9.00007 9.79548L7.14781 11.6477C6.92814 11.8674 6.57199 11.8674 6.35232 11.6477C6.13265 11.4281 6.13265 11.0719 6.35232 10.8522L8.20457 8.99998L6.35231 7.14773C6.13264 6.92806 6.13264 6.5719 6.35231 6.35223Z'
      fill={fill || white}
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M5.48761 2.82655C7.80328 2.56774 10.1968 2.56774 12.5125 2.82655C13.8823 2.97965 14.9884 4.05863 15.1496 5.43635C15.4265 7.80404 15.4265 10.1959 15.1496 12.5636C14.9884 13.9413 13.8823 15.0203 12.5125 15.1734C10.1968 15.4322 7.80328 15.4322 5.48761 15.1734C4.11782 15.0203 3.01165 13.9413 2.85051 12.5636C2.57359 10.1959 2.57359 7.80404 2.85051 5.43635C3.01165 4.05863 4.11783 2.97965 5.48761 2.82655ZM12.3875 3.94459C10.1549 3.69506 7.84519 3.69506 5.61257 3.94459C4.75424 4.04052 4.06721 4.71796 3.9679 5.56704C3.70113 7.8479 3.70113 10.1521 3.9679 12.4329C4.06721 13.282 4.75424 13.9595 5.61257 14.0554C7.84519 14.3049 10.1549 14.3049 12.3875 14.0554C13.2459 13.9595 13.9329 13.282 14.0322 12.4329C14.299 10.1521 14.299 7.8479 14.0322 5.56704C13.9329 4.71796 13.2459 4.04052 12.3875 3.94459Z'
      fill={fill || white}
    />
  </svg>
);
