import { Typography } from '@/atomic-design-components';

export const levelBadge = ({ level, width, height }) => (
  <svg
    width={width || 51}
    height={height || 53}
    viewBox='0 0 51 53'
    fill='none'
  >
    <path
      d='M20.1304 9.41177L19 0H32L30.3043 10L20.1304 9.41177Z'
      fill='url(#paint0_linear_620_866)'
    />
    <path
      d='M35.2312 15.6876L40.7773 8L50.3379 16.8089L42.3148 23.0141L35.2312 15.6876Z'
      fill='url(#paint1_linear_620_866)'
    />
    <path
      d='M41.034 30.2255L49.75 33.9524L43.25 45.2107L35.4376 38.7422L41.034 30.2255Z'
      fill='url(#paint2_linear_620_866)'
    />
    <path
      d='M30.2055 42.7989L31.3359 52.2107H18.3359L20.0316 42.2107L30.2055 42.7989Z'
      fill='url(#paint3_linear_620_866)'
    />
    <path
      d='M15.1047 36.5231L9.55859 44.2107L-0.00195134 35.4019L8.02112 29.1966L15.1047 36.5231Z'
      fill='url(#paint4_linear_620_866)'
    />
    <path
      d='M9.30198 21.9852L0.585938 18.2583L7.08594 6.99997L14.8984 13.4684L9.30198 21.9852Z'
      fill='url(#paint5_linear_620_866)'
    />
    <path
      d='M24.1102 6.80242C24.9702 6.30588 26.0298 6.30588 26.8898 6.80242L41.8637 15.4476C42.7237 15.9441 43.2535 16.8618 43.2535 17.8548V35.1452C43.2535 36.1382 42.7237 37.0559 41.8637 37.5524L26.8898 46.1976C26.0298 46.6941 24.9702 46.6941 24.1102 46.1976L9.13631 37.5524C8.27628 37.0559 7.74648 36.1382 7.74648 35.1452V17.8548C7.74648 16.8618 8.27628 15.9441 9.13631 15.4476L24.1102 6.80242Z'
      fill='#0083FF'
    />
    <g filter='url(#filter0_i_620_866)'>
      <path
        d='M24.1114 8.88713C24.9714 8.3906 26.031 8.3906 26.891 8.88714L40.0594 16.4899C40.9195 16.9865 41.4493 17.9041 41.4493 18.8972V34.1028C41.4493 35.0958 40.9195 36.0135 40.0594 36.51L26.891 44.1128C26.031 44.6093 24.9714 44.6093 24.1114 44.1128L10.9429 36.51C10.0829 36.0135 9.55311 35.0958 9.55311 34.1028V18.8972C9.55311 17.9041 10.0829 16.9865 10.9429 16.4899L24.1114 8.88713Z'
        fill='url(#paint6_linear_620_866)'
      />
    </g>
    <foreignObject x='0' y='0' width='51' height='53'>
      <Typography
        displayCssProp='flex'
        component='div'
        fontWeight={800}
        fontSize='19.3px'
        color='white'
        style={{
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          width: '100%',
        }}
      >
        {level}
      </Typography>
    </foreignObject>
    <defs>
      <filter
        id='filter0_i_620_866'
        x='9.55469'
        y='8.51471'
        width='31.8945'
        height='38.7502'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='BackgroundImageFix'
          result='shape'
        />
        <feColorMatrix
          in='SourceAlpha'
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
          result='hardAlpha'
        />
        <feOffset dy='2.77966' />
        <feGaussianBlur stdDeviation='1.38983' />
        <feComposite in2='hardAlpha' operator='arithmetic' k2='-1' k3='1' />
        <feColorMatrix
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0'
        />
        <feBlend
          mode='normal'
          in2='shape'
          result='effect1_innerShadow_620_866'
        />
      </filter>
      <filter
        id='filter1_d_620_866'
        x='17.8922'
        y='17.6127'
        width='12.6414'
        height='19.0652'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feColorMatrix
          in='SourceAlpha'
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
          result='hardAlpha'
        />
        <feOffset />
        <feGaussianBlur stdDeviation='1.38983' />
        <feComposite in2='hardAlpha' operator='out' />
        <feColorMatrix
          type='matrix'
          values='0 0 0 0 0.2375 0 0 0 0 0.629216 0 0 0 0 1 0 0 0 1 0'
        />
        <feBlend
          mode='normal'
          in2='BackgroundImageFix'
          result='effect1_dropShadow_620_866'
        />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='effect1_dropShadow_620_866'
          result='shape'
        />
      </filter>
      <linearGradient
        id='paint0_linear_620_866'
        x1='25.5'
        y1='0'
        x2='25.5'
        y2='10'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#0083FF' stopOpacity='0' />
        <stop offset='1' stopColor='#0083FF' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_620_866'
        x1='45.5576'
        y1='12.4044'
        x2='38.7816'
        y2='19.7587'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#0083FF' stopOpacity='0' />
        <stop offset='1' stopColor='#0083FF' />
      </linearGradient>
      <linearGradient
        id='paint2_linear_620_866'
        x1='46.5'
        y1='39.5816'
        x2='37.8397'
        y2='34.5816'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#0083FF' stopOpacity='0' />
        <stop offset='1' stopColor='#0083FF' />
      </linearGradient>
      <linearGradient
        id='paint3_linear_620_866'
        x1='24.8359'
        y1='52.2107'
        x2='24.8359'
        y2='42.2107'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#0083FF' stopOpacity='0' />
        <stop offset='1' stopColor='#0083FF' />
      </linearGradient>
      <linearGradient
        id='paint4_linear_620_866'
        x1='4.77832'
        y1='39.8063'
        x2='11.5544'
        y2='32.452'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#0083FF' stopOpacity='0' />
        <stop offset='1' stopColor='#0083FF' />
      </linearGradient>
      <linearGradient
        id='paint5_linear_620_866'
        x1='3.83594'
        y1='12.6291'
        x2='12.4962'
        y2='17.6291'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#0083FF' stopOpacity='0' />
        <stop offset='1' stopColor='#0083FF' />
      </linearGradient>
      <linearGradient
        id='paint6_linear_620_866'
        x1='25.5012'
        y1='8.08472'
        x2='25.5012'
        y2='44.9152'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#1D77FF' />
        <stop offset='1' stopColor='#8DB6FF' />
      </linearGradient>
    </defs>
  </svg>
);
