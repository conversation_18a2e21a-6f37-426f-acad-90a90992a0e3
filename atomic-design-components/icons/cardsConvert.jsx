export const cardsConvert = ({
  fill,
  stroke,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 16}
    height={height || 16}
    viewBox='0 0 16 16'
    fill='none'
  >
    <path
      d='M14.6667 10C14.6667 12.58 12.58 14.6667 10 14.6667L10.7 13.5'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M1.33398 5.99992C1.33398 3.41992 3.42065 1.33325 6.00065 1.33325L5.30065 2.49992'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M14.6667 2.79992V2.87325C14.6667 3.05992 14.52 3.20659 14.3333 3.20659H8.33333C8.14667 3.20659 8 3.05992 8 2.87325V2.79992C8 1.62659 8.29333 1.33325 9.48 1.33325H13.1867C14.3733 1.33325 14.6667 1.62659 14.6667 2.79992Z'
      fill={fill || lightest}
    />
    <path
      d='M8.33333 3.87329C8.14667 3.87329 8 4.01996 8 4.20662V4.87329V5.53329C8 6.70662 8.29333 6.99996 9.48 6.99996H13.1867C14.3733 6.99996 14.6667 6.70662 14.6667 5.53329V4.87329V4.20662C14.6667 4.01996 14.52 3.87329 14.3333 3.87329H8.33333Z'
      fill={fill || lightest}
    />
    <path
      d='M8.00065 10.4667V10.54C8.00065 10.7267 7.85398 10.8733 7.66732 10.8733H1.66732C1.48065 10.8733 1.33398 10.7267 1.33398 10.54V10.4667C1.33398 9.29333 1.62732 9 2.81398 9H6.52065C7.70732 9 8.00065 9.29333 8.00065 10.4667Z'
      fill={fill || lightest}
    />
    <path
      d='M1.66732 11.54C1.48065 11.54 1.33398 11.6867 1.33398 11.8734V12.54V13.2C1.33398 14.3734 1.62732 14.6667 2.81398 14.6667H6.52065C7.70732 14.6667 8.00065 14.3734 8.00065 13.2V12.54V11.8734C8.00065 11.6867 7.85398 11.54 7.66732 11.54H1.66732Z'
      fill={fill || lightest}
    />
  </svg>
);
