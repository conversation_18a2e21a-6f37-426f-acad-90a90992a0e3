export const rocket = ({
  fill,
  width,
  height,
  // theme: {
  //   color: {
  //     general: { light },
  //   },
  // },
}) => (
  <svg
    width={width || 20}
    height={height || 27}
    viewBox='0 0 17 16'
    fill='none'
  >
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M7.50495 11.7505L4.28846 8.53244C4.64209 7.93997 4.99442 7.32736 5.34425 6.7191C6.50734 4.69681 7.64283 2.72251 8.70657 1.6956C11.5953 -1.19458 15.5522 0.481237 15.5522 0.481237C15.5522 0.481237 17.2272 4.44005 14.3384 7.33022C13.3205 8.38573 11.3782 9.50476 9.37235 10.6604C8.74741 11.0205 8.11619 11.3841 7.50495 11.7505ZM9.95491 4.2701C9.95491 3.26981 10.7654 2.45891 11.7653 2.45891C12.765 2.45891 13.5756 3.26981 13.5756 4.2701C13.5756 5.2704 12.765 6.08131 11.7653 6.08131C10.7654 6.08131 9.95491 5.2704 9.95491 4.2701ZM5.27273 4.0046C3.64991 3.57255 2.12716 4.37255 0.776056 5.61247C0.504288 5.86189 0.561045 6.29813 0.876936 6.48853L3.05756 7.8028L3.06008 7.79855C3.37464 7.27154 3.72625 6.66107 4.0825 6.04258C4.48928 5.33633 4.90224 4.61938 5.27273 4.0046ZM8.23426 12.9819L9.5479 15.1635C9.7382 15.4796 10.1742 15.5364 10.4235 15.2645C11.6633 13.9123 12.463 12.3883 12.0302 10.7641C11.4335 11.1238 10.806 11.4861 10.1845 11.8443L10.0896 11.8989C9.46182 12.2606 8.84051 12.6186 8.24014 12.9784L8.23426 12.9819ZM3.01281 10.6965C3.32016 10.6911 3.62545 10.7477 3.91049 10.8629C4.19551 10.978 4.45444 11.1495 4.6718 11.367C4.88917 11.5844 5.06053 11.8435 5.17566 12.1287C5.29079 12.4138 5.34733 12.7192 5.3419 13.0268C5.33648 13.3343 5.2692 13.6375 5.14409 13.9185C5.01962 14.198 4.84045 14.4497 4.61715 14.6587C4.36332 14.9019 3.96615 15.0921 3.59793 15.2384C3.20532 15.3946 2.75732 15.5341 2.33905 15.6483C1.91938 15.7629 1.51856 15.8551 1.21586 15.9152C1.06554 15.945 0.932945 15.9682 0.83131 15.9821C0.782522 15.9887 0.728634 15.9949 0.678896 15.9971C0.656349 15.9981 0.615594 15.9993 0.568991 15.9949C0.546652 15.9927 0.503403 15.9876 0.451971 15.9722C0.418022 15.962 0.286142 15.9218 0.176272 15.7936C0.0821671 15.6838 0.0565474 15.5691 0.0503755 15.5415C0.0404816 15.4973 0.0374571 15.4599 0.0363126 15.44C0.0339382 15.3987 0.0357334 15.3622 0.0371383 15.3405C0.040186 15.2934 0.0466786 15.2415 0.053742 15.193C0.068362 15.0923 0.0921143 14.9607 0.122541 14.8107C0.18374 14.509 0.276656 14.1098 0.391774 13.6918C0.506491 13.2751 0.646209 12.8289 0.802413 12.4378C0.949008 12.0706 1.13908 11.675 1.38172 11.4216C1.59064 11.1982 1.8422 11.019 2.12154 10.8944C2.40233 10.7693 2.70545 10.702 3.01281 10.6965Z'
      fill={fill || '#0083FF'}
    />
  </svg>
);
