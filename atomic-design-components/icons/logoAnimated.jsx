export const logoAnimated = ({ height, width }) => {
  return (
    <svg
      width={width || '356px'}
      height={height || '167px'}
      viewBox='0 0 356 167'
      fill='none'
    >
      <defs>
        <linearGradient
          id='linear-mascot'
          x1='-47'
          x2='26'
          y1='-16'
          y2='-16'
          gradientUnits='userSpaceOnUse'
        >
          <stop className='mascot__gradient-stop' offset='0' stopOpacity='0' />
          <stop
            className='mascot__gradient-stop'
            offset='.5'
            stopOpacity='.2'
          />
          <stop className='mascot__gradient-stop' offset='1' stopOpacity='0' />
        </linearGradient>
        <g id='path-mascot' fillRule='evenodd' clipRule='evenodd'>
          <path
            d='M192.02 113.49C191.79 113.78 190.68 113.49 190.78 113.14C193.08 105.05 204.32 68.15 210.83 46.97C217.11 26.55 222.32 18.26 216.36 11.95C210.73 5.99 201.19 0.329998 196.68 0.00999816C190.05 -0.460002 191.48 12.41 186.87 40.52C181.65 72.36 161.56 139.3 158.24 144.13C154.92 148.96 168.4 156.77 177.24 161.6C186.09 166.43 195.66 145.77 201.74 138.8C207.82 131.83 208.16 138.74 217.56 147.32C226.96 155.9 243.55 164.49 255.71 159.12C267.87 153.76 239.38 141.07 232.74 132.49C226.1 123.91 221.98 112.99 221.98 112.99C221.98 112.99 242.29 87.5 252.25 81.07C262.2 74.63 263.45 67.93 255.71 60.96C247.97 53.99 243.54 51.13 235.7 59.43C227.86 67.73 197.95 105.96 192 113.51L192.02 113.49Z'
            fill='#394F69'
          />
          <path
            d='M126.2 72.08C127.86 64.65 136.28 68.98 144.49 74.48C156.4 82.47 152.96 84.49 149.47 98.45C146.35 110.92 132.78 156.16 126.89 160.07C121 163.98 105.31 154.31 107.3 148.41C119.38 112.47 126.21 72.08 126.21 72.08H126.2Z'
            fill='#394F69'
          />
          <path
            d='M120.73 37.45C110.83 21.09 119.8 13.43 126.81 13.21C133.82 12.99 133.91 23.83 139.59 19.88C145.27 15.93 154.78 2.11999 163.72 14.99C174.73 30.84 157.34 44.47 144.64 55.8C136.02 63.49 120.73 37.45 120.73 37.45Z'
            fill='#0083FF'
          />
          <path
            d='M350 101.94C364.6 88.3 344.84 61.56 324.83 51.59C316.23 47.3 297.55 56.3 280.25 77.81C262.95 99.32 249.4 144.76 287.28 156.5C326.21 168.57 354.33 137.09 347.84 132.89C341.35 128.69 322.43 136.04 299.72 138.14C292.91 138.77 279.94 132.6 288.05 115.81C308.06 118.96 335.4 115.58 350 101.95V101.94ZM315.06 76.72C319.39 75.67 334.75 81.61 329.89 90C325.42 97.71 308.45 98.85 297.27 97.3C292.52 96.64 311.63 77.55 315.06 76.72Z'
            fill='#394F69'
          />
          <path
            d='M86.56 108.36C92.16 107.59 103.3 131.48 100.11 136.66C87.64 156.92 24.37 168.55 20.38 166.56C14.29 163.51 6.53002 157.57 4.04002 155C-2.04998 148.72 1.90735e-05 151.33 15.88 113.09C34.73 67.7 42.09 16.46 42.09 5.30001C42.09 -5.85999 61.27 4.34001 73.89 14.2C84.22 22.27 49.04 112.34 34.2 132.7C30.21 138.18 28.76 139.51 36.32 136.65C73.72 122.51 79.44 109.33 86.55 108.35L86.56 108.36Z'
            fill='#394F69'
          />
        </g>
        <mask
          id='mask-mascot'
          width='356'
          height='167'
          x='0'
          y='0'
          maskType='alpha'
          maskUnits='userSpaceOnUse'
        >
          <use href='#path-mascot' />
        </mask>
      </defs>
      <use href='#path-mascot' />
      <g mask='url(#mask-mascot)'>
        <path
          fill='url(#linear-mascot)'
          d='M-47-16h356v187H-47z'
          style={{
            animation: 'mascot-animation 2s ease-in-out infinite',
            fill: 'url(#linear-mascot)',
          }}
        />
      </g>
    </svg>
  );
};
