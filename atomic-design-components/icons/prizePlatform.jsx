import Typography from '../atoms/Typography';

export const prizePlatform = ({
  width,
  height,
  firstName,
  firstPrize,
  secondName,
  secondPrize,
  thirdName,
  thirdPrize,
  theme: {
    font: {
      weight: { bold },
    },
    color: {
      general: { darkest },
    },
  },
}) => (
  <svg
    width={width || 590}
    height={height || 222}
    viewBox='0 0 590 222'
    fill='none'
  >
    <g clipPath='url(#clip0_819_2680)'>
      <g filter='url(#filter0_f_819_2680)'>
        <ellipse cx='295' cy='209' rx='240' ry='7' fill={darkest} />
      </g>
      <rect
        x='535'
        y='208'
        width='3.00002'
        height='480'
        transform='rotate(90 535 208)'
        fill={darkest}
      />
      <rect
        x='375'
        y='211'
        width='3.00001'
        height='160'
        transform='rotate(90 375 211)'
        fill={darkest}
      />
      <circle
        cx='295'
        cy='62'
        r='90'
        fill='url(#paint0_linear_819_2680)'
        fillOpacity='0.2'
      />
      <circle
        cx='455'
        cy='110'
        r='60'
        fill='url(#paint1_linear_819_2680)'
        fillOpacity='0.2'
      />
      <circle
        cx='135'
        cy='110'
        r='60'
        fill='url(#paint2_linear_819_2680)'
        fillOpacity='0.2'
      />
      <path d='M375 208H535V156H375V208Z' fill='url(#paint3_linear_819_2680)' />
      <path
        d='M373.931 156H535L526.943 148H371L373.931 156Z'
        fill='url(#paint4_linear_819_2680)'
      />
      <path d='M215 157H55V208H215V157Z' fill='url(#paint5_linear_819_2680)' />
      <path
        d='M218.033 157H55L63.1553 149H221L218.033 157Z'
        fill='url(#paint6_linear_819_2680)'
      />
      <path d='M375 141H215V211H375V141Z' fill='url(#paint7_linear_819_2680)' />
      <path
        d='M375 141H215L221.549 132H368.454L375 141Z'
        fill='url(#paint8_linear_819_2680)'
      />
      <rect
        x='85'
        y='58'
        width='100'
        height='100'
        fill='url(#pattern0_819_2680)'
      />
      <rect
        x='405'
        y='58'
        width='100'
        height='100'
        fill='url(#pattern1_819_2680)'
      />
      <rect
        x='220'
        y='-2'
        width='150'
        height='150'
        fill='url(#pattern2_819_2680)'
      />
      <foreignObject x='375' y='159' width='160' height='26'>
        <Typography
          displayCssProp='flex'
          component='div'
          type='body2'
          fontWeight={bold}
          color={darkest}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 'auto',
            textWrap: 'nowrap',
          }}
        >
          {thirdPrize}
        </Typography>
      </foreignObject>
      <foreignObject x='375' y='186' width='160' height='18'>
        <Typography
          displayCssProp='flex'
          component='div'
          type='label1'
          color={darkest}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 'auto',
            textWrap: 'nowrap',
          }}
          className='max-sm:!hidden'
        >
          {thirdName}
        </Typography>
      </foreignObject>
      <foreignObject x='55' y='159' width='160' height='26'>
        <Typography
          displayCssProp='flex'
          component='div'
          type='body2'
          fontWeight={bold}
          color={darkest}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 'auto',
            textWrap: 'nowrap',
          }}
        >
          {secondPrize}
        </Typography>
      </foreignObject>
      <foreignObject x='55' y='186' width='160' height='18'>
        <Typography
          displayCssProp='flex'
          component='div'
          type='label1'
          color={darkest}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 'auto',
          }}
          className='max-sm:!hidden'
        >
          {secondName}
        </Typography>
      </foreignObject>
      <foreignObject x='215' y='154' width='160' height='26'>
        <Typography
          displayCssProp='flex'
          component='div'
          type='body2'
          fontWeight={bold}
          color={darkest}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 'auto',
          }}
        >
          {firstPrize}
        </Typography>
      </foreignObject>
      <foreignObject x='215' y='180' width='160' height='18'>
        <Typography
          displayCssProp='flex'
          component='div'
          type='label1'
          color={darkest}
          style={{
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            width: 'auto',
          }}
          className='max-sm:!hidden'
        >
          {firstName}
        </Typography>
      </foreignObject>
    </g>
    <defs>
      <filter
        id='filter0_f_819_2680'
        x='5'
        y='152'
        width='580'
        height='114'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='BackgroundImageFix'
          result='shape'
        />
        <feGaussianBlur
          stdDeviation='25'
          result='effect1_foregroundBlur_819_2680'
        />
      </filter>
      <pattern
        id='pattern0_819_2680'
        patternContentUnits='objectBoundingBox'
        width='1'
        height='1'
      >
        <use href='#image0_819_2680' transform='scale(0.00195312)' />
      </pattern>
      <pattern
        id='pattern1_819_2680'
        patternContentUnits='objectBoundingBox'
        width='1'
        height='1'
      >
        <use href='#image1_819_2680' transform='scale(0.00195312)' />
      </pattern>
      <pattern
        id='pattern2_819_2680'
        patternContentUnits='objectBoundingBox'
        width='1'
        height='1'
      >
        <use href='#image2_819_2680' transform='scale(0.00195312)' />
      </pattern>
      <linearGradient
        id='paint0_linear_819_2680'
        x1='295'
        y1='-28'
        x2='295'
        y2='152'
        gradientUnits='userSpaceOnUse'
      >
        <stop offset='0.4' stopColor='#232C3B' stopOpacity='0' />
        <stop offset='1' stopColor='#D3B517' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_819_2680'
        x1='455'
        y1='50'
        x2='455'
        y2='170'
        gradientUnits='userSpaceOnUse'
      >
        <stop offset='0.4' stopColor='#232C3B' stopOpacity='0' />
        <stop offset='1' stopColor='#CD7F32' />
      </linearGradient>
      <linearGradient
        id='paint2_linear_819_2680'
        x1='135'
        y1='50'
        x2='135'
        y2='170'
        gradientUnits='userSpaceOnUse'
      >
        <stop offset='0.4' stopColor='#232C3B' stopOpacity='0' />
        <stop offset='1' stopColor='#C0C0C0' />
      </linearGradient>
      <linearGradient
        id='paint3_linear_819_2680'
        x1='455.002'
        y1='208'
        x2='455.002'
        y2='155.998'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#C7C7C7' />
        <stop offset='0.56' stopColor='#E9E9E9' />
        <stop offset='0.99' stopColor='#E5E5E5' />
      </linearGradient>
      <linearGradient
        id='paint4_linear_819_2680'
        x1='453'
        y1='156'
        x2='453'
        y2='148'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#C7C7C7' />
        <stop offset='0.03' stopColor='#F2F2F2' />
        <stop offset='1' stopColor='#F2F2F2' />
      </linearGradient>
      <linearGradient
        id='paint5_linear_819_2680'
        x1='135.002'
        y1='208.001'
        x2='135.002'
        y2='157'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#C7C7C7' />
        <stop offset='0.38' stopColor='#E9E9E9' />
        <stop offset='0.99' stopColor='#E5E5E5' />
      </linearGradient>
      <linearGradient
        id='paint6_linear_819_2680'
        x1='138'
        y1='157'
        x2='138'
        y2='149'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#F1F1F1' />
        <stop offset='0.03' stopColor='#F2F2F2' />
        <stop offset='0.68' stopColor='#F2F2F2' />
      </linearGradient>
      <linearGradient
        id='paint7_linear_819_2680'
        x1='295.002'
        y1='211.001'
        x2='295.002'
        y2='141'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#C7C7C7' />
        <stop offset='0.99' stopColor='#E5E5E5' />
      </linearGradient>
      <linearGradient
        id='paint8_linear_819_2680'
        x1='295.002'
        y1='141'
        x2='295.002'
        y2='132'
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#C7C7C7' />
        <stop offset='0.03' stopColor='#F2F2F2' />
        <stop offset='0.96' stopColor='white' />
      </linearGradient>
      <clipPath id='clip0_819_2680'>
        <rect width='590' height='222' fill='white' />
      </clipPath>
      <image
        id='image0_819_2680'
        xlinkHref='/secondPl.png'
        height='512'
        width='512'
      />
      <image
        id='image1_819_2680'
        xlinkHref='/thirdPl.png'
        height='512'
        width='512'
      />
      <image
        id='image2_819_2680'
        xlinkHref='/firstPl.png'
        height='512'
        width='512'
      />
    </defs>
  </svg>
);
