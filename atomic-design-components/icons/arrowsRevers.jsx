export const arrowsRevers = ({
  stroke,
  width,
  height,
  theme: {
    color: {
      general: { lightest },
    },
  },
}) => (
  <svg
    width={width || 24}
    height={height || 24}
    viewBox='0 0 24 24'
    fill='none'
  >
    <path
      d='M10.45 6.71997L6.72998 3L3.01001 6.71997'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M6.72998 21V3'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M13.55 17.28L17.27 21L20.99 17.28'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M17.27 3V21'
      stroke={stroke || lightest}
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);
