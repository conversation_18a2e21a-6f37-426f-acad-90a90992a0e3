export const disappointedFaceCircle = ({
  fill,
  width,
  height,
  theme: {
    color: {
      general: { white },
    },
  },
}) => (
  <svg
    width={width || 128}
    height={height || 127}
    fill='none'
    viewBox='0 0 128 127'
  >
    <g clipPath='url(#clip0_1195_2547)'>
      <circle cx='64.4337' cy='63.7391' r='63.4415' fill='#255896' />
      <g filter='url(#filter0_f_1195_2547)'>
        <circle cx='64.9194' cy='63.4104' r='31.9194' fill='#0183FF' />
      </g>
      <path
        d='M63.9961 98.0002C45.7997 98.0002 30.9961 83.1967 30.9961 65.0002C30.9961 46.8038 45.7997 32.0002 63.9961 32.0002C82.1925 32.0002 96.9961 46.8038 96.9961 65.0002C96.9961 83.1967 82.1925 98.0002 63.9961 98.0002ZM63.9961 36.1252C48.0746 36.1252 35.1211 49.0788 35.1211 65.0002C35.1211 80.9217 48.0746 93.8752 63.9961 93.8752C79.9176 93.8752 92.8711 80.9217 92.8711 65.0002C92.8711 49.0788 79.9176 36.1252 63.9961 36.1252ZM79.3566 79.2202C80.3754 78.7107 80.789 77.4722 80.2795 76.4523C80.0867 76.0686 75.4512 67.0627 63.9961 67.0627C52.541 67.0627 47.9055 76.0686 47.7137 76.4523C47.2063 77.467 47.6167 78.6983 48.6294 79.2109C49.6442 79.7213 50.8817 79.3171 51.3994 78.3065C51.5479 78.0157 55.148 71.1877 63.9961 71.1877C72.8442 71.1877 76.4443 78.0157 76.5887 78.2982C76.9507 79.0211 77.6787 79.4388 78.4357 79.4377C78.745 79.4377 79.0596 79.3687 79.3566 79.2202ZM74.3086 60.8752C72.0337 60.8752 70.1836 59.0252 70.1836 56.7502C70.1836 54.4753 72.0337 52.6252 74.3086 52.6252C76.5835 52.6252 78.4336 54.4753 78.4336 56.7502C78.4336 59.0252 76.5835 60.8752 74.3086 60.8752ZM53.6836 60.8752C51.4087 60.8752 49.5586 59.0252 49.5586 56.7502C49.5586 54.4753 51.4087 52.6252 53.6836 52.6252C55.9585 52.6252 57.8086 54.4753 57.8086 56.7502C57.8086 59.0252 55.9585 60.8752 53.6836 60.8752Z'
        fill={fill || white}
      />
    </g>
    <defs>
      <filter
        id='filter0_f_1195_2547'
        x='-8.34328'
        y='-9.85231'
        width='146.526'
        height='146.525'
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity='0' result='BackgroundImageFix' />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='BackgroundImageFix'
          result='shape'
        />
        <feGaussianBlur
          stdDeviation='20.6716'
          result='effect1_foregroundBlur_1195_2547'
        />
      </filter>
      <clipPath id='clip0_1195_2547'>
        <rect
          width='126.63'
          height='126.63'
          fill={fill || white}
          transform='translate(0.832031 0.297607)'
        />
      </clipPath>
    </defs>
  </svg>
);
