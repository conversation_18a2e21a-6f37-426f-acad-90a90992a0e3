import React from 'react'

export const gifts = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      secondary: { main },
    },
  },
}) => (
  <svg width={width || 73} height={height || 73} viewBox="0 0 73 73" fill="none">
    <circle cx="36.5" cy="36.5" r="36.5" fill={fill || '#292D3A'} />
    <circle cx="36.5" cy="36.5" r="32" fill={fill || '#292D3A'} stroke={stroke || '#0083FF'} />
    <path
      d="M38.3756 51.0626H37.2506V35.3125H44.5629V36.0382C44.5629 36.3489 44.8146 36.6007 45.1254 36.6007C45.4362 36.6007 45.6879 36.3489 45.6879 36.0382V35.2085C46.3418 34.975 46.8129 34.3563 46.8129 33.625V30.25C46.8129 29.7086 46.5527 29.2319 46.1548 28.9225C47.3768 28.4177 48.4976 27.6723 49.4609 26.7105C49.6071 26.5642 49.662 26.3491 49.6015 26.1494C49.541 25.9511 49.3765 25.8021 49.174 25.7613L46.7285 25.2719L46.2391 22.8264C46.1927 22.5958 46.0099 22.42 45.7779 22.382C45.5431 22.3426 45.3152 22.4551 45.1999 22.6576C45.0002 23.0064 44.7837 23.3397 44.5572 23.6659C44.5277 22.4341 44.0369 21.2781 43.1608 20.402C42.2566 19.4978 41.0571 19 39.7816 19C37.4093 19 35.4404 20.7395 35.0693 23.0079C34.5869 22.6184 33.9808 22.3751 33.3129 22.3751C32.6449 22.3751 32.0402 22.6184 31.5565 23.0079C31.1852 20.7382 29.2165 19 26.8441 19C25.5686 19 24.3691 19.4978 23.4649 20.402C22.5874 21.2781 22.0966 22.4341 22.0685 23.6659C21.8421 23.3397 21.6255 23.0064 21.4258 22.6576C21.3091 22.4537 21.0785 22.3441 20.8478 22.382C20.6158 22.42 20.4316 22.5972 20.3866 22.8264L19.8972 25.2719L17.4517 25.7613C17.2478 25.8021 17.0833 25.9511 17.0242 26.1494C16.9638 26.3477 17.0186 26.5642 17.1649 26.7105C18.1281 27.6724 19.2489 28.4177 20.4709 28.9225C20.073 29.2319 19.8128 29.7086 19.8128 30.25V33.625C19.8128 34.3577 20.2839 34.9764 20.9378 35.2085V50.5002C20.9378 51.4312 21.6944 52.1877 22.6253 52.1877H38.3753C38.6861 52.1877 38.9378 51.936 38.9378 51.6252C38.9378 51.3145 38.6864 51.0626 38.3756 51.0626ZM29.3756 34.1875V29.6875H37.2506V34.1875H29.3756ZM30.5006 28.5625H28.8131C27.8822 28.5625 27.1256 27.8059 27.1256 26.875C27.1256 25.9441 27.8822 25.1875 28.8131 25.1875C29.744 25.1875 30.5006 25.9441 30.5006 26.875V28.5625ZM36.1256 26.875C36.1256 25.9441 36.8822 25.1875 37.8131 25.1875C38.744 25.1875 39.5006 25.9441 39.5006 26.875C39.5006 27.8059 38.744 28.5625 37.8131 28.5625H36.1256V26.875ZM45.1256 29.6875C45.4364 29.6875 45.6881 29.9392 45.6881 30.25V33.625C45.6881 33.9358 45.4364 34.1875 45.1256 34.1875H38.3756V29.6875H45.1256ZM45.411 24.4182L45.6993 25.861C45.7443 26.0832 45.9173 26.2576 46.1409 26.3026L47.8453 26.6443C46.2619 27.8888 44.3198 28.5638 42.2736 28.5638H40.8434C42.0429 27.8776 43.1229 27.0282 44.0693 26.0396C44.0707 26.0382 44.0721 26.0354 44.0749 26.034C44.5559 25.5306 45.0045 24.9934 45.4123 24.4196L45.411 24.4182ZM39.7817 20.1248C40.7563 20.1248 41.6731 20.5059 42.365 21.1978C43.0569 21.8897 43.438 22.8066 43.438 23.7811C43.438 24.3169 43.3466 24.8414 43.1722 25.3434C42.3988 26.1365 41.5269 26.827 40.572 27.405C40.6044 27.2334 40.6255 27.0563 40.6255 26.8748C40.6255 25.3237 39.3641 24.0623 37.813 24.0623C37.1774 24.0623 36.5966 24.2817 36.1255 24.6389V23.7811C36.1255 21.7645 37.7653 20.1248 39.7817 20.1248ZM33.3129 23.4998C34.2438 23.4998 35.0004 24.2564 35.0004 25.1873V28.5623H31.6254V25.1873C31.6254 24.2564 32.382 23.4998 33.3129 23.4998ZM24.2607 21.1978C24.9526 20.5059 25.8695 20.1248 26.844 20.1248C28.8606 20.1248 30.5002 21.7645 30.5002 23.781V24.6388C30.0291 24.2816 29.4483 24.0622 28.8127 24.0622C27.2616 24.0622 26.0002 25.3236 26.0002 26.8747C26.0002 27.0561 26.0213 27.2319 26.0536 27.4049C25.1002 26.8269 24.2269 26.1365 23.4549 25.3433C23.2805 24.8413 23.1877 24.3167 23.1877 23.781C23.1877 22.8065 23.5689 21.8897 24.2607 21.1978ZM20.4865 26.3012C20.7087 26.2562 20.8831 26.0832 20.9281 25.8596L21.2164 24.4168C21.6228 24.9906 22.0714 25.5277 22.5537 26.0312C22.5551 26.0326 22.5579 26.0354 22.5593 26.0368C23.5057 27.0254 24.5857 27.8748 25.7838 28.561H24.3537C22.3076 28.561 20.3656 27.886 18.7819 26.6415L20.4863 26.2998L20.4865 26.3012ZM28.2502 29.6874V34.1874H21.5003C21.1895 34.1874 20.9378 33.9357 20.9378 33.6249V30.2499C20.9378 29.9391 21.1895 29.6874 21.5003 29.6874H28.2502ZM22.0626 50.4998V35.3121H29.3749V51.0621H22.6249C22.3141 51.0621 22.0626 50.8106 22.0626 50.4998ZM30.5002 51.0623V35.3123H36.1252V51.0623H30.5002ZM51.3125 41.5H50.7359C51.0931 41.0289 51.3125 40.4481 51.3125 39.8125C51.3125 38.2614 50.0511 36.9999 48.5 36.9999C47.5761 36.9999 46.7633 37.4528 46.25 38.1418C45.7367 37.4528 44.9239 36.9999 44 36.9999C42.4489 36.9999 41.1875 38.2614 41.1875 39.8125C41.1875 40.4481 41.4069 41.0289 41.7641 41.5H41.1875C40.2566 41.5 39.5 42.2565 39.5 43.1875V53.3125C39.5 54.2434 40.2566 55 41.1875 55H51.3125C52.2434 55 53 54.2434 53 53.3125V43.1875C53 42.2565 52.2434 41.5 51.3125 41.5ZM51.875 43.1875V44.875H46.8127V42.625H51.3127C51.6235 42.625 51.875 42.8767 51.875 43.1875ZM48.5 38.1251C49.4309 38.1251 50.1875 38.8817 50.1875 39.8126C50.1875 40.7436 49.4309 41.5001 48.5 41.5001H46.8125V39.8126C46.8125 38.8817 47.5691 38.1251 48.5 38.1251ZM42.3123 39.8126C42.3123 38.8817 43.0689 38.1251 43.9998 38.1251C44.9308 38.1251 45.6873 38.8817 45.6873 39.8126V41.5001H43.9998C43.0689 41.5001 42.3123 40.7436 42.3123 39.8126ZM40.6249 43.1876C40.6249 42.8769 40.8766 42.6251 41.1874 42.6251H45.6873V44.8751H40.625L40.6249 43.1876ZM40.6249 53.3127V46.0003H45.6872V53.8754H41.1872C40.8764 53.8754 40.6249 53.6235 40.6249 53.3127ZM51.8748 53.3127C51.8748 53.6235 51.6231 53.8752 51.3123 53.8752H46.8123V46.0002H51.8746L51.8748 53.3127Z"
      fill={stroke || '#0083FF'}
    />
  </svg>
)
