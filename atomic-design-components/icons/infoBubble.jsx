import React from 'react'

export const infoBubble = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  strokeOpacity,
  fillCorner,
  colorText,
  theme: {
    color: {
      secondary: { main },
    },
  },
}) => (
  <svg width={width || 18} height={height || 18} viewBox="0 0 18 18" fill="none">
    <path
      d="M8.9998 11.7V8.99999M8.9998 6.29999V6.36766M16.1998 8.99999C16.1998 10.035 15.9814 11.019 15.5882 11.9085L16.2012 16.1993L12.524 15.28C11.4823 15.8658 10.2801 16.2 8.9998 16.2C5.02335 16.2 1.7998 12.9764 1.7998 8.99999C1.7998 5.02354 5.02335 1.79999 8.9998 1.79999C12.9763 1.79999 16.1998 5.02354 16.1998 8.99999Z"
      stroke={stroke || main}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
)
