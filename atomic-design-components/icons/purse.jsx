import React from 'react'

export const purse = ({
  fill,
  stroke,
  strokeWidth,
  width,
  height,
  theme: {
    color: {
      secondary: { main },
    },
  },
}) => (
  <svg width={width || 73} height={height || 73} viewBox="0 0 73 73" fill="none">
    <circle cx="36.5" cy="36.5" r="36.5" fill={fill || '#292D3A'} />
    <circle cx="36.5" cy="36.5" r="32" fill={fill || '#292D3A'} stroke={stroke || '#0083FF'} />
    <path
      d="M37.4779 40.337C37.4779 41.3956 38.3353 42.253 39.3939 42.253C40.4526 42.253 41.3099 41.3956 41.3099 40.337C41.3099 39.2783 40.4526 38.4209 39.3939 38.4209C38.3353 38.4209 37.4779 39.2783 37.4779 40.337ZM39.9534 40.337C39.9534 40.6469 39.6998 40.8965 39.3939 40.8965C39.084 40.8965 38.8344 40.6429 38.8344 40.337C38.8344 40.027 39.088 39.7775 39.3939 39.7775C39.7039 39.7734 39.9534 40.027 39.9534 40.337Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M23.7073 27.6855H47.5693C47.9436 27.6855 48.2455 27.3836 48.2455 27.0092C48.2455 26.6348 47.9436 26.3329 47.5693 26.3329H23.7073C23.3329 26.3329 23.0311 26.6348 23.0311 27.0092C23.0311 27.3836 23.3289 27.6855 23.7073 27.6855Z"
      fill={stroke || '#0083FF'}
    />
    <path
      d="M52.2024 27.0012C52.2024 24.7953 50.4031 23 48.1892 23H24.0132C22.9747 23 21.9402 23.4267 21.1754 24.1754C20.4267 24.9201 20 25.9505 20 27.0012V46.9988C20 49.2047 21.7993 51 24.0132 51H48.1892C49.2639 51 50.2703 50.5814 51.027 49.8246C51.7838 49.0679 52.2024 48.0615 52.2024 46.9988V33.6711C52.2024 33.659 52.1984 33.6469 52.1984 33.6308V27.0213C52.1984 27.0132 52.2024 27.0092 52.2024 27.0012ZM22.1294 25.1415C22.6366 24.6423 23.3249 24.3565 24.0132 24.3565H48.1892C49.6504 24.3565 50.8419 25.5359 50.8459 26.9931C50.8459 26.9971 50.8419 27.0012 50.8419 27.0092V30.6883C50.1334 30.0564 49.2116 29.6619 48.1892 29.6619H24.0132C22.548 29.6619 21.3565 28.4704 21.3565 27.0052C21.3565 26.3088 21.6383 25.6285 22.1294 25.1415ZM50.8459 42.9856H39.4301C37.973 42.9856 36.7855 41.7941 36.7855 40.3289C36.7855 39.6164 37.0592 38.9563 37.5704 38.4572C38.0575 37.958 38.7177 37.6843 39.4301 37.6843H50.8459V42.9856ZM50.8459 36.3278H39.4301C38.3473 36.3278 37.345 36.7464 36.6124 37.4951C35.8516 38.2398 35.433 39.2461 35.433 40.3249C35.433 42.5388 37.2283 44.3381 39.4342 44.3381H50.8499V46.9948C50.8499 47.6952 50.5722 48.3594 50.073 48.8626C49.5699 49.3657 48.9057 49.6394 48.1932 49.6394H24.0132C22.548 49.6394 21.3565 48.452 21.3565 46.9948V29.9839C22.065 30.6159 22.9908 31.0144 24.0132 31.0144H48.1892C49.6423 31.0144 50.8217 32.1857 50.8419 33.6308V33.6711C50.8419 33.6791 50.8459 33.6832 50.8459 33.6912V36.3278Z"
      fill={stroke || '#0083FF'}
    />
  </svg>
)
