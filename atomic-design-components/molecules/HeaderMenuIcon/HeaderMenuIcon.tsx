import clsx from 'clsx';
import PropTypes from 'prop-types';
import { useCallback, useEffect, useState } from 'react';

import Icon from '../../atoms/Icon';
import { StyledHeaderMenuIcon } from './styled';

interface HeaderMenuIconProps {
  fill?: string;
  className?: string;
  opened?: boolean;
  iconName?: string;
  onClick?: () => void;
  width?: number;
  wrapperColor?: string;
}
const HeaderMenuIcon = ({
  fill = '',
  className,
  opened,
  iconName,
  onClick,
  width,
  wrapperColor,
  ...otherProps
}: HeaderMenuIconProps) => {
  const [clicked, setClicked] = useState(opened);

  const onIconClick = useCallback(() => {
    setClicked((prevState) => !prevState);
    if (onClick) {
      onClick();
    }
  }, [onClick]);

  useEffect(() => {
    setClicked(opened);
  }, [opened]);

  return (
    <StyledHeaderMenuIcon
      className={className}
      onClick={onIconClick}
      {...otherProps}
    >
      <Icon
        wrapperColor={wrapperColor}
        width={width}
        name={iconName || 'hamburgerMenu'}
        fill={fill}
        className={clsx(clicked && 'clicked')}
      />
    </StyledHeaderMenuIcon>
  );
};

export default HeaderMenuIcon;

HeaderMenuIcon.propTypes = {
  className: PropTypes.string,
  fill: PropTypes.string,
  onClick: PropTypes.func,
};
