'use client';
import styled from 'styled-components';

import { getTokens } from '../../';

export const StyledButton = styled.button`
  cursor: pointer;
  outline: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  white-space: nowrap;
  ${({ theme, size }) => getTokens(`typography-sub3-black-${size}`, theme)};

  &.withIcon {
    min-width: inherit;
    text-align: left;
  }

  &.primary {
    &:hover:not(.disabled) {
      background-color: ${({ theme }) => theme.color?.primary.dark};
    }
  }
  &.secondary {
    &:hover:not(.disabled) {
      background-color: ${({ theme }) => theme.color?.general.dark};
    }
  }
  &.transparent {
    &:hover:not(.disabled) {
      background-color: ${({ theme }) => theme.color?.general.darker};
    }
  }
  &.withBorder {
    border: 1px solid ${({ theme }) => theme.color?.general.dark};
  }
  &&.disabled {
    background-color: #546073;
    color: ${({ theme }) => theme.color?.general.lighter};
    cursor: not-allowed;
  }

  &.primary,
  &.secondary,
  &.transparent {
    ${({ variant, size, theme }) =>
      getTokens(`button-standard-${variant}-${size}`, theme)};
    background-color: ${({ $backgroundColor }) => $backgroundColor};
    border-radius: ${({ $borderRadius }) => $borderRadius};
    padding: ${({ padding }) => padding};
  }

  &.fullWidth {
    width: 100%;
  }
`;
