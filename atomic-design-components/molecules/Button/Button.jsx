import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';
import React from 'react';
import { withTheme } from 'styled-components';

import { useEffect } from 'react';
import { useTimeout } from '../../../hooks/useTimeout';
import Icon from '../../atoms/Icon';
import Tooltip from '../Tooltip';
import { StyledButton } from './styled';
import { theme as defaultTheme } from '@/theme';

const Button = (props) => {
  const {
    as,
    backgroundColor,
    borderRadius,
    children,
    className,
    color,
    fullWidth,
    disabled,
    disableType = 'opacity',
    iconName,
    iconSize,
    iconRightProps,
    iconLeftProps,
    onClick,
    padding,
    size = 'medium',
    text,
    tooltipProps,
    theme = defaultTheme,
    uppercase,
    variant = 'primary',
    withIcon,
    withLetterSpacing = true,
    confirmButtonProps,
    t,
    withBorder,
    ...otherProps
  } = props;

  const [isConfirmDeleteActive, setIsConfirmDeleteActive] = React.useState(false);
  const deleteConfirmRef = React.useRef(null);

  const onDeleteClick = (e) => {
    e.preventDefault();
    setIsConfirmDeleteActive(true);
  };

  useTimeout(
    () => {
      setIsConfirmDeleteActive(false);
    },
    3000,
    [isConfirmDeleteActive],
  );

  const onDeleteClickConfirm = (e) => {
    e.preventDefault();
    if (confirmButtonProps.onDeleteConfirm) {
      confirmButtonProps.onDeleteConfirm(e);
    }

    if (onClick) {
      onClick(e);
    }

    deleteConfirmRef.current = setTimeout(() => {
      setIsConfirmDeleteActive(false);
    }, 1000);
  };

  useEffect(() => {
    return () => {
      clearTimeout(deleteConfirmRef.current);
    };
  }, []);

  const confirmButtonPropsExtended =
    confirmButtonProps && isConfirmDeleteActive
      ? {
          backgroundColor: confirmButtonProps.backgroundColor || theme.color?.general.white,
          color: confirmButtonProps.color || theme.color?.status.error,
          variant: confirmButtonProps.variant || 'primary',
          onClick: onDeleteClickConfirm,
        }
      : {};

  const confirmIconProps =
    confirmButtonProps && isConfirmDeleteActive && confirmButtonProps.iconLeftProps;
  const confirmText = confirmButtonProps && isConfirmDeleteActive && t('clickToConfirm');
  const hasChildren = !!children || !!confirmText || !!text;

  return (
    <Tooltip {...tooltipProps} disableTooltip={tooltipProps ? tooltipProps.disableTooltip : true}>
      <StyledButton
        as={as}
        $backgroundColor={backgroundColor}
        $borderRadius={borderRadius}
        $disableType={disableType}
        disabled={disabled}
        onClick={confirmButtonProps ? onDeleteClick : onClick}
        padding={padding}
        size={size}
        variant={variant}
        theme={theme}
        className={clsx(
          className,
          variant,
          disabled && 'disabled',
          fullWidth && 'fullWidth',
          (withIcon || iconName || iconLeftProps || iconRightProps) && 'withIcon',
          uppercase && 'uppercase',
          withBorder && 'withBorder',
          withLetterSpacing && 'withLetterSpacing',
          confirmButtonProps && isConfirmDeleteActive && 'confirmState',
          confirmButtonProps && isConfirmDeleteActive && confirmButtonProps?.classNameConfirm,
        )}
        color={color}
        {...otherProps}
        {...confirmButtonPropsExtended}
      >
        {iconLeftProps && (
          <Icon
            fill={
              disabled
                ? theme.components.button.disabled?.large?.color
                : color || theme.color?.general.white
            }
            margin={hasChildren ? '0 6px 0 0' : '0'}
            {...iconLeftProps}
          />
        )}
        {children || confirmText || text}
        {(iconName || iconRightProps) && (
          <Icon
            name={iconName}
            fill={
              disabled
                ? theme.components.button.disabled?.large?.color
                : color || theme.color?.general.white
            }
            size={iconSize || 16}
            margin='0 0 0 8px'
            {...iconRightProps}
            {...confirmIconProps}
          />
        )}
      </StyledButton>
    </Tooltip>
  );
};

export default withTheme(Button);

Button.propTypes = {
  backgroundColor: T.string,
  borderRadius: T.string,
  children: T.node,
  className: T.string,
  color: T.string,
  disabled: T.bool,
  disableType: T.oneOf(['opacity', 'color']),
  fullWidth: T.bool,
  iconName: T.string,
  iconRightProps: T.object,
  iconLeftProps: T.object,
  onClick: T.func,
  padding: T.string,
  size: T.oneOf(['small', 'medium', 'large']),
  text: T.string,
  tooltipProps: T.object,
  uppercase: T.bool,
  variant: T.oneOf(['primary', 'secondary', 'success', 'transparent']),
  withIcon: T.bool,
  withLetterSpacing: T.bool,
};
