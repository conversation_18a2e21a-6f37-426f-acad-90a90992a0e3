'use client';
import styled from 'styled-components';

export const StyledTabs = styled.div<any>`
  &.hideTabsTitles {
    padding: 0;

    & > .tabsTitles {
      display: none;
    }
  }

  .rts___tabs___container {
    padding: 0;
    width: 100%;

    .rts___nav___btn {
      border: none;
      padding: 0;

      &:disabled.rts___btn svg path {
        stroke: ${({ backgroundColor, theme }) =>
          backgroundColor || theme.color?.general.dark};
      }

      &.left-nav-btn {
        padding-right: 10px;
      }

      &.right-nav-btn {
        padding-left: 10px;

        svg {
          transform: rotate(180deg);
        }
      }

      &:hover {
        background: none;
        transition: none;
      }
    }

    .rts___tab {
      margin: 0;
      position: relative;
      background: none;
      box-shadow: none;
      border: none;
      border-radius: 0;
      padding: ${({ tabPadding }) => tabPadding || '8px 16px'};
      width: 100%;

      &:not(:first-child) {
        margin-left: 1px;
      }

      .typography {
        .icon {
          margin: 0 4px 0 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }

  &.inlines:not(.verticalPaymentMethods) > {
    .tabsTitles {
      .rts___tabs___container {
        .rts___tabs {
          border-bottom: 1px solid #334155;
          padding: 0;
          width: 100%;

          .rts___tab {
            padding: ${({ tabPadding }) => tabPadding || '8px 16px 6px'};
            border-bottom: 2px solid transparent;

            .typography {
              color: ${({ theme }) => theme.color?.general.lighter};
            }

            &.rts___tab___selected {
              border-bottom: 2px solid ${({ theme }) => theme.color?.primary.main};

              .typography {
                color: ${({ theme }) => theme.color?.general.lightest};
              }
            }

            &:not(.rts___tab___selected):hover {
              border-bottom: 2px solid ${({ theme }) => theme.color?.general.dark};

              .typography {
                color: ${({ theme }) => theme.color?.general.white};
              }
            }
          }
        }
      }
    }
  }

  &.withBGColor {
    .tabsTitles {
      .rts___tabs___container {
        .rts___tabs {
          border-bottom: none !important;

          .rts___tab {
            border-bottom: none !important;
            border-radius: 48px;

            &.rts___tab___selected {
              background-color: ${({ theme }) => theme.color?.general.darker};
            }
          }
        }
      }
    }
  }

  &.buttons {
    &.verticalPaymentMethods {
      .rts___tabs___container {
        .rts___tabs {
          @media (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
            padding: 0;
            height: 51px;
          }
        }
      }
    }

    .tabsTitles {
      padding: ${({ tabPadding }) => tabPadding || '0'};
    }

    .rts___tabs___container {
      .rts___tabs {
        width: 100%;
        padding: 8px;
        border-radius: 8px;
        background-color: #1e293b;
        gap: 8px;
        border-bottom: 0;

        .rts___tab {
          background-color: transparent;
          border: none;
          padding: 4px 16px;
          border-radius: 8px;

          .faqsList & {
            padding: 11px 16px 9px;
          }

          &.rts___tab___selected {
            background-color: #334155;
            border-bottom: 0;
          }
        }
      }

      .faqsList & {
        width: auto;

        .rts___tabs {
          background-color: transparent;
          padding: 0;
        }
      }
    }
  }
}

&.authTabs {
  height: 100%;
  width: 360px;

  .animate__animated.activeTab {
    height: 100%;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    width: calc(100vw - 16px);
  }

  &.mobileView {
    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      width: calc(100vw - 16px);
    }
    @media (min-width: ${({ theme }) =>
      theme.breakpoints?.sm + 1}px) and (max-width: ${({ theme }) =>
      theme.breakpoints?.md - 1}px) {
      width: calc(100vw - 100px);
    }
  }

  .formWrapper {
    height: calc(100% - 42px);

    .react-select__menu-list {
      max-height: 200px;
    }

    form {
      height: 100%;

      @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        max-width: 100%;
      }

      &.mobileView {
        max-width: 100%;
      }
    }

    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      height: calc(100% - 48px);
    }
  }
}

&.mobileMenuTabs {
  .rts___tabs___container {
    .rts___tabs {
      .rts___tab {
        width: 50%;
      }
    }
  }
}

&.notificationsTabs {
  height: 100%;

  .activeTab[role='tabpanel'] {
    height: calc(100% - 42px);
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      height: calc(100% - 56px);
    }
  }
}
`;
