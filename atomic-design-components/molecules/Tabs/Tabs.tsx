import clsx from 'clsx';
import { useEffect, useState } from 'react';
import { Tab, Tabs as TabsComponent } from 'react-tabs-scrollable';
import { useIsTouchMobileView } from '@/app/wrappers/TouchDevicesMobileViewProvider.tsx';
import 'react-tabs-scrollable/dist/rts.css';
import FlexRow from '../../atoms/FlexRow';
import Icon from '../../atoms/Icon/Icon';
import Dropdown from '../Dropdown';
import { StyledTabs } from './styled';
import Link from 'next/link';

interface TabsProps {
  activeTabProp?: number;
  backgroundColor?: string;
  className?: string;
  childrenBeforeTabTitles?: any;
  color?: string;
  dropdownItems?: any[];
  errorTabIndex?: any;
  isDisabled?: boolean;
  typographyType?: string;
  tabsTitles: any[];
  tabsContents?: any[];
  tabPadding?: string;
  onDropItemClick?: (e: any) => void;
  onTabChange?: (e: any, idx: number) => void;
  withAddAction?: boolean;
  getTabTitle?: Function;
  hideNavBtns?: boolean;
  margin?: string;
  setActiveField?: any;
  type?: 'inlines' | 'buttons';
}
const Tabs = (
  {
    activeTabProp = 0,
    backgroundColor,
    className,
    childrenBeforeTabTitles,
    color,
    dropdownItems,
    errorTabIndex,
    isDisabled,
    typographyType,
    tabsTitles,
    tabsContents,
    tabPadding,
    onDropItemClick,
    onTabChange,
    withAddAction = true,
    getTabTitle,
    hideNavBtns = false,
    margin,
    setActiveField,
    type = 'inlines',
  }: TabsProps,
  props: any,
) => {
  const [activeTab, setActiveTab] = useState(activeTabProp || 0);

  const { isTouchDevice } = useIsTouchMobileView();

  useEffect(() => {
    if (activeTabProp >= 0) {
      setActiveTab(activeTabProp);
    }
  }, [activeTabProp]);

  useEffect(() => {
    if (errorTabIndex?.index >= 0) {
      setActiveTab(errorTabIndex.index);
    }
  }, [errorTabIndex]);

  if (
    !tabsTitles?.length &&
    !tabsContents?.length &&
    !(!isDisabled && withAddAction && dropdownItems?.length)
  ) {
    return null;
  }

  const onTabClick = (e: any, idx: number) => {
    if (onTabChange) {
      onTabChange(e, idx);
    }
    setActiveTab(idx);
    if (setActiveField) {
      setActiveField(idx);
    }
  };

  return (
    <StyledTabs
      variant={typographyType || 'caption1'}
      color={color}
      backgroundColor={backgroundColor}
      className={clsx(className, type, {
        mobileView: isTouchDevice,
      }, isDisabled && 'cursor-not-allowed')}
      tabPadding={tabPadding}
    >
      <FlexRow margin={margin} className={clsx('tabsTitles', isDisabled && 'pointer-events-none')}>
        {childrenBeforeTabTitles}
        {!isDisabled && withAddAction && !!dropdownItems?.length && (
          <Dropdown
            MenuButton={Icon}
            buttonProps={{
              name: 'plus2',
              wrapperWidth: 32,
              wrapperHeight: 32,
            }}
            openDirection='right'
            className='tabsDropdown'
            dropdownItems={dropdownItems}
            onItemClick={onDropItemClick}
          />
        )}
        <TabsComponent
          activeTab={activeTab}
          className={isDisabled ? 'pointer-events-none' : ""}
          onTabClick={onTabClick}
          hideNavBtnsOnMobile={false}
          rightNavBtnClassName='right-nav-btn'
          rightBtnIcon={<Icon name='chevronLeft' />}
          leftBtnIcon={<Icon name='chevronLeft' />}
          hideNavBtns={hideNavBtns}
        >
          {tabsTitles.map((item, idx) => (
            <Tab
              key={idx}
              id={item.props?.id || item.id || item}
              as={item.href ? Link : undefined}
              href={item.href}
              data-1p-ignore // ensure 1Password autofill doesn't switch tabs
            >
              {getTabTitle ? getTabTitle(item, idx) : item.title || item}
            </Tab>
          ))}
        </TabsComponent>
      </FlexRow>
      {tabsContents?.map((item, index) => {
        return (
          <div
            className={clsx(
              activeTab === index && 'activeTab',
              'animate__animated animate__fadeInLeft',
            )}
            role='tabpanel'
            key={index}
            {...props}
          >
            {activeTab === index && item}
          </div>
        );
      })}
    </StyledTabs>
  );
};

export default Tabs;
