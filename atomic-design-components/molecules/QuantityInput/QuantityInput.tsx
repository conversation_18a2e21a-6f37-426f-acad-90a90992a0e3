import clsx from 'clsx';
import {
  forwardRef,
  ForwardRefExoticComponent,
  PropsWithoutRef,
  RefAttributes,
  useEffect,
  useState,
} from 'react';

import Icon from '../../atoms/Icon';
import { StyledQuantityInput, StyledWrapper } from './styled';

interface QuantityInputProps {
  fullWidth?: boolean;
  onChange?: Function;
  className?: string;
  disabled?: boolean;
  iconProps?: object;
  initialValue?: string | number;
  value?: any;
  min?: number;
  max?: number;
  placeholder?: string;
  step?: number;
  // width
  withBorder?: boolean;
  hideButtons?: boolean;
  variant?: 'primary' | 'secondary';
  name?: string;
}

const toString = (value: any) => {
  return typeof value === 'number' ? value.toString(10) : value;
};

const toNumber = (value: any) => {
  if (value && typeof value === 'string') {
    return +value;
  }
  return value;
};

const QuantityInput: ForwardRefExoticComponent<
  PropsWithoutRef<QuantityInputProps> & RefAttributes<HTMLInputElement>
> = forwardRef(
  (
    {
      // borderColor,
      fullWidth,
      onChange,
      className,
      disabled,
      iconProps = {},
      initialValue,
      value,
      min,
      max,
      placeholder,
      step = 1,
      // width,
      withBorder,
      hideButtons,
      variant = 'primary',
      name,
      ...otherProps
    },
    ref,
  ) => {
    const [inputValue, setInputValue] = useState(toString(initialValue) || '');

    useEffect(() => {
      if (typeof value !== 'undefined') {
        setInputValue(toString(value) || '');
      }
    }, [value]);

    useEffect(() => {
      if (
        typeof inputValue === 'string' &&
        inputValue?.charAt(0) === '0' &&
        inputValue?.length > 1
      ) {
        setInputValue(inputValue.substring(1));
      }
    }, [inputValue]);

    const checkMinMax = (inputValue: any) => {
      if (inputValue === '') {
        return true;
      }
      return !(
        (min !== undefined && +inputValue < min) ||
        (max !== undefined && +inputValue > max)
      );
    };

    const onButtonClick = (changeDirection: any) => (e: any) => {
      e.preventDefault();
      const getNewValue = (oldValue: any) => {
        const numValue = +oldValue + +`${changeDirection}1` * (+step || 1);
        return numValue.toString(10);
      };
      let checkPassed = checkMinMax(getNewValue(inputValue));
      if (checkPassed) {
        setInputValue((prevState: any) => getNewValue(prevState));

        if (onChange) {
          onChange(toNumber(getNewValue(inputValue)));
        }
      }
    };

    const onInputChange = (e: any) => {
      const { value } = e.target;
      let checkPassed = checkMinMax(value);
      if (!isNaN(+value)) {
        setInputValue(value);

        if (onChange) {
          onChange(toNumber(value));
        }
      }
    };

    return (
      <StyledWrapper
        {...otherProps}
        className={clsx(
          className,
          fullWidth && 'fullWidth',
          disabled && 'disabled',
        )}
      >
        {!hideButtons && !disabled && (
          <Icon
            wrapperWidth={20}
            wrapperHeight={40}
            name='minus2'
            onClick={onButtonClick('-')}
            {...iconProps}
          />
        )}
        <StyledQuantityInput
          ref={ref}
          step={step}
          fullWidth={fullWidth}
          type='number'
          value={inputValue}
          onChange={onInputChange}
          placeholder={placeholder}
          variant={variant}
          disabled={disabled}
          name={name}
          withBorder={withBorder}
          min={min}
          max={max}
        />
        {!hideButtons && !disabled && (
          <Icon
            wrapperWidth={20}
            wrapperHeight={40}
            name='plus2'
            onClick={onButtonClick('+')}
            {...iconProps}
          />
        )}
      </StyledWrapper>
    );
  },
);
export default QuantityInput;
