'use client';
import styled from 'styled-components';
import { getTokens } from '../..';

export const StyledWrapper = styled.div<any>`
  display: inline-flex;

  &.fullWidth {
    display: flex;
    flex-grow: 1;

    input {
      width: 100%;
    }
  }

  &.disabled input {
    background-color: #28364a;
    border-color: transparent;
    cursor: not-allowed;
  }
`;

export const StyledQuantityInput = styled.input<any>`
  width: 42px;
  outline: none;
  border: ${({ withBorder, theme: { color } }) =>
    `1px solid ${withBorder ? color.general.dark : 'transparent'}`};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: 7px 12px;
  ${({ variant, theme }) => getTokens(`input-${variant}-black-large`, theme)};
  &:hover {
    border-color: #475569;
  }
  &:focus {
    border: 1px solid ${({ theme }) => theme.color?.primary.main};
  }

  .hasError & {
    border: 1px solid ${({ theme }) => theme.color?.status.error};
  }

  .hasRightIcon & {
    padding-right: 36px;
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  -moz-appearance: textfield;

  // @media only screen and (max-width: ${({ theme }) =>
    theme.breakpoints?.sm}px) {
  //   font-size: 16px !important;
  // }
`;
