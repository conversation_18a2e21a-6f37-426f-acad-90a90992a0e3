'use client';
import styled from 'styled-components';
import Input from '../Input/Input';
import { MAX_CONTAINER_WIDTH } from '@/constants.ts';

export const StyledSearch: any = styled(Input)`
  width: ${({ width }: { width: any }) => width || '240px'};
`;

export const StyledSearchContainer: any = styled.div`
  width: ${({ width }: { width: any }) => width || '240px'};
  height: 100dvh;
  max-width: ${MAX_CONTAINER_WIDTH};

  .searchWrapper {
    height: calc(100dvh - 56px);
    overflow-y: scroll;
  }

  .searchInputWrapper,
  .gamesWrapper {
    padding: 8px;
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      padding: 24px 24px 8px;
    }
  }

  &:not(.mobileView) {
    @media (min-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      height: auto;
      .searchWrapper {
        height: calc(70dvh - 72px);
      }
    }
    @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
      height: 70dvh;
    }
  }
`;

export const StyledSearchResults: any = styled.div`
  display: block;
  width: 100%;
  text-align: center;
  padding: 0 8px;
  overflow: hidden;
  @media (min-width: ${({ theme }) => theme.breakpoints?.md}px) {
    padding: 0 24px;
  }
`;
