'use client';
import { getGames } from '@/app/api/getGames';
import GamesList from '@/app/components/Games/GamesList';
import { useTranslation } from '@/app/i18n/client';
import { Button, Typography } from '@/atomic-design-components';
import useClickOutsideElement from '@/hooks/useClickOutsideElement';
import { theme } from '@/theme';
import { LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal';
import clsx from 'clsx';
import { usePathname } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import ProposedGames from './ProposedGames';
import Search from './Search';
import { StyledSearchContainer, StyledSearchResults } from './styled';
import LoadingGameCardsGrid from '@/app/components/Skeletons/LoadingGameCardsGrid.tsx';
import { useDebounce } from '@/hooks/useDebounce.ts';

const LIMIT = 32;
const SEARCH_QUERY_MIN_LENGTH = 3;

const SEARCHED_GAMES_LIST_BREAKPOINTS = {
  '(min-width: 1350px)': {
    perView: 6,
    spacing: 16,
  },
  '(max-width: 1350px)': {
    perView: 5,
    spacing: 16,
  },
  '(min-width: 960px) and (max-width: 1050px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 959px)': {
    perView: 5,
    spacing: 16,
  },
  '(max-width: 870px)': {
    perView: 4,
    spacing: 16,
  },
  '(max-width: 770px)': {
    perView: 3,
    spacing: 8,
  },
  '(max-width: 620px)': {
    perView: 4,
    spacing: 8,
  },
  '(max-width: 580px)': {
    perView: 3,
    spacing: 8,
  },
  '(max-width: 359px)': {
    perView: 2,
    spacing: 8,
  },
};

const closeShadowScreen = () => {
  const shadowScreen = window.document.querySelector('.shadowScreen');
  if (shadowScreen?.classList.contains('scrolled')) {
    shadowScreen?.classList.remove('shown', 'scrolled');
    shadowScreen?.setAttribute('style', `height: 100vh`);
  }
};

const SearchContainer = ({
  lng,
  width,
  isAlwaysOpened = false,
  isDialogOpen,
  isTouchDevice,
}: {
  lng: LanguagesType;
  width?: string;
  isAlwaysOpened?: boolean;
  isDialogOpen?: boolean;
  isTouchDevice?: boolean;
}) => {
  const { t } = useTranslation(lng);
  const [inputValue, setInputValue] = useState('');
  const [games, setGames] = useState({ items: [], total: 0 });
  const [isLoading, setIsLoading] = useState(false);

  const debouncedInputValue = useDebounce(
    inputValue,
    800,
    SEARCH_QUERY_MIN_LENGTH,
  );

  const pathname = usePathname();

  const ref = useRef<HTMLElement>(null);
  useClickOutsideElement(ref, () => {
    if (
      ref.current?.classList.contains('openedModal') &&
      !document.getElementById('continueGameWithZeroWager')?.dataset
        ?.gameUrlClicked &&
      !document
        .getElementById('activeFreespinsInGame')
        ?.classList?.contains('opened') &&
      !document
        .getElementById('unfinishedGameModal')
        ?.classList?.contains('opened') &&
      !document.getElementById('gameModal')?.classList?.contains('opened')
    ) {
      setInputValue('');
      closeShadowScreen();
    }
  });

  useEffect(() => {
    closeShadowScreen();
  }, [pathname]);

  useEffect(() => {
    if (
      debouncedInputValue.length < SEARCH_QUERY_MIN_LENGTH &&
      inputValue !== debouncedInputValue
    ) {
      setIsLoading(true);
    }
  }, [debouncedInputValue, inputValue]);

  useEffect(() => {
    if (debouncedInputValue.length < SEARCH_QUERY_MIN_LENGTH) {
      setGames({ items: [], total: 0 });
      return;
    }
    setIsLoading(true);
    getInitGames().finally(() => setIsLoading(false));
  }, [debouncedInputValue]);

  const handleKeyDown = (e: any) => {
    if (e.key === 'Escape') {
      setInputValue('');
      if (isAlwaysOpened) {
        setInputValue('');
        closeModal('searchModal');
      } else {
        closeShadowScreen();
      }
    }
  };

  const getInitGames = async () => {
    const searchedGames = await getGames(
      0,
      LIMIT,
      {
        'name|external_id|meta_search__like': [inputValue],
      },
      'release_date=asc',
    );

    setGames(searchedGames);
    setIsLoading(false);
  };

  return (
    <StyledSearchContainer
      ref={ref}
      id='gameSearchContainer'
      onKeyDown={handleKeyDown}
      className={clsx('openedModal', isTouchDevice && 'mobileView')}
      width={width}
    >
      <div className='searchInputWrapper flex gap-2'>
        <Search
          lng={lng}
          inputValue={inputValue}
          setInputValue={(val: string) => {
            setInputValue(val);
          }}
          closeShadowScreen={closeShadowScreen}
          width={width}
          isAlwaysOpened={true}
          placeholder={t('searchChars')}
          isAbsolutePositioned={false}
        />
        <Button
          text={t('cancel')}
          onClick={() => closeModal('searchModal')}
          variant='transparent'
          size='small'
        />
      </div>
      <div className='searchWrapper'>
        <StyledSearchResults>
          {inputValue && inputValue.length >= SEARCH_QUERY_MIN_LENGTH ? (
            <>
              <Typography
                text={t('searchResults')}
                type='h1'
                margin='8px 0 16px'
              />
              {games.items.length || isLoading ? (
                isLoading ? (
                  <LoadingGameCardsGrid
                    breakpoints={SEARCHED_GAMES_LIST_BREAKPOINTS}
                  />
                ) : (
                  <GamesList
                    initialData={games.items}
                    totalItems={games.total}
                    limit={LIMIT}
                    filtersMain={{ 'name|external_id__like': [inputValue] }}
                    sort='release_date=asc'
                    itemsInRow={6}
                    breakpoints={SEARCHED_GAMES_LIST_BREAKPOINTS}
                    isGamesInsideModal
                    isInitiallyLoaded
                  />
                )
              ) : (
                <Typography
                  type='body2'
                  padding='28px 0'
                  justifyContent='center'
                  color={theme.color?.general.lighter}
                >
                  {t('nothingFound')}
                </Typography>
              )}
            </>
          ) : null}
        </StyledSearchResults>
        {isDialogOpen && <ProposedGames lng={lng} />}
      </div>
    </StyledSearchContainer>
  );
};
export default SearchContainer;
