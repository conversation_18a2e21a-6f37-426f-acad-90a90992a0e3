'use client';
import { getGames, IGame } from '@/app/api/getGames';
import { getRecentGamesClient } from '@/app/api/user/games/getRecentGamesClient.ts';
import GameCard from '@/app/components/Games/GameCard';
import Slider from '@/app/components/Slider/Slider';
import { useTranslation } from '@/app/i18n/client';
import { useUser } from '@/app/wrappers/userProvider';
import Typography from '@/atomic-design-components/atoms/Typography';
import { GamePageType, LanguagesType } from '@/types/global';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { closeModal } from '@/utils/closeModal.ts';
import LoadingGameCard from '@/app/components/Skeletons/LoadingGameCard.tsx';
import { useCookies } from 'react-cookie';
import { LANDING_GAMES_GROUPS } from '@/app/components/Games/config.ts';

export const GAMES_SLIDER_IN_MODAL_BREAKPOINTS = {
  '(min-width: 1350px)': {
    slides: {
      perView: 6,
      spacing: 16,
    },
  },
  '(max-width: 1350px)': {
    slides: {
      perView: 5,
      spacing: 16,
    },
  },
  '(min-width: 960px) and (max-width: 1050px)': {
    slides: {
      perView: 4,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 959px)': {
    slides: {
      perView: 5,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 870px)': {
    slides: {
      perView: 4,
      spacing: 16,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 770px)': {
    slides: {
      perView: 3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 620px)': {
    slides: {
      perView: 4,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 540px)': {
    slides: {
      perView: 3,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
  '(max-width: 359px)': {
    slides: {
      perView: 2,
      spacing: 8,
      dragSpeed: 0,
      defaultAnimation: { duration: 4000 },
    },
  },
};

const ProposedGames = ({ lng }: { lng: LanguagesType }) => {
  const { t } = useTranslation(lng);
  const { gamePageType = 'casino' }: { gamePageType: GamePageType } =
    useParams();

  const [cookies] = useCookies(['userCurrency']);

  const pageType = ['casino', 'live-casino'].includes(gamePageType as string)
    ? gamePageType
    : 'casino';

  const { user } = useUser();
  const isUserAuthorized = !!user?.id;
  const hasActiveWager = !!user?.active_wager;

  const [topGames, setTopGames] = useState<IGame[]>([]);
  const [recentGames, setRecentGames] = useState<IGame[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const mainFilter = LANDING_GAMES_GROUPS[pageType].top?.filter;
  const sort = LANDING_GAMES_GROUPS[pageType].top?.sort;
  const bonusFilter =
    hasActiveWager && pageType === 'casino'
      ? {
          wager_percentage__gte: [1],
        }
      : {};

  const topFilters = {
    ...mainFilter,
    ...bonusFilter,
    is_live: [(pageType === 'live-casino').toString()],
  };

  const fetchGames = async () => {
    const { items: top } = await getGames(0, 32, cookies?.userCurrency, topFilters, sort);

    setTopGames(top);
    if (isUserAuthorized) {
      const { items: recent } = await getRecentGamesClient(0, 32);
      setRecentGames(recent);
    }
    setIsLoaded(true);
  };

  useEffect(() => {
    fetchGames();
  }, []);

  return (
    <div className='gamesWrapper'>
      {isUserAuthorized && (
        <>
          {(!isLoaded || !!recentGames.length) && (
            <Typography
              text={t('recent')}
              iconName='clockIcon'
              type='h1'
              margin='0 0 8px'
              iconProps={{ width: 16, height: 16 }}
            />
          )}
          <Slider
            items={isLoaded ? recentGames : Array(6).fill(<LoadingGameCard />)}
            total={isLoaded ? recentGames.length : 6}
            Slide={GameCard}
            isCarousel
            limit={recentGames?.length}
            perView={6}
            buttonText={t('all')}
            buttonHref={`/${lng}/${pageType}/recent`}
            onButtonClick={() => {
              closeModal('continueGameWithZeroWager');
              closeModal('searchModal');
              closeModal('gamePageOverlayModal');
            }}
            withLoadMore
            breakpoints={GAMES_SLIDER_IN_MODAL_BREAKPOINTS}
            className='mb-6'
          />
        </>
      )}
      <>
        <Typography
          text={t('top')}
          iconName='star'
          type='h1'
          margin='0 0 8px'
        />
        <Slider
          items={isLoaded ? topGames : Array(6).fill(<LoadingGameCard />)}
          total={isLoaded ? topGames.length : 6}
          Slide={GameCard}
          isCarousel
          limit={topGames?.length}
          perView={6}
          withLoadMore
          buttonText={t('all')}
          buttonHref={`/${lng}/${pageType}/games/top`}
          onButtonClick={() => {
            closeModal('continueGameWithZeroWager');
            closeModal('searchModal');
            closeModal('gamePageOverlayModal');
          }}
          breakpoints={GAMES_SLIDER_IN_MODAL_BREAKPOINTS}
        />
      </>
    </div>
  );
};
export default ProposedGames;
