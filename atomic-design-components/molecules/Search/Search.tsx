'use client';
import { useTranslation } from '@/app/i18n/client';
import { LanguagesType } from '@/types/global';
import { closeModal } from '@/utils/closeModal';
import clsx from 'clsx';
import { useEffect, useRef } from 'react';
import { StyledSearch } from './styled';

const Search = ({
  lng,
  isOpened,
  setIsOpened,
  inputValue,
  setInputValue,
  onOpen,
  closeShadowScreen,
  width,
  isAlwaysOpened,
  withCross,
  placeholder,
  colorType = 'general',
  disabled = false,
  isAbsolutePositioned = true,
  className,
}: {
  lng: LanguagesType;
  isOpened?: boolean;
  setIsOpened?: any;
  inputValue: string;
  setInputValue: any;
  onOpen?: Function;
  closeShadowScreen?: Function;
  width?: string;
  isAlwaysOpened?: boolean;
  withCross?: boolean;
  placeholder?: string;
  colorType?: string;
  disabled?: boolean;
  isAbsolutePositioned?: boolean;
  className?: string;
}) => {
  const { t } = useTranslation(lng);
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (isOpened) ref.current?.focus();
  }, [isOpened]);

  function handleSearch(term: string) {
    setInputValue(term);
  }

  const onSearchCleanClick = () => {
    if (inputValue.length > 0) {
      setInputValue('');
      ref.current?.focus();
    } else {
      if (isAlwaysOpened) {
        setInputValue('');
        closeModal('searchModal');
      } else {
        if (setIsOpened) setIsOpened(false);
        if (closeShadowScreen) closeShadowScreen();
      }
    }
  };

  return (
    <StyledSearch
      colorType={colorType}
      ref={ref}
      value={inputValue}
      disabled={disabled}
      className={clsx(
        className,
        (isAlwaysOpened || isOpened) && 'opened',
        colorType,
        isAbsolutePositioned && 'absolute',
      )}
      placeholder={placeholder || t('search')}
      onClick={() => onOpen && onOpen()}
      onChange={(e: any) => {
        handleSearch(e.target.value);
      }}
      leftIconName='search'
      rightIconName={
        (isAlwaysOpened || isOpened || withCross) && inputValue.length
          ? 'cross'
          : ''
      }
      width={width}
      iconRightProps={{
        onClick: onSearchCleanClick,
      }}
    />
  );
};
export default Search;
