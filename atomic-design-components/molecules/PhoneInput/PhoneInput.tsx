import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';

import { Button, FlexRow, Input, Select } from '@/atomic-design-components';
import useCountries from '@/hooks/useCountries';
import { theme } from '@/theme';
import { StyledLabel } from '../Input/styled';
import { StyledWrapper } from './styled';

interface PhoneInputProps {
  autoFocus?: boolean;
  backToInitialValueOnCrossClick?: boolean;
  fullWidth?: boolean;
  // eslint-disable-next-line no-unused-vars
  onChange?: (data: {
    dialCode: string;
    inputValue: string;
    country: string;
  }) => void;
  className?: string;
  disabled?: boolean;
  initialValue?: string;
  initialCode?: string;
  initialCountry?: string;
  value?: any;
  placeholder?: string;
  withBorder?: boolean;
  name?: string;
  error?: string;
  t?: any;
  labelTop?: string;
  labelBottom?: string | React.ReactNode;
  size?: 'small' | 'medium' | 'large';
  withAutoFocus?: boolean;
  withButtonInside?: boolean;
  buttonText?: string;
  onCrossClick?: any;
  onButtonClick?: any;
  buttonDisabled?: boolean;
}

const PhoneInput = ({
  autoFocus,
  backToInitialValueOnCrossClick,
  fullWidth,
  onChange,
  className,
  disabled,
  initialValue = '',
  initialCode = '',
  initialCountry = '',
  placeholder,
  withBorder = true,
  name,
  error,
  t,
  labelTop,
  labelBottom,
  size = 'large',
  withAutoFocus = true,
  withButtonInside,
  buttonText,
  onCrossClick,
  onButtonClick,
  buttonDisabled,
  ...otherProps
}: PhoneInputProps) => {
  const { countries } = useCountries();
  const sort = [
    'Russia',
    'Czech Republic',
    'Poland',
    'Kazakhstan',
    'Uzbekistan',
    'Turkey',
  ];

  const sortedCountries = countries.sort((a: any, b: any) => {
    const indexA = sort.indexOf(a.name);
    const indexB = sort.indexOf(b.name);
    return indexB - indexA;
  });

  const [country, setCountry] = useState<string>(initialCountry || '');
  const [dialCode, setDialCode] = useState<string>(initialCode || '');
  const [inputValue, setInputValue] = useState<string>(initialValue || '');
  const [inputWidth, setInputWidth] = useState<string>('');
  const wrapperRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (wrapperRef?.current) {
      setInputWidth(`${wrapperRef.current.offsetWidth}px`);
    }
  }, []);

  const handleSelectChange = (selectedCountry: any) => {
    setDialCode(selectedCountry.dial_code);
    setCountry(selectedCountry.code);
    if (withAutoFocus) {
      inputRef.current?.focus();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // const cleanInputValue = value.replace(/[^0-9-]/g, '');
    setInputValue(e.target.value);
  };

  useEffect(() => {
    if (onChange) {
      onChange({
        country: country || '',
        dialCode: dialCode || '',
        inputValue: inputValue || '',
      });
    }
  }, [inputValue, dialCode, country]);

  return (
    <StyledWrapper
      {...otherProps}
      className={clsx(
        className,
        fullWidth && 'fullWidth',
        disabled && 'disabled',
        error && 'hasError',
        withButtonInside && 'withButtonInside',
      )}
      ref={wrapperRef}
    >
      {labelTop && (
        <StyledLabel className={clsx('label top')}>{labelTop}</StyledLabel>
      )}
      <FlexRow gap='10px'>
        <Select
          isDisabled={disabled}
          menuWidth={inputWidth}
          name='dialCode'
          options={sortedCountries}
          labelKey='name'
          valueKey='name'
          placeholder={t('code')}
          menuPortalTarget={
            typeof document !== 'undefined' ? document.body : undefined
          }
          controlShouldRenderValue
          customGetOptionLabel={(option: any) => (
            <FlexRow gap='4px' className='phoneCode_wrapper'>
              <div className='flagIcon'>{option.flag}</div>
              <span className='country'>{option.name}</span>
              <span className='dialCode'>{option.dial_code}</span>
            </FlexRow>
          )}
          getSelectedOption={(props: any) => {
            const value = props.getValue()[0];
            return (
              <FlexRow
                gap='4px'
                className='phoneCode_wrapper'
                key={value.dial_code}
              >
                <div className='flagIcon'>{value?.flag}</div>
                <span className='dialCode'>{value?.dial_code}</span>
              </FlexRow>
            );
          }}
          onChange={handleSelectChange}
          value={sortedCountries.find((c: any) => c.code === country)}
          withBorder
        />
        <Input
          autoFocus={autoFocus}
          ref={inputRef}
          fullWidth={fullWidth}
          value={inputValue}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          name={name}
          withBorder={withBorder}
          size={size}
          type='text'
          rightIconName={onCrossClick ? 'cross' : undefined}
          onRightIconClick={() => {
            if (!onCrossClick) return;
            if (backToInitialValueOnCrossClick) {
              setInputValue(initialValue);
            } else {
              setInputValue('');
            }
            onCrossClick();
          }}
        />
      </FlexRow>
      {withButtonInside && (
        <Button
          text={buttonText}
          onClick={onButtonClick}
          variant='secondary'
          disabled={buttonDisabled}
        />
      )}
      {(labelBottom || error) && (
        <StyledLabel
          variant='label2'
          className={clsx('label', 'bottom', !!error && 'error')}
          color={error ? theme.color?.status.error : ''}
        >
          {error ? t(error) || error : labelBottom}
        </StyledLabel>
      )}
    </StyledWrapper>
  );
};

export default PhoneInput;
