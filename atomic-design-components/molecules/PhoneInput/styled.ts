'use client';
import styled from 'styled-components';
import { getTokens } from '../..';

export const StyledWrapper = styled.div<any>`
  display: inline-flex;
  flex-direction: column;
  position: relative;
  &.fullWidth {
    display: flex;
    flex-grow: 1;
    width: 100%;

    input {
      width: 100%;
    }
  }

  .selectWrapper {
    width: 50%;
  }

  &.hasError {
    input {
      border-color: ${({ theme }) => theme.color?.status.error};
    }
    .react-select__control {
      border-color: ${({ theme }) => theme.color?.status.error};
    }
  }

  .phoneCode_wrapper {
    .flagIcon {
      font-size: 18px;
    }
  }
  &.withButtonInside {
    input {
      padding-right: 115px;
    }

    button {
      position: absolute;
      bottom: 0;
      right: 0;
      max-width: 110px;
      border-radius: 6px;
      height: 45px;
    }
  }
`;

export const StyledPhoneInput = styled.input<any>`
  ${({ theme, size }) => getTokens(`input-primary-black-${size}`, theme)};
  width: 42px;
  outline: none;
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  -moz-appearance: textfield;
`;
