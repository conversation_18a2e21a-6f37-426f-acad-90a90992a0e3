'use client';
import styled from 'styled-components';

import { getTokens } from '../..';

export const StyledAlertsDialog = styled.div`
    z-index: 1100;
    position: fixed;
    right: 20px;
    top: 20px;

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
        right: 50%;
        transform: translateX(50%);
    }
`;

export const StyledPopupAlert = styled.div<any>`
  ${({ theme }) => getTokens(`popupAlert-standard-default-large`, theme)};
  position: relative;
  margin-bottom: 16px;
  display: flex;
  height: fit-content;
  max-width: 344px;
  min-width: 320px;
  width: auto;
  gap: 8px;
  border-radius: 8px;
  border: 1px solid #475569;
  background-color: #334155;
  padding: 12px;
  transition: all 0.2s;
  white-space: pre-line;

  .closeIcon {
    margin-left: auto;
  }

  @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
    width: 90dvw;
  }
`;
