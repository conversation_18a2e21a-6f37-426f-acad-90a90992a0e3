'use client';
import { useAlert } from '@/app/wrappers/AlertProvider';
import { PopupAlert } from '@/atomic-design-components';
import { StyledAlertsDialog } from './styled.ts';

const PopupAlertContainer = () => {
  const { alerts, hideAlert } = useAlert();

  return (
    <StyledAlertsDialog className='popupAlertContainer' id='popupContainer'>
      {alerts.map((alert, index) => {
        return (
          <PopupAlert
            alert={alert}
            index={index}
            hideAlert={hideAlert}
            key={alert.id}
          />
        );
      })}
    </StyledAlertsDialog>
  );
};

PopupAlertContainer.displayName = 'PopupAlertContainer';
export default PopupAlertContainer;
