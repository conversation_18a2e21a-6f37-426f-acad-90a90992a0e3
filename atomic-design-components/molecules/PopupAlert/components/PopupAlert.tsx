'use client';
import Typography from '@/atomic-design-components/atoms/Typography';
import { useTimeout } from '@/hooks/useTimeout';
import React, { useEffect, useCallback } from 'react';
import Icon from '../../../atoms/Icon';
import { StyledPopupAlert } from '../styled';
import { CloseIcon } from '@/atomic-design-components';

const MAP_TYPE_TO_ICON: any = {
  success: 'checkboxInCircle',
  warning: 'info',
  error: 'error',
};

interface PopupAlertProps {
  alert: {
    type: 'default' | 'new' | 'success' | 'warning' | 'error';
    timeout: number;
    withCloseButton: boolean;
    position: 'top' | 'bottom';
    content: string | object;
    children: React.ReactNode;
    id: string;
  };
  index: number;
  hideAlert: Function;
}

const PopupAlert = ({ alert, index, hideAlert }: PopupAlertProps) => {
  const {
    type = 'default',
    timeout,
    withCloseButton,
    content,
    children,
    id,
  } = alert;
  const leftIcon = MAP_TYPE_TO_ICON[type];

  // const [hide, setHide] = useState(false);

  const isNotClosable = timeout === 0;

  // useEffect(() => {
  //   if (hide) {
  //     hideAlert(id);
  //   }
  // const closeId = setTimeout(() => {
  //
  // }, 300);

  // return () => {
  //   clearTimeout(closeId);
  // };
  // }, [hide]);

  const onPopupClose = useCallback(() => {
    // setHide(true);
    hideAlert(id);
  }, [hideAlert, id]);

  useEffect(() => {
    if (timeout && index > 2) {
      onPopupClose();
    }
  }, [timeout, index]);

  useTimeout(
    () => {
      if (!isNotClosable) {
        onPopupClose();
      }
    },
    timeout,
    [id],
  );

  return (
    <StyledPopupAlert>
      {leftIcon && <Icon name={leftIcon} className='leftIcon' />}
      {children || <Typography text={content} type='body2' />}
      {withCloseButton && (
        <CloseIcon onClick={onPopupClose} className='rightIcon' />
      )}
    </StyledPopupAlert>
  );
};

export default PopupAlert;
