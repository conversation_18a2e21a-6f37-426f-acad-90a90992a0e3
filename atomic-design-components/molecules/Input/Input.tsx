import clsx from 'clsx';
import React, {
  ForwardRefExoticComponent,
  PropsWithoutRef,
  RefAttributes,
  forwardRef,
} from 'react';
import { omit } from 'lodash';
import { useTranslation } from '@/app/i18n/client';
import { theme } from '@/theme.ts';
import { useParams } from 'next/navigation';
import { isObjectEmpty } from '../../../utils/object.ts';
import Icon from '../../atoms/Icon/index.js';
import Button from '../Button/Button.jsx';
import QuantityInput from '../QuantityInput/index.js';
import {
  StyledFlexRow,
  StyledInput,
  StyledInputWrapper,
  StyledLabel,
  StyledWrapper,
} from './styled.ts';
import { CloseIcon } from '@/atomic-design-components';

interface InputProps {
  autoComplete?: string;
  autoFocus?: boolean;
  className?: string;
  customT?: any;
  disabled?: boolean;
  error?: string | boolean;
  focusBorderColorKey?: string;
  focusOutlineColorKey?: string;
  fullWidth?: boolean;
  iconLeftProps?: any;
  iconRightProps?: any;
  id?: string;
  initialHeight?: string;
  initialValue?: string;
  inputMode?:
    | 'none'
    | 'text'
    | 'decimal'
    | 'numeric'
    | 'tel'
    | 'search'
    | 'email'
    | 'url';
  inverted?: boolean;
  labelLeft?: string | React.ReactNode;
  labelTop?: string | React.ReactNode;
  labelBottom?: string | React.ReactNode;
  labelWidth?: string;
  min?: number;
  max?: number;
  maxLength?: number;
  maxHeight?: string;
  multiline?: boolean;
  name?: string;
  onBlur?: Function;
  onChange?: Function;
  onEnter?: Function;
  onFocus?: Function;
  onLeftIconClick?: (e: any) => void;
  onRightIconClick?: (e: any) => void;
  onSubmit?: (e: any) => void;
  placeholder?: string;
  placeholderColor?: string;
  size?: 'small' | 'medium' | 'large';
  step?: number;
  submitByEnterPressed?: boolean;
  success?: boolean;
  toolbarOptions?: any;
  type?: 'text' | 'password' | 'number' | 'richText' | 'time' | 'tel';
  pattern?: string;
  required?: boolean;
  rightIconName?: string;
  leftIconName?: string;
  value?: string | number | object | null;
  width?: string;
  withAutosize?: boolean;
  withDebounce?: boolean;
  withFocusBorder?: boolean;
  withFocusOutline?: boolean;
  withoutValidation?: boolean;
  withButtonInside?: boolean;
  onButtonClick?: any;
  buttonText?: string;
  buttonDisabled?: boolean;
  readOnly?: boolean;
  buttonProps?: any;
  withBorder?: boolean;
}

const Input: ForwardRefExoticComponent<
  PropsWithoutRef<InputProps> & RefAttributes<HTMLInputElement>
> = forwardRef((props, ref) => {
  const {
    autoComplete = 'off',
    autoFocus,
    className,
    disabled,
    error,
    fullWidth,
    iconLeftProps = {},
    iconRightProps = {},
    id,
    inputMode,
    labelLeft,
    labelTop,
    labelBottom,
    labelWidth,
    maxLength,
    multiline = false,
    name,
    onChange,
    onEnter,
    onFocus,
    onLeftIconClick,
    onRightIconClick,
    onSubmit,
    pattern,
    placeholder,
    placeholderColor,
    size = 'large',
    submitByEnterPressed = true,
    success,
    customT,
    type,
    required,
    rightIconName,
    leftIconName,
    value,
    withDebounce,
    withFocusBorder,
    withFocusOutline,
    withoutValidation,
    withButtonInside,
    onButtonClick,
    buttonText,
    buttonDisabled,
    readOnly = false,
    buttonProps,
    withBorder,
    ...otherProps
  } = props;

  const { lng } = useParams();
  const { t } = useTranslation(lng, customT);

  const isRightIconCross =
    rightIconName === 'cross' || iconRightProps?.name === 'cross';

  const onChangeState = (e: any) => {
    if (onChange && !withDebounce) {
      onChange(e);
    }
  };

  const handleKeyDown = (e: any) => {
    // it triggers by pressing the enter key
    if (
      !disabled &&
      submitByEnterPressed &&
      (onEnter || onSubmit) &&
      e.key === 'Enter'
    ) {
      if (onSubmit) {
        onSubmit(e.target.value);
      }
      if (onEnter) {
        onEnter(e.target.value);
      }
    }
  };

  const hasLeftIconProps = !isObjectEmpty(iconLeftProps);
  const hasRightIconProps = !isObjectEmpty(iconRightProps);
  const hasError = !!error && typeof error === 'string';

  const getInputWithoutLeftLabel = () => {
    const classes = clsx(
      !!error && 'hasError',
      success && 'success',
      !!(leftIconName || hasLeftIconProps) && 'hasLeftIcon',
      !!(rightIconName || hasRightIconProps) && 'hasRightIcon',
      multiline && 'multiline',
      withFocusBorder && 'withFocusBorder',
      withFocusOutline && 'withFocusOutline',
      disabled && 'disabled',
      readOnly && 'readOnly',
      withBorder && 'withBorder',
    );

    let InputField;

    if (type === 'number') {
      InputField = (
        <QuantityInput
          {...omit(props, ['onRightIconClick'])}
          className={classes}
          name={name}
          ref={ref}
        />
      );
    } else {
      InputField = (
        <StyledInput
          id={id}
          autoComplete={autoComplete}
          autoFocus={autoFocus}
          onKeyDown={handleKeyDown}
          onFocus={onFocus}
          // as={multiline ? 'textarea' : null}
          ref={ref}
          name={name}
          disabled={disabled}
          value={value}
          onChange={onChangeState}
          placeholder={placeholder}
          placeholderColor={placeholderColor}
          size={size}
          type={type}
          className={classes}
          readOnly={readOnly}
          inputMode={inputMode}
          maxLength={maxLength}
          pattern={pattern}
          {...otherProps}
        />
      );
    }

    return (
      <StyledInputWrapper
        className={clsx(
          className,
          withoutValidation && 'withoutValidation',
          error && 'hasError',
          'inputContainer',
        )}
        $fullWidth={fullWidth}
      >
        {labelTop && (
          <StyledLabel
            className={clsx('label', required && 'required', 'top')}
            type='label1'
          >
            {labelTop}
          </StyledLabel>
        )}
        <StyledWrapper
          $fullWidth={fullWidth}
          className={clsx(
            'inputWrapper',
            disabled && 'disabled',
            readOnly && 'readOnly',
            !!error && 'hasError',
            success && 'success',
            withoutValidation && 'withoutValidation',
            withButtonInside && 'withButtonInside',
          )}
        >
          {!!(hasLeftIconProps || leftIconName) && (
            <Icon
              width={16}
              height={16}
              className='leftIcon'
              name={leftIconName}
              onClick={onLeftIconClick}
              {...iconLeftProps}
            />
          )}

          {InputField}

          {(rightIconName || hasRightIconProps) &&
            (isRightIconCross ? (
              <CloseIcon
                onClick={onRightIconClick}
                className='rightIcon'
                {...{ width: 16, height: 16, ...iconRightProps }}
              />
            ) : (
              <Icon
                name={rightIconName}
                width={16}
                height={16}
                className='rightIcon'
                onClick={onRightIconClick}
                {...iconRightProps}
              />
            ))}

          {withButtonInside && (
            <Button
              text={buttonText}
              onClick={onButtonClick}
              variant='secondary'
              disabled={buttonDisabled}
              {...buttonProps}
            />
          )}

          {success && (
            <Icon
              name='checkRounded'
              borderRadius='50%'
              wrapperHeight={14}
              wrapperWidth={14}
              width={8}
              className='rightIcon success'
            />
          )}
        </StyledWrapper>
        {(labelBottom || error) && (
          <StyledLabel
            type='label1'
            className={clsx(
              'label',
              required && 'required',
              'bottom',
              hasError && 'error',
            )}
            color={hasError ? theme.color?.status.error : ''}
          >
            {hasError ? t(error) || error : labelBottom}
          </StyledLabel>
        )}
      </StyledInputWrapper>
    );
  };

  return labelLeft ? (
    <StyledFlexRow
      className={clsx(
        'labelLeft',
        'leftLabelWrapper',
        className,
        withoutValidation && 'withoutValidation',
      )}
    >
      <StyledLabel
        className={clsx('label', required && 'required')}
        $labelWidth={labelWidth}
        type='label1'
      >
        {labelLeft}
      </StyledLabel>
      {getInputWithoutLeftLabel()}
    </StyledFlexRow>
  ) : (
    getInputWithoutLeftLabel()
  );
});

Input.displayName = 'Input';
export default Input;
