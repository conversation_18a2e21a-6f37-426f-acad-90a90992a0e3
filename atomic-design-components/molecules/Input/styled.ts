'use client';
import styled from 'styled-components';
import { getTokens } from '../..';
import { Typography } from '@/atomic-design-components';

export const StyledInputWrapper = styled.div<any>`
  width: ${({ $fullWidth }) => $fullWidth && '100%'};

  &.withoutValidation {
    margin-bottom: 0;
  }
`;

export const StyledWrapper = styled.div<any>`
  position: relative;
  display: flex;
  flex-grow: 1;

  .labelLeft &,
  &.withoutValidation {
    margin-bottom: 0;
  }

  .rightIcon {
    position: absolute;
    right: 12px;
    top: 0;
    bottom: 0;
    display: flex;

    &.success {
      top: 35%;
      background-color: ${({ theme }) => theme.color?.status.success};

      path {
        fill: ${({ theme }) => theme.color?.general.white};
      }
    }
  }

  & > .icon.leftIcon {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 12px;
  }

  width: ${({ $fullWidth }) => $fullWidth && '100%'};

  &.withButtonInside {
    input {
      padding-right: 115px;
    }

    button {
      position: absolute;
      top: 0;
      right: 0;
      max-width: 110px;
      border-radius: 80px;
    }
  }

  &.readOnly input {
    cursor: initial;
  }
`;

export const StyledInput = styled.input<any>`
  outline: none;
  width: 100%;
  flex-grow: 1;
  border: 1px solid transparent;
  padding: 7px 12px;

  &:hover {
    border-color: #475569;
  }

  &.withBorder {
    border: 1px solid ${({ theme }) => theme.color?.general.dark};
  }

  &.multiline {
    line-height: 22px;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  &:focus:not(.readOnly):not(.hasError) {
    border-color: ${({ theme }) => theme.color?.primary.main};
  }

  &.hasLeftIcon {
    padding-left: 36px;
  }

  &.hasRightIcon {
    padding-right: 36px;
  }

  &.hasError {
    border-color: ${({ theme }) => theme.color?.status.error};
  }

  &.success {
    border-color: ${({ theme }) => theme.color?.status.success};
  }

  &.disabled {
    background-color: #28364a;
    border-color: transparent;
    cursor: not-allowed;
  }

  //for dark autofilled input
  color-scheme: dark;

  ${({ theme, size }) => getTokens(`input-primary-black-${size}`, theme)};

  ::placeholder {
    opacity: 1;
    color: ${({ theme, placeholderColor }) =>
      placeholderColor || theme.color?.general.light};
  }

  :-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: ${({ theme, placeholderColor }) =>
      placeholderColor || theme.color?.general.light};
  }

  ::-ms-input-placeholder {
    /* Microsoft Edge */
    color: ${({ theme, placeholderColor }) =>
      placeholderColor || theme.color?.general.light};
  }
`;

export const StyledFlexRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 25px;

  &.withoutValidation {
    margin-bottom: 0;
  }
`;

export const StyledLabel = styled(Typography)<any>`
  display: block;

  &.required::after {
    content: ' *';
    color: ${({ theme }) => theme.color?.status.error};
  }

  &.top {
    margin-bottom: 4px;
  }

  &.bottom {
    margin-top: 4px;
  }

  &.required::after {
    content: ' *';
    color: ${({ theme }) => theme.color?.status.error};
  }
`;
