import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';
import { useEffect, useState } from 'react';
import { withTheme } from 'styled-components';

import Typography from '../../atoms/Typography';
import { StyledSwitch, StyledSwitchLabel } from './styled';

const Switch = ({
  checked,
  disabled,
  height,
  gap,
  id,
  inProgress,
  width,
  handleDiameter,
  justifyContent,
  label,
  labelColor,
  leftLabel,
  margin,
  name,
  onChange,
  readOnly,
  theme,
  className,
  textVariant,
  revert,
  labelProps,
}) => {
  const [stateChecked, setStateChecked] = useState(checked || false);

  const onClick = () => {
    if (typeof checked === 'undefined') {
      setStateChecked((prev) => !prev);
    }
    if (onChange) {
      onChange(!stateChecked, { target: { name } });
    }
  };

  useEffect(() => {
    setStateChecked(checked);
  }, [checked]);

  return (
    <StyledSwitchLabel
      htmlFor={id || 'switch'}
      margin={margin}
      className={clsx(className, disabled && 'disabled', revert && 'revert')}
      justifyContent={justifyContent}
      gap={gap}
    >
      {leftLabel && (
        <Typography
          type='body2'
          text={leftLabel}
          color={labelColor}
          {...labelProps}
        />
      )}
      <StyledSwitch
        name={name}
        checked={stateChecked}
        onChange={onClick}
        disabled={disabled}
        readOnly={readOnly}
        uncheckedIcon={false}
        checkedIcon={false}
        offColor={theme.color?.general.darker}
        onColor={theme.color?.general.darker}
        offHandleColor={theme.color?.general.lightest}
        onHandleColor={theme.color?.primary.main}
        $borderColor={theme.color?.general.darker}
        handleDiameter={handleDiameter || 20}
        height={height || 24}
        width={width || 40}
        className={clsx('switch', inProgress && 'inProgress')}
        id={`${id} switch`}
      />
      {label && (
        <Typography
          type='body2'
          text={label}
          variant={textVariant}
          color={labelColor}
          {...labelProps}
        />
      )}
    </StyledSwitchLabel>
  );
};

export default withTheme(Switch);

Switch.propTypes = {
  checked: T.bool,
  disabled: T.bool,
  label: T.string,
  margin: T.string,
  onChange: T.func,
  readOnly: T.bool,
  theme: T.object,
};
