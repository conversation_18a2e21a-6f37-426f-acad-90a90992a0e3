import Switch from 'react-switch';
import styled from 'styled-components';

export const StyledSwitch = styled(Switch)`
  border-radius: 2em !important;
  border: ${({ $borderColor }) => `1px solid ${$borderColor}`};
  margin: 0 8px;

  .react-switch-handle {
    width: 16px !important;
    height: 16px !important;
    top: 4px !important;
    transform: translateX(4px) !important;
    border: ${({ borderColor }) => `1px solid ${borderColor} !important`};
    box-shadow: none !important;
  }

  //&.inProgress {
  //  .react-switch-handle {
  //    display: none !important;
  //  }
  //}

  &:has(input) {
    .react-switch-bg {
      width: 48px !important;
    }
  }
  &:has(input[aria-checked='true']) {
    .react-switch-handle {
      transform: translateX(28px) !important;
    }
  }
  &.disabled {
    cursor: not-allowed;
  }
`;

export const StyledSwitchLabel = styled.label`
  margin: ${({ margin }) => margin};
  display: flex;
  align-items: center;
  justify-content: ${({ justifyContent }) => justifyContent};
  gap: ${({ gap }) => gap};
  cursor: auto;
  .typography {
    cursor: default;
  }
  &.revert {
    .switch {
      order: 1;
    }
  }
`;
