import { useState } from 'react';

import clsx from 'clsx';
import FlexRow from '../../atoms/FlexRow';
import HeaderMenuIcon from '../HeaderMenuIcon/HeaderMenuIcon.tsx';
import { StyledDropdown, StyledMenu } from './styled';

interface DropdownMenuProps {
  children?: any;
  buttonProps?: any;
  MenuButton?: any;
  openDirection?: 'left' | 'right';
  className?: string;
  dropdownItems?: any;
  onItemClick?: any;
}

const DropdownMenu = ({
  children,
  buttonProps,
  MenuButton: MenuButtonProp,
  openDirection = 'left',
  className,
  dropdownItems,
  onItemClick,
}: DropdownMenuProps) => {
  const [menuOpened, setMenuOpened] = useState(false);

  const MenuButton = MenuButtonProp || HeaderMenuIcon;

  const onBtnClick = () => {
    setMenuOpened((prev) => !prev);
  };

  const onClick = (id: string | number) => () => {
    if (onItemClick) onItemClick(id);
  };

  return (
    <StyledDropdown className={className}>
      <MenuButton onClick={onBtnClick} opened={menuOpened} {...buttonProps} />
      <StyledMenu
        className={clsx(menuOpened && 'opened', openDirection)}
        onClick={() => setMenuOpened(false)}
      >
        {dropdownItems?.length &&
          dropdownItems.map((option: any, index: number) => {
            return (
              <FlexRow
                onClick={onClick(option.id || index)}
                key={index}
                className='w-full text-nowrap text-left'
              >
                {option.label || option}
              </FlexRow>
            );
          })}
        {children}
      </StyledMenu>
    </StyledDropdown>
  );
};

export default DropdownMenu;
