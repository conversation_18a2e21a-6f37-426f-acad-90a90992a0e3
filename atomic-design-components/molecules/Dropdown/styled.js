'use client';
import styled from 'styled-components';

export const StyledDropdown = styled.div`
  position: relative;
`;

export const StyledMenu = styled.div`
  display: none;
  position: absolute;
  background-color: ${({ theme }) => theme.color?.primary.light};
  padding: 5px 5px;
  box-shadow: 0 10px 16px -4px rgb(71 95 139 / 60%);
  z-index: 2;
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  border: 1px solid ${({ theme }) => theme.color?.general.lighter};
  color: ${({ theme }) => theme.color?.general.white};
  &.right {
    left: 0;
    right: auto;
  }
  &.left {
    right: 0;
    top: 100%;
  }
  &.opened {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-top: 5px;
  }

  & > * {
    padding: 10px 10px;
    cursor: pointer;
    border-top: 1px solid ${({ theme }) => theme.color?.general.lighter};
  }
  & > *:first-child {
    border-top: none;
  }
`;
