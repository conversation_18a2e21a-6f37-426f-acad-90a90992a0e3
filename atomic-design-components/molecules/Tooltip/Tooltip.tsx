'use client';
import React from 'react';

// import Icon from '../../atoms/Icon/index.js';
import Typography from '../../atoms/Typography';
import { StyledTooltip } from './styled';
import clsx from 'clsx';

interface TooltipProps {
  // arrowPosition?: 'left' | 'center' | 'right';
  backgroundColor?: string;
  borderColor?: string;
  borderRadius?: string;
  bottom?: string;
  children?: React.ReactNode;
  className?: string;
  disableTooltip?: boolean;
  left?: string;
  right?: string;
  padding?: string;
  text?: any;
  textLineHeight?: string;
  textVariant?: string;
  textColor?: string;
  tooltipClassName?: string;
  tooltipContent?: React.ReactNode;
  // tipPosition?: 'top' | 'bottom';
  top?: string;
  whiteSpace?: string;
  width?: string;
  textAlign?: string;
}

const Tooltip = ({
  // arrowPosition = 'center', // 'left', 'center', 'right
  backgroundColor,
  borderRadius,
  bottom,
  children,
  className,
  disableTooltip,
  left,
  right,
  padding,
  text,
  textLineHeight,
  textVariant,
  textColor,
  tooltipClassName,
  tooltipContent,
  // tipPosition = 'top', // 'top', 'bottom'
  top,
  whiteSpace,
  textAlign,
  width = '258px',
  ...rest
}: TooltipProps) => {
  const [show, setShow] = React.useState(false);

  if (disableTooltip) {
    return children;
  }

  return (
    <StyledTooltip
      backgroundColor={backgroundColor}
      textColor={textColor}
      className={className}
      left={left}
      right={right}
      whiteSpace={whiteSpace}
      width={width}
      padding={padding}
      borderRadius={borderRadius}
      bottom={bottom}
      top={top}
      textAlign={textAlign}
      onMouseEnter={() => setShow(true)}
      onMouseLeave={() => setShow(false)}
    >
      <div
        style={show ? { visibility: 'visible' } : {}}
        className={clsx(tooltipClassName, 'tooltip')}
      >
        <Typography
          type={textVariant || 'body1'}
          lineHeight={textLineHeight}
          className='tooltipText'
        >
          {text}
        </Typography>
        {tooltipContent}
        {/* <Icon
          name='tooltipArrow'
          className='tooltip-arrow'
          fill={backgroundColor||theme.color?.general.dark}
        /> */}
      </div>
      <div {...rest}>{children}</div>
    </StyledTooltip>
  );
};

export default Tooltip;
