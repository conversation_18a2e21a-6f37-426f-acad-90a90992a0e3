'use client';
import styled from 'styled-components';

export const StyledTooltip = styled.div<{
  backgroundColor?: string;
  whiteSpace?: string;
  left?: string;
  right?: string;
  bottom?: string;
  top?: string;
  width?: string;
  borderColor?: string;
  textColor?: string;
  padding?: string;
  borderRadius?: string;
  textAlign?: string;
}>`
  position: relative;

  .tooltip {
    position: absolute;
    background: ${({ backgroundColor, theme }) =>
      backgroundColor || theme.color?.general.dark};
    visibility: hidden;
    border-radius: ${({ theme }) => theme.size.border.radius.main};
    z-index: 10;
    min-width: 100px;
    text-align: ${({ textAlign }) => textAlign || 'center'};
    white-space: ${({ whiteSpace }) => whiteSpace || 'nowrap'};
    left: ${({ left }) => left};
    right: ${({ right }) => right};
    bottom: ${({ bottom }) => bottom};
    top: ${({ top }) => top};
    width: ${({ width }) => width};
    padding: ${({ padding }) => padding || '10px'};

    .tooltipText {
      color: ${({ textColor, theme }) =>
        textColor || theme.color?.general.lightest};
    }

    //&.right {
    //  right: -5px;
    //}
    //&.left {
    //  left: -5px;
    //}
    //
    //&.bottom {
    //  bottom: 0;
    //}
    //&.top {
    //  bottom: 95%;
    //}
  }

  .tooltip-arrow {
    position: absolute;
    top: -7px;
    right: 20px;

    //&.left {
    //  left: 10%;
    //}
    //&.center {
    //  left: 45%;
    //}
    //&.right {
    //  right: 10%;
    //}
  }
`;
