import clsx from 'clsx';
import Icon from '../../atoms/Icon';
import { StyledCloseIcon } from './styled';

const CloseIcon = ({
  className,
  onClick,
  size = 'medium',
  disabled = false,
  fill,
  wrapperWidth,
  wrapperHeight,
  ...props
}: {
  className?: string;
  onClick?: any;
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  fill?: string;
  wrapperWidth?: number;
  wrapperHeight?: number;
}) => {
  return (
    <StyledCloseIcon
      className={clsx(className, size, disabled && 'disabled', 'closeIcon')}
      onClick={onClick}
    >
      <Icon
        name='cross'
        fill={fill}
        wrapperWidth={wrapperWidth}
        wrapperHeight={wrapperHeight}
        {...props}
      />
    </StyledCloseIcon>
  );
};

export default CloseIcon;
