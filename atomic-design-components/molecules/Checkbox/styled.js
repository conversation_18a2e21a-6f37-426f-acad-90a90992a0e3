'use client';
import styled from 'styled-components';

export const StyledCheckboxWrapper = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;

  & .typography {
    margin-left: 8px;
    // color: ${({ theme }) => theme.color?.general.dark};
  }
`;

export const StyledCheckbox = styled.div`
  display: flex;
  flex-direction: row;

  &.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    & input {
      cursor: not-allowed;
    }
    &:not(.checked) {
      .icon svg,
      .icon svg path {
        fill: ${({ theme }) => theme.color?.general.darker};
      }
    }
    &.checked {
      .icon svg path.fill {
        fill: ${({ theme }) => theme.color?.general.darker};
      }
    }
  }

  & input {
    position: absolute;
    z-index: 1;
    opacity: 0;
    width: 20px;
    cursor: pointer;
    height: 20px;
  }
  &:not(.checked):not(.disabled):not(.hasError):hover {
    .icon svg path {
      stroke: #475569;
    }
  }
  &:not(.disabled):not(.hasError).checked:hover {
    .icon svg path.fill {
      fill: ${({ theme }) => theme.color?.primary.darker};
    }
  }

  &:not(.checked).hasError {
    .icon svg path {
      stroke: ${({ theme }) => theme.color?.status.error};
    }
  }
  &.checked.hasError {
    .icon svg path.fill {
      fill: ${({ theme }) => theme.color?.status.error};
    }
  }
`;
