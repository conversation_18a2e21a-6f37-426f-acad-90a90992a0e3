import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';
import { withTheme } from 'styled-components';

import { useTranslation } from '@/app/i18n/client';
import { FlexRow } from '@/atomic-design-components';
import { useParams } from 'next/navigation';
import Icon from '../../atoms/Icon';
import Typography from '../../atoms/Typography';
import Link from 'next/link';
import { StyledLabel } from '../Input/styled';
import { StyledCheckboxWrapper, StyledCheckbox } from './styled';

const Checkbox = ({
  checked,
  className,
  disabled,
  disableLabelClick,
  fontWeight,
  handleChange,
  label,
  labelProps,
  labelType,
  name,
  readOnly,
  type = 'checkbox',
  value,
  error,
  link,
  linkTarget,
  theme,
  ...otherProps
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const onChange = (e) => {
    handleChange && handleChange(e.target.checked, e);
  };

  const onLabelClick = () => {
    if (!disableLabelClick) {
      // Check if label click is disabled
      handleChange &&
        handleChange(type === 'radio' ? true : !checked, {
          target: { name, value },
        });
    }
  };

  return (
    <StyledCheckboxWrapper className={clsx(className, type)}>
      <FlexRow>
        <StyledCheckbox
          className={clsx(
            checked && 'checked',
            error && 'hasError',
            disabled && 'disabled',
            'checkbox',
          )}
        >
          <input
            type={type}
            checked={checked}
            disabled={disabled}
            name={name}
            onChange={disabled || readOnly ? undefined : onChange}
            readOnly={readOnly}
            value={value}
            {...otherProps}
          />
          {checked ? (
            <Icon name={`${type}Checked`} />
          ) : (
            <Icon name={`${type}Unchecked`} />
          )}
          {(label && !link) || disabled ? (
            <Typography
              type={labelType || 'label1'}
              onClick={!disabled && !readOnly ? onLabelClick : undefined}
              fontWeight={fontWeight}
              className='w-full'
              {...labelProps}
            >
              {label}
            </Typography>
          ) : null}
        </StyledCheckbox>
        {label && link && !disabled ? (
          <Link href={link} target={linkTarget}>
            <Typography
              type={labelType || 'label1'}
              onClick={!disabled && !readOnly ? onLabelClick : undefined}
              fontWeight={fontWeight}
              className='w-full'
              {...labelProps}
            >
              {label}
            </Typography>
          </Link>
        ) : null}
      </FlexRow>
      {!!error && typeof error === 'string' && (
        <StyledLabel
          className='error bottom'
          color={theme.color?.status.error}
          variant='label2'
        >
          {name === 'terms' ? t('mustAgreeTerms') : t(error) || error}
        </StyledLabel>
      )}
    </StyledCheckboxWrapper>
  );
};

export default withTheme(Checkbox);

Checkbox.propTypes = {
  type: T.string.isRequired,
  link: T.string,
  linkTarget: T.string,
  disableLabelClick: T.bool,
};
