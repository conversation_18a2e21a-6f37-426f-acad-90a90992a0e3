'use client';
import clsx from 'clsx';
import { pick } from 'dot-object';
import { PropTypes as T } from 'prop-types';
import { useCallback, useEffect, useRef, useState } from 'react';
import ReactSelect, { components } from 'react-select';
import AsyncSelect from 'react-select/async';
import { withTheme } from 'styled-components';
import { useParams } from 'next/navigation';
import { useTranslation } from '@/app/i18n/client';
import Icon from '../../atoms/Icon';
import Tag from '../../atoms/Tag';
import ClearIndicator from './components/ClearIndicator';
import Control from './components/Control';
import {
  StyledFlexRow,
  StyledLabel,
  StyledOptionRow,
  StyledSelect,
  StyledWrapper,
} from './styled';
import { StyledLabel as StyledError } from '../../molecules/Input/styled';

const Select = ({
  autosize,
  className,
  // createLabelText = 'Create',
  // createOptionPosition = 'first',
  customGetOptionLabel,
  error,
  fullWidth,
  getOptionBeforeTextComponent,
  iconName = 'chevronDown',
  iconProps,
  inputValue,
  isClearable,
  isSearchable = true,
  isDisabled,
  isMulti,
  label,
  labelType = 'top',
  labelKey = 'label',
  labelWidth = '150px',
  loadOptions,
  menuPlacement,
  menuPortalTarget,
  MultiValue: MultiValueCustom,
  getSelectedOption,
  noOptionsMessage,
  onChange,
  // onCreateOption,
  onInputChange,
  onOptionClick,
  optionIconColor,
  optionIconKey,
  options,
  placeholder,
  readOnly,
  required,
  theme,
  value,
  valueKey = 'id',
  width,
  withShadowScreen,
  menuWidth,
  optionClass,
  withoutDisabledStyling,
  controlShouldRenderValue,
  ...otherProps
}) => {
  const { lng } = useParams();
  const { t } = useTranslation(lng);

  const selectRef = useRef(null);
  const selectWrapperRef = useRef(null);
  const isFocusedRef = useRef(false);
  const [customStyles, setCustomStyles] = useState({});
  const [portalMenuMaxHeight, setPortalMenuMaxHeight] = useState(null);
  const [portalMenuPlacement, setPortalMenuPlacement] = useState(null);
  const [shadowShown, setShadowShown] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    const styles = () =>
      autosize && {
        container: (base, state) => {
          isFocusedRef.current = state.isFocused;
          return {
            ...base,
            display: 'inline-block',
          };
        },
        placeholder: (base, state) => {
          return {
            ...base,
            ...(isFocusedRef.current && state.value
              ? {}
              : {
                  position: 'static',
                  top: 'auto',
                  transform: 'none',
                }),
          };
        },
        input: (base, state) => {
          return {
            ...base,
            ...(isFocusedRef.current && state.value
              ? {}
              : {
                  position: 'absolute',
                  top: '2px',
                  // transform: 'translateY(-40%)',
                }),
          };
        },
        singleValue: (base, state) => {
          return {
            ...base,
            maxWidth: 'none',
            ...(isFocusedRef.current && state.value
              ? {}
              : {
                  position: 'static',
                  top: 'auto',
                  transform: 'none',
                }),
          };
        },
        valueContainer: (base) => ({
          ...base,
        }),
      };
    setCustomStyles(styles());
  }, []);

  useEffect(() => {
    if (withShadowScreen) {
      isMenuOpen && setShadowShown(true);
      !isMenuOpen && setShadowShown(false);
    }
  }, [isMenuOpen]);

  const handleChange = (option) => {
    selectRef.current.blur();
    if (onChange) onChange(option);
  };

  const onClear = () => {
    setTimeout(() => selectRef.current.blur(), 0);
  };

  const DropdownIndicator = (props) => {
    return (
      <components.DropdownIndicator {...props}>
        <Icon
          name={iconName}
          className={iconName}
          width={16}
          height={16}
          fill={theme.color?.general.lightest}
          {...iconProps}
        />
      </components.DropdownIndicator>
    );
  };

  const Option = useCallback(
    (props) => {
      // const optionValue =
      //   valueKey && props.data ? pick(valueKey, props.data) : props.value;
      const optionIcon =
        props.data.iconName || (optionIconKey && props.data[optionIconKey]);

      //for debugging opened menu
      // setTimeout(() => {
      //   debugger;
      // }, 5000);
      return (
        <StyledOptionRow
          onClick={onOptionClick && onOptionClick(props)}
          className={optionIcon ? 'withLeftIcon' : ''}
        >
          {getOptionBeforeTextComponent && getOptionBeforeTextComponent(props)}
          <components.Option className={optionClass} {...props}>
            {optionIcon && (
              <Icon
                name={optionIcon}
                height={20}
                width={20}
                fill={optionIconColor}
              />
            )}
            {props.children}
          </components.Option>
          {/* {value && valueKey && optionValue === pick(valueKey, value) && (
            <Icon className="rightIcon" name="check" />
          )} */}
        </StyledOptionRow>
      );
    },
    [
      valueKey,
      optionIconKey,
      onOptionClick,
      optionIconColor,
      getOptionBeforeTextComponent,
      value,
    ],
  );

  const ValueContainer = ({ children, ...props }) => {
    const { hasValue } = props;
    if (!hasValue) {
      return (
        <components.ValueContainer ref={props.innerRef} {...props}>
          {children}
        </components.ValueContainer>
      );
    }
    const customValue =
      getSelectedOption && !isMenuOpen
        ? [getSelectedOption(props), children[1]]
        : null;

    return (
      <components.ValueContainer ref={props.innerRef} {...props}>
        {customValue
          ? customValue.map((child, index) => <div key={index}>{child}</div>)
          : children}
      </components.ValueContainer>
    );
  };

  const MultiValue = (props) => {
    return (
      <components.MultiValueContainer {...props}>
        <Tag type='gray' removeProps={props.removeProps} withCrossIcon>
          <components.MultiValueLabel {...props} />
        </Tag>
      </components.MultiValueContainer>
    );
  };

  const getOptionLabel = (option) => {
    const hasSpareLabelKeyToCheck = typeof labelKey !== 'string';
    const firstLabelValue = pick(
      hasSpareLabelKeyToCheck ? labelKey[0] : labelKey,
      option,
    );

    return hasSpareLabelKeyToCheck
      ? firstLabelValue || pick(labelKey[1] || '', option)
      : firstLabelValue;
  };

  const portalMenuCalculation = () => {
    if (selectWrapperRef?.current) {
      const selectBounds = selectWrapperRef?.current?.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      // set menu placement
      const menuPlacementCalc =
        windowHeight - selectBounds.bottom >= 200 ||
        windowHeight / 2 >= selectBounds.top + 19
          ? 'bottom'
          : 'top';
      setPortalMenuPlacement(menuPlacementCalc);
      // set menu height
      if (menuPlacementCalc === 'top') {
        setPortalMenuMaxHeight(`${selectBounds.top - 16}px`);
      } else {
        setPortalMenuMaxHeight(`${windowHeight - selectBounds.bottom - 16}px`);
      }
    }
  };

  const getSelect = useCallback(() => {
    return (
      <StyledWrapper
        ref={selectWrapperRef}
        className={clsx(
          !label && className,
          'selectWrapper',
          isDisabled && !withoutDisabledStyling && 'disabled',
        )}
        $fullWidth={fullWidth}
        width={width}
      >
        <StyledSelect
          ref={selectRef}
          blurInputOnSelect
          openMenuOnClick
          onMenuOpen={() => {
            menuPortalTarget && portalMenuCalculation();
            setIsMenuOpen(true);
          }}
          onMenuClose={() => {
            selectRef.current.blur();
            setIsMenuOpen(false);
          }}
          onClear={onClear}
          controlShouldRenderValue={controlShouldRenderValue || !isMenuOpen}
          // menuIsOpen
          menuPlacement={menuPlacement || portalMenuPlacement || 'auto'}
          menuPortalTarget={menuPortalTarget}
          as={loadOptions ? AsyncSelect : ReactSelect}
          inputValue={inputValue}
          onInputChange={onInputChange}
          loadOptions={loadOptions}
          isSearchable={isSearchable}
          // autoFocus
          // defaultInputValue
          // defaultValue=
          themeCustom={theme}
          styles={{
            ...customStyles,
            ...(menuPortalTarget && {
              menuPortal: (base) => ({
                ...base,
                zIndex: 9999,
              }),
              menu: (base) => ({
                ...base,
                backgroundColor: theme.color?.general.darker,
                borderRadius: theme.size.border.radius.main,
                boxShadow:
                  '0px 0px 0px 0px #e1e2e3 inset, 0px 8px 24px 0px rgba(26, 27, 28, 0.16)',
                margin: '4px 0px',
                width: menuWidth ? menuWidth : '100%',
              }),
              menuList: (base) => ({
                ...base,
                borderRadius: theme.size.border.radius.main,
                padding: '8px 0',
                maxHeight: portalMenuMaxHeight,
              }),
              option: (base) => ({
                ...base,
                backgroundColor: theme.color?.general.darker,
              }),
            }),
          }}
          menuWidth={menuWidth}
          t={t}
          className={clsx(
            'select',
            labelType === 'top' && 'labelTop',
            !!error && 'hasError',
            autosize && 'autosize',
          )}
          getNewOptionData={(inputValue, optionLabel) => {
            // TODO: When we'll have another case of creatable options add necessary values to config (=> this object)
            return { [valueKey]: inputValue, [labelKey]: optionLabel };
          }}
          noOptionsMessage={() => noOptionsMessage}
          value={value}
          onChange={handleChange}
          options={options}
          classNamePrefix='react-select'
          closeMenuOnSelect={!isMulti}
          isClearable={isClearable}
          isMulti={isMulti}
          isDisabled={isDisabled || readOnly}
          components={{
            Control,
            DropdownIndicator,
            Option,
            ClearIndicator,
            MultiValue: MultiValueCustom || MultiValue,
            ValueContainer,
          }}
          placeholder={placeholder || ''}
          getOptionLabel={
            customGetOptionLabel
              ? (option) =>
                  customGetOptionLabel(option, otherProps.t, otherProps.lng)
              : labelKey && getOptionLabel
          }
          getOptionValue={valueKey && ((option) => option[valueKey])}
          {...otherProps}
        />

        {!!error && (
          <StyledError
            className='error bottom'
            color={theme.color?.status.error}
            variant='label2'
          >
            {t(error) || error}
          </StyledError>
        )}
      </StyledWrapper>
    );
  }, [
    value,
    t,
    options,
    placeholder,
    error,
    inputValue,
    isMenuOpen,
    isDisabled,
  ]);

  return label ? (
    <StyledFlexRow
      className={clsx(className, labelType === 'top' && 'labelTop', 'wrapper')}
      $fullWidth={fullWidth}
    >
      <StyledLabel
        type='label1'
        text={label}
        $labelWidth={labelWidth}
        className={clsx('label', required && 'required', labelType)}
      />
      {getSelect()}
    </StyledFlexRow>
  ) : (
    <>
      <div
        className={clsx(
          'absolute left-0 top-0 z-[1] h-full w-full',
          shadowShown ? 'block' : 'hidden',
        )}
        style={{
          backgroundColor: 'rgba(15, 23, 42, 0.8)',
        }}
      />
      {getSelect()}
    </>
  );
};

export default withTheme(Select);

Select.propTypes = {
  createLabelText: T.string,
  createOptionPosition: T.oneOf(['first', 'last']),
  isDisabled: T.bool,
  fullWidth: T.bool,
  iconName: T.string,
  label: T.string,
  labelKey: T.oneOfType([T.string, T.array]),
  labelType: T.oneOf(['top', 'left']),
  labelWidth: T.string,
  menuPlacement: T.oneOf(['auto', 'bottom', 'top']),
  menuPortalTarget: T.HTMLElement,
  name: T.string,
  onChange: T.func,
  placeholder: T.string,
  valueKey: T.string,
  width: T.string,
  withShadowScreen: T.bool,
};
