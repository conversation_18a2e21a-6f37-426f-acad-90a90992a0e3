'use client';
import { components } from 'react-select';
import CloseIcon from '../../../molecules/CloseIcon';

const ClearIndicator = ({ children, ...props }) => {
  const { isDisabled, error, themeCustom: theme } = props.selectProps;

  const clearValue = (e) => {
    e.stopPropagation();
    props.selectProps.onClear && props.selectProps.onClear();
    props.clearValue();
  };

  const innerProps = {
    ...props.innerProps,
    onMouseDown: clearValue,
    onTouchEnd: clearValue,
  };

  return (
    <components.ClearIndicator {...props} innerProps={innerProps}>
      <CloseIcon
        fill={
          isDisabled
            ? theme.color?.general.light
            : error
              ? theme.color?.status.error
              : theme.color?.general.lightest
        }
      />
    </components.ClearIndicator>
  );
};

export default ClearIndicator;
