'use client';
import styled from 'styled-components';

import { getTokens } from '../../';
import Typography from '../../atoms/Typography';

export const StyledLabel = styled(Typography)`
  display: block;
  min-width: ${({ $labelWidth }) => $labelWidth};
  &.top {
    margin-bottom: 4px;
  }
  &.bottom {
    margin-top: 4px;
  }
  &.required::after {
    content: ' *';
    color: ${({ theme }) => theme.color?.status.error};
  }
`;

export const StyledFlexRow = styled.div`
  display: flex;
  align-items: center;
  width: ${({ $fullWidth, width }) => width || ($fullWidth && '100%')};

  &.labelTop {
    display: block;
  }
`;

export const StyledOptionRow = styled.div`
  display: flex;
  align-items: center;
  &:hover {
    color: ${({ theme }) => theme.color?.general.light};
    //background-color: ${({ theme }) => theme.color?.general.lightest};
  }
  &:not(:last-child) {
    border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
  }
  .icon {
    height: 100%;

    &.rightIcon {
      padding-right: 16px;
    }
  }

  .react-select__option {
    display: flex;
    justify-content: flex-start;
    .icon {
      padding-right: 10px;
    }
    ${({ theme }) => getTokens('typography-body1-black-large', theme)};
    padding: 10px 16px 10px 8px;
    border-radius: 0;

    &:hover {
      background-color: ${({ theme }) => theme.color?.general.light};
      cursor: pointer;
    }
  }

  .react-select__option--is-focused {
    background-color: transparent;
  }

  .react-select__option--is-selected {
    color: ${({ theme }) => theme.color?.primary.main};
  }
`;

export const StyledWrapper = styled.div`
  align-items: center;
  position: relative;
  width: ${({ $fullWidth, width }) => width || ($fullWidth && '100%')};

  &.disabled {
    cursor: not-allowed;
    .react-select__control {
      background-color: #28364a;
      border-color: transparent;
    }
  }

  &.fullWidthList {
    .react-select__menu-list {
      width: fit-content;
      & * {
        text-wrap: nowrap;
      }
    }
  }
`;

export const StyledSelect = styled.div`
  flex: 1;

  .react-select__control {
    border: 1px solid ${({ theme }) => theme.color?.general.dark};
    padding: 0 12px;
    gap: 8px;
    flex-wrap: nowrap;
    height: 40px;
    ${({ theme }) => getTokens('select-primary-black-large', theme)};

    &:hover {
      border: 1px solid #475569;
      cursor: pointer;
    }
  }

  &.hasError {
    .react-select__control {
      border-color: ${({ theme }) => theme.color?.status.error};
    }
  }

  .react-select__control--is-focused {
    box-shadow: none;
    border-color: ${({ theme }) => theme.color?.primary.main};

    &:hover {
      border-color: ${({ theme }) => theme.color?.primary.main};
    }
  }

  .react-select__control--menu-is-open {
    border-color: ${({ theme }) => theme.color?.primary.main} !important;
    flex-wrap: nowrap;
    .react-select__input-container {
      color: ${({ theme }) => theme.color?.general.light};
    }
  }

  .react-select__value-container {
    padding: 0;
    min-width: fit-content;
    min-height: ${({ theme }) =>
      theme.components.typography.body1.black.large['line-height']};
    display: flex;
    flex-wrap: nowrap;
    pointer-events: none;

    & > div:last-child {
      margin: 0;
      padding: 0;
      top: initial;
    }

    &.react-select__value-container--is-multi {
      & > div {
        display: flex;
        margin: 5px 2px 5px 0;

        &:last-child {
          line-height: 18px;
        }
        .react-select__input {
          width: 2px !important;
        }
      }
    }
  }

  .react-select__placeholder {
    color: ${({ theme }) => theme.color?.general.light};
  }
  .react-select__single-value {
    margin: 0;
    color: ${({ theme }) => theme.color?.general.lightest};
  }
  .react-select__indicators {
    gap: 8px;
  }
  .react-select__dropdown-indicator {
    padding-right: 0;
    padding-left: 0;
    pointer-events: none;
  }
  .react-select__clear-indicator {
    padding: 0;
  }
  .react-select__indicator-separator {
    display: none;
  }

  .react-select__menu {
    box-shadow:
      0px 0px 0px 0px #e1e2e3 inset,
      0px 8px 24px 0px rgba(26, 27, 28, 0.16);
    z-index: 300;
    width: 100%;
    border-radius: 9px;
    margin-top: 4px;
  }

  &.autosize {
    .react-select__menu {
      width: auto;
    }
  }

  .react-select__menu-list {
    padding: 8px 0;
    border-radius: ${({ theme }) => theme.size.border.radius.main};
    width: ${({ menuWidth }) => menuWidth || '100%'};

    @media (max-width: ${({ theme }) => theme.breakpoints?.sm}px) {
      max-height: 200px;
    }
    &,
    & * {
      background-color: ${({ theme }) => theme.color?.general.darker};
    }
    .react-select__option * {
      background-color: transparent;
    }
    // @media (max-width: 768px) {
    //   max-height: 200px;
    // }
  }

  .react-select__menu-notice--no-options {
    ${({ theme }) => getTokens('typography-body1-black-large', theme)};
    text-align: left;
    min-width: 120px;
  }
`;
