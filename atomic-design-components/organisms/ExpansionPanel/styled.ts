'use client';
import styled from 'styled-components';

export const StyledExpansionPanel = styled.div<any>`
  padding: ${({ padding }) => padding};
  transition: max-height 0.3s ease-in-out;
  background-color: ${({ theme }) => theme.color?.general.darker};
  border-radius: ${({ theme }) => theme.size.border.radius.main};

  &.opened {
    // margin-bottom: ${({ marginBottomWhenOpened }) => marginBottomWhenOpened};
  }
`;

export const StyledHeaderWrapper = styled.div<any>`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: border-radius 0.3s ease-in-out;
  background-color: ${({ headerBackgroundColor, theme }) =>
    headerBackgroundColor || theme.color?.general.darker};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: ${({ headerPadding }) => headerPadding || '4px 8px 0 16px'};

  &.opened {
    border-radius: ${({ theme }) =>
      `${theme.size.border.radius.main} ${theme.size.border.radius.main} 0 0`};

    & .headerChevronIcon {
      transform: rotate(180deg);
    }
  }

  &.disabled {
    cursor: default;
  }
`;

export const StyledDetailsWrapper = styled.div<any>`
  overflow: hidden;
  transition:
    max-height 0.3s ease-in-out,
    border-radius 0.3s ease-in-out;
  width: 100%;
  background-color: ${({ theme }) => theme.color?.general.darker};
  border-radius: ${({ theme }) => theme.size.border.radius.main};
  padding: ${({ detailsPadding }) => detailsPadding || '0 8px 4px'};

  &.opened {
    border-radius: ${({ theme }) =>
      `0 0 ${theme.size.border.radius.main} ${theme.size.border.radius.main}`};
  }
`;
