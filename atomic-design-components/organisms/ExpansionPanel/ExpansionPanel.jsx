import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';
import { useCallback, useEffect, useRef, useState } from 'react';

import Icon from '../../atoms/Icon';
import Typography from '../../atoms/Typography/Typography';
import { useLocalStorage } from './hooks/useLocalStorage';
import { StyledDetailsWrapper, StyledExpansionPanel, StyledHeaderWrapper } from './styled';
import { isClientReady } from '../../../utils/index.ts';
import { getFromLocalStorage } from '../../../utils/localStorage.ts';

const ExpansionPanel = (props) => {
  const {
    className,
    children,
    disabled,
    align,
    header,
    headerBackgroundColor,
    headerIconRightProps = {},
    headerPadding,
    detailsPadding,
    id,
    // headerIconLeftProps,
    initialOpened,
    isHidden,
    headerTypographyProps,
    marginBottomWhenOpened,
    padding,
    panelName,
    withChevronInHeader = true,
    activePanelId = '',
    controlledOpened,
    onToggle,
    ...otherProps
  } = props;

  const stateFromLs = getFromLocalStorage('expPanelStates');
  const currentPanelStates = isClientReady() ? stateFromLs || {} : {};

  const hasLocalStorageState = panelName && currentPanelStates?.[panelName] !== undefined;

  const refDetailsWrapper = useRef(null);
  const refHeaderWrapper = useRef(null);

  const [displayed, setDisplayed] = useState(initialOpened);
  const [opened, setOpened] = useState(
    hasLocalStorageState ? currentPanelStates?.[panelName] : initialOpened,
  );
  const [maxHeight, setMaxHeight] = useState('0px');
  useLocalStorage({
    panelName,
    opened,
    id,
    setDisplayed,
    initialOpened,
    setOpened,
  });

  useEffect(() => {
    if (activePanelId && id) {
      setDisplayed(id === activePanelId);
      setOpened(id === activePanelId);
    }
  }, [activePanelId, id]);

  useEffect(() => {
    if (displayed) setOpened(true);
  }, [displayed]);

  useEffect(() => {
    if (refDetailsWrapper.current) {
      setMaxHeight('1000px');
    }
  }, [opened]);

  useEffect(() => {
    if (controlledOpened !== undefined) {
      setOpened(controlledOpened);
      setDisplayed(controlledOpened);
    }
  }, [controlledOpened]);

  const toggleOpened = useCallback(() => {
    if (!opened) setDisplayed(true);
    if (displayed) setOpened(false);
    if (onToggle) onToggle(!opened);
  }, [opened, displayed, onToggle]);

  if (isHidden) {
    return (
      <StyledExpansionPanel
        align={align}
        className={clsx(className, 'expansionPanel')}
        padding={padding}
        {...otherProps}
      >
        {children}
      </StyledExpansionPanel>
    );
  }

  return (
    <StyledExpansionPanel
      marginBottomWhenOpened={marginBottomWhenOpened}
      align={align}
      className={clsx(
        className,
        opened && 'opened',
        'expansionPanel',
        !children || (Array.isArray(children) && !children.length && 'empty'),
      )}
      padding={padding}
      // style={{ maxHeight: opened ? maxHeight : '48px' }}
      {...otherProps}
    >
      <StyledHeaderWrapper
        ref={refHeaderWrapper}
        className={clsx(opened && 'opened', disabled && 'disabled', 'panelHeader')}
        onClick={disabled ? undefined : toggleOpened}
        headerBackgroundColor={headerBackgroundColor}
        headerPadding={headerPadding}
      >
        {/*{headerIconLeftProps && <Icon {...headerIconLeftProps} />}*/}
        <Typography type='body2' {...headerTypographyProps}>
          {header}
        </Typography>
        {withChevronInHeader && (
          <Icon
            name='chevronDown'
            className='headerChevronIcon'
            strokeWidth={1}
            wrapperWidth={40}
            wrapperHeight={40}
            width={20}
            height={20}
            {...headerIconRightProps}
          />
        )}
      </StyledHeaderWrapper>
      {displayed && (
        <StyledDetailsWrapper
          ref={refDetailsWrapper}
          className={clsx(opened && 'opened', 'detailsWrapper')}
          detailsPadding={detailsPadding}
          style={{ maxHeight: opened ? maxHeight : '0px' }}
        >
          {children}
        </StyledDetailsWrapper>
      )}
    </StyledExpansionPanel>
  );
};

export default ExpansionPanel;

ExpansionPanel.propTypes = {
  align: T.oneOf(['inherit', 'left', 'center', 'right', 'justify']),
  children: T.node,
  className: T.string,
  disabled: T.bool,
  header: T.oneOfType([T.string, T.object]),
  isHidden: T.bool,
  headerBackgroundColor: T.string,
  // detailsBackgroundColor: T.string,
  marginBottomWhenOpened: T.string,
  withChevronInHeader: T.bool,
  activePanelId: T.string,
  controlledOpened: T.bool,
  onToggle: T.func,
};
