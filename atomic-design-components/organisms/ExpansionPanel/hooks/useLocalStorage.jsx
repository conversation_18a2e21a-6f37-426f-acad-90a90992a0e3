import { nanoid } from 'nanoid';
import { useEffect, useRef, useState } from 'react';
import { getFromLocalStorage, saveToLocalStorage } from '../../../../utils/localStorage.ts';

export const PANEL_LS_STATE_KEY = 'expPanelStates';

const isClientReady = () => typeof window !== 'undefined';

export const useLocalStorage = ({
  panelName,
  opened,
  id,
  setDisplayed,
  initialOpened,
  setOpened,
}) => {
  const [currentPanelStates, setCurrentPanelStates] = useState({});
  const panelId = useRef(id || nanoid());

  useEffect(() => {
    if (isClientReady()) {
      setCurrentPanelStates(getFromLocalStorage(PANEL_LS_STATE_KEY) || {});
    } else {
      setCurrentPanelStates({});
    }
  }, []);

  useEffect(() => {
    const expPanelStates = panelName
      ? { ...currentPanelStates, [panelName]: opened }
      : currentPanelStates;

    if (isClientReady()) {
      saveToLocalStorage(PANEL_LS_STATE_KEY, expPanelStates);
      window[`closeExpPanel-${panelId?.current}`] = setTimeout(() => {
        if (!opened) setDisplayed(false);
      }, 300);
    }

    return () => isClientReady() && clearTimeout(window[`closeExpPanel-${panelId?.current}`]);
  }, [opened]);

  useEffect(() => {
    const hasLocalStorageState = Boolean(panelName && currentPanelStates?.[panelName]);
    const currentState = currentPanelStates?.[panelName] ?? {};

    setOpened(hasLocalStorageState && currentState ? currentState : initialOpened);
    setDisplayed(hasLocalStorageState && currentState ? currentState : initialOpened);
  }, [panelName, currentPanelStates]);
};
