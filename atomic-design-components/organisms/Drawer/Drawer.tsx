import clsx from 'clsx';
import React, { forwardRef, useEffect } from 'react';

import FlexRow from '../../atoms/FlexRow';
import Typography from '../../atoms/Typography';
import { StyledDrawer } from './styled';
import { CloseIcon } from '@/atomic-design-components';

interface IProps {
  absolutePositioned?: boolean;
  isOpenedFixed?: boolean;
  children: any;
  className?: string;
  closeIconPadding?: string;
  closeIconProps?: any;
  isStatic?: boolean;
  headerHeight?: string;
  labelProps?: any;
  side?: 'left' | 'right';
  title?: string | React.ReactNode;
  maxWidth?: string;
  withCloseIcon?: boolean;
  withTitleSection?: boolean;
  opened: any;
  close: any;
  withShadowScreen?: boolean;
  withBluredScreen?: boolean;
}

const closeShadowScreen = (extraClass: string) => {
  const shadowScreen = window.document.querySelector('.shadowScreen');
  if (shadowScreen?.classList.contains(extraClass)) {
    shadowScreen?.classList.remove('shown', extraClass);
    shadowScreen?.setAttribute('style', `height: 100vh`);
  }
};

const Drawer = forwardRef(
  (
    {
      absolutePositioned = false,
      isOpenedFixed = false,
      children,
      className,
      closeIconPadding = '20px',
      closeIconProps,
      isStatic = false,
      headerHeight = '0px',
      labelProps,
      side = 'right',
      title,
      maxWidth,
      withCloseIcon = true,
      withTitleSection = true,
      opened,
      close,
      withShadowScreen = true,
      withBluredScreen = false,
    }: IProps,
    ref: any,
  ) => {
    useEffect(() => {
      if (withShadowScreen) {
        const extraClass = withBluredScreen ? 'blured' : 'shadowed';
        if (opened) {
          const documentElement = window.document;

          const shadowScreen = documentElement.querySelector('.shadowScreen');
          shadowScreen?.classList.add('shown', extraClass);
          shadowScreen?.setAttribute(
            'style',
            `height: ${documentElement.body.clientHeight}px`,
          );
        } else {
          closeShadowScreen(extraClass);
        }
      }
    }, [opened]);

    return (
      <StyledDrawer
        $absolutePositioned={absolutePositioned}
        $isOpenedFixed={isOpenedFixed}
        $closeIconPadding={closeIconPadding}
        className={clsx(
          className,
          'drawer',
          opened && 'opened',
          'displayed',
          side,
          isStatic && 'static',
        )}
        $headerHeight={headerHeight}
        ref={ref}
        maxWidth={maxWidth}
      >
        {withTitleSection && (
          <FlexRow justifyContent='space-between' className='titleRow'>
            {typeof title === 'string' ? (
              <Typography
                type='h2'
                text={title}
                lineHeight='40px'
                {...labelProps}
              />
            ) : (
              title
            )}
            {withCloseIcon && (
              <CloseIcon
                onClick={close}
                wrapperWidth={32}
                wrapperHeight={32}
                {...closeIconProps}
              />
            )}
          </FlexRow>
        )}
        {children}
      </StyledDrawer>
    );
  },
);

Drawer.displayName = 'Drawer';
export default Drawer;
