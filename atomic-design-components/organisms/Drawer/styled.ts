'use client';
import styled from 'styled-components';
import { HEADER_HEIGHT } from '@/constants';

const MENU_HEIGHT = `${parseInt(HEADER_HEIGHT)}px`;

export const StyledDrawer = styled.div<any>`
    display: block;
    position: ${({ $absolutePositioned }) =>
            $absolutePositioned ? 'absolute' : 'fixed'};
    z-index: 120;
    width: 100%;
    max-width: ${({ maxWidth }) => maxWidth};
    background-color: ${({ theme }) => theme.color?.general.darkest};
    transition: transform 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
    //overflow: hidden;
    -webkit-overflow-scrolling: touch;
    top: ${({ $headerHeight }) => $headerHeight};
    height: calc(100% - ${({ $headerHeight }) => $headerHeight} - ${MENU_HEIGHT});
    overflow: hidden;
    padding-bottom: 8px;

    &.mobileMenuDrawer {
        position: fixed;
        z-index: 100;
        height: calc(
                calc(
                        100% - ${({ $headerHeight }) => $headerHeight} - ${MENU_HEIGHT} - env(safe-area-inset-bottom)
                )
        );
    }

    .titleRow {
        position: sticky;
        top: 0;
        background-color: ${({ theme }) => theme.color?.general.darkest};
        border-bottom: 1px solid ${({ theme }) => theme.color?.general.dark};
        z-index: 3;
        padding: 8px 16px;
    }

    &.static {
        transition: none;
    }

    &.opened {
        overflow-y: visible;
        position: ${({ $isOpenedFixed }) => $isOpenedFixed && 'fixed'};

        &.scrollable {
            overflow-y: scroll;
        }

        //& > div {
        //  overflow-x: hidden;
        //}
    }

    &.left {
        transform: translateX(-100%);

        &.opened {
            transform: translateX(0);
        }
    }

    &.right {
        right: 0;
        left: unset;
        transform: translateX(150%);

        &.opened {
            transform: translateX(0);
        }
    }

    &.displayed {
        display: block;
        //will-change: transform;
    }

    @media only screen and (max-width: ${({ theme }) => theme.breakpoints?.md}px) {
        &.right.opened,
        &.left.opened {
            top: ${({ $headerHeight }) => $headerHeight};
        }

        .titleRow {
            padding: 8px;
        }
    }

    @media only screen and (min-width: ${({ theme }) =>
            theme.breakpoints?.md}px) and (max-width: 900px) {
        &.right.opened,
        &.left.opened {
            position: absolute;
            right: 0;
            top: 0;
        }
    }
`;
