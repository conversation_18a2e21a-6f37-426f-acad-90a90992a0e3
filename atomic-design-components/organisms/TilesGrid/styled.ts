'use client';
import styled, { css } from 'styled-components';

import FlexRow from '../../atoms/FlexRow/index.js';

export const StyledTilesGrid: any = styled(FlexRow)<any>`
    width: 100%;
    gap: ${({ rowGap }: { rowGap: string }) => rowGap};

    ${({ breakpoints }: { breakpoints: any }) => {
      if (!breakpoints) return;
      return Object.entries(breakpoints).map(([key, value]: [string, any]) => {
        return css`
          @media ${key} {
            gap: ${value.slides?.spacing || value.spacing}px;

            & .tilesGridItem {
              min-width: calc(
                100% / ${value.slides?.preView || value.perView} - calc(
                    ${value.slides?.spacing || value.spacing}px /
                      ${value.slides?.preView || value.perView} *
                      (${value.slides?.preView || value.perView} - 1)
                  )
              );
              max-width: calc(
                100% / ${value.slides?.preView || value.perView} - calc(
                    ${value.slides?.spacing || value.spacing}px /
                      ${value.slides?.preView || value.perView} *
                      (${value.slides?.preView || value.perView} - 1)
                  )
              );
            }
          }
        `;
      });
    }};
}
`;
