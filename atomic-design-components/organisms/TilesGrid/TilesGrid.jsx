'use client';
import clsx from 'clsx';
import { PropTypes as T } from 'prop-types';

import {
  ALIGN_JUSTIFY_CONTENT_TYPES,
  ALIGN_JUSTIFY_ITEMS_TYPES,
} from '../../../types/types.ts';
import { StyledTilesGrid } from './styled.ts';

const TilesGrid = ({
  alignContent = 'center',
  alignItems = 'center',
  // currentBreakpoint,
  breakpoints,
  children,
  className,
  itemsInRow,
  justifyContent = 'start',
  justifyItems = 'center',
  rowGap,
  tiles = [],
  customChild,
}) => {
  return (
    <StyledTilesGrid
      flexWrap='wrap'
      perView={itemsInRow}
      justifyItems={justifyItems}
      alignItems={alignItems}
      justifyContent={justifyContent}
      alignContent={alignContent}
      rowGap={rowGap}
      className={clsx(className, 'tilesGrid')}
      breakpoints={breakpoints}
    >
      {customChild}
      {(children || tiles).map((tile, i) => (
        <div key={i} className='tilesGridItem'>
          {tile}
        </div>
      ))}
    </StyledTilesGrid>
  );
};

TilesGrid.propTypes = {
  alignContent: T.oneOf(ALIGN_JUSTIFY_CONTENT_TYPES),
  alignItems: T.oneOf(ALIGN_JUSTIFY_ITEMS_TYPES),
  className: T.string,
  currentBreakpoint: T.string,
  itemsInRow: T.number,
  justifyContent: T.oneOf(ALIGN_JUSTIFY_CONTENT_TYPES),
  justifyItems: T.oneOf(ALIGN_JUSTIFY_ITEMS_TYPES),
  rowGap: T.string,
  tiles: T.array,
};

export default TilesGrid;
