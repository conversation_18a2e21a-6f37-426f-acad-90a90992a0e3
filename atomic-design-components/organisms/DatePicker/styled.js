'use client';
import styled from 'styled-components';

export const StyledDatePicker = styled.div`
  width: 180px;
  &.fullWidth {
    width: 100%;
  }
  div[data-slot='base'] {
    border-radius: 6px;
    border: 1px solid #2d4672;
    background: #253a5e;
    color: white;
    padding: 11px 16px;
    > div {
      height: auto;
    }
    div[data-slot='inner-wrapper'] {
      font-size: 13px;
      line-height: 9px;
      font-weight: ${({ theme }) => theme.font.weight.medium};
      text-transform: uppercase;
      gap: 10px;
    }
    div[data-slot='input-field'] {
      padding: 2px 0 0;
    }
    div[aria-label='time zone'] {
      display: none;
    }
    span[data-slot='label'] {
      display: none;
    }
    button {
      width: 20px;
    }
  }
  &.simpleInput {
    div[data-slot='base'] {
      color: #94a3b8;
      background-color: #2e4369;
      border-color: #475f8b;
      border-radius: 6px;
      padding: 15px 16px;
      line-height: 17px;
      font-size: 14px;
      font-weight: 300;
    }
  }
`;
