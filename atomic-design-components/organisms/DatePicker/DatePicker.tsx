import { DatePicker as DatePickerComponent } from '@nextui-org/react';
import { useState } from 'react';
import { StyledDatePicker } from './styled';

const DatePicker = ({
  onChange,
  className,
}: {
  onChange: any;
  className?: string;
}) => {
  let [date, setDate] = useState();
  return (
    <StyledDatePicker className={className}>
      <DatePickerComponent
        className='max-w-[182px]'
        value={date}
        onChange={(date: any) => {
          setDate(date);
          onChange(date);
        }}
        label='Date'
      />
    </StyledDatePicker>
  );
};

export default DatePicker;
