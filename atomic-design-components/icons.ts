import { calendar } from '@/atomic-design-components/icons/calendar';
import { cards } from '@/atomic-design-components/icons/cards';
import { cards2 } from '@/atomic-design-components/icons/cards2';
import { checkboxChecked } from '@/atomic-design-components/icons/checkboxChecked';
import { checkboxUnchecked } from '@/atomic-design-components/icons/checkboxUnchecked';
import { cherry } from '@/atomic-design-components/icons/cherry';
import { chevronDown } from '@/atomic-design-components/icons/chevronDown';
import { chip } from '@/atomic-design-components/icons/chip';
import { chipPlay } from '@/atomic-design-components/icons/chipPlay';
import { clock } from '@/atomic-design-components/icons/clock';
import { cross } from '@/atomic-design-components/icons/cross';
import { cup } from '@/atomic-design-components/icons/cup';
import { dice } from '@/atomic-design-components/icons/dice';
import { dices } from '@/atomic-design-components/icons/dices';
import { disappointedFace } from '@/atomic-design-components/icons/disappointedFace';
import { disappointedFaceCircle } from '@/atomic-design-components/icons/disappointedFaceCircle';
import { discountTicket } from '@/atomic-design-components/icons/discountTicket';
import { eyeSign } from '@/atomic-design-components/icons/eyeSign';
import { eyeSignCross } from '@/atomic-design-components/icons/eyeSignCross';
import { file } from '@/atomic-design-components/icons/file';
import { files } from '@/atomic-design-components/icons/files';
import { filters } from '@/atomic-design-components/icons/filters';
import { fingerprint } from '@/atomic-design-components/icons/fingerprint';
import { fire } from '@/atomic-design-components/icons/fire';
import { giftBonus } from '@/atomic-design-components/icons/giftBonus';
import { giftPromo } from '@/atomic-design-components/icons/giftPromo';
import { gifts } from '@/atomic-design-components/icons/gifts';
import { grid } from '@/atomic-design-components/icons/grid';
import { headphones } from '@/atomic-design-components/icons/headphones';
import { heart } from '@/atomic-design-components/icons/heart';
import { info } from '@/atomic-design-components/icons/info';
import { infoBubble } from '@/atomic-design-components/icons/infoBubble';
import { infoBubbleCircle } from '@/atomic-design-components/icons/infoBubbleCircle';
import { inputError } from '@/atomic-design-components/icons/inputError';
import { instagram } from '@/atomic-design-components/icons/instagram';
import { levelBadge } from '@/atomic-design-components/icons/levelBadge';
import { lifeline } from '@/atomic-design-components/icons/lifeline';
import { lock } from '@/atomic-design-components/icons/lock';
import { logo } from '@/atomic-design-components/icons/logo';
import { logoAnimated } from '@/atomic-design-components/icons/logoAnimated';
import { man } from '@/atomic-design-components/icons/man';
import { medal } from '@/atomic-design-components/icons/medal';
import { newIcon } from '@/atomic-design-components/icons/newIcon';
import { other } from '@/atomic-design-components/icons/other';
import { play } from '@/atomic-design-components/icons/play';
import { plusInCircle } from '@/atomic-design-components/icons/plusInCircle';
import { profile } from '@/atomic-design-components/icons/profile';
import { purse } from '@/atomic-design-components/icons/purse';

import { android } from '@/atomic-design-components/icons/android';
import { apple } from '@/atomic-design-components/icons/apple';
import { arrow } from '@/atomic-design-components/icons/arrow';
import { arrowsRevers } from '@/atomic-design-components/icons/arrowsRevers';
import { bell } from '@/atomic-design-components/icons/bell';
import { cardPlus } from '@/atomic-design-components/icons/cardPlus';
import { cardsConvert } from '@/atomic-design-components/icons/cardsConvert';
import { chartDecreasing } from '@/atomic-design-components/icons/chartDecreasing';
import { chartIncreasing } from '@/atomic-design-components/icons/chartIncreasing';
import { check } from '@/atomic-design-components/icons/check';
import { checkboxInCircle } from '@/atomic-design-components/icons/checkboxInCircle';
import { clockIcon } from '@/atomic-design-components/icons/clockIcon';
import { clockOutlined } from '@/atomic-design-components/icons/clockOutlined';
import { coins } from '@/atomic-design-components/icons/coins';
import { copy } from '@/atomic-design-components/icons/copy';
import { creditCard } from '@/atomic-design-components/icons/creditCard';
import { crossBordered } from '@/atomic-design-components/icons/crossBordered';
import { crown } from '@/atomic-design-components/icons/crown';
import { cyberNetwork } from '@/atomic-design-components/icons/cyberNetwork';
import { emailIncoming } from '@/atomic-design-components/icons/emailIncoming';
import { error } from '@/atomic-design-components/icons/error';
import { favoriteChart } from '@/atomic-design-components/icons/favoriteChart';
import { fullscreen } from '@/atomic-design-components/icons/fullscreen';
import { fullscreenExit } from '@/atomic-design-components/icons/fullscreenExit';
import { giftOpened } from '@/atomic-design-components/icons/giftOpened';
import { graph } from '@/atomic-design-components/icons/graph';
import hamburgerMenu from '@/atomic-design-components/icons/hamburgerMenu';
import { heartShape } from '@/atomic-design-components/icons/heartShape';
import { home } from '@/atomic-design-components/icons/home';
import { infoIcon } from '@/atomic-design-components/icons/infoIcon';
import { infoIconEmpty } from '@/atomic-design-components/icons/infoIconEmpty';
import { letter } from '@/atomic-design-components/icons/letter';
import { levelCup } from '@/atomic-design-components/icons/levelCup';
import { levelUp } from '@/atomic-design-components/icons/levelUp';
import { like } from '@/atomic-design-components/icons/like';
import { loudspeaker } from '@/atomic-design-components/icons/loudspeaker';
import { medal1 } from '@/atomic-design-components/icons/medal1';
import { network } from '@/atomic-design-components/icons/network';
import { percentIcon } from '@/atomic-design-components/icons/percentIcon';
import { placeStar } from '@/atomic-design-components/icons/placeStar';
import { prizePlatform } from '@/atomic-design-components/icons/prizePlatform';
import { prizePlatformMobile } from '@/atomic-design-components/icons/prizePlatformMobile';
import { radioChecked } from '@/atomic-design-components/icons/radioChecked';
import { radioUnchecked } from '@/atomic-design-components/icons/radioUnchecked';
import { reloadArrows } from '@/atomic-design-components/icons/reloadArrows';
import { rocket } from '@/atomic-design-components/icons/rocket';
import { rotateLeft } from '@/atomic-design-components/icons/rotateLeft';
import { roulette } from '@/atomic-design-components/icons/roulette';
import { safe } from '@/atomic-design-components/icons/safe';
import { search } from '@/atomic-design-components/icons/search';
import { seven } from '@/atomic-design-components/icons/seven';
import { shield } from '@/atomic-design-components/icons/shield';
import { slotMachine } from '@/atomic-design-components/icons/slotMachine';
import { slotsMachine } from '@/atomic-design-components/icons/slotsMachine';
import { smartphone } from '@/atomic-design-components/icons/smartphone';
import { smile } from '@/atomic-design-components/icons/smile';
import { star } from '@/atomic-design-components/icons/star';
import { star2 } from '@/atomic-design-components/icons/star2';
import { starFlying } from '@/atomic-design-components/icons/starFlying';
import { stars } from '@/atomic-design-components/icons/stars';
import { telegram } from '@/atomic-design-components/icons/telegram';
import { tooltipArrow } from '@/atomic-design-components/icons/tooltipArrow';
import { twitter } from '@/atomic-design-components/icons/twitter';
import { user } from '@/atomic-design-components/icons/user';
import { verificationPlanet } from '@/atomic-design-components/icons/verificationPlanet';
import { verificationShield } from '@/atomic-design-components/icons/verificationShield';
import { videoPlay } from '@/atomic-design-components/icons/videoPlay';
import { wallet } from '@/atomic-design-components/icons/wallet';
import { walletCross } from '@/atomic-design-components/icons/walletCross';
import { walletTime } from '@/atomic-design-components/icons/walletTime';
import { bellSlashed } from '@/atomic-design-components/icons/bellSlashed';
import { levelBonusDefault } from '@/atomic-design-components/icons/levelBonusDefault';

const Icons = {
  android,
  apple,
  arrow,
  arrowsRevers,
  bell,
  bellSlashed,
  calendar,
  cardPlus,
  cards,
  cards2,
  cardsConvert,
  chartDecreasing,
  chartIncreasing,
  check,
  checkboxChecked,
  checkboxInCircle,
  checkboxUnchecked,
  cherry,
  chevronDown,
  chip,
  chipPlay,
  clock,
  clockIcon,
  clockOutlined,
  coins,
  copy,
  creditCard,
  cross,
  crossBordered,
  crown,
  cup,
  cyberNetwork,
  dice,
  dices,
  disappointedFace,
  disappointedFaceCircle,
  discountTicket,
  emailIncoming,
  error,
  eyeSign,
  eyeSignCross,
  favoriteChart,
  file,
  files,
  filters,
  fingerprint,
  fire,
  fullscreen,
  fullscreenExit,
  hamburgerMenu,
  headphones,
  heart,
  heartShape,
  home,
  giftBonus,
  giftOpened,
  giftPromo,
  gifts,
  graph,
  grid,
  info,
  infoBubble,
  infoBubbleCircle,
  infoIcon,
  infoIconEmpty,
  inputError,
  instagram,
  letter,
  levelBadge,
  levelBonusDefault,
  levelCup,
  levelUp,
  lifeline,
  like,
  lock,
  logo,
  logoAnimated,
  loudspeaker,
  man,
  medal,
  medal1,
  network,
  newIcon,
  other,
  percentIcon,
  placeStar,
  play,
  plusInCircle,
  prizePlatform,
  prizePlatformMobile,
  profile,
  purse,
  radioChecked,
  radioUnchecked,
  reloadArrows,
  rocket,
  rotateLeft,
  roulette,
  safe,
  search,
  seven,
  shield,
  slotMachine,
  slotsMachine,
  smartphone,
  smile,
  star,
  star2,
  starFlying,
  stars,
  telegram,
  tooltipArrow,
  twitter,
  user,
  wallet,
  walletTime,
  walletCross,
  verificationPlanet,
  verificationShield,
  videoPlay,
};

export default Icons;
