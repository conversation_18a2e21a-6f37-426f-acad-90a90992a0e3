import { fontello, gilroy } from './app/fonts';

export const theme = {
  breakpoints: {
    xxxl: 3000,
    xxl: 1680,
    xl: 1440,
    lg: 1050,
    md: 960,
    sm: 620,
    xs: 414,
    xxs: 320,
  },
  gutter: {
    container: {
      side: {
        base: 5,
        xxs: 10,
        xs: 20,
        sm: 20,
        md: 20,
        lg: 20,
        xl: 20,
      },
    },
  },
  color: {
    primary: {
      darker: '#003C80',
      dark: '#0059B7',
      main: '#0083FF',
      light: '#60A5FA',
    },
    secondary: {
      main: '#FB923C',
      dark: '#FF8800',
    },
    general: {
      black: '#00031A',
      white: '#ffffff',
      lightest: '#F8FAFC',
      lighter: '#CBD5E1',
      light: '#94A3B8',
      dark: '#334155',
      darker: '#1E293B',
      darkest: '#0F172A',
    },
    status: {
      success: '#489F37',
      warning: '#B3870E',
      error: '#EB5645',
      new: '#3392EA',
      neutral: '#8B8D8F',
    },
  },
  font: {
    family: {
      primary: gilroy,
      customUsz: fontello,
    },
    size: {
      h1: {
        value: '28px',
        lineHeight: '36px',
      },
      h2: {
        value: '24px',
        lineHeight: '32px',
      },
      h3: {
        value: '20px',
        lineHeight: '28px',
      },
      body1: {
        value: '14px',
        lineHeight: '20px',
      },
      body2: {
        value: '16px',
        lineHeight: '24px',
      },
      caption1: {
        value: '14px',
        lineHeight: '16px',
      },
      caption2: {
        value: '12px',
        lineHeight: '16px',
      },
    },
    weight: {
      regular: 400,
      medium: 500,
      bold: 600,
    },
  },
  size: {
    border: {
      radius: {
        main: '8px',
        smaller: '4px',
        big: '12px',
        bigger: '16px',
        biggest: '800px',
      },
    },
    height: {
      header: {
        mobile: '68px',
        desktop: '100px',
      },
      // footer: {
      //   desktop: '72px',
      //   mobile: '83px',
      // },
    },
  },
  grid: {
    xl: {
      columns: {
        count: 8,
        columnWidth: null,
        gapWidth: '40px',
      },
      filtersCols: 3,
    },
    lg: {
      columns: {
        count: 6,
        columnWidth: null,
        gapWidth: '30px',
      },
      filtersCols: 3,
    },
    md: {
      columns: {
        count: 6,
        columnWidth: null,
        gapWidth: '20px',
      },
      filtersCols: 3,
    },
    sm: {
      columns: {
        count: 3,
        columnWidth: null,
        gapWidth: '15px',
      },
      filtersCols: 4,
    },
    xs: {
      columns: {
        count: 3,
        columnWidth: null,
        gapWidth: '10px',
      },
      filtersCols: 4,
    },
    xxs: {
      columns: {
        count: 3,
        columnWidth: null,
        gapWidth: '10px',
      },
      filtersCols: 4,
    },
    base: {
      columns: {
        count: 3,
        columnWidth: null,
        gapWidth: '10px',
      },
      filtersCols: 4,
    },
  },
  get components() {
    return {
      button: {
        standard: {
          primary: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.primary.main,
              'border-radius': '48px',
              padding: '16px 24px',
            },
            medium: {
              color: this.color.general.lightest,
              'background-color': this.color.primary.main,
              'border-radius': '48px',
              padding: '12px 16px',
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.primary.main,
              'border-radius': '48px',
              padding: '8px 16px',
            },
          },
          secondary: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-radius': '48px',
              padding: '16px 24px',
            },
            medium: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-radius': '48px',
              padding: '12px 16px',
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-radius': '48px',
              padding: '8px 16px',
            },
          },
          transparent: {
            large: {
              color: this.color.general.lightest,
              'background-color': 'transparent',
              'border-radius': '48px',
              padding: '16px 24px',
            },
            medium: {
              color: this.color.general.lightest,
              'background-color': 'transparent',
              'border-radius': '48px',
              padding: '12px 16px',
            },
            small: {
              color: this.color.general.lightest,
              'background-color': 'transparent',
              'border-radius': '48px',
              padding: '8px 16px',
            },
          },
        },
      },
      typography: {
        h1: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h1.lineHeight,
              'font-size': this.font.size.h1.value,
              'font-weight': this.font.weight.bold,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h1.lineHeight,
              'font-size': this.font.size.h1.value,
              'font-weight': this.font.weight.bold,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h3.lineHeight,
              'font-size': this.font.size.h3.value,
              'font-weight': this.font.weight.bold,
            },
          },
        },
        h2: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h2.lineHeight,
              'font-size': this.font.size.h2.value,
              'font-weight': this.font.weight.bold,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h2.lineHeight,
              'font-size': this.font.size.h2.value,
              'font-weight': this.font.weight.bold,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h3.lineHeight,
              'font-size': this.font.size.h3.value,
              'font-weight': this.font.weight.bold,
            },
          },
        },
        h3: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h3.lineHeight,
              'font-size': this.font.size.h3.value,
              'font-weight': this.font.weight.bold,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h3.lineHeight,
              'font-size': this.font.size.h3.value,
              'font-weight': this.font.weight.bold,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.h3.lineHeight,
              'font-size': this.font.size.h3.value,
              'font-weight': this.font.weight.bold,
            },
          },
        },
        sub1: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.medium,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.medium,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.medium,
            },
          },
        },
        sub2: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.bold,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.bold,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.bold,
            },
          },
        },
        sub3: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.bold,
              'text-transform': 'uppercase',
              'letter-spacing': '0.28px',
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.bold,
              'text-transform': 'uppercase',
              'letter-spacing': '0.28px',
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.bold,
              'text-transform': 'uppercase',
              'letter-spacing': '0.28px',
            },
          },
        },
        body1: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.regular,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.regular,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.regular,
            },
          },
        },
        body2: {
          black: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
            },
            medium: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
            },
            small: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
            },
          },
        },
        label1: {
          black: {
            large: {
              color: this.color.general.lighter,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
            },
            medium: {
              color: this.color.general.lighter,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
            },
            small: {
              color: this.color.general.lighter,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
            },
          },
        },
        label2: {
          black: {
            large: {
              color: this.color.general.light,
              'line-height': this.font.size.caption2.lineHeight,
              'font-size': this.font.size.caption2.value,
              'font-weight': this.font.weight.regular,
            },
            medium: {
              color: this.color.general.light,
              'line-height': this.font.size.caption2.lineHeight,
              'font-size': this.font.size.caption2.value,
              'font-weight': this.font.weight.regular,
            },
            small: {
              color: this.color.general.light,
              'line-height': this.font.size.caption2.lineHeight,
              'font-size': this.font.size.caption2.value,
              'font-weight': this.font.weight.regular,
            },
          },
        },
      },
      input: {
        primary: {
          black: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-color': this.color.general.dark,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
              'border-radius': this.size.border.radius.main,
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-color': this.color.general.dark,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
              'border-radius': this.size.border.radius.main,
            },
          },
        },
      },
      select: {
        primary: {
          black: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-color': this.color.general.dark,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.regular,
              'border-radius': this.size.border.radius.main,
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'border-color': this.color.general.dark,
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.regular,
              'border-radius': this.size.border.radius.main,
            },
          },
        },
      },
      tag: {
        standard: {
          primary: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darkest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.biggest,
              padding: '11px 12px',
            },
            medium: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darkest,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.biggest,
              padding: '7px 12px',
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darkest,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.biggest,
              padding: '2px 8px',
            },
          },
          gray: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.biggest,
              padding: '11px 12px',
            },
            medium: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.biggest,
              padding: '7px 12px',
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.general.darker,
              'line-height': this.font.size.caption1.lineHeight,
              'font-size': this.font.size.caption1.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.biggest,
              padding: '2px 8px',
            },
          },
          lightGray: {
            large: {
              color: this.color.general.lightest,
              'background-color': this.color.general.dark,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.smaller,
              padding: '11px 12px',
            },
            medium: {
              color: this.color.general.lightest,
              'background-color': this.color.general.dark,
              'line-height': this.font.size.caption2.lineHeight,
              'font-size': this.font.size.caption2.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.smaller,
              padding: '7px 12px',
            },
            small: {
              color: this.color.general.lightest,
              'background-color': this.color.general.dark,
              'line-height': this.font.size.caption2.lineHeight,
              'font-size': this.font.size.caption2.value,
              'font-weight': this.font.weight.regular,
              border: `1px solid ${this.color.general.dark}`,
              'border-radius': this.size.border.radius.smaller,
              padding: '2px 8px',
            },
          },
        },
      },
      link: {
        standard: {
          main: {
            large: {
              'line-height': this.font.size.body1.lineHeight,
              'font-size': this.font.size.body1.value,
              'font-weight': this.font.weight.regular,
            },
          },
        },
      },
      badge: {
        standard: {
          primary: {
            large: {
              'line-height': '10px',
              'font-size': '10px',
              'font-weight': this.font.weight.regular,
              'border-radius': '50%',
            },
          },
        },
      },
      popupAlert: {
        standard: {
          default: {
            large: {
              color: this.color.general.lightest,
              'line-height': this.font.size.body2.lineHeight,
              'font-size': this.font.size.body2.value,
              'font-weight': this.font.weight.regular,
              'border-radius': this.size.border.radius.main,
            },
          },
        },
      },
    };
  },
  appName: 'app',
  name: 'like-casino',
};
